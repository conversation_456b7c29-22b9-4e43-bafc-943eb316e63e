.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/10.
 */
.wrapContent {
  height: 100%;
  display: flex;
}

.antLayoutSider {
  z-index: 1000;
  transition: all .3s;
}

.LayoutLogo {
  position: relative;
}

.LayoutLogo+div {
  background: #151737 !important;
}

.LayoutLogo a {
  color: #108ee9;
  text-decoration: none;
}

.LayoutLogoLink {
  padding-left: 8px;
  height: 48px;
  line-height: 48px;
  color: #108ee9;
  user-select: none;
  cursor: pointer;
  font-size: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;

}

.LayoutLogoTag {
  position: absolute;
  top: 12px;
  right: 5px;
  display: inline-block;
  padding: 2px 4px;
  line-height: 20px;
  font-size: 12px;
  color: #52c41a;
  background: #1f5124;
  border-radius: 10px;
}

.LayoutLogoTagAlpha {
  top: 12px;
  right: 5px;
  display: inline-block;
  padding: 2px 6px;
  line-height: 20px;
  font-size: 12px;
  color: #fff;
  background: #52c41a;
  border-radius: 10px;
}

.antLayoutMain {
  background-color: #fff;
  transition: all .3s;
  height: 100vh;
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 0;
  min-width: 786px;
  overflow: auto;
}

.antLayoutTop {
  height: 44px;
  line-height: 44px;
  //.dragTabContainer {
  //  line-height: 1;
  //}
  // background-color: #Fff;
  background-color: #151737;
  border-left: 1px solid rgba(#b3b7c1, );
  z-index: 80;
  display: flex;
  justify-content: space-between;
  transition: all .3s;
  //flex: 2;
  position: sticky;
  top: 0;
}

.breadcrumb {
  margin-left: 45px;
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;

  span {
    color: #b3b7c1;
  }

  span:last-child {
    color: #ffffff;
  }
}

.antLayoutLeft {
  // width: 800px;
  vertical-align: middle;
  /* flex: 2; */
}

.antLayoutRight {
  height: 100%;
  padding-right: 10px;

  // max-width: 610px;
  :global {
    .shein-components_apmtoolbar_toolbar {
      color: #fff;

      &:hover {
        color: #1890ff;
      }
    }
  }
}



// 菜单
.quickEntry {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.dropdown {
  margin-right: 15px;
  color: #fff;
  cursor: pointer;
  position: relative;

  &:hover {
    color: #1890ff;
  }
}

.antLayoutContent {
  padding: 12px;
  //padding-top: 14px;
  background-color: #fff;
  //height: 100%;
  // height: calc(100vh - 125px);
  display: flex;
  flex-direction: column;
}

//@media screen and (max-width: 650px) {
//  .antLayoutSider, .antLayoutTop {
//    display: none;
//  }
//
//  .antLayoutMain {
//    padding-left: 0;
//    padding-top: 0;
//  }
//
//  .ant-calendar-picker-input {
//    height: 32px;
//  }
//}
//
//@media screen and (max-width: 800px) {
//  .antLayoutRight {
//    display: none;
//  }
//}

.messageIcon {
  position: fixed;
  top: 10px;
  right: 400px;
  display: none;
}

.warehouse {
  display: inline;
  margin-right: 10px;
}

.linkToOperatorGroupsManage {
  white-space: nowrap;
}

.time {
  display: inline-block;
  color: #fff;
}

// 选择时差 & 选择仓库，选中项hover时高亮
.timeSelect,
.warehouseSelect {
  color: #fff;

  .selectedText {
    &:hover {
      color: #1890ff !important;
    }
  }
}

.timeSelect>div,
.warehouseSelect>div {
  background-color: #151737 !important;
  border: none !important;
  box-shadow: none !important;
  ;
}

.warehouseSelectedText {
  text-align: right;
  min-width: 70px;
}

.warehouseSelect {
  min-width: 70px;
  width: auto;
}

.labelMargin {
  margin-right: 12px;
  font-weight: 450;
}

.bottomTip {
  color: #f5bd02;
  margin-top: 20px;
}

.renderItem {
  padding-right: 16px;

  .icon {
    font-size: 16px;
    margin-right: 6px;
    color: #fff;
  }
}

.renderLeaf {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .collectedIcon,
  .starIcon {
    justify-self: flex-end;
    padding: 0 5px;
    font-size: 16px;
    cursor: pointer;
  }

  .collectedIcon,
  .starIcon:hover {
    color: #f5bd02;
  }

  // 收藏动画
  .starAnimation {
    position: relative;
    animation: banuce 0.2s ease 0s 2 alternate;
  }

  .starAnimation::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    border-radius: 50%;
    background: rgba(254, 208, 1, 0.1);
    opacity: 0;
    animation: banuce 0.3s ease 0.02s 1 alternate;
  }

  .starAnimation::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    width: 10%;
    height: 10%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background: transparent;
    box-shadow: 10px -10px 0 #f5bd02, 10px 10px 0 #f5bd02,
      -10px -10px 0 #f5bd02, -10px 10px 0 #f5bd02;
    opacity: 0;
    animation: show 0.2s steps(1, end) 0s 1;
  }
}

/* 放大 */
@keyframes banuce {
  0% {
    transform: scale(0.8);
  }

  100% {
    transform: scale(1.35);
  }
}

/* 缩放加透明度变化 */
@keyframes circle {
  0% {
    transform: scale(0.2);
    opacity: 0.8;
  }

  100% {
    transform: scale(1.5);
    opacity: 1;
  }
}

/* 出现。用opacity来控制元素隐藏显示 */
@keyframes show {
  0% {
    opacity: 1;
  }
}

.categoryButtonClass {
  margin-left: 12px;
}

.loadingBar {
  padding: 0 10px 0 3px;
  display: inline-block;
  vertical-align: middle;
}

.loadingDot {
  float: left;
  width: 6px;
  height: 6px;
  margin: 0 4px;
  background-color: black;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  opacity: 0;

  -webkit-box-shadow: 0 0 2px black;
  -moz-box-shadow: 0 0 2px black;
  -ms-box-shadow: 0 0 2px black;
  -o-box-shadow: 0 0 2px black;
  box-shadow: 0 0 2px black;

  -webkit-animation: loadingFade 1s infinite;
  -moz-animation: loadingFade 1s infinite;
  animation: loadingFade 1s infinite;
}

.loadingDot:nth-child(1) {
  -webkit-animation-delay: 0s;
  -moz-animation-delay: 0s;
  animation-delay: 0s;
}

.loadingDot:nth-child(2) {
  -webkit-animation-delay: 0.1s;
  -moz-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.loadingDot:nth-child(3) {
  -webkit-animation-delay: 0.2s;
  -moz-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.loadingDot:nth-child(4) {
  -webkit-animation-delay: 0.3s;
  -moz-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

@-webkit-keyframes loadingFade {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 0.8;
  }

  100% {
    opacity: 0;
  }
}

@-moz-keyframes loadingFade {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 0.8;
  }

  100% {
    opacity: 0;
  }
}

@keyframes loadingFade {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 0.8;
  }

  100% {
    opacity: 0;
  }
}


/* 操作指引 START */
.operatingBox {
  position: fixed;
  z-index: 99;
  right: 20px;
  top: 200px;
  width: 70px;
  background-color: #fff;
  /* transition: all .15s ease-in-out; */
  border-radius: 10px;
  box-shadow: 0 2px 2px 2px #eee;
}

.operatingBtn {
  width: 70px;
  height: 70px;
  display: flex;
  border-radius: 10px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.operatingActive,
.operatingBtn:hover,
.operatingBtn:active,
.operatingItem:hover {
  background-color: var(--primary-color, #1890ff);
  color: #fff;
  /* transition: all .15s ease-in-out; */
}

.operatingContent {
  position: fixed;
  /* right: 90px; */
  bottom: 0;
  color: var(--gray-900, #212529);
  background-color: #fff;
}

.operatingItem {
  white-space: nowrap;
  padding: 4px 16px;
  border-radius: 14px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all .15s ease-in-out;
  box-shadow: 0 2px 2px 2px #eee;
}

/* 操作指引 END */

.netCheckItem {
  margin-bottom: 10px;
}

.netCheckText {
  display: inline-block;
  margin-right: 12px;
}

.successStyle {
  color: green;
}

.failStyle {
  color: red;
}


/*stretchMenu start*/
.stretchMenu {
  background: #151737 !important;

  .menuSearch {
    padding: 8px 16px;
  }

  :global {
    .shein-components_stretchmenu_expandIcon {
      border: none !important;

      .so-icon {
        color: #fff !important;
      }
    }

    .so-input input {
      padding-left: 5px;
      line-height: 20px;
      color: #b3b7c1;
    }

    .so-input,
    .so-input:hover,
    .so-input input {
      border: none !important;
      border-radius: 16px !important;
      background: #293053 !important;
    }

    .so-input:focus {
      box-shadow: none;
    }

    .so-menu ul.so-menu-root {
      background-color: #151737;
    }

    .so-menu-title {
      padding: 10px 0 10px 16px;
      min-height: 40px;
      height: auto;
      line-height: 20px;
      white-space: normal;
      color: #fff !important;

      .linkBlock {
        padding-right: 16px;
      }
    }
  }

  .emptyText {
    margin: 100px auto;
    width: 130px;
    font-size: 14px;
    color: #ccc;
    text-align: center;
  }
}

.inputWidth {
  width: 200px;
}

/*stretchMenu end*/

.commonSeparator {
  vertical-align: middle;
  display: inline-block;
  border-radius: 2px;
  width: 4px;
  height: 14px;
  margin-right: 5px;
}

.verticalMiddle {
  vertical-align: middle;
}

.blueSeparator {
  .commonSeparator;
  background-color: #0059ce;
}

.orangeSeparator {
  .commonSeparator;
  background-color: #ffb979;
}

.popoverContent {
  padding: 12px 16px;
  font-size: 14px;
  color: rgba(0, 0, 0, .65);

  .onlineIconStyle {
    color: #ffb979;
    padding-right: 10px;
  }

  .offlineIconStyle {
    color: #0059ce;
    padding-right: 10px;
  }

  .workBarcodeArea {
    display: flex;
    padding-top: 10px;

    .workLabel {
      line-height: 32px;
      padding-right: 10px;
    }
  }

  .workFooterArea {
    text-align: right;
    padding-top: 15px;
  }

  .recordList {
    padding-top: 10px;

    .rankText {
      font-weight: bold;
      color: #ffb979;
    }
  }
}

.iconMargin {
  margin-right: 6px;

  &:hover {
    color: white !important;
  }
}

// 设置上机/下机按钮样式
.workButton {
  &:hover {
    background-color: #ffb979 !important;
    color: #fff;
  }

  background-color: #ffb979 !important;
  color: #fff;
}

.announcementNum {
  height: 16px;
  line-height: 14px;
  min-width: 16px;
  border-radius: 10px;
  padding: 0 3px;
  text-align: center;
  font-size: 12px;
  bottom: 1px;
}

.dot {
  height: 6px;
  width: 6px;
  z-index: 10;
  border-radius: 100%;
  background-color: #f5222d;
  display: inline-block;
  box-shadow: 0 0 0 1px #fff;
  position: absolute;
  right: 10px;
  top: -5px
}

.userOptionsDropdown {
  top: -1px;
  left: -10px;

  div {
    line-height: normal;
  }

  button {
    background-color: #151737;
    outline: none;
    border: none;

    &:hover,
    &:active,
    &:focus,
    &:visited {
      background-color: #151737;
      outline: none;
      border: none;
    }

    svg {
      fill: #fff;
      width: 14px;
      margin-top: -4px;
      margin-left: -18px;
    }
  }

  .logout {
    &:hover {
      color: #1890ff;
    }
  }

  .labelCursor {
    cursor: pointer;
    display: inline-block;
    width: 100%;
  }
}

.dropdownIcon {
  font-size: 12px !important;
  margin-right: 1px;
}

.settingLabel {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.chromeVersion {
  color: #ec3942;
  margin-left: 56px;
}

.recommendBrowserVersion {
  color: #f5bd02;
  margin-left: 15px;
}

.warnTip {
  color: #f59632;
}

.changeInfo {
  .greenColor {
    color: #32b45a;
  }

  .orangeColor {
    color: #f59632;
  }

  .redColor {
    color: #ea2626;
  }
}

.viewMsg {
  margin-top: 5px;
  font-size: 16px;
  cursor: pointer;
  color: #3399fe;
}

.wmsVersionModal {
  position: fixed;
  top: 10px;
  left: 10px;
  background-color: white;
  padding: 8px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  font-size: 14px;

  .wmsVersionFooter {
    padding-top: 24px;
    text-align: right;
  }
}

.wmsVersionModal>.wmsVersionBtn:not(:last-child) {
  margin-right: 6px;
}

.wmsVersionBtn {
  cursor: pointer;
  display: inline-block;
  margin-left: 8px;
  margin-bottom: 0;
  border: 1px solid #d9d9d9;
  background-image: none;
  font-weight: 400;
  outline: none;
  text-align: center;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  vertical-align: middle;
  white-space: nowrap;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 14px;
  -ms-user-select: none;
  user-select: none;
  background-color: #fff;
  color: #333e59;
  fill: #333e59;
}

.wmsVersionBtnPrimary {
  border-color: transparent;
  background-color: #197afa;
  color: #fff;
  fill: #fff;
  transition: all .15s ease-in-out;

  &:hover {
    background-color: #3288fb;
    color: #fff;
  }
}

.wmsVersionBtnDefault {
  border-color: #d9d9d9;
  background-color: #fff;
  color: #333e59;
  fill: #333e59;
  transition: all .15s ease-in-out;

  &:hover {
    border-color: #197afa;
    background-color: #fff;
    color: #197afa;
  }
}

.borderRadius {
  border-radius: 10px !important;
}

// 左上角版本更新弹窗样式覆盖

.modalContainer {
  :global {

    .so-card-header::after,
    .so-card-footer:after {
      content: none !important;
      height: 0px;
    }

    .so-card-body {
      padding-bottom: 5px !important;
    }

    .so-card-footer {
      border-radius: 10px !important;
      padding: 10px 10px !important;
    }
  }

  position: absolute !important;
  left: 10px;
  top: 10px !important;
  .borderRadius
}

.iconRemark {
  color: #cecece;
  margin-left: 4px;
}

.uniformCaret {
  color: #fff !important;

  svg {
    fill: #fff !important;
    width: 14px;
    margin-top: -2px;
  }
}

.newBgColor {
  background-color: #f4f5f8;
}

.tabArea {
  background-color: #fff;
  padding: 1px 1px;
  z-index: 11;
}

.jiraNotice {
  position: absolute;
  top: -20px;
  right: -54px;
  background-color: #1890ff;
  font-size: 12px;
  white-space: nowrap;
  color: #fff;
  padding: 2px 8px;
  border-radius: 8px 8px 8px 0;
}
