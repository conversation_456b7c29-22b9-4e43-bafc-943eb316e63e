/* eslint-disable import/no-cycle */
/**
 * Created by dell on 2017/1/3.
 */
import { sendPostRequest } from '@src/server/common/public';

// 校验当前用户是否绑定打包工位/校验工位以及操作人 以及扫码工位条码
export const workingApi = (param) => sendPostRequest({
  url: '/second_sowing/scan_work_location_code',
  param,
}, process.env.WOS_URI);

// 上/下机更新工位信息
export const updateWorkingApi = (param) => sendPostRequest({
  url: '/second_sowing/update_on_work_location_info',
  param,
}, process.env.WOS_URI);

// 获取用户昨日打包数量
export const getSowingGoodsNumApi = (param) => sendPostRequest({
  url: '/package/get_user_yesterday_package_num',
  param,
}, process.env.WPOC_URI);

/**
 * pda二分获取用户当日打包数量
 * @param param
 * @returns {*}
 */
// export const getSecondSowingNumApi = (param) => sendPostRequest({
//   url: '/package/package_packed_statistics_info_request',
//   param,
// }, process.env.WPOC_URI);

/**
 * 根据当前用户获取角色列表
 * @param param
 * @returns {*}
 */
export const getPositionListByUserApi = (param) => sendPostRequest({
  url: '/authPositon/get_position_list_by_user',
  param,
}, process.env.WAS_URI);

/**
 * 用户有权限的仓库列表
 */
export const getPermissionWarehouse = (param) => sendPostRequest({
  url: '/data_permission/warehouse/list',
  param,
}, process.env.WAS_URI);

/**
 * 返回用户有权限的子仓列表
 */
export const getPermissionSubWarehouse = (param) => sendPostRequest({
  url: '/data_permission/sub_warehouse/list',
  param,
}, process.env.WAS_URI);

/**
 * 获取仓库详情 - 主要是时差
 */
// export const getWarehouseDetailById = (param) => sendPostRequest({
//   url: '/warehouse/get_warehouse_detail',
//   param,
// }, process.env.BASE_URI_WMD);

/*
 * 获取cloud-message token
 */
export const getCloudMessageTokenAPI = () => sendPostRequest({
  url: '/cloud_message/generate_token',
}, process.env.WGS_FRONT_BRIDGE);

// 组员排班-验收登入是否过期
export const timeoutServerAPI = (param) => sendPostRequest({
  url: '/member_login_time_limit_config/check_login_time',
  param,
}, process.env.OSM_FRONT);

// 注销
export const logoutServerAPI = (param) => sendPostRequest({
  url: '/logout',
  param,
}, process.env.WAS_FRONT_LOGIN);

// JIRA单跟进 判断是否跳转外部系统
export const getSignatureAPI = (param) => sendPostRequest({
  url: '/jira_follow/get_cms_login_signature',
  param,
}, process.env.WAS_URI);

// 获取帮助中心系统信息
export const getHfcClientAuthAPI = (param) => sendPostRequest({
  url: '/hfc/get_login_auth_token',
  param,
}, process.env.WGS_FRONT_BRIDGE);

// 这两个getWarehouseApi接口移到这里（不用common.js）是为了解决循环引用的问题
// 获取仓库
export const getWarehouseApi = (param) => sendPostRequest({
  url: '/warehouse/get_list',
  param,
}, process.env.BASE_URI_WMD);

// 查询过期时间
export const queryExpiryDate = (param) => sendPostRequest({
  url: '/inf_user/query_expiry_date',
  param,
}, process.env.OSM_FRONT);
