import { t } from '@shein-bbl/react';
import moment from 'moment';
import { userNameSessionStorage, userNoSessionStorage } from '@src/lib/storage-new';

// 看板页面
export const boardPage = [
  '/home/<USER>',
  '/home/<USER>',
];

// 需要请求上机/下机信息的页面路径
export const workUrlArr = [
  '#/outbound/packChecking/rechecking',
  '#/outbound/packChecking/rechecking-new',
  '#/outbound/packChecking/rechecking-refactor',
  '#/combination/pre-rechecking-refactor/list',
  '#/combination/package-review-refactor',
];

// 测试版本/正式版本切换提示语
export const infoData = {
  features: t('新的打印功能（解决打印慢的问题）。'),
  page: [
    t('出库管理/打包复核/精准出库扫描---》打印包裹标签功能'),
  ],
};

// apm- pv/uv统计图展示信息栏
export const apmToolBarInfo = {
  types: [
    { name: t('访问'), keys: ['pv', 'uv'] },
    {
      name: t('关键用户'),
      keys: 'user',
    },
  ],
  category: [
    {
      name: t('近一天'),
      startTime: () => moment().subtract(1, 'days').format('YYYY-MM-DD'),
      endTime: () => moment().subtract(0, 'days').format('YYYY-MM-DD'),
    },
    {
      name: t('近一周'),
      startTime: () => moment().subtract(7, 'days').format('YYYY-MM-DD'),
      endTime: () => moment().subtract(0, 'days').format('YYYY-MM-DD'),
    },
    {
      name: t('近一月'),
      startTime: () => moment().subtract(30, 'days').format('YYYY-MM-DD'),
      endTime: () => moment().subtract(0, 'days').format('YYYY-MM-DD'),
    },
  ],
  userColumns: [
    { title: t('序号'), render: 'index', width: 40 },
    { title: t('姓名'), render: 'key', width: 80 },
    { title: t('累计 PV'), render: 'doc_count', width: 80 },
  ],
};

export const iconList = {
  '/qms': 'biaozhunhuaruku',
  '/in-warehouse': 'zaikukucun',
  '/outbound': 'tiaobochuku',
  '/transferBill-manage': 'gd-ts',
  '/board': 'chakandaping',
  '/wms/sysconfig': 'odec-system',
  'wms/sysconfig': 'odec-system',
  '/management-sys': 'odec-airplay',
  '/management': 'repair',
  '/human-mgt': 'odec-people',
  '/person-manage': 'odec-people',
  '/charging': 'exchange',
  '/jira-follow-up': 'jira',
};

// 版本推送开关
export const CLOSE = 0;

// 网络检测 - 后端PING空接口
export const defaultSystemNetCheck = [
  {
    system: 'WIS', url: `${process.env.WIS}/outside/frond_ping`, averageTime: '', failReason: '',
  },
  {
    system: 'WOS', url: '/wos/outside/frond_ping', averageTime: '', failReason: '', method: 'GET',
  },
  {
    system: 'WPOC', url: '/wpoc/outside/frond_ping', averageTime: '', failReason: '', method: 'GET',
  },
  {
    system: 'WMS', url: '/wms/outside/frond_ping', averageTime: '', failReason: '', method: 'GET',
  },
];

export const PRE_RECHECKING_CLOSE_BOX = 10;

export const getFetchWithCacheEnableKey = () => {
  const userName = userNameSessionStorage.getItem(); // 中文名
  const userNo = userNoSessionStorage.getItem(); // 工号
  return `fetchCacheEnable${userNo + userName}`;
};
