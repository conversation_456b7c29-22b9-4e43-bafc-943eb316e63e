import React from 'react';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import { Message, Modal } from 'shineout';
import Watermark from '@shein-components/WatermarkSdk';
import { conf } from 'sheinq';
import {
  apolloFormatObj, getLoginInfo,
} from '@src/lib/dealFunc';
import { invocationRecorder } from '@src/lib/InvocationRecorder';
import showMessage, { reducerErrorMsg } from '@src/lib/modal';
import {
  getApolloConfigAPI,
  getMenusApi,
  getWarehouseDetailById,
  getUserInfoApi, checkUrlPermissionAPI,
  queryWaitCountAPI,
} from '@src/server/common/common';
import {
  getApolloConfig,
} from '@src/server/common/cache-api';
import {
  degradeFrontLocalStorage,
  enNameSessionStorage,
  formatNameSessionStorage,
  isDebugModeLocalStorage,
  isPDFHistoryModeLocalStorage,
  isSyncReloadLocalStorage,
  messageUserLocalStorage,
  timeDiffNumLocalStorage,
  timeRangeConfigLocalStorage,
  timeTransformPagesLocalStorage,
  userNameSessionStorage,
  userNoSessionStorage,
  versionLocalStorage,
  warehouseIdLocalStorage,
  wmsMenuObjSessionStorage,
  scanWorkLocationCodeSuccessSessionStorage,
  upgradeSubWarehouseIdSessionStorage,
  workLocationCodeSessionStorage,
} from '@src/lib/storage-new';
import { fullScreenRoutes } from '@src/lib/constants';
import { clearIndexedDBInfo, systemDataDB, clearFetchCache } from '@wms/wms-public-tools';
import { getLang, langPickerGroup } from '@src/lib/language-enum';
import {
  isPDFHistoryMode, isStorageOverTime, setPDFHistoryMode, setTimeStorage,
} from '@src/lib/storage';
import { getCurrentWareHouseServer } from '@src/server/user/warehouse';
import { cloudMessagePublishApi } from '@src/server/common/cloud-message';
import { getData, setData as cloudSetData } from '@src/lib/cloud-sdk';
import { setSessionStoragePDFKey } from '@src/lib/print-new';
import {
  normalizeMenu, normalizeBreadcrumb, flattenArray, getAllLeaf, getAuthMenu,
  dispatchWarehouseChange, syncWarehouse,
} from '@src/component/nav/util';
import { selectSubWarehouse } from '@src/server/basic/sub-warehouse';
import {
  getWarehouseApi,
  // 非敏感词
  getCloudMessageTokenAPI,
  getPermissionSubWarehouse, getPermissionWarehouse, getSignatureAPI, getSowingGoodsNumApi,
  logoutServerAPI,
  timeoutServerAPI, updateWorkingApi, workingApi, getHfcClientAuthAPI,
} from './server';
import styles from './style.less';
import { getFetchWithCacheEnableKey } from './constants';

// 仓库相关数据
export const warehouseInfo = {
  subWarehouseList: [], // 分仓项目 权限子仓下拉数据
  warehouseList: [],
  currentWarehouseList: [], // 有权限的仓库列表
  warehouseId: warehouseIdLocalStorage.getItem() * 1 || '',
};

// 时差相关信息
export const timeDiffInfo = {
  timeDiff: timeDiffNumLocalStorage.getItem() * 1 || 0,
  timeList: [],
};

/**
 * 测试token敏感词检查
 */
const defaultState = {
  current: '/',
  menus: [],
  linkList: [],
  userName: '',
  ready: false,
  list: [],
  version: versionLocalStorage.getItem() || '',
  showSelectModal: false,
  permissionSubWarehouseList: [],
  lang: getLang(),
  langList: langPickerGroup,
  // 非敏感词
  setToken: false,
  btnLoading: false,
  workStatus: 1, // 1 待上机页面 2上机扫描页面 3 上机成功页面  4上机中  5 下机确认页面
  workLocationCode: '', // 工位编码
  onlineTotalTime: '', // 上机总时长
  rank: '', // 我的排名
  total: '', // 总排位
  secondSowingCount: '', // 今日工作量
  isSuperAdmin: false, // 是否超级管理员
  unreadNum: 0,
  bc: '', // 建立广播频道 用于同源下多tab间通信
  isSyncReload: true, // 是否开启蓝绿版本推送同步刷新
  isDebugMode: true, // 是否开启调试模式
  isPDFHistoryMode: isPDFHistoryMode(), // 是否开始pdf历史记录模式
  operationList: [], // 操作指引列表
  pdaGuidelineList: [], // 操作指引总表
  switchFlag: 1, // 导入开关
  OwisSwitchFlag: 1, // owis 导出开关
  formatName: '', // 右上角用户名(有英文名 就展示英文名+工号如果有的话 否则展示中文名)
  isWsConnect: 0, // 是否建立ws连接 0-请求中 1-建立成功 2-建立失败
  wsFailReason: '', // ws连接建立失败原因
  isShowQuickEntry: false, // 是否开启
  quickEntryList: [], // 快捷数据配置
  activeTab: null, // 当前高亮的tab
  ...timeDiffInfo,
  ...warehouseInfo,
  settingVisible: false, // 设置弹窗
  // 帮助中心
  authHfcCode: '',
  // userWarehouseIdList: [], // 用户绑定仓库id
  // userIP: '', // 用户所在ip
  // wattConfigObj: {}, // watt配置
  qaList: [], // 问答知识库列表数据
  grayUserSwitch: 1, // 灰度验收名单新逻辑开关
  isCacheEnable: false, // 接口数据缓存开关
  showApmUv: false,
  redCount: 0, // 机器人右上角数量
  userType: 0, // 用户类型 1-灰度用户 2-非灰度用户
  pcForceGray: 0, // PC 是否强制灰度用户
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    // 改变仓库时顺便更新localStorage值
    if (Object.keys(data).find((item) => item === 'warehouseId')) {
      warehouseIdLocalStorage.setItem(data.warehouseId);
    }
    Object.assign(state, data);
  },
  // 非敏感词
  * getNewToken() {
    yield this.changeData({
      // 非敏感词
      setToken: false,
    });
    // 非敏感词
    const res = yield getCloudMessageTokenAPI();
    if (res.code === '0') {
      // 非敏感词
      const { token } = res.info;
      // 非敏感词
      setTimeStorage('messageToken', token);
      const info = getLoginInfo();
      // 设置获取token的用户
      messageUserLocalStorage.setItem(info && info.name);
      yield this.changeData({
        // 非敏感词
        setToken: true,
      });
    } else {
      console.log(res.msg);
    }
  },
  // 非敏感词
  * setMessageToken() {
    const info = getLoginInfo();
    // 非敏感词, token过期或者切换了用户 则重新获取token
    if (isStorageOverTime('messageToken') || info.name !== messageUserLocalStorage.getItem()) {
      // 非敏感词, 设置token
      yield this.getNewToken();
    } else {
      yield this.changeData({
        // 非敏感词
        setToken: true,
      });
    }
  },

  * logoutWorker() {
    const data = yield logoutServerAPI();
    if (data.code === '0') {
      showMessage(t('退出成功'), false);
      yield this.changeData({
        ready: false,
      });
      scanWorkLocationCodeSuccessSessionStorage.setItem('');
      if (clearIndexedDBInfo && typeof clearIndexedDBInfo === 'function') {
        yield clearIndexedDBInfo();
      }
      window.location.href = process.env.LOGIN_ADDR || 'https://ulp.sheincorp.cn';
    } else {
      showMessage(data.msg || t('退出失败'), false);
    }
  },

  // 组员排班-验收登入是否过期--不阻塞主流程
  * validTimeout() {
    try {
      const res = yield timeoutServerAPI();
      if (res.code === '0') {
        // msgCode: 1: 正常  2：即将掉线 3：已经超时，注销登录
        const { msgCode, message } = res.info;
        if (msgCode === 2) {
          Message.warn(message, 2);
        } else if (msgCode === 3) {
          Message.error(message, 2);
          yield this.logoutWorker();
        }
      }
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * 灰度验收名单校验
   */
  * validateAcceptAuthUser(user) {
    const isProd = window.location.host === process.env.PROD_HOST; // 生产环境
    const isAlphaUrl = window.location.host === process.env.ALPHA_URL; // 灰度环境
    // 生产环境
    if (isProd) { // todo 调试用测试环境
      try {
        const { grayUserSwitch, userType, pcForceGray } = user.info;
        yield this.changeData({
          grayUserSwitch, // 灰度验收名单新逻辑开关
        });
        if (grayUserSwitch) { // 如果开启灰度验收名单新逻辑开关
          if (userType === 1 && pcForceGray === 1) {
            yield clearFetchCache();
            const url = `${window.location.protocol}//${process.env.ALPHA_URL}/${window.location.hash}`;
            window.location.href = url;
          }
        }
      } catch (error) {
        // TODO 增加埋点上报
        console.log(error);
      }
    }

    // 灰度环境
    if (isAlphaUrl) {
      try {
        const { grayUserSwitch, userType } = user.info;
        if (grayUserSwitch) { // 如果开启灰度验收名单新逻辑开关
          yield this.changeData({
            grayUserSwitch, // 灰度验收名单新逻辑开关
          });
          if (userType !== 1) { // 没有灰度权限
            Modal.error({
              title: t('您没有灰度权限！请联系权限管理员'),
              esc: false,
              hideClose: true,
              maskCloseAble: false,
              onOk() {
                const url = `${window.location.protocol}//${process.env.ALPHA_URL}/${window.location.hash}`;
                window.location.replace(url);
                window.location.reload();
              },
            });
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  },

  // 云存储 - 快捷入口设置
  * saveData() {
    const { isShowQuickEntry, quickEntryList } = yield '';
    const cloudData = yield getData('quickEntryObj');
    const oldList = cloudData?.quickEntryList || [];

    const quickEntryObj = {
      isShowQuickEntry,
      quickEntryList,
    };
    cloudSetData({ quickEntryObj }).then(() => {
      console.log(t('quickEntryObj，存云服务器成功'));
    });
    // 删除 - homeQuickEntry 同步删除
    const deleteList = oldList.filter((item) => !quickEntryList.find((i) => i.id === item.id));
    if (deleteList.length) {
      const data = yield getData('homeQuickEntry');
      const newQuickEntryList = (data?.quickEntryList || []).filter((item) => !deleteList.find((i) => i.id === item.id));
      cloudSetData({ homeQuickEntry: { quickEntryList: newQuickEntryList } }).then(() => {
        console.log(t('homeQuickEntry，存云服务器成功'));
      });
    }
  },

  // 云存储 - 快捷入口获取
  * getCloudData() {
    const { menus } = yield '';
    const menuLinks = [];
    getAuthMenu(menuLinks, menus);

    const cloudData = yield getData('quickEntryObj');
    const { isShowQuickEntry = false, quickEntryList = [] } = cloudData || {};

    const newQuickEntryList = [];
    quickEntryList.forEach((entry) => {
      if (menuLinks?.includes(entry.link)) {
        newQuickEntryList.push(entry);
      }
    });
    yield this.changeData({
      isShowQuickEntry,
      quickEntryList: newQuickEntryList,
    });
  },
  // 接口缓存开关数据初始化
  * initOpenCaheSwitch() {
    try {
      yield systemDataDB.open();
      const fetchCacheEnable = getFetchWithCacheEnableKey();
      const cacheVal = yield systemDataDB.get(fetchCacheEnable);
      if (cacheVal?.content !== undefined) { // motCacheEnableLocalStorage有值
        yield this.changeData({
          isCacheEnable: cacheVal.content,
        });
      } else { // 缓存中无值
        yield this.changeData({
          isCacheEnable: true,
        });
        yield systemDataDB.put({
          id: fetchCacheEnable,
          content: true,
        });
      }
    } catch (e) {
      console.error(e, 'initOpenCaheSwitch met error');
    }
  },
  * getUserInfo() {
    try {
      const resp = yield getUserInfoApi();
      if (resp.code === '0') {
        const { userName = '', userNo = '' } = resp.info || {};
        userNameSessionStorage.setItem(userName); // 中文名
        userNoSessionStorage.setItem(userNo); // 工号
        yield systemDataDB.open();
        yield systemDataDB.put({
          id: 'currentUserInfo',
          content: resp.info,
        });
      }
    } catch (err) {
      console.error(err, 'getUserInfoApi');
    }
  },
  /** 该方法内的函数调用需要增加try catch避免阻塞页面加载 */
  * initAsyncLoadData(action, ctx) {
    const { warehouseId } = yield '';
    yield ctx.initOpenCaheSwitch();
    yield ctx.checkUvAuth(); // 获取用户uv查看权限
    yield ctx.queryRedCount({ warehouseId }); // 获取机器人右上角数量
    yield ctx.getHfcClient(); // 获取帮助中心
    yield ctx.getApolloConfig(); // 获取ApolloConfig配置
    yield ctx.switchDebugMode(); // 设置调试模式
  },
  * loadData(action, ctx) {
    const { warehouseId, timeDiff } = yield '';
    try {
      yield this.getUserInfo();
      // 存在则根据保持不变
      if (isSyncReloadLocalStorage.getItem()) {
        yield ctx.changeData({
          isSyncReload: isSyncReloadLocalStorage.getItem() === '1',
        });
      } else if (process.env.wmsenv !== 'test') {
        // 不存在且不为测试环境则默认设为开启,否则保持原有设置
        isSyncReloadLocalStorage.setItem('1');
      } else {
        // 若为测试环境，默认为关闭
        isSyncReloadLocalStorage.setItem('0');
        yield ctx.changeData({
          isSyncReload: false,
        });
      }
      const [user, warehouse, currentWareHouse, resSub, PermissionSub] = yield Promise.all([
        getMenusApi({}),
        getWarehouseApi({ enabled: 1 }),
        getPermissionWarehouse({ enabled: 1 }),
        selectSubWarehouse({
          warehouseId,
          enabled: 1,
        }),
        getPermissionSubWarehouse({
          warehouseId,
          enabled: 1,
        }),
      ]);

      if ([user, warehouse, currentWareHouse, resSub, PermissionSub].every((v) => v.code === '0')) {
        const {
          userName, enName, userNo, list,
        } = user.info;
        userNameSessionStorage.setItem(userName || ''); // 中文名
        enNameSessionStorage.setItem(enName || ''); // 英文名
        userNoSessionStorage.setItem(userNo || ''); // 工号
        // 格式话用户名展示-有英文名 就展示英文名 +（工号）如果有的话，否则展示中文名
        formatNameSessionStorage.setItem(enName ? `${enName}${userNo ? `(${userNo})` : ''}` : userName); // 纯展示用的用户名
        // 灰度验收名单校验
        yield this.validateAcceptAuthUser(user);
        yield ctx.changeData({
          menus: normalizeMenu(list || []),
          linkList: [{ link: '/', name: t('WMS首页') }, ...flattenArray(normalizeBreadcrumb(list || [], ''))],
          // ready: true,
          userName,
          enName,
          userNo,
          formatName: enName ? `${enName}${userNo ? `(${userNo})` : ''}` : userName, // 右上角的用户名展示
          list: list || [],
          grayUserSwitch: user.info.grayUserSwitch || 0, // 强制灰度跳转逻辑开关 1-开启 0-关闭
          userType: user.info.userType || 0, // 用户类型 1-已配置灰度权限
          pcForceGray: user.info.pcForceGray || 0, // PC 是否强制灰度用户
          // userWarehouseIdList: warehouseIds || [],
          // userIP: ip,
        });
        // 将menu中的所有叶子存储在sessionStorage中
        wmsMenuObjSessionStorage.setItem(getAllLeaf(user.info.list || []));
        // 获取用户有权限的仓库
        const selfCurrentWarehouseList = currentWareHouse.info ? currentWareHouse.info.data : [];
        // 匹配用户仓库 是否在权限内
        if (!(selfCurrentWarehouseList.find((item) => item.id === warehouseId))) {
          yield ctx.changeData({
            warehouseId: '',
          });
          warehouseIdLocalStorage.setItem('');
        }
        yield ctx.changeData({
          // 获取所有仓库
          warehouseList: warehouse.info.data || [],
          currentWarehouseList: selfCurrentWarehouseList,
          subWarehouseList: resSub.info && warehouseId ? resSub.info.data : [],
          permissionSubWarehouseList: PermissionSub.info && warehouseId ? PermissionSub.info.data : [],
        });
        // 设置埋点用户信息，埋点鉴权成功之后会覆盖
        conf({
          customAuthInfoSync: {
            name: user.info.userName,
            enName: user.info.enName,
            emplid: user.info.userNo,
            systemName: 'WMS',
          },
          plugin: {
            error: {
              sendErrorToLogcenter: !['dev', 'test', 'sit'].includes(process.env.wmsenv),
            },
          },
        });
        const link = window.location.hash.substr(1);
        const isKanban = fullScreenRoutes.has(link);
        const watername = user.info.enName ? user.info.enName : user.info.userName;
        // 设置全局水印
        const watermark = new Watermark({
          text: `${watername} ${user.info.userNo}\nWMS ${process.env.wmsenv} ${moment().format('YYYY-MM-DD')}`,
          lineHeight: 1.3,
          density: 7,
          fontSize: 14,
          textColor: isKanban ? '#fff' : '#000',
          // 高于侧边栏的层级1000
          zIndex: 1005,
        });
        watermark.addBodyWatermark();
      } else {
        const title = [user, warehouse, currentWareHouse, resSub, PermissionSub].find((v) => v.code !== '0').msg || t('后端返回数据出错1');
        showMessage(title, false);
      }
      yield this.getTimeDiffNum({ warehouseId, isPageInit: true });
      yield this.changeData({
        ready: true,
      });
      // 非敏感词, 设置token
      yield this.setMessageToken();
      // 获取云存储信息
      yield this.getCloudData();
      yield ctx.initAsyncLoadData();
    } catch
    (e) {
      if (!e.reason || e.reason.status !== 302) {
        throw e;
      }
    }
  },
  * getApolloConfig() {
    const apolloConfigFormatRes = yield getApolloConfig(['PDF_URL_SWITCH', 'TIME_RANGE_CONFIG', 'DEGRADE_FRONT']);

    timeRangeConfigLocalStorage.setItem(apolloConfigFormatRes?.TIME_RANGE_CONFIG); // 保存获取到的配置信息
    // 保存获取到的配置信息
    setSessionStoragePDFKey(apolloConfigFormatRes?.PDF_URL_SWITCH);
    // 非主流程接口降级开关配置
    degradeFrontLocalStorage.setItem(apolloConfigFormatRes?.DEGRADE_FRONT);
  },

  // todo getCurrentWareHouse函数仅在特殊出库管理/装箱扫描页面使用，已迁移至outbound出库子应用，微前端二阶段上线后删除
  /**
   * 获取用户当前绑定的仓库及仓库列表
   * @param action
   * @returns {IterableIterator<*>}Type
   */
  * getCurrentWareHouse() {
    try {
      const res = yield getCurrentWareHouseServer();
      let currentWarehouseList = [];
      if (res.code === '0') {
        currentWarehouseList = res.info.warehouseList || [];
      }
      // 当前用户没有配置仓库或配置了超过1个的仓库，弹出仓库选择框
      if (!currentWarehouseList.length || currentWarehouseList.length > 1) {
        yield this.changeData({
          warehouseId: '',
          showSelectModal: true,
        });
      }

      // 当前用户只配置了一个仓库，将该仓库设置为选中仓库
      if (currentWarehouseList.length === 1) {
        const itemId = currentWarehouseList[0].warehouseId;
        yield this.changeData({
          warehouseId: itemId,
        });
        warehouseIdLocalStorage.setItem(itemId);
      }
    } catch (e) {
      reducerErrorMsg(e);
    }
  },

  /**
   * 获取子仓下拉数据
   * 获取权限子仓下拉数据
   * @param action
   * @returns {IterableIterator<*>}Type
   */
  * getSubWarehouse(action) {
    // 清空点数装箱页面工号标识,强制弹出 和 子仓
    scanWorkLocationCodeSuccessSessionStorage.setItem('');
    upgradeSubWarehouseIdSessionStorage.setItem('');
    const { warehouseId } = action;
    if (!warehouseId) {
      yield this.changeData({
        subWarehouseList: [],
        permissionSubWarehouseList: [],
      });
      // 新写法：触发统一派发右上角仓库改变事件
      return [[], '', []];
    }
    const [permissionRes, res] = yield Promise.all([
      getPermissionSubWarehouse({
        warehouseId,
        enabled: 1,
      }),
      selectSubWarehouse({
        warehouseId,
        enabled: 1,
      }),
    ]);
    // 获取子仓下拉数据 selectSubWarehouse
    let subWarehouseList = [];
    let permissionSubWarehouseList = [];
    if (res.code === '0' && permissionRes.code === '0') {
      subWarehouseList = res.info ? res.info.data : [];
      permissionSubWarehouseList = permissionRes.info ? permissionRes.info.data : [];
    }

    yield this.changeData({
      warehouseId,
      subWarehouseList,
      permissionSubWarehouseList,
    });
    // 触发统一派发右上角仓库改变事件
    return [subWarehouseList, warehouseId, permissionSubWarehouseList];
  },

  * updateWorking(action) {
    const res = yield updateWorkingApi(action);
    if (res.code === '0') {
      if (res.info.respFlag === 1) {
        // 存储工位条码
        if (res.info.workLocationCode) {
          workLocationCodeSessionStorage.setItem(res.info.workLocationCode);
        }
        yield this.changeData({
          workStatus: 3,
          onlineTime: res.info.onlineTime,
          onlineTotalTime: res.info.onlineTotalTime,
        });
        const getSowingGoodsNum = yield getSowingGoodsNumApi();
        if (getSowingGoodsNum.code === '0') {
          Modal.info({
            title: t('上机操作成功'),
            content: (
              // eslint-disable-next-line react/jsx-filename-extension
              <div>
                {t('昨日战绩榜单详情')}
                <div>
                  <table width="100%" style={{ marginTop: '15px' }}>
                    <tr>
                      <td width="50%">
                        <span className={styles.blueSeparator} />
                        <span style={{ verticalAlign: 'middle' }}>{t('工作量')}</span>
                      </td>
                      <td>
                        {getSowingGoodsNum.info.packageNum}
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <span className={styles.orangeSeparator} />
                        <span style={{ verticalAlign: 'middle' }}>{t('排名')}</span>
                      </td>
                      <td>
                        /
                      </td>
                    </tr>
                  </table>
                </div>
              </div>
            ),
            zIndex: 999999,
            onOk: () => {
              //
            },
          });
        } else {
          showMessage(getSowingGoodsNum.msg, false);
        }
      } else if (res.info.respFlag === 2) {
        showMessage(t('下机操作成功'), true);
        // 下机删除工位条码
        workLocationCodeSessionStorage.removeItem();
      }
      if (res.info.respFlag === 2) {
        yield this.changeData({
          workStatus: 1,
          workLocationCode: '',
        });
      }
    } else {
      showMessage(res.msg, false);
    }
  },

  * working(action) {
    const { param, replaceCb, resReturn } = action;
    if (!param.workLocationCode) return;
    const res = yield workingApi({ ...param, workLocationTypeFlag: 2 });
    if (resReturn) resReturn(res);
    if (res.code === '0') {
      // 存储工位条码
      if (res.info.workLocationCode) workLocationCodeSessionStorage.setItem(res.info.workLocationCode);
      // 初始化界面上机中
      if (res.info.respFlag === 3) {
        yield this.changeData({
          workStatus: 4,
          onlineTime: res.info.onlineTime,
        });
      }
      // 收工扫工位条码 如果已经绑定工位弹出确认框
      if (res.info.respFlag === 1) {
        const { workLocationCode, operator } = res.info;
        const userName = userNameSessionStorage.getItem() || '';
        // 当前用户和工位绑定人员不一致的时候，就弹窗更新; 否则展示原来的工位信息
        if (operator !== userName) {
          // eslint-disable-next-line no-unused-expressions
          if (replaceCb) {
            replaceCb({
              workLocationCode,
              operator,
              info: res.info || {},
            });
          }
        } else {
          yield this.changeData({
            workStatus: 4,
            onlineTime: res.info.onlineTime,
          });
        }
      }

      // 工位还没有人用，进行上机操作
      if (res.info.respFlag === 2) {
        yield this.updateWorking({
          operateFlag: 1,
          workLocationCode: res.info.workLocationCode,
        });
      }
      if (res.info.onlineTotalTime) {
        yield this.changeData({
          onlineTotalTime: res.info.onlineTotalTime,
        });
      }
      if (res.info.workLocationCode) {
        yield this.changeData({
          workLocationCode: res.info.workLocationCode,
        });
      }
    } else {
      Message.info(res.msg);
    }
  },

  // 切换调试模式
  * switchDebugMode(args = null) {
    let isDebugMode = args ? args.isDebugMode : isDebugModeLocalStorage.getItem();
    isDebugMode = Boolean(isDebugMode);
    isDebugModeLocalStorage.setItem(isDebugMode);
    yield this.changeData({
      isDebugMode,
      ready: true,
    });
    if (isDebugMode) {
      invocationRecorder.start();
    } else {
      invocationRecorder.stop();
    }
  },

  // 切换PDF记录模式
  * switchPDFMode(args = null) {
    try {
      let isPDFHistoryModeVal = args ? args.isPDFHistoryMode : isPDFHistoryModeLocalStorage.getItem();
      isPDFHistoryModeVal = Boolean(isPDFHistoryModeVal);
      setPDFHistoryMode(isPDFHistoryModeVal);
      yield this.changeData({
        isPDFHistoryMode: isPDFHistoryModeVal,
      });
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * 获取时差配置页面
   * @returns {Generator<*, void, *>}
   */
  * overseasTimeDiff() {
    if (timeTransformPagesLocalStorage.isExpired()) {
      // 过期或无值  因为灰度和线上的配置是一套: 新增灰度环境校验需要配置OVERSEAS_TIME_DIFF_HD 避免影响线上环境的功能
      const apolloConfig = process.env.wmsenv === 'alpha' ? 'OVERSEAS_TIME_DIFF_HD' : 'OVERSEAS_TIME_DIFF';
      // 兼容wgs服务异常，导致首页无法正常加载
      try {
        const res = yield getApolloConfigAPI({ params: [apolloConfig] });
        const apolloFormatRes = apolloFormatObj(res.info);

        if (res.code === '0' && apolloFormatRes) {
          timeTransformPagesLocalStorage.setItem(apolloFormatRes[apolloConfig] || []);
        } else {
          Modal.error({ title: t('获取时差配置失败') });
        }
      } catch (e) {
        console.error(e);
      }
    }
  },

  /**
   * 获取仓库时差及时差选择列表
   * @param param
   * @returns
   */
  * getTimeDiffNum(param) {
    // isPageInit 是否为页面初始化，
    const { warehouseId, isPageInit } = param;
    const { timeDiff: lastTimeDiff } = yield '';
    // 未选仓库 则将时区置为 '默认时间'
    if (!warehouseId) {
      yield this.changeData({
        timeDiff: 0,
        timeList: [],
      });
      timeDiffNumLocalStorage.setItem('0');
      return;
    }
    const res = yield getWarehouseDetailById({ id: warehouseId });
    if (res.code === '0') {
      // 设置 初始时差 和 时区列表
      // 页面初始化时只有当时差不为0（即默认时间）且不为当前仓库的时差，则使用当前仓库的时差
      if (!isPageInit || Number(lastTimeDiff) !== 0 && Number(lastTimeDiff) !== res.info.timeDiff) {
        timeDiffNumLocalStorage.setItem(res.info.timeDiff || 0);
        yield this.changeData({
          timeDiff: res.info.timeDiff || 0,
        });
      }
      // 存在时差的界面
      if (res.info.timeDiff && res.info.timeDiff !== 0) {
        yield this.changeData({
          timeList: [{ id: 1, label: res.info.timeDiff || 0 }],
        });
      } else {
        // 不存在时差的界面 或 非时区改造范围内的界面
        yield this.changeData({
          timeList: [],
        });
      }
    } else {
      Modal.error({ title: t('获取仓库时差失败') });
    }
  },
  /**
   * 控制操作指引显示
   */
  * showOperational(param) {
    const { pathname } = param;
    const { pdaGuidelineList } = yield '';

    const currentList = pdaGuidelineList.filter((item) => item.rule === pathname);
    let operationList = [];
    if (currentList.length > 0) {
      operationList = currentList[0].assets;
    }
    yield this.changeData({
      operationList,
    });
  },
  /**
   * JIRA单跟进-跳转外部系统
   */
  * getSignature() {
    // 页面枚举值 1.JIRA数据源-生产IT部 2.JIRA数据源-MRP 3.WMS
    const { code, info, msg } = yield getSignatureAPI({ menuType: '3' });
    if (code === '0') {
      window.open(info.signatureUrl);
    } else {
      Modal.error({ title: msg || t('获取JIRA单跟进页面失败') });
    }
  },

  * checkWsConnect() {
    // 获取当前登录的用户信息
    const info = getLoginInfo();
    const params = {
      title: t('测试WS连接'),
      content: t('Websocket连接建立成功'),
      targetList: [info && info.name],
      // 只发给自己
      publishType: 'unicast',
      module: 5, // 消息推送
      messageType: 'message',
    };
    // 前端推送消息
    yield this.changeData({
      isWsConnect: 0,
    });
    const res = yield cloudMessagePublishApi(params);
    // 请求未发送
    if (res.code !== '0') {
      yield this.changeData({
        isWsConnect: 2,
        wsFailReason: res?.errMsg || t('接口异常'),
      });
    }
  },

  /**
   * 上机扫描条码 - 是否替代工位弹窗
   * @param param
   * @returns {Generator<void|Generator<Promise<*>|*, void, *>|Promise<unknown>, void, *>}
   */
  * workReplaceCb(param) {
    // eslint-disable-next-line no-promise-executor-return
    const status = yield new Promise((r) => Modal.confirm({
      zIndex: '999999',
      content: `${t('当前工位')}${param.workLocationCode}${t('已绑定作业人员')}${param.operator}，${t('请确认是否要将其顶下线？')}`,
      onOk: () => r('ok'),
      onCancel: () => r('cancel'),
      autoFocusButton: null,
    }));
    if (status === 'ok') {
      yield this.updateWorking({
        operateFlag: 1,
        workLocationCode: param.workLocationCode,
      });
    } else if (status === 'cancel') {
      yield this.changeData({
        workLocationCode: '',
        workStatus: 4,
        onlineTime: param.info.onlineTime,
      });
    }
  },
  // 获取帮助中心数据
  * getHfcClient() {
    try {
      const res = yield getHfcClientAuthAPI();
      if (res.code === '0') {
        yield this.changeData({
          authHfcCode: res?.info || '',
        });
      } else {
        Message.error(res?.msg || t('获取帮助中心数据失败'));
      }
    } catch (err) {
      console.info(err);
      Message.error(t('获取帮助中心数据失败'));
    }
  },

  * checkUvAuth() {
    try {
      const res = yield checkUrlPermissionAPI({
        url: '/was/front/apm/auth',
      });
      // 保存获取到的配置信息，不用接口报错弹窗提示
      yield this.changeData({ showApmUv: res.code === '0' });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      reducerErrorMsg(e);
    }
  },
  // 获取机器人右上角数量
  * queryRedCount({ warehouseId }) {
    try { // 不阻塞主流程
      const res = yield queryWaitCountAPI({ warehouseId });
      if (res.code !== '0') {
        showMessage(res.msg, false);
        return;
      }
      yield this.changeData({
        redCount: res.info || 0,
      });
    } catch (e) {
      console.error(e);
    }
  },
  * handleChangeWarehouseId(action, ctx) {
    const { warehouseId: id, ignoreSyncWarehouse } = action;
    const { warehouseId } = yield '';
    if (id !== warehouseId) {
      yield ctx.getTimeDiffNum({ warehouseId: id });
      yield ctx.queryRedCount({ warehouseId: id });
      yield ctx.changeData({ warehouseId: id });
      if (!ignoreSyncWarehouse) {
        syncWarehouse.postMessage('changeWarehouseId', id);
      }
      const res = yield this.getSubWarehouse({
        warehouseId: id,
      });
      dispatchWarehouseChange(res);
    }
  },
};
