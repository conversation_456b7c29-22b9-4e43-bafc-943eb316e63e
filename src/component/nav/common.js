/* eslint-disable @shein-bbl/bbl/translate-i18n-byT,no-use-before-define */
/**
 * @desc 独自一个文件封装/management/storage-manage菜单用到的一些公共方法、常量
 * 理由：lego 懒加载导致用户需打开/management/storage-manage才能加载对应资源
 */
import jsCookie from 'js-cookie';
import { cloudMessagePublishApi } from '@src/server/common/cloud-message';
import { getLoginInfo } from '@src/lib/dealFunc';
import { clearFetchCache } from '@wms/wms-public-tools';
import { startServiceWorker, stopServiceWorker } from '@src/component/nav/cache';
import { SWCloseLocalStorage } from '@src/lib/storage-new';

export const STORAGE_RECEIVE_TYPE = 11; // 管理员 -> 用户
export const STORAGE_RESPONSE_TYPE = 12; // 用户 -> 管理员

// 操作分类
export const STORAGE_SORT = 'storage';
export const ACTION_SORT = 'action';

// 操作类型
export const operateTypeList = [
  {
    dictCode: '1',
    dictNameZh: '查询',
    action: 'getItem',
  },
  {
    dictCode: '2',
    dictNameZh: '新增/修改',
    action: 'setItem',
  },
  {
    dictCode: '3',
    dictNameZh: '删除',
    action: 'removeItem',
  },
];

// 存储类型
export const storageTypeList = [
  {
    dictCode: '1',
    dictNameZh: 'sessionStorage',
    target: 'sessionStorage',
  },
  {
    dictCode: '2',
    dictNameZh: 'localStorage',
    target: 'localStorage',
  },
  {
    dictCode: '3',
    dictNameZh: 'cookie',
    target: 'jsCookie',
  },
];

// 用户行为
export const actionTypeList = [
  {
    dictCode: '1',
    dictNameZh: '刷新浏览器',
    action: () => { window.location.reload(); },
  },
  {
    dictCode: '2',
    dictNameZh: 'SW离线',
    action: () => {
      stopServiceWorker();
      SWCloseLocalStorage.setItem(true);
      clearFetchCache();
    },
  },
  {
    dictCode: '3',
    dictNameZh: 'SW开启',
    action: () => {
      startServiceWorker();
      SWCloseLocalStorage.removeItem();
    },
  },
];
/**
 * @desc 接收端处理接收数据
 * 对于data有约定：返回对象为列表数据。返回2为修改成功，返回3为删除成功，返回字符串为其他信息
 */
export async function handleReceiveMessage(data) {
  const { title } = data;
  switch (title) {
    case STORAGE_SORT:
      await handleStorage(data);
      break;
    case ACTION_SORT:
      await handleAction(data);
      break;
    default:
      console.error('error title');
  }
}
/**
 * @desc 处理缓存操作
 * 对于data有约定：返回对象为列表数据。返回2为修改成功，返回3为删除成功，返回字符串为其他信息
 */
async function handleStorage(data) {
  const userInfo = getLoginInfo();
  const { content } = data;
  const {
    publisher,
    operateType,
    storageType,
    storageKey,
    storageValue,
    options,
    silence, // 静默模式下订阅者不发送响应值
  } = JSON.parse(content);
  const storage = storageTypeList.find(({ dictCode }) => dictCode === storageType)?.target;
  try {
    let res = null;
    if (storage === 'jsCookie') {
      const parseOptions = options ? JSON.parse(options) : undefined;
      switch (operateType) {
        case '1': {
          // key 有值就查单个，否则查所有数据
          if (storageKey) {
            const val = jsCookie.get(storageKey, parseOptions);
            res = val ? { [storageKey]: val } : {};
          } else {
            res = jsCookie.get();
          }
          break;
        }
        case '2':
          jsCookie.set(storageKey, storageValue, parseOptions);
          res = 2;
          break;
        case '3':
          jsCookie.remove(storageKey, parseOptions);
          res = 3;
          break;
        default:
          break;
      }
    } else {
      const storageInstance = window[storage];
      switch (operateType) {
        case '1': {
          // key 有值就查单个，否则查所有数据
          if (storageKey) {
            const val = storageInstance.getItem(storageKey);
            res = val ? { [storageKey]: val } : {};
          } else {
            res = { ...storageInstance };
          }
          break;
        }
        case '2':
          storageInstance.setItem(storageKey, storageValue);
          res = 2;
          break;
        case '3':
          storageInstance.removeItem(storageKey, storageValue);
          res = 3;
          break;
        default:
          break;
      }
    }
    // 非静默模式下，发送响应消息
    if (!silence) {
      await cloudMessagePublishApi({
        targetList: [publisher],
        title: '200', // 使用200来判断成功
        content: JSON.stringify({
          responder: userInfo?.name,
          data: res,
        }),
        module: STORAGE_RESPONSE_TYPE, // 响应module
        messageType: 'message',
        publishType: 'unicast',
      });
    }
  } catch (e) {
    console.error(e);
    // 非静默模式下，发送响应消息
    if (!silence) {
      await cloudMessagePublishApi({
        targetList: [publisher],
        title: '500', // 使用500来判断成功失败
        content: JSON.stringify({
          responder: userInfo?.name,
          data: `${e}`,
        }),
        module: STORAGE_RESPONSE_TYPE, // 响应module
        messageType: 'message',
        publishType: 'unicast',
      });
    }
  }
}

/**
 * @desc 处理用户行为
 * 用户行为不发送响应
 */
async function handleAction(data) {
  const { content } = data;
  const {
    actionType,
  } = JSON.parse(content);
  const action = actionTypeList.find(({ dictCode }) => dictCode === actionType)?.action;
  try {
    if (action) {
      action();
    }
  } catch (e) {
    console.error(e);
  }
}
