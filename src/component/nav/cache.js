/* eslint-disable no-console */
/* eslint-disable max-len */
import { t } from '@shein-bbl/react';
import { clearIndexedDBInfo } from '@wms/wms-public-tools';

// 不传tableName时，默认清空数据库中的全部表
export async function clearObjectStoreData(dbName, tableName) {
  if (!dbName) {
    return null;
  }
  return new Promise((resolve, reject) => {
    // 打开指定的数据库
    const request = indexedDB.open(dbName);

    console.log('clearObjectStoreData begin');
    request.onsuccess = function (event) {
      const db = event.target.result;

      // 获取所有对象存储的名称
      const { objectStoreNames } = db;

      console.log('clearObjectStoreData 0');
      if (objectStoreNames.length === 0) {
        console.log(t('数据库中没有对象存储（表）可清空。'));
        db.close();
        resolve(false);
        return;
      }

      console.log('clearObjectStoreData 1');
      // 创建包含所有对象存储的读写事务
      const transaction = db.transaction(objectStoreNames, 'readwrite');

      transaction.oncomplete = function () {
        console.log(t('所有对象存储已清空。'));
        db.close();
        resolve(true);
      };

      transaction.onerror = function (e) {
        console.error(t('事务出错：'), e.target.error);
        db.close();
        reject(e.target.error);
      };

      const clearSingObjectStore = (storeName) => new Promise((res, rej) => {
        const objectStore = transaction.objectStore(storeName);
        const clearRequest = objectStore.clear();

        clearRequest.onsuccess = function () {
          console.log(`${t("对象存储 '")}${storeName}${t('已清空。')}`);
          res(true);
        };

        clearRequest.onerror = function (e) {
          console.error(`${t('清空对象存储')}${storeName}${t('时出错：')}`, e.target.error);
          rej(e.target.error);
        };
      });

      console.log('clearObjectStoreData 2');
      // 参数指定单个表，则清除单个表
      if (tableName) {
        clearSingObjectStore(tableName).then(resolve, reject);
        return;
      }

      // 默认遍历并清空每个对象存储
      for (let i = 0; i < objectStoreNames.length; i++) {
        const storeName = objectStoreNames[i];
        clearSingObjectStore(storeName);
      }
      console.log('clearObjectStoreData 3');
    };

    request.onerror = (e) => {
      console.error(t('打开数据库时出错：'), e.target.error);
      reject(e.target.error);
    };

    request.onblocked = async (e) => {
      console.warn(`${t("打开 IndexedDB 数据库 '")}${dbName}${t('被阻塞')}`);
      reject(e.target.error);
      // alert(`${t("删除 IndexedDB 数据库 '")}${dbName}${t('被阻塞')}${t('，因为还有其他页面或进程正在使用它。请关闭那些页面或进程后重试。')}`);
    };
  });
}

function clearAllIndexedDB() {
  return new Promise((resolve, reject) => {
  // 获取所有数据库名称
    const dbsRequest = indexedDB.databases ? indexedDB.databases() : Promise.resolve([]);
    dbsRequest.then((dbs) => {
      if (!dbs.length) {
        console.log(t('没有 IndexedDB 数据库'));
        resolve();
        return;
      }
      // const deletePromises = dbs.map((dbInfo) => deleteSingleIndexedDB(dbInfo.name));
      const deletePromises = dbs.map((dbInfo) => clearObjectStoreData(dbInfo.name));
      Promise.all(deletePromises).then(() => resolve()).catch((err) => reject(err));
    }).catch((err) => {
      console.error(t('获取 IndexedDB 数据库列表时出错：'), err);
      reject(err);
    });
  });
}

// 清除indexedDB
// 参考文档：https://stackoverflow.com/questions/9384128/how-to-delete-indexeddb
export const clearIndexedDB = async (dbName, tableName) => {
  if (!window.indexedDB) {
    return false;
  }
  if (typeof indexedDB.databases !== 'function') {
    return false;
  }
  // 默认删除所有数据库
  if (!dbName && !tableName) {
    return clearAllIndexedDB();
  }
  // 删除特定数据库
  if (!tableName) {
    // return deleteSingleIndexedDB(dbName);
    return clearObjectStoreData(dbName);
  }
  // 删除特定表
  // const res = await deleteObjectStore(dbName, tableName);
  const res = await clearObjectStoreData(dbName, tableName);
  return res;
};

export const clearCache = async () => {
// 清除cache storage
  // 参考文档：https://developer.mozilla.org/en-US/docs/Web/API/CacheStorage
  // CacheStorage 是 Cache 对象存储的接口，可以通过window.caches获取 (https状态下有效)
  if (window.caches) {
    const keys = await caches.keys();
    keys.forEach((key) => (caches.delete(key)));
  }
};

// export const clearStorage = () => {
//   // 清除localStorage
//   // 参考文档：https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage
//   localStorage.clear();
//   // 清除sessionStorage
//   // 参考文档：https://developer.mozilla.org/en-US/docs/Web/API/Window/sessionStorage
//   // 在关闭窗口或标签页之后就会删除这些数据,所以可以不用手动清除
//   sessionStorage.clear();
// };

export const clearStorage = () => {
  try {
    localStorage.clear();
    sessionStorage.clear();
    console.debug(t('LocalStorage 已清除'));
  } catch (e) {
    console.error(t('清除 LocalStorage 时出错：'), e);
  }
};

export const stopServiceWorker = async () => {
  // 清除serviceWorker
  // 参考文档：https://stackoverflow.com/questions/33704791/how-do-i-uninstall-a-service-worker
  if (window.navigator && navigator.serviceWorker) {
    const registrations = await navigator.serviceWorker.getRegistrations();
    registrations.forEach((registration) => { registration.unregister(); });
  }
};

export const startServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('serviceWorker.js').then((registration) => {
      console.log('serviceWorker registered success: ', registration);
    }).catch((err) => {
      console.log('serviceWorker registration failed: ', err);
    });
  }
};

// export const clearCookies = () => {
//   // 清除cookie
//   // 参考文档：https://stackoverflow.com/questions/179355/clearing-all-cookies-with-javascript
//   // HttpOnly的无法删除，因为HttpOnly会禁止js对cookie的访问
//   const cookies = document.cookie.split('; ');
//   for (let c = 0; c < cookies.length; c++) {
//     const d = window.location.hostname.split('.');
//     while (d.length > 0) {
//       const cookieBase = `${encodeURIComponent(cookies[c].split(';')[0].split('=')[0])}=; expires=Thu, 01-Jan-1970 00:00:01 GMT; domain=${d.join('.')} ;path=`;
//       const p = window.location.pathname.split('/');
//       document.cookie = `${cookieBase}/`;
//       while (p.length > 0) {
//         document.cookie = cookieBase + p.join('/');
//         p.pop();
//       }
//       d.shift();
//     }
//   }
// };

export const clearCookies = () => {
  try {
    const cookies = document.cookie.split('; ');
    // eslint-disable-next-line no-restricted-syntax
    for (const c of cookies) {
      const eqPos = c.indexOf('=');
      const name = eqPos > -1 ? c.substr(0, eqPos) : c;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    }
    console.debug(t('Cookies 已清除'));
  } catch (e) {
    console.error(t('清除 Cookies 时出错：'), e);
  }
};

export const clearAll = async () => {
  try {
    await stopServiceWorker();
    await clearCache();
    await clearCookies();
    await clearStorage();
    // await clearIndexedDB('wmdConfigDB');
    if (clearIndexedDBInfo && typeof clearIndexedDBInfo === 'function') {
      await clearIndexedDBInfo();
    }
  } catch (e) {
    console.error('clearAll error', e);
  }
};
/* 清除字典/参数缓存 */
export const clearConfigCache = async () => {
  const configs = [
    ['wmdConfigStoreSW', 'wmdConfigStore'],
    ['wmdDictionaryCacheDBSW', 'wmdDictionary'],
    ['qcDictionaryCacheDBSW', 'qcDictionary'],
  ];
  await Promise.all(configs.map((item) => clearIndexedDBInfo(item[0], item[1])));
};
