import { useEffect } from 'react';

// 路由配置信息               [旧路由， 新路由,  apollo system值标识]
const routerSwitchConfig: [string, string, string][] = [
  // ['/basic/supplier-sub-warehouse', '/inbound-standard/basic/supplier-sub-warehouse', 'wis']
  ['/outbound/order-package', '/wms-standard/outbound/order-package', 'ors'],
  ['/outbound/order-goods-detail', '/wms-standard/outbound/order-goods-detail', 'ors'],
  ['/outbound/inventory-rule-config', '/wms-standard/outbound/inventory-rule-config', 'ors'],
  ['/outbound/inventory-allocation-rule-calculation', '/wms-standard/outbound/inventory-allocation-rule-calculation', 'ors'],
  ['/outbound/package-control-rule-config', '/wms-standard/outbound/package-control-rule-config', 'ors'],
];

const routerSwitchConfigList: { [key: string]: number } = {};
routerSwitchConfig.forEach((item, index) => {
  const [oldRouter, newRouter] = item;
  routerSwitchConfigList[oldRouter] = index;
  routerSwitchConfigList[newRouter] = index;
});

function getAllRouterDatByLink(link: string) {
  const routerConfigIndex = routerSwitchConfigList[link];
  if (routerConfigIndex >= 0) {
    const routerConfig = routerSwitchConfig[routerConfigIndex];
    return [routerConfig[0], routerConfig[1]];
  }
  return [link];
}
export { getAllRouterDatByLink };

interface IStandardRollbackPageItem {
  system: string;
  switch: 0 | 1; // 0: 关闭 1: 开启
  key: string;
}

export const routerControl = (router: string) => {
  let queryParam = '';
  // 查找当前路由是否在配置中
  const configIndex = routerSwitchConfig.findIndex((item) => item.some((val) => {
    const isMatch = router.startsWith(val);
    if (isMatch) {
      queryParam = router.replace(val, '');
    }
    return isMatch;
  }));
  // 存在需要切换的路由
  if (configIndex > -1) {
    // 路由配置信息
    const configItem = routerSwitchConfig[configIndex];
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    // false: 返回新路由 true: 返回旧路由
    const matchingConfig = (window?.standardRollbackPage || []).find((item: IStandardRollbackPageItem) => configItem[2] === item.system && item.switch === 1);
    const [oldHash = '', newHash = ''] = configItem;

    // 有匹配到的开启的开关 回到旧页面
    if (matchingConfig) {
      // 替换前缀 回到旧页面
      return oldHash + queryParam;
    }
    // 非回滚的旧页面 则需要重定向到新页面
    return newHash + queryParam;
  }
  return router;
};

const routerSwitch = () => {
  // *新旧页面同一时间只能访问其中一个 不并存
  useEffect(
    () => {
      let newRouter = window.location.hash;
      const regex = /#(\/.*)/; // 正则表达式
      const pathName = window.location.hash.match(regex); // 匹配 URL
      const hashVal = pathName?.[1] || '';
      if (hashVal) {
        newRouter = routerControl(hashVal);
      }
      window.location.hash = newRouter;
    },
    [window.location.hash],
  );
};

export default routerSwitch;
