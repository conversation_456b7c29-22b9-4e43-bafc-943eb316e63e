import React, { useEffect, useState } from 'react';
import {
  Button, Message, Popover, Spin,
} from 'shineout';
import { t } from '@shein-bbl/react';
import { clearAll } from '@src/component/nav/cache';
import Icon from '@shein-components/Icon';
import PropTypes from 'prop-types';

function Loading({ loadingDuration = 5000 }) {
  const [isLongLoading, setIsLongLoading] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLongLoading(true); // 超过指定时间后，显示提示文字
    }, loadingDuration);

    return () => clearTimeout(timer); // 清理定时器
  }, []);

  return (
    <div style={{ textAlign: 'center', marginTop: '20px' }}>
      {isLongLoading ? (
        <div>
          <Spin
            loading
            tip={t('如果页面长时间处于加载中，你可以点击下方按钮进行页面缓存清理')}
          />
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Button size="small" type="primary">
              <Popover.Confirm
                onCancel={() => {}}
                onOk={async () => {
                  await clearAll();
                  window.location.href = process.env.LOGIN_ADDR || 'https://ulp.sheincorp.cn';
                  Message.success(t('缓存清除成功，请重新登录～'));
                }}
                text={{ ok: t('确定'), cancel: t('取消') }}
              >
                {t('清除缓存后需要重新登录')}
              </Popover.Confirm>
              {t('清理缓存并刷新')}
            </Button>
            <span style={{ paddingLeft: '10px' }}>
              <Popover trigger="hover" position="top-left" style={{ padding: '5px' }}>
                <p>{t('你也可以按照图例直接清理浏览器缓存')}</p>
                <div>
                  <img
                    src="https://files.dotfashion.cn/wgs-cn-prod/2025/05/07/1746620571274856f1ee41305d040c1fd6f96b12a2.png" alt={t('你也可以按照图例直接清理浏览器缓存')} style={{ width: '500px' }}
                  />
                </div>
              </Popover>
              <Icon type="primary" name="help" />
            </span>
          </div>
        </div>
      ) : (
        <Spin
          loading
        />
      )}
    </div>
  );
}

Loading.propTypes = {
  loadingDuration: PropTypes.number,
};

export default Loading;
