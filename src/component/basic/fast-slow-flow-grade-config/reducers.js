import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import {
  getListAPI, saveAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  nationalLineType: '', // 包裹国家线
};

export const defaultAddObj = {
  id: '',
  nationalLineType: '', // 包裹国家线
  historySaleDay: '', // 历史销量天数
  configSaleList: [{
    fastLevel: '', // 快流级别
    saleSortRangeMax: '', // 销量排名区间最大值
    saleSortRangeMin: '', // 销量排名区间最小值
  }],
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域

  limit: defaultLimit,
  /** 配置类型，1-历史销量标签组合 2-快流个数上限 3-算法预测销量(旧)，4-算法预测销量（新） */
  configTypeList: [],
  // 1-是 0-否
  isAbleList: [],
  exceptionTypeList: [],
  operatorType: 0, // 0关闭 1新增 2编辑
  addEditObj: defaultAddObj,
  sortCodeList: [], // 快慢流分级维度下拉
  nationalLineTypeList: [], // 国家线下拉
  processFlowData: [], // 详情数据
  detailModalVisiable: 0, // 详情弹框
  file: '',
  showImportCenter: false,
  dayList: [],
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 修改 新增和编辑对象
  changeAddEditObjData(state, data) {
    Object.assign(state, {
      addEditObj: {
        ...state.addEditObj,
        ...data,
      },
    });
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const selectData = yield dictSelect({ catCode: ['FAST_SLOW_FLOW_TAG_TYPE', 'NATIONAL_LINE_TYPE', 'FAST_SLOW_FLOW_CONFIG_IS_ABLE'] });
    if (selectData.code === '0') {
      yield this.changeData({
        nationalLineTypeList: selectData.info.data.find((x) => x.catCode === 'NATIONAL_LINE_TYPE').dictListRsps,
        configTypeList: selectData.info.data.find((item) => item.catCode === 'FAST_SLOW_FLOW_TAG_TYPE').dictListRsps,
        isAbleList: selectData.info.data.find((x) => x.catCode === 'FAST_SLOW_FLOW_CONFIG_IS_ABLE').dictListRsps,
      });
    } else {
      Modal.error({ title: selectData.msg });
    }
    const dayList = [];
    for (let i = 1; i < 15; i++) {
      dayList.push({
        dictCode: i,
      });
    }
    yield this.changeData({
      dayList,
    });
  },
  // 搜索
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 快慢流分级配置-新增或编辑
  * addOrEdit() {
    markStatus('loading');
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { addEditObj, operatorType } = yield '';
    let postBool = false;
    if (
      (!addEditObj.nationalLineType && addEditObj.nationalLineType !== 0)
      || !addEditObj.historySaleDay
    ) postBool = true;
    // eslint-disable-next-line array-callback-return
    addEditObj.configSaleList.map((item) => {
      if (
        !item.fastLevel
        || (!item.saleSortRangeMin && item.saleSortRangeMin !== 0)
        || (!item.saleSortRangeMax && item.saleSortRangeMax !== 0)
      ) postBool = true;
    });
    if (postBool) {
      Modal.error({ title: t('请填写所有必填信息') });
      return;
    }
    const { code, msg } = yield saveAPI({ ...addEditObj, warehouseId });
    if (code === '0') {
      Message.success(operatorType === 1 ? t('新增成功') : t('编辑成功'));
      yield this.changeData({
        operatorType: 0,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
};
