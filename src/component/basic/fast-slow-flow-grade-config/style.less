.recordButton{
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}
.packageNumStyle{
    color: #1890ff;
    cursor: pointer;
}
.titleTips{
    color: red;
}
.importBtnDiv{
    margin-left: 20px;
    display: flex;
    align-items: flex-start;
}
.importBtn{
    margin-left: 8px;
    display: flex;
}
.importCenter{
    color: #1890ff;
    margin-left: 20px;
    cursor: pointer;
}
.gradeOutDiv {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.gradeDiv {
    width: 650px;
    padding: 8px 5px 5px;
    border: 1px solid #eeeeee;
}
.gradeInput {
    display: flex!important;
    justify-content: flex-start;
    align-items: center!important;
}
.gradeInput > div:nth-child(2){
    display: flex!important;
    justify-content: flex-start;
    align-items: center;
}
.listGrade {
    padding: 6px 20px 6px 10px;
    background: #f4f5f8;
    border-radius: 5px;
    margin-bottom: 8px;
    display: inline-block;
}
