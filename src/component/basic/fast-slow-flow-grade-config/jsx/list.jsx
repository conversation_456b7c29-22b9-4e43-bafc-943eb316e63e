import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';
import styles from '../style.less';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseIdName',
        width: 150,
      },
      {
        title: t('包裹国家线'),
        render: 'nationalLineTypeName',
        width: 150,
      },
      {
        title: t('预测天数'),
        render: 'historySaleDay',
        width: 100,
      },
      {
        title: t('快流等级'),
        render: (d) => (
          d.configSaleList && d.configSaleList.length > 0 && d.configSaleList.map((item) => (
            <div className={styles.listGrade}>
              { item.fastLevel }
              :
              {' '}
              { item.saleSortRangeMinName }
              ~
              { item.saleSortRangeMaxName }
              %
            </div>
          ))
        ),
        width: 180,
      },
      {
        title: t('更新时间'),
        width: 180,
        render: 'lastUpdateTime',
      },
      {
        title: t('更新人'),
        width: 180,
        render: 'updateUserName',
      },
      {
        title: t('操作'),
        width: 140,
        fixed: 'right',
        render: (record) => (
          <>
            <Button
              text
              type="primary"
              onClick={() => {
                // 处理下saleSortRangeMinName转saleSortRangeMin
                record?.configSaleList?.forEach((item) => {
                  item.saleSortRangeMin = item.saleSortRangeMinName;
                  item.saleSortRangeMax = item.saleSortRangeMaxName;
                });
                store.changeData({
                  operatorType: 2,
                  addEditObj: record,
                  showImportCenter: false,
                  file: '',
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              className={styles.recordButton}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作记录')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'FAST_SLOW_FLOW_LEVEL_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  processFlowData: PropTypes.arrayOf(PropTypes.shape()),
  detailModalVisiable: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
