import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Modal, Select, Rule, Input, Message,
} from 'shineout';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store, { defaultAddObj } from '../reducers';
import style from '../style.less';

const rule = Rule();

class Handle extends React.Component {
  render() {
    const {
      loading,
      operatorType,
      addEditObj,
      nationalLineTypeList,
      dayList,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          onClick={() => store.changeData({
            operatorType: 1,
            addEditObj: JSON.parse(JSON.stringify(defaultAddObj)),
            showImportCenter: false,
            file: '',
          })}
        >
          {t('新增')}
        </Button>
        <Modal
          destroy
          visible={operatorType}
          bodyStyle={{ maxHeight: 550, overflow: 'auto' }}
          width={800}
          maskCloseAble={false}
          title={t('快流等级配置')}
          onClose={() => store.changeData({ operatorType: 0 })}
          footer={(
            <div>
              <Button key="cancel" onClick={() => store.changeData({ operatorType: 0 })}>{t('取消')}</Button>
              <Modal.Submit
                key="confirm"
                disabled={!loading}
              >
                {t('确认')}
              </Modal.Submit>
            </div>
              )}
        >
          <Form
            labelWidth={130}
            labelAlign="right"
            value={addEditObj}
            onSubmit={() => {
              store.addOrEdit();
            }}
            inline
            formRef={(f) => store.changeData({ addOrEditFormRef: f })}
          >
            <Form.Item required label={t('包裹国家线')}>
              <Select
                value={addEditObj.nationalLineType}
                keygen="dictCode"
                format="dictCode"
                renderItem={(w) => w.dictNameZh}
                data={nationalLineTypeList}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                width={200}
                absolute
                compressed
                placeholder={t('请选择')}
                rules={[rule.required(t('请选择包裹国家线'))]}
                onChange={(val) => {
                  store.changeAddEditObjData({
                    nationalLineType: val,
                  });
                }}
              />
            </Form.Item>
            <h3>{t('快流分级')}</h3>
            <Form.Item required label={t('历史销量天数')}>
              <Select
                value={addEditObj.historySaleDay}
                keygen="dictCode"
                format="dictCode"
                renderItem={(w) => w.dictCode}
                data={dayList}
                onFilter={(text) => (d) => d.dictCode.toString().indexOf(text.toString()) >= 0}
                clearable
                width={200}
                absolute
                compressed
                placeholder={t('请选择')}
                rules={[rule.required(t('请选择历史销量天数'))]}
                onChange={(val) => {
                  store.changeAddEditObjData({
                    historySaleDay: val,
                  });
                }}
              />
            </Form.Item>
            { addEditObj.configSaleList && addEditObj.configSaleList.map((item, index) => (
              <div className={style.gradeOutDiv}>
                <div className={style.gradeDiv}>
                  <Form.Item required label={t('快流等级')}>
                    <Input
                      value={item.fastLevel} maxLength={10} rules={[rule.required(t('请输入快流等级'))]}
                      onChange={(val) => {
                        const newLevelInfoList = [...addEditObj.configSaleList];
                        newLevelInfoList[index].fastLevel = val;
                        store.changeAddEditObjData({
                          configSaleList: newLevelInfoList,
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    required label={t('销量排名区间')} className={style.gradeInput}
                  >
                    <Input.Number
                      max={100}
                      min={0}
                      digits={7}
                      allowNull
                      hideArrow
                      value={item.saleSortRangeMin}
                      onChange={(val) => {
                        const newLevelInfoList = [...addEditObj.configSaleList];
                        if (!Number.isInteger(Number(val))) {
                          // 修正带小数点的数
                          const len = val.toString().split('.')[1]?.length;
                          const fixVal = parseFloat(val).toFixed(len);
                          newLevelInfoList[index].saleSortRangeMin = fixVal;
                        } else {
                          newLevelInfoList[index].saleSortRangeMin = val;
                        }
                        store.changeAddEditObjData({
                          configSaleList: newLevelInfoList,
                        });
                      }}
                      onBlur={(e) => {
                        const val = e.target.value;
                        const newLevelInfoList = [...addEditObj.configSaleList];
                        if (val && (item.saleSortRangeMax && Number(val) > Number(item.saleSortRangeMax))) {
                          newLevelInfoList[index].saleSortRangeMin = '';
                        } else if (!Number.isInteger(Number(val))) {
                          // 修正带小数点的数
                          const len = val.toString().split('.')[1]?.length;
                          const fixVal = parseFloat(val).toFixed(len);
                          newLevelInfoList[index].saleSortRangeMin = fixVal;
                        }
                        store.changeAddEditObjData({
                          configSaleList: newLevelInfoList,
                        });
                      }}
                    />
                    <span style={{ margin: '6px' }}>%</span>
                    <span style={{ margin: '6px 16px 6px 6px' }}>——</span>
                    <Input.Number
                      max={100}
                      min={0}
                      digits={7}
                      allowNull
                      hideArrow
                      value={item.saleSortRangeMax}
                      onChange={(val) => {
                        const newLevelInfoList = [...addEditObj.configSaleList];
                        if (!Number.isInteger(Number(val))) {
                          const len = val.toString().split('.')[1]?.length;
                          const fixVal = parseFloat(val).toFixed(len);
                          newLevelInfoList[index].saleSortRangeMax = fixVal;
                        } else {
                          newLevelInfoList[index].saleSortRangeMax = val;
                        }
                        store.changeAddEditObjData({
                          configSaleList: newLevelInfoList,
                        });
                      }}
                      onBlur={(e) => {
                        const val = e.target.value;
                        const newLevelInfoList = [...addEditObj.configSaleList];
                        if (val && (item.saleSortRangeMin && Number(val) < Number(item.saleSortRangeMin))) {
                          newLevelInfoList[index].saleSortRangeMax = '';
                        } else if (!Number.isInteger(Number(val))) {
                          const len = val.toString().split('.')[1]?.length;
                          const fixVal = parseFloat(val).toFixed(len);
                          newLevelInfoList[index].saleSortRangeMax = fixVal;
                        }
                        store.changeAddEditObjData({
                          configSaleList: newLevelInfoList,
                        });
                      }}
                    />
                    <span style={{ margin: '6px' }}>%</span>
                  </Form.Item>
                </div>
                <Icon
                  name="plus-o" style={{ margin: 6, fontSize: '20px', color: '#40ca0f' }}
                  onClick={() => {
                    // 最多配置到5项
                    const addLength = 10;
                    const newLevelInfoList = [...addEditObj.configSaleList];
                    if (newLevelInfoList.length < addLength) {
                      newLevelInfoList.push({
                        fastLevel: '', // 快流级别
                        saleSortRangeMax: '', // 销量排名区间最大值
                        saleSortRangeMin: '', // 销量排名区间最小值
                      });
                      store.changeAddEditObjData({
                        configSaleList: newLevelInfoList,
                      });
                    } else {
                      Message.info(t('最多只能配置10项'));
                    }
                  }}
                />
                {addEditObj.configSaleList.length > 1 && (
                <Icon
                  name="odec-close-one-fill" style={{ fontSize: '20px', color: '#ef7243' }}
                  onClick={() => {
                    const newLevelInfoList = [...addEditObj.configSaleList];
                    newLevelInfoList.splice(index, 1);
                    store.changeAddEditObjData({
                      configSaleList: newLevelInfoList,
                    });
                  }}
                />
                )}
              </div>
            ))}
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  operatorType: PropTypes.number,
  addEditObj: PropTypes.shape(),
  nationalLineTypeList: PropTypes.arrayOf(PropTypes.shape()),
  dayList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
