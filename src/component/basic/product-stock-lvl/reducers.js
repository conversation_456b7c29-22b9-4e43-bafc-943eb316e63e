import { Modal } from 'shineout';
import { markStatus } from 'rrc-loader-helper';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect, getStoreAttr } from '@src/server/basic/dictionary';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import { handleListMsg } from '@src/lib/dealFunc';
import { STATISTICAL_DOWNLOAD } from '@src/lib/jump-url';
import { queryApi, exportApi } from './server';

export const defaultLimit = {
  skuCode: '',
  skc: '',
  size: '',
  nationalLineTypeList: [],
  storeTypeIdList: [],
  levelCodeList: [],
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  loading: 0, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeOptions: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  selectedRows: [],
  nationalLineTypeListSelect: [],
  storeTypeIdListSelect: [],
  levelCodeListSelect: [],
};

export default {
  state: defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  * init(action, ctx) {
    markStatus('loading');
    const selectType = {
      catCode: ['NATIONAL_LINE_TYPE', 'STORAGE_LEVEL'],
    };
    const [selectDictData, storeAttrData] = yield Promise.all([
      dictSelect(selectType),
      getStoreAttr({
        enabled: 1,
      }),
    ]);
    if (selectDictData.code === '0' && storeAttrData.code === '0') {
      yield ctx.changeData({
        nationalLineTypeListSelect: selectDictData.info.data.find((x) => x.catCode === 'NATIONAL_LINE_TYPE').dictListRsps,
        levelCodeListSelect: selectDictData.info.data.find((x) => x.catCode === 'STORAGE_LEVEL').dictListRsps,
        storeTypeIdListSelect: storeAttrData.info.data || [],
      });
    } else {
      handleListMsg([selectDictData, storeAttrData], false);
    }
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    formRef.clearValidate();
  },
  // 查询
  * search(props = {}, ctx) {
    markStatus('loading');
    const {
      pageInfo: {
        pageNum,
        pageSize,
      },
      limit,
    } = this.state;
    const data = yield queryApi(clearEmpty(paramTrim({
      ...limit, ...props, pageNum, pageSize,
    }), [0, '0']));
    if (data.code === '0') {
      yield ctx.changeData({
        list: data.info.data || [],
        selectedRows: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: data.info.meta.count,
        },
      });
    } else {
      Modal.error({ title: data.msg });
    }
  },
  /**
   * 导出
   * @param action
   * @returns {IterableIterator<*>}
   */
  * exportCheck(data) {
    markStatus('loading');
    // 后端说这里分页字段随便传
    const info = yield exportApi(clearEmpty(paramTrim({
      ...data,
      pageNum: 1,
      pageSize: 1000,
    }), [0, '0']));
    if (info.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: info.msg });
    }
  },
  // 页签改变
  * handlePaginationChange(arg = {}) {
    const { formRef } = this.state;
    // 校验
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    if (validateFlag) {
      yield this.changeData({
        pageInfo: {
          ...this.state.pageInfo,
          ...arg,
        },
      });
      yield this.search();
    }
  },
};
