import React from 'react';
import PropTypes from 'prop-types';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import styles from '@src/component/style.less';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      selectedRows,
      pageInfo,
    } = this.props;

    const columns = [
      {
        title: t('存储属性'),
        width: 120,
        render: 'storeTypeName',
      },
      {
        title: t('SKU'),
        render: 'skuCode',
        width: 120,
      },
      {
        title: t('SKC'),
        width: 120,
        render: 'skc',
      },
      {
        title: t('尺码'),
        render: 'size',
        width: 100,
      },
      {
        title: t('国家线'),
        render: 'nationalLineName',
        width: 120,
      },
      {
        title: t('日均销量'),
        render: 'avgSalecnt',
        width: 120,
      },
      {
        title: t('推荐拣货区件数'),
        render: 'threshold',
        width: 140,
      },
      {
        title: t('存储等级'),
        render: 'levelName',
        width: 100,
      },
      {
        title: t('上一次存储等级'),
        render: 'preLevelName',
        width: 140,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 150,
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            style={{ height: '100%' }}
            rowClassName={() => styles.borderInner}
            bordered
            fixed="both"
            loading={!loading}
            data={list}
            columns={columns}
            keygen="id"
            empty={t('暂无数据')}
            size="small"
            width={columns.reduce((pre, current) => pre + (current.width || 100), 0)}
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape).isRequired,
  pageInfo: PropTypes.shape().isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
};

export default List;
