import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Input, Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { validateSize } from '@src/lib/validate';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      nationalLineTypeListSelect,
      storeTypeIdListSelect,
      levelCodeListSelect,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          value={limit}
          collapseOnSearch={false}
          clearUndefined={false}
          // labelStyle={{ width: 90 }} // 修改label宽度
          searching={!loading}
          onSearch={(form) => {
            console.log(form); console.log(limit);
            // 点搜索按钮，应该将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          onChange={(val) => {
            store.changeLimitData({ ...defaultLimit, ...val });
          }}
          onClear={() => store.clearLimitData()}
          onToggle={() => {}}
          formRef={(f) => {
            store.changeData({
              formRef: f,
            });
          }}
        >
          <Input
            label={t('SKU')}
            name="skuCode"
          />
          <Input
            label={t('SKC')}
            name="skc"
            onChange={(value) => {
              if (validateSize().test(value)) {
                store.changeLimitData({
                  size: '',
                });
              }
            }}
          />
          <Input
            disabled={validateSize().test(limit.skc)}
            label={t('尺码')}
            name="size"
          />
          <Select
            label={t('国家线')}
            name="nationalLineTypeList"
            keygen="dictCode"
            format="dictCode"
            renderItem={(w) => w.dictNameZh}
            data={nationalLineTypeListSelect}
            onFilter={(text) => (d) => d.dictNameZh.indexOf(text) >= 0}
            clearable
            multiple
            compressed
            placeholder={t('全部')}
          />
          <Select
            label={t('存储属性')}
            name="storeTypeIdList"
            keygen="id"
            format="id"
            renderItem={(w) => w.name}
            data={storeTypeIdListSelect}
            onFilter={(text) => (d) => d.name.indexOf(text) >= 0}
            clearable
            multiple
            compressed
            placeholder={t('全部')}
          />
          <Select
            label={t('存储等级')}
            name="levelCodeList"
            keygen="dictCode"
            format="dictCode"
            renderItem={(w) => w.dictNameZh}
            data={levelCodeListSelect}
            onFilter={(text) => (i) => i.dictNameZh.toUpperCase().includes(text.toUpperCase())}
            clearable
            multiple
            compressed
            placeholder={t('全部')}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number.isRequired,
  limit: PropTypes.shape(),
  nationalLineTypeListSelect: PropTypes.arrayOf(PropTypes.shape()),
  storeTypeIdListSelect: PropTypes.arrayOf(PropTypes.shape()),
  levelCodeListSelect: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
