import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Select, Checkbox,
} from 'shineout';
import { t } from '@shein-bbl/react';
import globalStyles from '@src/component/style.less';
import styles from '../style.css';
import store, {
  defaultState, EDIT_MODAL_HIDE, EDIT_MODAL_ADD, EDIT_MODAL_EDIT,
} from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
      selectedRows,
      functionList,
      modalType,
      modalObj,
      modalData,
      queryParkList,
      allChecked,
    } = this.props;

    const validateSubmit = () => {
      if (!modalObj.warehouseId
        || !modalObj.parkType
        || !modalObj.functionNameCode
        || !modalObj.subWarehouseIdList.length) {
        return false;
      }
      return true;
    };

    const getParkTypeList = () => {
      const parkList = queryParkList
        .filter((x) => ![undefined, null, 0].includes(x.parkType)) // 剔除"空"
        .map((x) => ({
          warehouseId: x.warehouseId,
          dictCode: x.parkType,
          dictNameZh: x.parkName,
        }))
        .sort((a, b) => a.dictCode - b.dictCode);

      // 去重
      const parkMap = new Map();
      parkList.forEach((item) => {
        if (!parkMap.has(item.dictCode)) {
          parkMap.set(item.dictCode, item);
        }
      });

      return [...parkMap.values()].filter((x) => x.warehouseId === modalObj.warehouseId) || [];
    };

    const getSubWarehouseList = () => {
      const list = queryParkList
        .map((x) => ({
          parkType: x.parkType,
          dictCode: x.id,
          dictNameZh: x.nameZh,
        }))
        .sort((a, b) => a.dictCode - b.dictCode);

      return list.filter((x) => x.parkType === modalObj.parkType) || [];
    };

    const parkTypeList = getParkTypeList() || []; // 园区列表
    const subWarehouseList = getSubWarehouseList() || []; // 子仓列表

    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              modalType: EDIT_MODAL_ADD,
              modalObj: {
                ...defaultState.modalObj,
              },
              modalData: {
                ...defaultState.modalData,
                warehouseList: modalData.warehouseList,
              },
              allChecked: false,
            });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            store.handleEdit();
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="danger"
          disabled={!loading || selectedRows.length === 0}
          onClick={() => {
            Modal.confirm({
              content: t('确定删除勾选的数据？'),
              onOk: () => {
                const ids = selectedRows.map((x) => x.id);
                store.handleDelete({ ids });
              },
              text: {
                ok: t('确认'),
                cancel: t('取消'),
              },
            });
          }}
        >
          {t('删除')}
        </Button>
        <Modal
          visible={!!modalType}
          title={modalType === EDIT_MODAL_ADD ? t('新增授权') : t('编辑授权')}
          width={780}
          maskCloseAble={false}
          onClose={() => store.changeData({ modalType: EDIT_MODAL_HIDE })}
          footer={[
            <Button
              onClick={() => { store.changeData({ modalType: EDIT_MODAL_HIDE }); }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              loading={!loading}
              disabled={!validateSubmit()}
              onClick={() => store.editSubmit()}
            >
              {t('保存')}
            </Button>,
          ]}
        >
          <div>
            <div style={{ marginBottom: 20 }}>
              <div className={styles.itemTitle}>
                <span style={{ color: 'red', marginRight: 5 }}>*</span>
                {t('基础信息')}
              </div>
              <div style={{ display: 'flex', flexWrap: 'wrap' }}>
                <div style={{ display: 'flex', marginTop: 10 }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    width: '70px',
                    marginRight: 10,
                  }}
                  >
                    {t('仓库')}
                  </div>
                  <Select
                    value={modalObj.warehouseId}
                    data={modalData.warehouseList}
                    disabled={modalType === EDIT_MODAL_EDIT}
                    keygen="id"
                    format="id"
                    renderItem="nameZh"
                    width={200}
                    placeholder={t('请选择')}
                    onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    onChange={(val) => {
                      store.changeData({
                        modalObj: {
                          ...modalObj,
                          warehouseId: val,
                        },
                      });
                      store.handleWarehouseChange({
                        data: {
                          warehouseId: val,
                        },
                        isInit: false,
                      });
                    }}
                  />
                </div>
                <div style={{ display: 'flex', marginTop: 10 }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    width: '70px',
                    marginRight: 10,
                  }}
                  >
                    {t('园区')}
                  </div>
                  <Select
                    disabled={!modalObj.warehouseId || modalType === EDIT_MODAL_EDIT}
                    value={modalObj.parkType}
                    data={parkTypeList}
                    keygen="dictCode"
                    format="dictCode"
                    renderItem="dictNameZh"
                    width={200}
                    placeholder={t('请选择')}
                    onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    onChange={(val) => {
                      store.changeData({
                        modalObj: {
                          ...modalObj,
                          parkType: val,
                          subWarehouseIdList: [],
                        },
                      });
                    }}
                  />
                </div>
                <div style={{ display: 'flex', marginTop: 10 }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    width: '70px',
                    marginRight: 10,
                  }}
                  >
                    {t('功能名称')}
                  </div>
                  <Select
                    value={modalObj.functionNameCode}
                    data={functionList}
                    disabled={modalType === EDIT_MODAL_EDIT}
                    keygen="dictCode"
                    format="dictCode"
                    renderItem="dictNameZh"
                    width={200}
                    placeholder={t('请选择')}
                    onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    onChange={(val) => {
                      store.changeData({
                        modalObj: {
                          ...modalObj,
                          functionNameCode: val,
                        },
                      });
                    }}
                  />
                </div>
              </div>
            </div>
            <div>
              <div className={styles.itemTitle} style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ color: 'red', marginRight: 5 }}>*</span>
                {t('授权子仓')}
                <div style={{ marginLeft: 15, marginTop: '-1px' }}>
                  <Checkbox
                    checked={allChecked}
                    disabled={subWarehouseList.length === 0}
                    onChange={(val) => {
                      store.changeData({
                        allChecked: val,
                        modalObj: {
                          ...modalObj,
                          subWarehouseIdList: val ? subWarehouseList.map((x) => x.dictCode) : [],
                        },
                      });
                    }}
                  >
                    {t('全选')}
                  </Checkbox>
                </div>
              </div>
              {
                subWarehouseList.length > 0 ? (
                  <div style={{ marginLeft: 10, padding: '4px 6px', border: '1px solid rgb(212,210,210)' }}>
                    <Checkbox.Group
                      keygen
                      value={modalObj.subWarehouseIdList}
                      onChange={(val) => {
                        store.changeData({
                          modalObj: {
                            ...modalObj,
                            subWarehouseIdList: val,
                          },
                        });
                      }}
                    >
                      {subWarehouseList.map((d) => (
                        <Checkbox key={d.dictCode} htmlValue={d.dictCode}>
                          {d.dictNameZh}
                        </Checkbox>
                      ))}
                    </Checkbox.Group>
                  </div>
                ) : (<span>{t('无数据')}</span>)
              }
            </div>
          </div>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  modalType: PropTypes.number,
  modalObj: PropTypes.shape(),
  modalData: PropTypes.shape(),
  functionList: PropTypes.arrayOf(PropTypes.shape()),
  queryParkList: PropTypes.arrayOf(PropTypes.shape()),
  allChecked: PropTypes.bool,
};
export default Handle;
