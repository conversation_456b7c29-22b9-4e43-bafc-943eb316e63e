import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Popover, Tag } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      selectedRows,
    } = this.props;

    const getSubWarehouseColumn = (data) => {
      const showPopover = data.startUpSubWarehouseList.length > 3 || data.notStartSubWarehouseList.length > 3;

      return (
        <div>
          {
            showPopover
            && (
            <Popover
              type="info"
              position="left"
              style={{ padding: 5 }}
              background="#fff"
              border="#fff"
            >
              <div style={{ display: 'flex', marginBottom: 5 }}>
                <span style={{ flexShrink: 0 }}>
                  {t('启用中')}
                  :
                </span>
                <span>{data.startUpSubWarehouseList.map((item) => (<Tag>{item.nameZh}</Tag>))}</span>
              </div>
              <div style={{ display: 'flex' }}>
                <span style={{ flexShrink: 0 }}>
                  {t('未启用')}
                  :
                </span>
                <span>{data.notStartSubWarehouseList.map((item) => (<Tag>{item.nameZh}</Tag>))}</span>
              </div>
            </Popover>
            )
          }
          <div style={{ display: 'flex', marginBottom: 5 }}>
            <span style={{ flexShrink: 0 }}>
              {t('启用中')}
              :
            </span>
            <span>
              {
              data.startUpSubWarehouseList.length > 3
                ? (
                  <span>
                    {data.startUpSubWarehouseList.slice(0, 3).map((item) => (<Tag>{item.nameZh}</Tag>))}
                    ...
                  </span>
                )
                : data.startUpSubWarehouseList.map((item) => (<Tag>{item.nameZh}</Tag>))
}
            </span>
          </div>
          <div style={{ display: 'flex' }}>
            <span style={{ flexShrink: 0 }}>
              {t('未启用')}
              :
            </span>
            <span>
              {data.notStartSubWarehouseList.length > 3
                ? (
                  <span>
                    {data.notStartSubWarehouseList.slice(0, 3).map((item) => (<Tag>{item.nameZh}</Tag>))}
                    ...
                  </span>
                )
                : data.notStartSubWarehouseList.map((item) => (<Tag>{item.nameZh}</Tag>))}
            </span>
          </div>
        </div>
      );
    };

    const columns = [
      {
        title: t('功能名称'),
        render: 'functionName',
        width: 100,
      },
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 120,
      },
      {
        title: t('园区'),
        render: 'parkName',
        width: 160,
      },
      {
        title: t('子仓'),
        render: (d) => getSubWarehouseColumn(d),
        width: 310,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 80,
      },
      {
        title: t('更新人'),
        render: 'updateUser',
        width: 120,
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
