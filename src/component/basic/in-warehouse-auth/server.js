import { sendPostRequest } from '@src/server/common/public';

// 库内授权信息列表查询
export const queryAPI = (param) => sendPostRequest({
  url: '/authorizes/query',
  param,
}, process.env.WWS_URI);

// 保存或更新库内授权信息接口
export const saveOrUpdateAPI = (param) => sendPostRequest({
  url: '/authorizes/saveOrUpdate',
  param,
}, process.env.WWS_URI);

// 批量删除库内授权信息记录接口
export const deleteAPI = (param) => sendPostRequest({
  url: '/authorizes/delete',
  param,
}, process.env.WWS_URI);

// 根据仓库id 查询子仓和园区信息(级联)
// export const queryParkAPI = (param) => sendPostRequest({
//   url: '/sub_warehouse/query_park',
//   param,
// }, process.env.BASE_URI_WMD);
