import { markStatus } from 'rrc-loader-helper';
import { t } from '@shein-bbl/react';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { getSubWarehouseApi } from '@src/server/basic/sub-warehouse';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import {
  queryAPI,
  saveOrUpdateAPI,
  deleteAPI,
} from './server';

export const EDIT_MODAL_HIDE = 0; // 不可见
export const EDIT_MODAL_EDIT = 1; // 编辑
export const EDIT_MODAL_ADD = 2; // 新增

const getSubWarehouseList = (queryParkList, modalObjParkType) => {
  const list = queryParkList
    .map((x) => ({
      parkType: x.parkType,
      dictCode: x.id,
      dictNameZh: x.nameZh,
    }))
    .sort((a, b) => a.dictCode - b.dictCode);

  return list.filter((x) => x.parkType === modalObjParkType) || [];
};

// 搜索默认值
export const defaultLimit = {
  functionNameCode: '', // 授权名称
  parkType: '', // 园区
  lastUpdateUser: '', // 更新人
};
// 其他默认值
export const defaultState = {
  formRef: {},
  loading: 0, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  list: [],
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  selectedRows: [], // 选中的表格列
  warehouseId: '', // 仓库id
  functionList: [], // 授权名称列表
  parkTypeList: [], // 园区列表

  modalType: EDIT_MODAL_HIDE, // modal 是否可见
  queryParkList: [], // 仓库下的园区和子仓数据
  modalObj: {
    warehouseId: '', // 仓库
    parkType: '', // 园区
    functionNameCode: '', // 功能名称
    subWarehouseIdList: [], // 授权子仓
  },
  modalData: {
    warehouseList: [], // 仓库列表
    parkTypeList: [], // 园区列表
  },
  currentWarehouseList: [], // 权限仓库列表
  allChecked: false, // 全选
};

export default {
  state: defaultState,
  // 改变state的值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  /**
   * 初始化数据
   */
  * init() {
    const { modalData } = this.state;

    markStatus('loading');
    const [selectData] = yield Promise.all([dictSelect({ catCode: ['LIST_OF_FUNCTIONS'] })]);
    if (selectData.code === '0') {
      yield this.changeData({
        functionList: selectData.info.data.find((v) => v.catCode === 'LIST_OF_FUNCTIONS').dictListRsps,
      });
    } else {
      Modal.error({
        title: selectData.msg || t('请求失败,请稍后再试'),
        autoFocusButton: 'ok',
      });
    }
    const { warehouseId, currentWarehouseList } = yield 'nav';

    yield this.changeData({
      warehouseId,
      modalData: {
        ...modalData,
        warehouseList: currentWarehouseList,
      },
    });
    const { parkTypeList } = yield this.queryPark({ warehouseId });
    yield this.changeData({
      parkTypeList,
    });
  },
  /**
   * 搜索操作
   */
  * search() {
    const { limit, pageInfo } = this.state;

    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield queryAPI(clearEmpty(paramTrim(param), [0, '0', false]));
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  /**
   * 页签改变
   * @param {Object} arg
   */
  * handlePaginationChange(arg = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  /**
   * 右上角仓库事件派发
   *
   */
  * handleWarehouseChange({ data, isInit = true }) {
    const { modalObj } = this.state;
    const { warehouseId } = data;
    const { parkTypeList, queryParkList } = yield this.queryPark({ warehouseId });

    if (isInit) {
      yield this.changeData({
        warehouseId,
        parkTypeList,
        limit: {
          ...this.state.limit,
          parkType: '',
        },
      });
    } else {
      yield this.changeData({
        queryParkList,
        modalObj: {
          ...modalObj,
          parkType: '',
          subWarehouseIdList: [],
        },
      });
    }
  },
  /**
   * 操作按钮 - 编辑
   */
  * handleEdit() {
    const { selectedRows, modalObj } = this.state;
    const {
      id,
      warehouseId,
      parkType,
      functionNameCode,
      startUpSubWarehouseList,
    } = selectedRows[0];
    const { queryParkList } = yield this.queryPark({ warehouseId });

    const allChecked = startUpSubWarehouseList.length === getSubWarehouseList(queryParkList, parkType).length;

    yield this.changeData({
      modalType: EDIT_MODAL_EDIT,
      queryParkList,
      allChecked,
      modalObj: {
        ...modalObj,
        id,
        warehouseId,
        parkType,
        functionNameCode,
        subWarehouseIdList: startUpSubWarehouseList.map((x) => x.id),
      },
    });
  },
  /**
   * 编辑
   */
  * editSubmit() {
    const { modalType, modalObj } = this.state;

    markStatus('loading');

    const { code, msg } = yield saveOrUpdateAPI(modalObj);
    if (code === '0') {
      Message.success(modalType === EDIT_MODAL_EDIT ? t('编辑成功') : t('新增成功'));
      yield this.changeData({
        modalType: EDIT_MODAL_HIDE,
      });
      yield this.search();
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  /**
   * 删除
   */
  * handleDelete({ ids }) {
    markStatus('loading');
    const { code, msg } = yield deleteAPI({ idList: ids });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  /**
   * header 校验表单
   */
  * handleFormValidate() {
    const { formRef } = this.state;
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 获取园区列表
  * queryPark({ warehouseId }) {
    markStatus('dataLoading');
    const { code, info, msg } = yield getSubWarehouseApi({ warehouseId });
    if (code === '0') {
      const parkList = info.data
        .filter((x) => ![undefined, null, 0].includes(x.parkType)) // 剔除"空"
        .map((x) => ({
          dictCode: x.parkType,
          dictNameZh: x.parkName,
        }))
        .sort((a, b) => a.dictCode - b.dictCode);

      // 去重
      const parkMap = new Map();
      parkList.forEach((item) => {
        if (!parkMap.has(item.dictCode)) {
          parkMap.set(item.dictCode, item);
        }
      });

      return {
        queryParkList: info.data,
        parkTypeList: [...parkMap.values()],
      };
    }
    Modal.error({
      title: msg,
    });
    return '';
  },
};
