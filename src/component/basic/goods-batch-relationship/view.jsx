import { i18n } from '@shein-bbl/react';
import React, { Component } from 'react';
import ContainerPage from '@public-component/search-queries/container';
// import store from './reducers';
import Header from './jsx/header';
import List from './jsx/list';

class Container extends Component {
  componentDidMount() {
    // store.init();
  }

  render() {
    return (
      <ContainerPage>
        <Header {...this.props} />
        <List {...this.props} />
      </ContainerPage>
    );
  }
}

export default i18n(Container);
