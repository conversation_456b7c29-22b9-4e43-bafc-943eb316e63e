import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
    } = this.props;

    const columns = [
      {
        title: t('商品批次号'),
        width: 100,
        render: 'goodsBatchNo',
      },
      {
        title: t('下单编号'),
        width: 80,
        render: 'orderNo',
      },
      {
        title: t('单据类型'),
        width: 80,
        render: 'billTypeDesc',
      },
      {
        title: t('供应商'),
        width: 80,
        render: 'supplier',
      },
      {
        title: t('创建时间'),
        width: 120,
        render: 'createTime',
      },
    ];

    return (
      <section className={[styles.tableSection, styles.listArea].join(' ')}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen={(d) => JSON.stringify(d)}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
};

export default List;
