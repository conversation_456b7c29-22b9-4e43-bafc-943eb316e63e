import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import {
  Input, Select, Rule,
} from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';
// import styles from '../style.less';

const rule = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      if (!formData.startTime || !formData.endTime) {
        callback(new Error(t('开始时间或结束时间必选')));
      }
      // 时间范围不能超过一个月
      if (moment(formData.endTime)
        .customDiff(moment(formData.startTime), 'months', true) > 1) {
        callback(new Error(t('时间范围不能超过{}个月', 1)));
      }
      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      billTypeList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 70 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('创建时间')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Input label={t('商品批次号')} name="goodsBatchNo" />
          <Input label={t('下单编号')} name="orderNo" />
          <Input label={t('供应商')} name="supplier" />
          <Select
            label={t('单据类型')}
            name="billType"
            data={billTypeList}
            keygen="value"
            format="value"
            renderItem="name"
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            compressed
            clearable
            placeholder={t('全部')}
          />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['startTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('创建时间')}
            span={2}
            rules={[rule.timeRange()]}
          />

        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  billTypeList: PropTypes.arrayOf(PropTypes.object),
};
export default Header;
