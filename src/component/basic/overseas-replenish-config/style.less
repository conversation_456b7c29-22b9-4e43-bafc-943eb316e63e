/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.handleExportButton{
    margin-right: 6px;
}


/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect{
    width: 140px;
 }
 
 .headerUnmatchedText{
     color: #bbb;
 }

.headerSearchTimeLine{
    display: flex;
}

.modalItem {
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
}

.modalItemLabel {
    margin-bottom: 8px;
}

.modalItemContent {

}

.modalSubItem {
    display: flex;
    margin-bottom: 5px;
    margin-right: 10px;
}

.modalSubItemLabel {
    flex-shrink: 0;
    margin-right: 5px;
    width: 60px;
    display: flex;
    flex-direction: row-reverse;
}


/* list.jsx
---------------------------------------------------------------- */
.listOperationButton{
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}
.listSection{
    height: 0px; /* 必写为了子domheight - 100%有效 */
    flex: 1;    
}




