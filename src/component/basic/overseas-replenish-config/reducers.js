import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import _ from 'lodash';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
// import { paramTrim } from '@src/lib/deal-func';
import { handleListMsg } from '@src/lib/dealFunc';
import { getListAPI, saveOrUpdateAPI } from './server';

export const replenishTypeMap = new Map([
  [t('紧急补货'), 1],
  [t('日常补货'), 2],
]);

export const ifList = [
  { dictCode: 1, dictNameZh: t('是') },
  { dictCode: 0, dictNameZh: t('否') },
];

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  replenishType: '', // 补货类型
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  warehouseList: [], // 仓库列表(已过滤佛山仓)
  replenishTypeList: [], // 补货类型 下拉数据
  replenishCycleList: [], // 补货周期 下拉数据
  replenishStateList: [], // 补货状态 下拉数据
  // 列表区域+分页区域
  pageCacheReplenishType: '', // 缓存补货类型
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  updateVisible: false, // 新增修改弹窗
  updateObj: { // 新增修改的数据
    warehouse: '', // 仓库id
    replenishType: '', // 补货类型
    state: '', // 补货状态
    replenishCycle: [], // 补货周期
    triggerTimes: [], // 触发时间
    maxSafetyDay: '', // 拣货区最大安全库存天数
    minSafetyDay: '', // 拣货区最小安全库存天数
    overOccupyThreshold: '', // 超量预占阈值
    ifDeductionOnWay: '', // 是否扣减在途
    ifEntireLocation: '', // 是否整库位预占
    ifAutoSendTask: '', // 是否自动下发任务
  },
  currentWarehouseList: [], // 权限仓库列表
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const { currentWarehouseList } = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
    });
    const { warehouseId, warehouseList } = yield 'nav';

    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
    }

    yield this.changeData({
      warehouseList: warehouseList.filter((item) => item.id !== 1) || [], // 海外仓，过滤’佛山仓‘仓库
    });

    const [selectData] = yield Promise.all([
      dictSelect({
        catCode: [
          'OVERSEAS_REPLENISH_CONFIG_TYPE',
          'OVERSEAS_REPLENISH_CONFIG_CYCLE',
          'OVERSEAS_REPLENISH_CONFIG_STATE',
        ],
      }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        replenishTypeList: selectData.info.data.find((item) => item.catCode === 'OVERSEAS_REPLENISH_CONFIG_TYPE').dictListRsps || [],
        replenishCycleList: selectData.info.data.find((item) => item.catCode === 'OVERSEAS_REPLENISH_CONFIG_CYCLE').dictListRsps || [],
        replenishStateList: selectData.info.data.find((item) => item.catCode === 'OVERSEAS_REPLENISH_CONFIG_STATE').dictListRsps || [],
      });
    } else {
      handleListMsg([selectData]);
    }
  },

  /**
   * 搜索
   */
  * search() {
    const { warehouseId } = yield 'nav';
    const { limit, pageInfo } = yield '';

    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }

    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        pageCacheReplenishType: limit.replenishType,
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  /**
   * 新增/编辑 弹窗，保存数据
   */
  * saveOrUpdateData() {
    const { updateObj, list } = yield '';

    const isEdit = Boolean(updateObj?.id);

    const params = _.pick(updateObj, [
      'id',
      'warehouse',
      'replenishType',
      'state',
      'replenishCycle',
      'triggerTimes',
      'maxSafetyDay',
      'minSafetyDay',
      'overOccupyThreshold',
      'ifDeductionOnWay',
      'ifEntireLocation',
      'ifAutoSendTask',
    ]);

    // triggerTimes 时区处理
    if (!isEdit) {
      // 新增
      params.triggerTimes = params.triggerTimes.map((x) => ({ noTimeDiff: x })); // 新增的，没有时间差，使用noTimeDiff
    } else {
      // 编辑
      const originTriggerTimes = list.find((item) => item.id === updateObj.id).triggerTimes;

      params.triggerTimes = params.triggerTimes.map((x) => {
        if (originTriggerTimes.map((y) => y.withTimeDiff).includes(x)) {
          return originTriggerTimes.find((y) => y.withTimeDiff === x); // 编辑中已有的，使用withTimeDiff
        }
        return { noTimeDiff: x }; // 编辑中新增的，没有时间差，使用noTimeDiff
      });
    }

    markStatus('loading');
    const { code, msg } = yield saveOrUpdateAPI(params);
    if (code === '0') {
      Message.success(isEdit ? t('编辑成功') : t('新增成功'));
      yield this.changeData({
        updateVisible: false,
        updateObj: {
          // id: '', // id
          warehouse: '', // 仓库id
          replenishType: '', // 补货类型
          state: '', // 补货状态
          replenishCycle: [], // 补货周期
          triggerTimes: [], // 触发时间
          maxSafetyDay: '', // 拣货区最大安全库存天数
          minSafetyDay: '', // 拣货区最小安全库存天数
          overOccupyThreshold: '', // 超量预占阈值
          ifDeductionOnWay: '', // 是否扣减在途
          ifEntireLocation: '', // 是否整库位预占
          ifAutoSendTask: '', // 是否自动下发任务
        },
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
};
