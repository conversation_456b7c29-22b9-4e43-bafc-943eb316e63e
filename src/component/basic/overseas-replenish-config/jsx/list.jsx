import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Tag } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import styles from '../style.less';
import store, { replenishTypeMap } from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      pageCacheReplenishType,
      list,
      pageInfo,
      recordId,
      recordVisible,
    } = this.props;

    const getCols = (type) => {
      const firstCols = [
        {
          title: t('仓库'),
          render: 'warehouseName',
          width: 100,
        },
        {
          title: t('状态'),
          render: 'stateName',
          width: 100,
        },
        {
          title: t('补货类型'),
          render: 'replenishTypeName',
          width: 120,
        },
        {
          title: t('补货周期'),
          render: (record) => (
            record.replenishCycleName.map((item) => (
              <Tag
                key={item}
                style={{ marginBottom: '5px' }}
              >
                {item}
              </Tag>
            ))
          ),
          width: 300,
        },
        {
          title: t('触发时间'),
          render: (record) => (
            record.triggerTimes.map((item) => (
              <Tag
                key={JSON.stringify(item)}
                style={{ marginBottom: '5px' }}
              >
                {item.withTimeDiff}
              </Tag>
            ))
          ),
          width: 300,
        },
      ];

      const middleDailyReplenishmentCols = [
        {
          title: t('最大安全库存天数'),
          render: 'maxSafetyDay',
          width: 150,
        },
        {
          title: t('最小安全库存天数'),
          render: 'minSafetyDay',
          width: 150,
        },
        {
          title: t('超量预占阈值'),
          render: 'overOccupyThreshold',
          width: 150,
        },
      ];

      const middleEmergencyReplenishmentCols = [
        {
          title: t('是否扣减在途'),
          render: 'ifDeductionOnWayName',
          width: 180,
        },
        {
          title: t('是否整库位预占'),
          render: 'ifEntireLocationName',
          width: 180,
        },
        {
          title: t('是否自动下发任务'),
          render: 'ifAutoSendTaskName',
          width: 180,
        },
      ];

      const lastCols = [
        {
          title: t('更新人'),
          render: 'updater',
          width: 100,
        },
        {
          title: t('更新时间'),
          render: 'lastUpdateTime',
          width: 120,
        },
        {
          title: t('操作'),
          fixed: 'right',
          width: 140,
          render: (record) => (
            <div style={{ display: 'flex', paddingTop: '10px' }}>
              <Button
                size="small"
                text
                type="primary"
                className={styles.listOperationButton}
                onClick={() => {
                  store.changeData({
                    updateVisible: true,
                    updateObj: {
                      ...record,
                      triggerTimes: record.triggerTimes.map((item) => (item.withTimeDiff)),
                    },
                  });
                }}
              >
                {t('编辑')}
              </Button>
              <Button
                size="small"
                text
                type="primary"
                className={styles.listOperationButton}
                onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
              >
                {t('操作日志')}
              </Button>
            </div>
          ),
        },
      ];

      switch (type) {
        case replenishTypeMap.get(t('紧急补货')):
          return [...firstCols, ...middleEmergencyReplenishmentCols, ...lastCols];
        case replenishTypeMap.get(t('日常补货')):
          return [...firstCols, ...middleDailyReplenishmentCols, ...lastCols];
        default:
          return [...firstCols, ...lastCols];
      }
    };

    const columns = getCols(pageCacheReplenishType);

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'OVERSEAS_REPLENISH_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  limit: PropTypes.shape(),
  pageCacheReplenishType: PropTypes.string,
};

export default List;
