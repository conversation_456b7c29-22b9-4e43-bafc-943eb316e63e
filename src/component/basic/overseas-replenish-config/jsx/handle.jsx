import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import moment from 'moment';
import {
  Button, Modal, Select, Checkbox, DatePicker, Tag, Form,
} from 'shineout';
import RuleInput from '@shein-components/WmsInput';
import globalStyles from '@src/component/style.less';
import styles from '../style.less';
import store, { replenishTypeMap, ifList } from '../reducers';

class Handle extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      time: '',
    };
  }

  render() {
    const {
      loading,
      updateVisible,
      updateObj,
      replenishTypeList,
      replenishCycleList,
      replenishStateList,
      currentWarehouseList,
    } = this.props;

    const {
      time,
    } = this.state;

    const isEdit = Boolean(updateObj?.id);

    const modalBtnDisabled = updateObj.warehouse === ''
      || updateObj.replenishType === ''
      || updateObj.state === ''
      || updateObj.replenishCycle.length === 0
      || updateObj.triggerTimes.length === 0
      || (updateObj.replenishType === replenishTypeMap.get(t('日常补货')) && updateObj.maxSafetyDay === '')
      || (updateObj.replenishType === replenishTypeMap.get(t('紧急补货')) && (
        updateObj.ifDeductionOnWay === ''
        || updateObj.ifEntireLocation === ''
        || updateObj.ifAutoSendTask === ''
      ));

    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              updateVisible: true,
              updateObj: {
                warehouse: '', // 仓库id
                replenishType: '', // 补货类型
                state: '', // 补货状态
                replenishCycle: [], // 补货周期
                triggerTimes: [], // 触发时间
                maxSafetyDay: '', // 拣货区最大安全库存天数
                minSafetyDay: '', // 拣货区最小安全库存天数
                overOccupyThreshold: '', // 超量预占阈值
                ifDeductionOnWay: '', // 是否扣减在途
                ifEntireLocation: '', // 是否整库位预占
                ifAutoSendTask: '', // 是否自动下发任务
              },
            });
          }}
        >
          {t('新增')}
        </Button>
        <Modal
          title={isEdit ? t('修改授权') : t('新增授权')}
          visible={updateVisible}
          maskCloseAble={null}
          onClose={() => {
            store.changeData({ updateVisible: false });
            this.setState({ time: '' });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({ updateVisible: false });
                this.setState({ time: '' });
              }}
            >
              {t('取消')}
            </Button>,
            <Modal.Submit
              disabled={!loading || modalBtnDisabled}
            >
              {t('保存')}
            </Modal.Submit>,
          ]}
          width={650}
        >
          <Form
            labelWidth={80}
            labelAlign="top"
            style={{ maxWidth: 650 }}
            onSubmit={() => {
              store.saveOrUpdateData();
              this.setState({ time: '' });
            }}
            onChange={(value) => {
              store.changeData({
                updateObj: value,
              });
            }}
            value={updateObj}
          >
            <div className={styles.modalItem}>
              <div className={styles.modalItemLabel}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('基础信息')}
              </div>
              <div style={{ display: 'flex', flexWrap: 'wrap' }}>
                <div className={styles.modalSubItem}>
                  {/* <span className={styles.modalSubItemLabel}>{t('仓库')}</span> */}
                  <Form.Item label={t('仓库')} labelAlign="right">
                    <Select
                      name="warehouse"
                      data={currentWarehouseList}
                      disabled={isEdit}
                      keygen="id"
                      format="id"
                      placeholder={t('请选择')}
                      renderItem="nameZh"
                      renderUnmatched={(r) => r.nameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
                      onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                      width={200}
                    />
                  </Form.Item>
                </div>
                <div className={styles.modalSubItem}>
                  <Form.Item label={t('补货类型')} labelAlign="right">
                    <Select
                      name="replenishType"
                      data={replenishTypeList}
                      disabled={isEdit}
                      keygen="dictCode"
                      format="dictCode"
                      placeholder={t('请选择')}
                      renderItem="dictNameZh"
                      renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
                      onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                      width={200}
                      onChange={(v) => {
                        if (replenishTypeMap.get(t('紧急补货')) === v) {
                          store.changeData({
                            updateObj: {
                              ...updateObj,
                              replenishType: v,
                              maxSafetyDay: '', // reset
                              minSafetyDay: '', // reset
                              overOccupyThreshold: '', // reset
                            },
                          });
                        }
                        if (replenishTypeMap.get(t('日常补货')) === v) {
                          store.changeData({
                            updateObj: {
                              ...updateObj,
                              replenishType: v,
                              ifDeductionOnWay: '', // reset
                              ifEntireLocation: '', // reset
                              ifAutoSendTask: '', // reset
                            },
                          });
                        }
                      }}
                    />
                  </Form.Item>
                </div>
                <div className={styles.modalSubItem}>
                  <Form.Item label={t('状态')} labelAlign="right">
                    <Select
                      name="state"
                      data={replenishStateList}
                      keygen="dictCode"
                      format="dictCode"
                      placeholder={t('请选择')}
                      renderItem="dictNameZh"
                      renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
                      onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                      width={200}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
            <Form.Item label={t('补货周期')} required>
              <Checkbox.Group
                name="replenishCycle"
                data={replenishCycleList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
              />
            </Form.Item>
            <Form.Item label={t('触发时间')} required>
              <div style={{ marginBottom: '10px' }}>
                <span style={{ marginLeft: '10px', marginRight: '10px' }}>{t('新增时间')}</span>
                <DatePicker
                  type="time"
                  format="HH:mm"
                  value={time}
                  onChange={(val) => {
                    const { triggerTimes = [] } = updateObj;

                    this.setState({
                      time: val,
                    });

                    if (val) {
                      store.changeData({
                        updateObj: {
                          ...updateObj,
                          triggerTimes: Array.from(new Set([...triggerTimes, val]))
                            .sort((a, b) => (moment(`2022-12-30 ${a}`).isBefore(moment(`2022-12-30 ${b}`)) ? -1 : 1)),
                        },
                      });
                    }
                  }}
                />
              </div>
              <div style={{ minHeight: '30px' }}>
                {
                  updateObj.triggerTimes?.map((item) => (
                    <Tag
                      key={item}
                      onClose={() => {
                        const { triggerTimes } = updateObj;

                        store.changeData({
                          updateObj: {
                            ...updateObj,
                            triggerTimes: triggerTimes.filter((i) => i !== item),
                          },
                        });
                      }}
                      style={{ marginBottom: '5px' }}
                    >
                      {item}
                    </Tag>
                  ))
                }
              </div>
            </Form.Item>
          </Form>
          {
            updateObj?.replenishType === replenishTypeMap.get(t('紧急补货')) && (
              <>
                <Form.Item label={t('是否扣减在途')} labelAlign="top" required>
                  <Select
                    value={updateObj.ifDeductionOnWay}
                    data={ifList}
                    keygen="dictCode"
                    format="dictCode"
                    placeholder={t('请选择')}
                    renderItem="dictNameZh"
                    renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
                    onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    width={200}
                    onChange={(v) => {
                      store.changeData({
                        updateObj: {
                          ...updateObj,
                          ifDeductionOnWay: v,
                        },
                      });
                    }}
                  />
                </Form.Item>
                <Form.Item label={t('是否整库位预占')} labelAlign="top" required>
                  <Select
                    value={updateObj.ifEntireLocation}
                    data={ifList}
                    keygen="dictCode"
                    format="dictCode"
                    placeholder={t('请选择')}
                    renderItem="dictNameZh"
                    renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
                    onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    width={200}
                    onChange={(v) => {
                      store.changeData({
                        updateObj: {
                          ...updateObj,
                          ifEntireLocation: v,
                        },
                      });
                    }}
                  />
                </Form.Item>
                <Form.Item label={t('是否自动下发任务')} labelAlign="top" required>
                  <Select
                    value={updateObj.ifAutoSendTask}
                    data={ifList}
                    keygen="dictCode"
                    format="dictCode"
                    placeholder={t('请选择')}
                    renderItem="dictNameZh"
                    renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
                    onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    width={200}
                    onChange={(v) => {
                      store.changeData({
                        updateObj: {
                          ...updateObj,
                          ifAutoSendTask: v,
                        },
                      });
                    }}
                  />
                </Form.Item>
              </>
            )
          }
          {
            updateObj?.replenishType === replenishTypeMap.get(t('日常补货')) && (
              <>
                <Form.Item label={t('拣货区最大安全库存天数')} labelAlign="top" required>
                  <RuleInput.Number
                    value={updateObj.maxSafetyDay}
                    type="number"
                    digits={1}
                    min={0}
                    placeholder={t('请输入')}
                    style={{ width: '200px' }}
                    onChange={(v) => {
                      store.changeData({
                        updateObj: {
                          ...updateObj,
                          maxSafetyDay: v,
                        },
                      });
                    }}
                  />
                </Form.Item>
                <Form.Item label={t('拣货区最小安全库存天数')} labelAlign="top">
                  <RuleInput.Number
                    value={updateObj.minSafetyDay}
                    type="number"
                    digits={1}
                    min={0}
                    placeholder={t('请输入')}
                    style={{ width: '200px' }}
                    onChange={(v) => {
                      store.changeData({
                        updateObj: {
                          ...updateObj,
                          minSafetyDay: v,
                        },
                      });
                    }}
                  />
                </Form.Item>
                <Form.Item label={t('超量预占阈值')} labelAlign="top">
                  <RuleInput.Number
                    value={updateObj.overOccupyThreshold}
                    type="number"
                    digits={0}
                    min={0}
                    placeholder={t('请输入')}
                    style={{ width: '200px' }}
                    onChange={(v) => {
                      store.changeData({
                        updateObj: {
                          ...updateObj,
                          overOccupyThreshold: v,
                        },
                      });
                    }}
                  />
                </Form.Item>
              </>
            )
          }
          <div />
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  updateVisible: PropTypes.bool,
  updateObj: PropTypes.shape(),
  replenishTypeList: PropTypes.arrayOf(PropTypes.shape()),
  replenishCycleList: PropTypes.arrayOf(PropTypes.shape()),
  replenishStateList: PropTypes.arrayOf(PropTypes.shape()),
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
