/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.handleExportButton{
    margin-right: 6px;
}


/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect{
    width: 140px;
 }
 
 .headerUnmatchedText{
     color: #bbb;
 }

.headerSearchTimeLine{
    display: flex;
}

/* list.jsx
---------------------------------------------------------------- */
.listOperation<PERSON>utton{
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}
.listSection{
    height: 0px; /* 必写为了子domheight - 100%有效 */
    flex: 1;    
}

/* ---------------------------------------------------------------- */
.itemTitle {
    font-size: 16px;
    padding: 6px;
    border-bottom: 1px dashed #6666;
    margin-bottom: 10px;
}



