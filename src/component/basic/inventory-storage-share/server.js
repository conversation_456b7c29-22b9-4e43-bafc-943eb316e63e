import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/inventory_storage_share/query',
  param,
}, process.env.WWS_URI);

/**
 * 新增
 * @param {*} param
 * @returns
 */
export const addAPI = (param) => sendPostRequest({
  url: '/inventory_storage_share/add',
  param,
}, process.env.WWS_URI);

/**
 * 编辑
 * @param {*} param
 * @returns
 */
export const editAPI = (param) => sendPostRequest({
  url: '/inventory_storage_share/update',
  param,
}, process.env.WWS_URI);

/**
 * 删除
 * @param {*} param
 * @returns
 */
export const deleteAPI = (param) => sendPostRequest({
  url: '/inventory_storage_share/delete',
  param,
}, process.env.WWS_URI);
