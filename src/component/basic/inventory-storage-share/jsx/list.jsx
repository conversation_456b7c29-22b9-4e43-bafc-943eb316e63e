import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Tag, Popover } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      selectedRows,
    } = this.props;

    const getListColumn = (d) => {
      let showPopover = false;

      if (d.storageComposeList.length > 3 || d.storageComposeList.find((x) => x.storagePropertyRspList.length > 3)) {
        showPopover = true;
      }

      return (
        <div>
          {
            showPopover
            && (
            <Popover
              type="info"
              position="left"
              style={{ padding: 5 }}
              background="#fff"
              border="#fff"
            >
              {d.storageComposeList.sort((a, b) => a.serialNum - b.serialNum).map((item) => (
                <div style={{ display: 'flex', marginBottom: 5 }}>
                  <div>
                    {item.serialNum}
                    :
                  </div>
                  <div>{item.storagePropertyRspList.map((storageItem) => (<Tag style={{ marginBottom: 5 }}>{storageItem.storageName}</Tag>))}</div>
                </div>
              ))}
            </Popover>
            )
          }
          {d.storageComposeList.sort((a, b) => a.serialNum - b.serialNum).slice(0, 3).map((item) => (
            <div style={{ display: 'flex', marginBottom: 5 }}>
              <div>
                {item.serialNum}
                :
              </div>
              <div>
                {item.storagePropertyRspList.slice(0, 3).map((storageItem) => (<Tag style={{ marginBottom: 5 }}>{storageItem.storageName}</Tag>))}
                {item.storagePropertyRspList.length > 3 ? '...' : null}
              </div>
            </div>
          ))}
          {d.storageComposeList.length > 3 ? '...' : null}
        </div>
      );
    };

    const columns = [
      {
        title: t('编码'),
        render: 'businessCode',
        width: 100,
      },
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('片区'),
        render: 'regionName',
        width: 120,
      },
      {
        title: t('存储属性组合'),
        width: 190,
        render: (d) => getListColumn(d),
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 180,
      },
      {
        title: t('更新人'),
        render: 'userName',
        width: 100,
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
