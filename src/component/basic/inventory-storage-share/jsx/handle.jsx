import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Select, Input, Rule, Checkbox,
} from 'shineout';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import globalStyles from '@src/component/style.less';
import styles from '../style.less';
import store from '../reducers';

const rules = Rule();

class Handle extends React.Component {
  render() {
    const {
      loading,
      selectedRows,
      modalType,
      modalInfo,
      regionData,
      goodList,
      canAdd,
      canDel,
      currentWarehouseList,
    } = this.props;
    const selectedGoods = modalInfo.storageComposeList.reduce((pre, cur) => pre.concat(cur), []);

    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.openModal(1);
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            store.openModal(0);
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            Modal.confirm({
              title: t('是否删除？'),
              onOk: () => {
                store.deleteData();
              },
            });
          }}
        >
          {t('删除')}
        </Button>

        <Modal
          maskCloseAble={null}
          visible={[0, 1].includes(modalType)}
          width={800}
          title={modalType ? t('新增') : t('编辑')}
          bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
          onClose={() => {
            store.closeModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Button type="primary" onClick={() => store.saveData()}>{t('确认')}</Button>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            inline
            formRef={(f) => store.changeData({ modalFormRef: f })}
          >
            <div style={{ marginBottom: 20 }}>
              <div className={styles.itemTitle}>{t('基础信息')}</div>
              <Form.Item required label={t('编码')} tip={t('混放编码示例：SS-AB（释义为三水-安博）')}>
                <Input
                  value={modalInfo.businessCode} placeholder={t('大写字母/字符/数字进行组合')} rules={[rules.required(t('请输入编码'))]}
                  disabled={!modalType} maxLength={50} width={250}
                  onChange={(v) => {
                    store.changeData({
                      modalInfo: {
                        ...modalInfo,
                        businessCode: v,
                      },
                    });
                  }}
                />
              </Form.Item>
            </div>

            <div style={{ marginBottom: 20 }}>
              <div className={styles.itemTitle}>{t('类型配置')}</div>
              <Form.Item required label={t('仓库')}>
                <Select
                  data={currentWarehouseList}
                  keygen="id"
                  format="id"
                  placeholder={t('请选择')}
                  renderItem="nameZh"
                  style={{ width: 180 }}
                  rules={[rules.required(t('请选择仓库'))]}
                  renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                  onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  value={modalInfo.warehouseId}
                  onChange={(v) => {
                    store.changeData({
                      modalInfo: {
                        ...modalInfo,
                        warehouseId: v,
                      },
                    });
                  }}
                  absolute
                />
              </Form.Item>
              <Form.Item required label={t('片区')}>
                <Select
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  data={regionData}
                  clearable
                  rules={[rules.required(t('请选择片区'))]}
                  placeholder={t('请选择')}
                  style={{ width: 180 }}
                  value={modalInfo.region}
                  renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  onChange={(v) => {
                    store.changeData({
                      modalInfo: {
                        ...modalInfo,
                        region: v,
                      },
                    });
                  }}
                  absolute
                />
              </Form.Item>
            </div>

            <div style={{ marginBottom: 20 }}>
              <div className={styles.itemTitle}>{t('存储属性组合')}</div>
              <div>
                {
                  modalInfo.storageComposeList.map((goodValue, i) => (
                    <div style={{
                      display: 'flex',
                      marginTop: 10,
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                    }}
                    >
                      <div style={{ flexShrink: 0, width: 50 }}>
                        <span style={{ color: 'red', marginRight: 5 }}>{`${i}` === '0' ? '*' : ''}</span>
                        {t('第{}组', i + 1)}
                      </div>
                      <div style={{ marginLeft: 10, padding: '4px 6px', border: '1px solid rgb(212,210,210)' }}>
                        {goodList.filter((x) => x.id !== 0).map((goods) => (
                          <Checkbox
                            checked={selectedGoods.includes(goods.id)}
                            disabled={selectedGoods.includes(goods.id) && !goodValue.includes(goods.id)}
                            key={goods.id}
                            htmlValue={goods.id}
                            onChange={(val) => {
                              let selectedList = goodValue.slice();

                              // 判断是否选中
                              if (val) {
                                selectedList.push(goods.id);
                              } else {
                                selectedList = selectedList.filter((x) => x !== goods.id);
                              }

                              store.changeGoodList({
                                selectedList,
                                index: i,
                                type: 'change',
                              });
                            }}
                          >
                            {goods.typeName}
                          </Checkbox>
                        ))}
                      </div>
                      <div style={{ flexShrink: 0 }}>
                        <Button
                          type="primary"
                          text
                          loading={!loading}
                          disabled={!canAdd}
                          onClick={() => {
                            store.changeGoodList({
                              selectedList: [],
                              index: i + 1,
                              type: 'add',
                            });
                          }}
                        >
                          <Icon name="plus-o" />
                        </Button>
                        <Button
                          style={{ marginLeft: 0 }}
                          type="danger"
                          text
                          loading={!loading}
                          disabled={!canDel}
                          onClick={() => {
                            store.changeGoodList({
                              selectedList: [],
                              index: i,
                              type: 'delete',
                            });
                          }}
                        >
                          <Icon name="minus-o" />
                        </Button>
                      </div>
                    </div>

                  ))
                }
              </div>
            </div>

          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  modalType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalInfo: PropTypes.shape(),
  regionData: PropTypes.arrayOf(PropTypes.shape()),
  goodList: PropTypes.arrayOf(PropTypes.shape()),
  canAdd: PropTypes.bool,
  canDel: PropTypes.bool,
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
