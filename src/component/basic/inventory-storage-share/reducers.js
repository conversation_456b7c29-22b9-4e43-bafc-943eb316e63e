import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect, getWarehouseApi } from '@src/server/basic/dictionary';
import { queryGoodsStoreType } from '@src/server/common/common';
import { handleListMsg } from '@src/lib/dealFunc';
import {
  getListAPI, addAPI, editAPI, deleteAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  warehouseId: 1, // 仓库必填
  businessCode: '', //  编码
  regionList: [], // 片区
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  // 操作区域
  warehouseIds: '', // 保留右上角仓库，由于init时判断是否要重置子仓等
  selectedRows: [], // 选中的表格列
  regionData: [], // 片区 下拉
  warehouseList: [], // 仓库 下拉
  goodList: [], // 商品存储属性 下拉
  modalFormRef: {}, // 用于校验弹窗内的表单项
  modalType: '', // 弹窗类型 1:新增 0:编辑
  modalInfo: {
    warehouseId: 1, // 仓库id，默认佛山仓
    businessCode: '', // 编码
    region: '', // 片区ID
    storageComposeList: [[]],
    // {
    //   serialNum: 1, // 序列号
    //   storageIds: [], // 存储属性ids
    // }],
  },
  canAdd: true, // 控制存储属性组合
  canDel: false, // 控制存储属性组合
  currentWarehouseList: [], // 权限仓库列表
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const { currentWarehouseList } = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
    });
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['REGION_ENUM'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        regionData: selectData.info.data.find((item) => item.catCode === 'REGION_ENUM').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }
  },

  /**
   * 搜索
   */
  * search() {
    const { warehouseId } = yield 'nav';
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  /**
   * @description: 组合组合
   * @param {array} selectedList 用户勾选变化
   * @param {number} index 第几组
   * @param {string} type  add/edit 新增组/修改组
   * @returns {varType}
   */
  * changeGoodList({ selectedList, index, type }) {
    const { modalInfo, goodList } = yield '';
    const list = modalInfo.storageComposeList.slice();
    let canDel = true;
    let canAdd = true;

    switch (type) {
      case 'add':
        list.splice(index, 0, []);
        break;
      case 'change':
        list.splice(index, 1, selectedList);
        break;
      case 'delete':
        list.splice(index, 1);
        break;
      default:
        throw new Error(`unknown param type: ${type}`);
    }
    // 只剩一个序号，不可删
    if (list.length === 1) {
      canDel = false;
    }
    // 已全选，不可新增
    if (goodList.length === list.reduce((pre, cur) => pre.concat(cur), []).length) {
      canAdd = false;
    }
    yield this.changeData({
      modalInfo: {
        ...modalInfo,
        storageComposeList: list,
      },
      canAdd,
      canDel,
    });
  },
  /**
   * 打开弹窗
   * @returns
   */
  * openModal(modalType) {
    const { selectedRows } = yield '';
    const [warehouseList, goodList] = yield Promise.all([
      getWarehouseApi({ enabled: 1 }),
      queryGoodsStoreType({ enabled: 1, pageNum: 1, pageSize: 100 }),
    ]);
    if (warehouseList.code === '0' && goodList.code === '0') {
      yield this.changeData({
        warehouseList: warehouseList.info.data,
        goodList: goodList.info.data,
      });
    }

    if (modalType === 0) {
      const {
        id,
        warehouseId,
        businessCode,
        region,
        storageComposeList,
      } = selectedRows[0];

      const list = storageComposeList.length > 0
        ? storageComposeList.sort((a, b) => a.serialNum - b.serialNum).map((x) => x.storagePropertyRspList.map((y) => y.storageId))
        : [[]];

      yield this.changeData({
        modalInfo: {
          id,
          warehouseId,
          businessCode,
          region,
          storageComposeList: list,
        },
      });
    }
    yield this.changeData({
      modalType,
    });
  },

  /**
   * 关闭弹窗
   * @returns
   */
  * closeModal() {
    const { modalFormRef } = yield '';
    yield this.changeData({
      modalType: '',
      modalInfo: {
        warehouseId: 1,
        businessCode: '',
        region: '',
        storageComposeList: [[]],
      },
    });
    if (modalFormRef && modalFormRef.clearValidate) modalFormRef.clearValidate();
  },

  /**
   * 保存
   * @returns
   */
  * saveData() {
    const { modalInfo, modalType, selectedRows } = yield '';
    const {
      warehouseId, region, businessCode,
    } = modalInfo;

    if (region === '') {
      Modal.error({ title: t('请输入片区') });
      return;
    }
    if (businessCode === '') {
      Modal.error({ title: t('请输入编码') });
      return;
    }

    const storageComposeList = modalInfo.storageComposeList
      .filter((x) => x.length !== 0)
      .map((x, index) => ({
        serialNum: index + 1,
        storageIds: x,
      }));

    if (storageComposeList.length === 0) {
      Modal.error({ title: t('存储属性组合必填一条') });
      return;
    }

    // eslint-disable-next-line no-promise-executor-return
    const status = yield new Promise((r) => Modal.confirm({
      title: t('本次修改内容将影响到操作员下一次领取任务的操作,请谨慎修改'),
      onOk: () => r('ok'),
      onCancel: () => r('cancel'),
      onClose: () => r('cancel'),
    }));
    if (status === 'ok') {
      const param = {
        warehouseId,
        businessCode,
        region,
        storageComposeList,
        id: modalType ? undefined : selectedRows[0].id,
      };

      markStatus('loading');
      const API = modalType ? addAPI : editAPI;
      const { code, msg } = yield API(param);
      if (code === '0') {
        Message.success(modalType ? t('新增成功') : t('编辑成功！'));
        yield this.closeModal();
        yield this.handlePaginationChange({ pageNum: 1 });
      } else {
        Modal.error({ title: msg });
      }
    }
  },

  /**
   * 删除
   * @returns
   */
  * deleteData() {
    const { selectedRows } = yield '';

    markStatus('loading');
    const { code, msg } = yield deleteAPI({
      ids: selectedRows.map((d) => d.id),
    });
    if (code === '0') {
      Message.success(t('删除成功！'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg || t('后台接口出错') });
    }
  },
};
