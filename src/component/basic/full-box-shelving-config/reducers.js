import { markStatus } from 'rrc-loader-helper';
import { t } from '@shein-bbl/react';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { queryRegionAPI } from '@src/server/common/common';
import {
  queryAPI,
  saveOrUpdateAPI,
  queryFastLevelAPI,
} from './server';

export const EDIT_MODAL_HIDE = 0; // 不可见
export const EDIT_MODAL_EDIT = 1; // 编辑
export const EDIT_MODAL_ADD = 2; // 新增

// 搜索默认值
export const defaultLimit = {
  region: [], // 片区
};

export const defaultModalObj = {
  region: '', // 上架片区
  fastLevel: '', // 快流等级
  enabled: 1, // 状态
  deviation: '', // 偏差值
};

// 其他默认值
export const defaultState = {
  formRef: {},
  loading: 1, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  list: [],
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  selectedRows: [], // 选中的表格列
  warehouseId: '', // 仓库id
  fastLevelList: [], // 授权名称列表
  regionList: [], // 片区列表
  modalType: EDIT_MODAL_HIDE, // modal 是否可见
  enabledList: [
    {
      dictCode: 1,
      dictNameZh: t('启用'),
    },
    {
      dictCode: 2,
      dictNameZh: t('禁用'),
    },
  ], // 状态下拉
  modalObj: defaultModalObj,
  recordVisible: false,
  recordId: '',
};

export default {
  state: defaultState,
  // 改变state的值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  /**
   * 初始化数据
   */
  * init() {
    markStatus('loading');
    yield this.queryFastLevel();
    yield this.getRegion();
  },
  /**
   * 搜索操作
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
    };
    markStatus('loading');
    const { code, info, msg } = yield queryAPI(param);
    if (code === '0') {
      yield this.changeData({
        list: info.data || [],
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  /**
   * 页签改变
   * @param {Object} arg
   */
  * handlePaginationChange(arg = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  /**
   * 右上角仓库事件派发
   *
   */
  * handleWarehouseChange() {
    yield this.changeLimitData({
      region: [], // 片区
    });
    yield this.getRegion();
  },
  /**
   * 操作按钮 - 编辑
   */
  * handleEdit() {
    const { selectedRows } = yield '';
    const record = selectedRows[0] || {};
    yield this.changeData({
      modalType: EDIT_MODAL_EDIT,
      modalObj: {
        ...record,
      },
    });
  },
  /**
   * 编辑
   */
  * editSubmit() {
    const { modalType, modalObj } = yield '';
    markStatus('loading');
    let warehouseIdParam;
    if (modalType === EDIT_MODAL_EDIT) {
      warehouseIdParam = modalObj.warehouseId;
    } else {
      const { warehouseId } = yield 'nav';
      if (!warehouseId) {
        Modal.error({ title: t('请在右上角选择仓库') });
        return;
      }
      warehouseIdParam = warehouseId;
    }
    const { code, msg } = yield saveOrUpdateAPI({
      ...modalObj,
      warehouseId: warehouseIdParam,
    });
    if (code === '0') {
      Message.success(modalType === EDIT_MODAL_EDIT ? t('编辑成功') : t('新增成功'));
      yield this.changeData({
        modalType: EDIT_MODAL_HIDE,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * header 校验表单
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  /**
   * 获取片区
   */
  * getRegion() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { code, info, msg } = yield queryRegionAPI({ warehouseId });
    if (code === '0') {
      yield this.changeData({
        regionList: info || [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * @description 快慢流等级配置-获取快流等级
   * @param warehouseId {number} 右上角的仓库
   * @returns {Generator<void|*, void, *>}
   */
  * queryFastLevel() {
    const { warehouseId } = yield 'nav';
    const { code, info, msg } = yield queryFastLevelAPI({ warehouseId });
    if (code === '0') {
      yield this.changeData({
        fastLevelList: (info || []).map((item) => (
          {
            dictCode: item,
            dictNameZh: item,
          }
        )),
      });
    } else {
      Modal.error({ title: msg });
    }
  },
};
