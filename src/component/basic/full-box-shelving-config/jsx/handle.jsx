import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Select, Form, Rule, Input,
} from 'shineout';
import { t } from '@shein-bbl/react';
import globalStyles from '@src/component/style.less';
import store, {
  EDIT_MODAL_HIDE, EDIT_MODAL_ADD, EDIT_MODAL_EDIT, defaultModalObj,
} from '../reducers';

const inputStyle = {
  width: '200px',
};
const rules = Rule({
  deviationRange: {
    func: (val, _, callback) => {
      if (val > 5 || val < -5) {
        callback(new Error(t('只允许输入[-5, 5]的数值，最长支持两位小数')));
      }
      callback(true);
    },
  },
});
class Handle extends React.Component {
  render() {
    const {
      loading,
      selectedRows,
      modalType,
      modalObj,
      regionList,
      fastLevelList,
      enabledList,
    } = this.props;

    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              modalType: EDIT_MODAL_ADD,
              modalObj: defaultModalObj,
            });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            store.handleEdit();
          }}
        >
          {t('编辑')}
        </Button>
        <Modal
          visible={!!modalType}
          title={modalType === EDIT_MODAL_ADD ? t('新增') : t('编辑')}
          width={780}
          destroy
          maskCloseAble={false}
          onClose={() => store.changeData({ modalType: EDIT_MODAL_HIDE })}
          footer={(
            <div>
              <Button onClick={() => store.changeData({
                modalType: EDIT_MODAL_HIDE,
              })}
              >
                {t('取消')}
              </Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              store.editSubmit(modalObj);
            }}
            onChange={(value) => {
              store.changeData({
                modalObj: value,
              });
            }}
            value={modalObj}
            inline
          >
            <Form.Item required label={t('上架片区')}>
              <Select
                data={regionList}
                name="region"
                required
                keygen="region"
                format="region"
                renderItem="regionName"
                placeholder={t('请选择')}
                onFilter={(text) => (d) => d.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                style={inputStyle}
                rules={[rules.required(t('请选择上架片区'))]}
                disabled={modalType === EDIT_MODAL_EDIT}
              />
            </Form.Item>
            <Form.Item required label={t('快流等级')}>
              <Select
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                name="fastLevel"
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                style={inputStyle}
                data={fastLevelList}
                placeholder={t('请选择')}
                rules={[rules.required(t('请选择快流等级'))]}
                disabled={modalType === EDIT_MODAL_EDIT}
                clearable
              />
            </Form.Item>
            <Form.Item required label={t('状态')}>
              <Select
                name="enabled"
                data={enabledList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={inputStyle}
                rules={[rules.required(t('请选择状态'))]}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
              />
            </Form.Item>
            <Form.Item required label={t('偏差值')}>
              <Input.Number
                name="deviation"
                style={inputStyle}
                digits={2}
                allowNull
                hideArrow
                rules={[rules.required(t('请输入偏差值')), rules.deviationRange()]}
                clearable
                beforeChange={(v) => {
                  if (v > 5 || v < -5) {
                    return modalObj.deviation;
                  }
                  return v;
                }}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  modalType: PropTypes.number,
  modalObj: PropTypes.shape(),
  regionList: PropTypes.arrayOf(PropTypes.shape()),
  fastLevelList: PropTypes.arrayOf(PropTypes.shape()),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
