import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import OperationModal from '@public-component/modal/operation-modal';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      selectedRows,
      recordVisible,
      recordId,
    } = this.props;

    const columns = [
      {
        title: t('片区'),
        render: 'regionName',
        width: 100,
      },
      {
        title: t('状态'),
        render: 'enabledDesc',
        width: 120,
      },
      {
        title: t('快流等级'),
        render: 'fastLevel',
        width: 120,
      },
      {
        title: t('偏差值'),
        render: 'deviation',
        width: 120,
      },
      {
        title: t('更新人'),
        render: 'operator',
        width: 120,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 180,
      },
      {
        title: t('操作'),
        width: 100,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              text
              type="primary"
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.id,
              })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'BOX_THRESHOLD_CONFIGURATION',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  recordVisible: PropTypes.bool,
  recordId: PropTypes.string,
};

export default List;
