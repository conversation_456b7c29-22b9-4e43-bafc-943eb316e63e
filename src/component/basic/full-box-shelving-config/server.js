import { sendPostRequest } from '@src/server/common/public';

// 查询整箱上架阈值配置
export const queryAPI = (param) => sendPostRequest({
  url: '/box_threshold_config/query',
  param,
}, process.env.WWS_URI);

// 添加/编辑整箱上架阈值配置
export const saveOrUpdateAPI = (param) => sendPostRequest({
  url: '/box_threshold_config/add',
  param,
}, process.env.WWS_URI);

// 快慢流等级配置-获取快流等级
export const queryFastLevelAPI = (param) => sendPostRequest({
  url: '/fast_slow_flow_level_config/query_all_fast_level',
  param,
}, process.env.WWS_URI);
