import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/country_safety_stock_factor/list',
  param,
}, process.env.WWS_URI);

/**
 * 导出
 */
export const editAPI = (param) => sendPostRequest({
  url: '/country_safety_stock_factor/edit',
  param,
}, process.env.WWS_URI);

/**
 * 更新
 */
export const updateAPI = (param) => sendPostRequest({
  url: '/country_safety_stock_factor/update',
  param,
}, process.env.WWS_URI);

// 预约
export const appointmentAPI = (param) => sendPostRequest({
  url: '/country_safety_stock_factor/appointment',
  param,
}, process.env.WWS_URI);

export const getAppointmentAPI = (param) => sendPostRequest({
  url: '/country_safety_stock_factor/appointment_query',
  param,
}, process.env.WWS_URI);
