import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { t } from '@shein-bbl/react';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import moment from 'moment';
import { handleListMsg } from '@src/lib/dealFunc';
import {
  getListAPI, editAPI, updateAPI, appointmentAPI, getAppointmentAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  nationalLineId: [],
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  modalFormRef: {},
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  nationalLineList: [],
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  editVisible: 0, // 编辑弹框
  editObj: {
    id: '',
    acrossRegionMaxDay: '', // 跨片区最大安全库存天数
    inRegionMaxDay: '', // 片区内最大安全库存天数
    inRegionMinDay: '', // 最小安全库存天数
  },
  appointmentVisible: 0,
  appointmentObj: {
    acrossRegionMaxDay: '', // 跨片区最大安全库存天数
    inRegionMaxDay: '', // 片区内最大安全库存天数
    inRegionMinDay: '', // 片区内最小安全库存天数
    appointmentTime: '', // 预约时间
    id: '',
  },
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['NATIONAL_LINE_TYPE'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        nationalLineList: selectData.info.data.find((item) => item.catCode === 'NATIONAL_LINE_TYPE').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }
  },

  /**
   * 搜索
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  /**
   * 编辑
   */
  * edit() {
    markStatus('loading');
    const { editObj } = yield '';
    const { code, msg } = yield editAPI({
      id: editObj.id,
      acrossRegionMaxDay: editObj.acrossRegionMaxDay,
      inRegionMaxDay: editObj.inRegionMaxDay,
      inRegionMinDay: editObj.inRegionMinDay,
    });
    if (code === '0') {
      yield this.handlePaginationChange({ pageNum: 1 });
      yield this.changeData({
        editVisible: 0,
        editObj: {},
      });
      Message.success(t('编辑成功'));
    } else {
      Modal.error({ title: msg });
    }
  },
  // 预约
  * appointment() {
    markStatus('loading');
    const { appointmentObj } = yield '';
    const { code, msg } = yield appointmentAPI({
      id: appointmentObj.id,
      acrossRegionMaxDay: appointmentObj.acrossRegionMaxDay,
      inRegionMinDay: appointmentObj.inRegionMinDay,
      inRegionMaxDay: appointmentObj.inRegionMaxDay,
      appointmentTime: appointmentObj.appointmentTime ? moment(appointmentObj.appointmentTime).format('YYYY-MM-DD HH:mm:ss') : '',
    });
    if (code === '0') {
      yield this.handlePaginationChange({ pageNum: 1 });
      yield this.changeData({
        appointmentVisible: 0,
        appointmentObj: {},
      });
      Message.success(t('保存成功'));
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 更新
   */
  * update() {
    markStatus('loading');
    const { code, msg } = yield updateAPI({});
    if (code === '0') {
      yield this.handlePaginationChange({ pageNum: 1 });
      Message.success(t('更新成功'));
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 获取预约数据
   */
  * getAppointment(record) {
    markStatus('loading');
    const { code, msg, info } = yield getAppointmentAPI({ id: record.id });
    if (code === '0') {
      yield this.changeData({
        appointmentVisible: 1,
        appointmentObj: {
          ...info,
          warehouseName: record.warehouseName,
          nationalLineName: record.nationalLineName,
          id: record.id,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
};
