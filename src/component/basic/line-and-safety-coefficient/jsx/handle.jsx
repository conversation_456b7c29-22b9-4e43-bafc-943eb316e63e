import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Input, Rule, DatePicker,
} from 'shineout';
import { t } from '@shein-bbl/react';
import globalStyles from '@src/component/style.less';
import store from '../reducers';
import style from '../style.less';

const rules = Rule({
  numberRule: {
    func: (val, formData, callback) => {
      if (val < 0) {
        return callback(new Error(t('仅允许输入大于等于{}且小数点后最多输入{}位', 0, 1)));
      }
      callback(true);
    },
  },
  acrossRegionMaxDayRule: {
    func: (val, formData, callback) => {
      if (formData.acrossRegionMaxDay < formData.inRegionMaxDay) {
        return callback(new Error(t('片区内最大安全库存天数不可大于最大安全库存天数')));
      }
      callback(true);
    },
  },
  inRegionMaxDayRule: {
    func: (val, formData, callback) => {
      if (formData.acrossRegionMaxDay < formData.inRegionMaxDay) {
        return callback(new Error(t('片区内最大安全库存天数不可大于最大安全库存天数')));
      }
      if (formData.inRegionMaxDay < formData.inRegionMinDay) {
        return callback(new Error(t('最小安全库存天数不可大于片区内最大安全库存天数')));
      }
      callback(true);
    },
  },
  inRegionMinDayRule: {
    func: (val, formData, callback) => {
      if (formData.inRegionMaxDay < formData.inRegionMinDay) {
        return callback(new Error(t('最小安全库存天数不可大于片区内最大安全库存天数')));
      }
      callback(true);
    },
  },
});

class Handle extends Component {
  render() {
    const {
      editVisible,
      editObj,
      loading,
      modalFormRef,
      appointmentVisible,
      appointmentObj,
    } = this.props;

    const dateReqiured = appointmentObj.acrossRegionMaxDay || appointmentObj.inRegionMaxDay || appointmentObj.inRegionMinDay;
    const acrossRegionMaxDayReqiured = appointmentObj.appointmentTime && !(appointmentObj.inRegionMaxDay || appointmentObj.inRegionMinDay);
    const mentInRegionMaxDaReqiured = appointmentObj.appointmentTime && !(appointmentObj.acrossRegionMaxDay || appointmentObj.inRegionMinDay);
    const regionMinDayReqiured = appointmentObj.appointmentTime && !(appointmentObj.acrossRegionMaxDay || appointmentObj.inRegionMaxDay);
    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.update();
          }}
        >
          {t('更新')}
        </Button>
        <Modal
          visible={editVisible}
          width={400}
          maskCloseAble={false}
          title={t('编辑')}
          onClose={() => store.changeData({ editVisible: 0 })}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ editVisible: 0 })}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={190}
            labelAlign="right"
            style={{ maxWidth: 400 }}
            onSubmit={() => store.edit()}
            onChange={(value) => {
              store.changeData({
                editObj: value,
              });
            }}
            value={editObj}
            formRef={(f) => {
              store.changeData({
                modalFormRef: f,
              });
            }}
          >
            <div className={style.header_wrap_view}>
              <Form.Item label={`${t('仓库')}:`} style={{ alignItems: 'center' }}>
                <span>{editObj?.warehouseName || ''}</span>
              </Form.Item>
              <Form.Item label={`${t('国家线')}:`} style={{ alignItems: 'center' }}>
                <span>{editObj?.nationalLineName || ''}</span>
              </Form.Item>
              <Form.Item required label={`${t('最大安全库存天数')}:`}>
                <Input.Number
                  name="acrossRegionMaxDay"
                  width={120}
                  digits={1}
                  max={99.9}
                  maxLength={4}
                  rules={[rules.required, rules.numberRule(), rules.acrossRegionMaxDayRule()]}
                  onChange={() => {
                    if (modalFormRef && modalFormRef.validate) {
                      modalFormRef.validate();
                    }
                  }}
                />
              </Form.Item>
              <Form.Item required label={`${t('片区内最大安全库存天数')}:`}>
                <Input.Number
                  name="inRegionMaxDay"
                  width={120}
                  digits={1}
                  max={99.9}
                  maxLength={4}
                  rules={[rules.required, rules.numberRule(), rules.inRegionMaxDayRule()]}
                  onChange={() => {
                    if (modalFormRef && modalFormRef.validate) {
                      modalFormRef.validate();
                    }
                  }}
                />
              </Form.Item>
              <Form.Item required label={`${t('最小安全库存天数')}:`}>
                <Input.Number
                  name="inRegionMinDay"
                  width={120}
                  digits={1}
                  max={99.9}
                  maxLength={4}
                  rules={[rules.required, rules.numberRule(), rules.inRegionMinDayRule()]}
                  onChange={() => {
                    if (modalFormRef && modalFormRef.validate) {
                      modalFormRef.validate();
                    }
                  }}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>

        <Modal
          visible={appointmentVisible}
          width={500}
          destroy
          maskCloseAble={false}
          title={t('预约配置')}
          onClose={() => store.changeData({ appointmentVisible: 0 })}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ appointmentVisible: 0 })}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={220}
            labelAlign="right"
            onSubmit={() => store.appointment()}
            onChange={(value) => {
              store.changeData({
                appointmentObj: value,
              });
            }}
            value={appointmentObj}
            formRef={(f) => {
              store.changeData({
                modalFormRef: f,
              });
            }}
          >
            <div className={style.header_wrap_view}>
              <Form.Item label={`${t('仓库')}:`} style={{ alignItems: 'center' }}>
                <span>{appointmentObj?.warehouseName || ''}</span>
              </Form.Item>
              <Form.Item label={`${t('国家线')}:`} style={{ alignItems: 'center' }}>
                <span>{appointmentObj?.nationalLineName || ''}</span>
              </Form.Item>
              <h3>{t('预约参数')}</h3>
              <Form.Item required={acrossRegionMaxDayReqiured} label={`${t('预约-最大安全库存天数')}:`}>
                <Input.Number
                  name="acrossRegionMaxDay"
                  width={180}
                  digits={1}
                  allowNull
                  max={99.9}
                  maxLength={4}
                  delay={0}
                  rules={acrossRegionMaxDayReqiured && [rules.required, rules.numberRule()]}
                  onChange={() => {
                    if (modalFormRef && modalFormRef.validate) {
                      modalFormRef.validate();
                    }
                  }}
                />
              </Form.Item>
              <Form.Item required={mentInRegionMaxDaReqiured} label={`${t('预约-片区内最大安全库存天数')}:`}>
                <Input.Number
                  name="inRegionMaxDay"
                  width={180}
                  allowNull
                  digits={1}
                  delay={0}
                  max={99.9}
                  maxLength={4}
                  rules={mentInRegionMaxDaReqiured && [rules.required, rules.numberRule()]}
                  onChange={() => {
                    if (modalFormRef && modalFormRef.validate) {
                      modalFormRef.validate();
                    }
                  }}
                />
              </Form.Item>
              <Form.Item required={regionMinDayReqiured} label={`${t('预约-最小安全库存天数')}:`}>
                <Input.Number
                  name="inRegionMinDay"
                  width={180}
                  digits={1}
                  allowNull
                  delay={0}
                  max={99.9}
                  maxLength={4}
                  rules={regionMinDayReqiured && [rules.required, rules.numberRule()]}
                  onChange={() => {
                    if (modalFormRef && modalFormRef.validate) {
                      modalFormRef.validate();
                    }
                  }}
                />
              </Form.Item>
              <Form.Item required={dateReqiured} label={`${t('预约生效时间')}:`}>
                <DatePicker
                  inputable
                  placeholder={t('预约生效时间')}
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  width={205}
                  rules={dateReqiured && [rules.required(t('请选择'))]}
                  name="appointmentTime"
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  editVisible: PropTypes.number,
  appointmentVisible: PropTypes.number,
  editObj: PropTypes.shape(),
  loading: PropTypes.number,
  modalFormRef: PropTypes.shape(),
  appointmentObj: PropTypes.shape(),
};
export default Handle;
