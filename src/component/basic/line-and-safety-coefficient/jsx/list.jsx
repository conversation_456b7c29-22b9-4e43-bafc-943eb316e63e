import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      modalFormRef,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('国家线'),
        render: 'nationalLineName',
        width: 120,
      },
      {
        title: t('最大安全库存天数'),
        render: 'acrossRegionMaxDay',
        width: 150,
      },
      {
        title: t('片区内最大安全库存天数'),
        render: 'inRegionMaxDay',
        width: 200,
      },
      {
        title: t('最小安全库存天数'),
        render: 'inRegionMinDay',
        width: 150,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 120,
      },
      {
        title: t('更新人'),
        render: 'updater',
        width: 120,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 140,
        render: (record) => (
          <div style={{ display: 'flex' }}>
            <Button
              text
              type="primary"
              onClick={() => {
                if (modalFormRef.clearValidate) modalFormRef.clearValidate();
                store.changeData({
                  editVisible: 1,
                  editObj: list.find((item) => item.id === record.id),
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              text
              type="primary"
              onClick={() => {
                if (modalFormRef.clearValidate) modalFormRef.clearValidate();
                store.getAppointment(record);
              }}
            >
              {t('预约配置')}
            </Button>
            <Button
              text
              type="primary"
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.id,
              })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          title={t('操作日志')}
          param={{
            operateId: recordId,
            operateCode: 'COUNTRY_SAFETY_STOCK_FACTOR',
          }}
          onCancel={() => store.changeData({
            recordVisible: false,
          })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  modalFormRef: PropTypes.shape(),
};

export default List;
