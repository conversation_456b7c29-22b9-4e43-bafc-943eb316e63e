import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Input, Rule, Select } from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import { validateCloseNumber } from '@src/lib/validate';
import store, { defaultLimit } from '../reducers';

const reg = validateCloseNumber();

const rule = Rule({
  printRule: {
    func: (val, formData, callback) => {
      if (formData.printNum && !reg.test(formData.printNum)) {
        callback(new Error(t('打印次数必须为>=0的整数')));
      }
      callback(true);
    },
  },
});

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      enabledList,
      usableStatusList,
      locationTypeList,
      subWarehouseList,
      nationalLineList,
      parkList,
      preSubWarehouseList,
      headerFormAreaList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 90 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('打印次数>')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Input label={t('集货/暂存位')} name="location" placeholder={t('请输入')} />
          <Select
            label={t('库位类型')}
            name="locationType"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...locationTypeList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('使用状态')}
            name="usableStatus"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...usableStatusList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Input
            required
            label={t('打印次数>')} rules={[rule.printRule()]}
            name="printNum" placeholder={t('请输入')}
          />
          <Select
            label={t('可用状态')}
            name="enabled"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...enabledList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('园区')}
            name="parkTypeList"
            required
            multiple
            compressed
            keygen="parkType"
            format="parkType"
            renderItem="parkName"
            data={parkList}
            clearable
            placeholder={t('全部')}
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              if (value && value.length > 0) {
                const { parkSubWarehouseList } = fliterSubwarehouse(value);
                store.changeData({
                  subWarehouseList: parkSubWarehouseList,
                });
              } else {
                store.changeData({
                  subWarehouseList: preSubWarehouseList,
                });
              }
              store.changeLimitData({
                parkTypeList: value,
                subWarehouseIds: [],
              });
            }}
          />
          <Select
            label={t('子仓')}
            name="subWarehouseIds"
            data={subWarehouseList}
            keygen="id"
            format="id"
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
            onChange={(val) => {
              store.changeData({
                headerFormAreaList: [],
                limit: {
                  ...limit,
                  subWarehouseIds: val,
                  areaIds: [],
                },
              });
              store.getHeaderFormAreaList({ subWarehouseIds: val });
            }}
          />
          <Select
            label={t('国家线')}
            name="nationalLineTypeList"
            data={nationalLineList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('库区')}
            name="areaIds"
            data={headerFormAreaList}
            keygen="id"
            format="id"
            renderItem="area"
            onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple={limit.subWarehouseIds && limit.subWarehouseIds.length === 1}
            compressed={limit.subWarehouseIds && limit.subWarehouseIds.length === 1}
            clearable
            placeholder={t('全部')}
          />
          <Input
            label={t('楼层')}
            name="floor"
            placeholder={t('请输入')}
            clearable
          />
          <Input.Number
            label={t('子分区')}
            name="childArea"
            allowNull
            digits={0}
            hideArrow
            min={0}
            max={99}
            maxLength={2}
            placeholder={t('请输入')}
            autocomplete="off"
            clearable
          />
          <Input
            required
            label={t('库位组号')}
            name="locationGroupNo" placeholder={t('请输入')}
          />
          <DateRangePicker
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['startTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('编辑时间')}
            span={2}
            clearable
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  usableStatusList: PropTypes.arrayOf(PropTypes.shape()),
  locationTypeList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  nationalLineList: PropTypes.arrayOf(PropTypes.shape()),
  headerFormAreaList: PropTypes.arrayOf(PropTypes.shape()),
  parkList: PropTypes.arrayOf(PropTypes.shape()),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
