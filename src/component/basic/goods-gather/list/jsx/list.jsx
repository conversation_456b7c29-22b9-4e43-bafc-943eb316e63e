import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
    } = this.props;
    const columns = [
      {
        title: t('集货/暂存位'),
        key: '1',
        width: 200,
        render: (record) => (
          <Button
            style={{ padding: 0, userSelect: 'text' }}
            type="link"
            onClick={() => {
              store.editOrAddShow({ modalType: 1, id: record.id });
            }}
          >
            {record.location}
          </Button>
        ),
      },
      {
        title: t('库位序号'),
        render: 'locationOrder',
        width: 100,
      },
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('园区'),
        render: 'parkTypeName',
        width: 100,
      },
      {
        title: t('子仓'),
        render: 'subWarehouseName',
        width: 100,
      },
      {
        title: t('楼层'),
        render: 'floor',
        width: 80,
      },
      {
        title: t('库区'),
        render: 'areaName',
        width: 80,
      },
      {
        title: t('子分区'),
        width: 100,
        render: (row) => row.childArea || '',
      },
      {
        title: t('库位组号'),
        width: 140,
        render: 'locationGroupNo',
      },
      {
        title: t('国家线'),
        render: 'nationalLineTypeName',
        width: 100,
      },
      {
        title: t('库位类型'),
        render: 'locationTypeName',
        width: 140,
      },
      {
        title: t('可用状态'),
        render: 'enabledName',
        width: 100,
      },
      {
        title: t('使用状态'),
        render: 'usableStatusName',
        width: 100,
      },
      {
        title: t('是否虚拟库位'),
        render: 'isVirtualName',
        width: 120,
      },
      {
        title: t('打印次数'),
        render: 'printNum',
        width: 90,
      },
      {
        title: t('最大存放箱数'),
        render: 'maxBoxNum',
        width: 120,
      },
      {
        title: t('存放托盘数'),
        render: 'palletNum',
        width: 120,
      },
      {
        title: t('操作'),
        width: 100,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];
    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'COLLECT_LOCATION_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  editAddressModal: PropTypes.bool,
  editAddressInfo: PropTypes.shape(),
  provinceList: PropTypes.arrayOf(PropTypes.shape()),
  cityList: PropTypes.arrayOf(PropTypes.shape()),
  districtList: PropTypes.arrayOf(PropTypes.shape()),
  contactModal: PropTypes.bool,
  contactInfo: PropTypes.shape(),
};

export default List;
