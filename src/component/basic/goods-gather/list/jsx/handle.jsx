import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Input, Modal, Select, Rule,
} from 'shineout';
import styles from '@src/component/style.less';
import { t } from '@shein-bbl/react';
import FileDelayUpload from '@src/component/public-component/upload/fileDelayUpload';
import { validatebOnlyNumber, validatebOnlyLetter } from '@src/lib/validate';
import store from '../reducers';

// 正则校验
const reg = validatebOnlyNumber();
const reg2 = validatebOnlyLetter();

class Handle extends React.Component {
  render() {
    const {
      limit,
      loading,
      addModal,
      modalType,
      selectedRows,
      addInfo,
      enabledList,
      modalSubWarehouseList,
      areaList,
      locationTypeList,
      floorList,
      nationalLineList,
      exportVisible,
      modalLoading,
      importModalVisible, // 导入弹出框是否显示 默认不显示
      file, // 导入文件对象
      currentWarehouseList,
      fpSubWarehouseList,
      normalExceptionMatchGoodsSubWarehouseList,
      isCheckLocationGroupNo,
    } = this.props;

    const rules = Rule({
      locationOrderRule: {
        func: (val, formData, callback) => {
          if (!formData.locationOrder) {
            callback(new Error(t('请输入库位序号')));
          }
          if (formData.locationOrder > 9999999 || formData.locationOrder < 999999) {
            callback(new Error(t('库位序号必须七位，且只能是数字')));
          }
          if (!reg.test(formData.locationOrder)) {
            callback(new Error(t('库位序号必须七位，且只能是数字')));
          }
          callback(true);
        },
      },
      locationRule: {
        func: (val, formData, callback) => {
          if (!formData.location || !reg2.test(formData.location)) {
            callback(new Error(t('请输入集货/暂存位且首位必须为字母')));
          }
          if (formData.location.length > 50) {
            callback(new Error(t('集货/暂存位不能超过50位')));
          }
          callback(true);
        },
      },
      floorRule: {
        func: (val, formData, callback) => {
          if ((formData.locationType === 7 || formData.locationType === 3) && !formData.floor) {
            callback(new Error(t('请选择楼层')));
          }
          callback(true);
        },
      },
      AreaRule: {
        func: (value, formData, callback) => {
          if ((formData.locationType === 7 || formData.locationType === 3 || formData.locationType === 10) && !formData.areaId) {
            callback(new Error(t('请选择库区')));
          }
          callback(true);
        },
      },
      locationGroupNo: {
        func: (val, formData, callback) => {
          if (Number(formData.locationType) === 23 && !formData.locationGroupNo) {
            callback(new Error(t('库位类型为异常商品货位时库位组号必填')));
          }
          // if (formData.locationGroupNo && !/^[A-Z][a-zA-Z0-9-]{0,19}$/g.test(formData.locationGroupNo)) {
          //   callback(new Error(t('库位组号只能输入字母和数字且首字母必须大写，最多输入20个字符')));
          // }
          // if (formData.locationGroupNo && !/^YCSP-[A-Za-z0-9]{1,19}-[A-Za-z0-9]+$/g.test(formData.locationGroupNo)) {
          //   callback(new Error(t('请输入正确格式：YCSP-子仓编码-序号，如YCSP-HS09-0001')));
          // }
          if (!isCheckLocationGroupNo.isPass && formData.locationGroupNo) {
            callback(new Error(isCheckLocationGroupNo.msg));
          }
          callback(true);
        },
      },
    });

    // 判断子分区是否必填
    const isRequiredChildArea = () => {
      const checkLocationType = addInfo.locationType === 3 || addInfo.locationType === 23;
      const checkSubWarehouseId = [...new Set([...fpSubWarehouseList, ...normalExceptionMatchGoodsSubWarehouseList])].includes(addInfo.subWarehouseId);
      if (checkLocationType && checkSubWarehouseId) {
        return true;
      }
      return false;
    };

    const ids = selectedRows.map((v) => v.id);
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          icon="plus"
          onClick={() => store.editOrAddShow({ modalType: 0 })}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            store.changeData({
              importModalVisible: true,
              file: '',
            });
          }}
        >
          {t('导入')}
        </Button>
        <Modal
          maskCloseAble={null}
          visible={importModalVisible}
          title={t('导入')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={!file}
              loading={!loading}
              onClick={() => {
                const formData = new FormData();
                formData.append('file', file);
                store.uploadFile(formData);
                return false;
              }}
            >
              {t('确认上传')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!loading}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
        </Modal>
        <Button
          disabled={exportVisible}
          type="primary"
          icon="plus"
          onClick={() => {
            if (limit.subWarehouseIds && limit.subWarehouseIds.length === 0) {
              delete limit.subWarehouseIds;
            }
            store.exportData();
          }}
        >
          {t('导出')}
        </Button>
        <Button
          type="primary"
          loading={!loading}
          onClick={() => store.downloadTemplate()}
        >
          {t('模版下载')}
        </Button>
        <Button
          type="primary"
          icon="printer"
          loading={!loading}
          disabled={ids.length === 0}
          onClick={() => {
            store.print();
          }}
        >
          {t('打印')}
        </Button>
        <Modal
          maskCloseAble={null}
          visible={addModal}
          width={800}
          title={!modalType ? t('新增') : t('编辑')}
          onClose={() => {
            store.closeModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!modalLoading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              store.commitData(addInfo);
            }}
            onChange={(value) => {
              store.changeData({
                addInfo: value,
              });
            }}
            value={addInfo}
            inline
            formRef={(f) => store.changeData({ modalFormRef: f })}
          >
            <Form.Item required label={t('集货/暂存位')}>
              <Input
                disabled={!!modalType}
                placeholder={t('必填')}
                name="location" rules={[rules.locationRule()]}
                onChange={(e) => store.changeAddInfoData({
                  location: (e || '').toUpperCase(),
                })}
              />
            </Form.Item>
            <Form.Item required label={t('库位类型')}>
              <Select
                name="locationType"
                data={locationTypeList}
                keygen="dictCode"
                format="dictCode"
                rules={[rules.required(t('请选择库位类型'))]}
                placeholder={t('必填')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={() => {
                  store.clearLocationGroupNoValidate();
                }}
              />
            </Form.Item>
            <Form.Item required={addInfo.enabled !== 2} label={t('库位序号')}>
              <Input
                name="locationOrder" disabled={addInfo.enabled === 2}
                rules={[addInfo.enabled !== 2 && rules.locationOrderRule()]}
              />
            </Form.Item>
            <Form.Item label={t('可用状态')}>
              <Select
                name="enabled"
                data={enabledList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                dictNameZh
                onChange={(value) => {
                  if (value === 2) {
                    store.changeAddInfoData({
                      locationOrder: '',
                    });
                  }
                  store.changeAddInfoData({
                    enabled: value,
                  });
                }}
              />
            </Form.Item>
            <Form.Item required label={t('仓库')}>
              <Select
                name="warehouseId"
                data={currentWarehouseList}
                keygen="id"
                format="id"
                rules={[rules.required(t('请选择仓库'))]}
                placeholder={t('必填')}
                renderItem="nameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.clearLocationGroupNoValidate();
                  store.changeAddInfoData({
                    warehouseId: value,
                    subWarehouseId: '',
                    floor: '',
                    areaId: '',
                  });
                  store.changeData({
                    modalSubWarehouseList: [],
                    floorList: [],
                    areaList: [],
                  });
                  if (value) {
                    store.getModalSubWarehouse({ warehouseId: value });
                  }
                }}
              />
            </Form.Item>
            <Form.Item required label={t('子仓')}>
              <Select
                name="subWarehouseId"
                data={modalSubWarehouseList}
                keygen="id"
                format="id"
                rules={[rules.required(t('请选择子仓'))]}
                placeholder={t('必填')}
                renderItem="nameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.clearLocationGroupNoValidate();
                  store.changeAddInfoData({
                    subWarehouseId: value,
                    floor: '',
                    areaId: '',
                  });
                  store.changeData({
                    floorList: [],
                    areaList: [],
                  });
                  if (value) {
                    store.getFloorArr({ subWarehouseId: value });
                    store.getAreaId({ subWarehouseId: value });
                  }
                }}
              />
            </Form.Item>
            <Form.Item required={(addInfo.locationType === 7 || addInfo.locationType === 3) && !addInfo.floor} label={t('楼层')}>
              <Select
                name="floor"
                data={floorList}
                keygen="floor"
                format="floor"
                rules={[rules.floorRule()]}
                placeholder={t('请选择')}
                renderItem="floor"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.floor || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.floor.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.getAreaId({ subWarehouseId: addInfo.subWarehouseId, floor: value });
                  store.changeAddInfoData({
                    floor: value,
                  });
                  store.changeData({
                    areaList: [],
                  });
                }}
                clearable
              />
            </Form.Item>
            <Form.Item
              required={addInfo.locationType === 7 || addInfo.locationType === 3 || addInfo.locationType === 10}
              label={t('库区')}
            >
              <Select
                name="areaId"
                data={areaList}
                keygen="id"
                format="id"
                rules={[rules.AreaRule()]}
                placeholder={t('请选择')}
                renderItem="area"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.area || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.getFloorArr({ subWarehouseId: addInfo.subWarehouseId, areaId: value });
                  store.changeAddInfoData({
                    areaId: value,
                  });
                  store.changeData({
                    floorList: [],
                  });
                }}
                clearable
              />
            </Form.Item>
            <Form.Item label={t('国家线')}>
              <Select
                name="nationalLineType"
                data={nationalLineList}
                keygen={(r) => `${r.dictCode}${r.dictNameZh}`}
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择状态'))]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item label={t('子分区')} required={isRequiredChildArea()}>
              <Input.Number
                name="childArea"
                maxLength={2}
                max={99}
                allowNull
                hideArrow
                rules={isRequiredChildArea() ? [rules.required(t('请输入子分区'))] : []}
              />
            </Form.Item>
            <Form.Item
              required={Number(addInfo.locationType) === 23}
              label={t('库位组号')}
            >
              <Input
                disabled={!!modalType}
                placeholder={t('请输入')}
                maxLength={40}
                name="locationGroupNo"
                rules={[rules.locationGroupNo()]}
                onChange={(e) => {
                  store.changeData({
                    isCheckLocationGroupNo: {
                      isPass: true,
                      msg: '',
                    },
                  });
                  store.changeAddInfoData({
                    locationGroupNo: e.toUpperCase().trim(),
                  });
                }}
                clearable
              />
            </Form.Item>
            <Form.Item label={t('存放最大箱数')}>
              <Input.Number
                placeholder={t('请输入存放最大箱数')}
                name="maxBoxNum"
                allowNull
                hideArrow
                digits={0}
                min={0}
                maxLength={4}
                max={9999}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  addModal: PropTypes.bool,
  modalType: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  addInfo: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  modalSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  areaList: PropTypes.arrayOf(PropTypes.shape()),
  nationalLineList: PropTypes.arrayOf(PropTypes.shape()),
  floorList: PropTypes.arrayOf(PropTypes.shape()),
  locationTypeList: PropTypes.arrayOf(PropTypes.shape()),
  exportVisible: PropTypes.bool,
  modalLoading: PropTypes.number,
  importModalVisible: PropTypes.bool,
  file: PropTypes.shape(),
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  fpSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  normalExceptionMatchGoodsSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  isCheckLocationGroupNo: PropTypes.shape(),
};
export default Handle;
