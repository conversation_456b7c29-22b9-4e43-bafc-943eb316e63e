import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';
import { camel2Under } from '@src/lib/camal-case-convertor';

export const downloadTemplate = () => {
  const uri = `${process.env.BASE_URI_WMD}/collection_location/download_template`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under()),
  });
};

export const addGoods = (param) => sendPostRequest({
  url: '/collection_location/add',
  param,
}, process.env.BASE_URI_WMD);

export const editGoods = (param) => sendPostRequest({
  url: '/collection_location/modify',
  param,
}, process.env.BASE_URI_WMD);

export const exportFile = (param) => sendPostRequest({
  url: '/collection_location/export',
  param,
}, process.env.BASE_URI_WMD);

export const getGoodsGatherById = (id) => sendPostRequest({
  url: `/collection_location/query?id=${id}`,
}, process.env.BASE_URI_WMD);

export const getGoodsGatherList = (param) => sendPostRequest({
  url: '/collection_location/list',
  param,
}, process.env.BASE_URI_WMD);

export const printAip = (param) => sendPostRequest({
  url: '/collection_location/print_location',
  param,
}, process.env.BASE_URI_WMD);

// 导入接口
export const collectionLocationImportURL = `${process.env.WGS_FRONT}/file_import/record/wmd/collection_location_import`;
