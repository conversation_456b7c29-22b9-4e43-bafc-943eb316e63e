import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Message, Modal } from 'shineout';
import {
  dictCatQuery, dictSelect, getWarehouseApi,
} from '@src/server/basic/dictionary';
import { editAddress, offSubWarehouse } from '@src/server/basic/sub-warehouse';
// import { getConfigByCodeApi } from '@src/server/common/common';
import { formdataPost } from '@src/server/common/fileFetch';
import { getSize } from '@src/middlewares/pagesize';
import {
  getFloorSelectList,
  getAreaSelectList,
  getSubWarehouseSelectList,
  getSystemConfig,
} from '@src/server/common/cache-api';
import { selectArea } from '@src/server/basic/area';
import { queryParkList } from '@src/lib/dealFunc';
import assign from 'object-assign';
import { paramTrim } from '@src/lib/deal-func';
import { transformToPdfUrl, textToBarcode } from '@src/lib/print-new';
import { warehouseIdLocalStorage } from '@src/lib/storage-new';
import { STATISTICAL_DOWNLOAD, STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import fileSaver from 'file-saver';
import tpl from './ejs/print.ejs';
import {
  addGoods, editGoods, getGoodsGatherById, getGoodsGatherList, printAip, exportFile, collectionLocationImportURL,
  downloadTemplate,
} from './server';

const delZero = (list) => {
  if (list.popCode && list.popCode.length === 0) {
    delete list.popCode;
  }
};
// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  location: '',
  locationType: '',
  enabled: 1,
  usableStatus: '',
  warehouseIds: [+warehouseIdLocalStorage.getItem()],
  subWarehouseIds: [],
  printNum: '',
  nationalLineTypeList: [],
  areaIds: [], // 库区
  floor: '', // 楼层
  startTime: undefined,
  endTime: undefined,
  parkTypeList: [],
  childArea: '',
  locationGroupNo: '',
};

const defaultState = {
  subWarehouseList: [], // list界面子仓数据
  modalSubWarehouseList: [], // 弹窗界面子仓数据
  areaList: [],
  modelVisible: false,
  warehouseIds: '', // 保留右上角仓库，由于init时判断是否要重置子仓等
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(1),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100, 1000], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  enabledList: [],
  warehouseList: [],
  isProduceList: [],
  recordList: [],
  parkTypeList: [], // 园区
  headerFormAreaList: [], // 库区
  addModal: false, // 新增Modal
  modalType: '', // 弹框类型 0:新增 1:编辑
  selectedRows: [], // 选中的表格列
  addInfo: {
    location: '',
    locationOrder: '',
    subWarehouseId: '',
    warehouseId: '',
    locationType: '',
    floor: '', // 楼层id
    areaId: '', // 库区id
    enabled: 1,
    nationalLineType: 0, // 默认无国家线
    childArea: '',
    locationGroupNo: '',
    maxBoxNum: null, // 最大存放箱数
  },
  exportVisible: true,
  usableStatusList: [],
  locationTypeList: [],
  itemList: [],
  nationalLineList: [],
  parkList: [],
  preSubWarehouseList: [],
  modalLoading: 1,
  importModalVisible: false, // 导入弹出框是否显示 默认不显示
  file: '', // 导入文件对象
  currentWarehouseList: [], // 权限仓库列表
  fpSubWarehouseList: [],
  normalExceptionMatchGoodsSubWarehouseList: [],
  isCheckLocationGroupNo: {
    isPass: true,
    msg: '',
  },
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改Modal里即addInfo属性值
  changeAddInfoData(state, data) {
    Object.assign(state.addInfo, data);
  },
  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { subWarehouseList, warehouseId } = data;
    // 获取园区列表
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      parkList,
      preSubWarehouseList: subWarehouseList,
      subWarehouseList,
      warehouseIds: warehouseId,
      headerFormAreaList: [],
      limit: {
        ...this.state.limit,
        subWarehouseIds: [],
        parkTypeList: [],
        areaIds: [],
      },
    });
  },
  * getModalSubWarehouse(param) {
    const { warehouseId } = param;
    const list = yield getSubWarehouseSelectList({ warehouseId, enabled: 1 });
    yield this.changeData({
      modalSubWarehouseList: list,
    });
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const { currentWarehouseList } = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
    });
    const [data, warehouseList] = yield Promise.all([
      dictSelect({ catCode: ['ENABLED', 'COLLECTION_LOCATION_USABLE_STATUS', 'COLLECTION_LOCATION_TYPE', 'NATIONAL_LINE_TYPE'] }),
      getWarehouseApi({ enabled: 1 }),
      dictCatQuery({ pageNum: 1, pageSize: 50 }),
    ]);
    if (data.code === '0' && warehouseList.code === '0') {
      yield this.changeData({
        warehouseList: warehouseList.info.data,
        enabledList: data.info.data.find((x) => x.catCode === 'ENABLED').dictListRsps,
        usableStatusList: data.info.data.find((x) => x.catCode === 'COLLECTION_LOCATION_USABLE_STATUS').dictListRsps,
        locationTypeList: data.info.data.find((x) => x.catCode === 'COLLECTION_LOCATION_TYPE').dictListRsps,
        nationalLineList: data.info.data.find((item) => item.catCode === 'NATIONAL_LINE_TYPE').dictListRsps,
      });
    } else {
      Modal.error({ title: data.msg });
    }

    // 获取子仓列表
    const { warehouseIds } = yield this.state;
    const { subWarehouseList, warehouseId } = yield 'nav';

    // 获取园区列表
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      parkList,
      preSubWarehouseList: subWarehouseList,
    });
    if (warehouseIds !== warehouseId) {
      yield this.warehouseChange({ subWarehouseList, warehouseId });
    }

    // const [fpSubWarehouseRes, normalExceptionMatchGoodsSubWarehouseRes] = yield Promise.all([
    //   yield getConfigByCodeApi({ param: 'FP_WELLEN_ISSUE_SUB_WAREHOUSE' }),
    //   yield getConfigByCodeApi({ param: 'NORMAL_EXCEPTION_MATCH_GOODS_SUB_WAREHOUSE' }),
    // ]);
    // if (fpSubWarehouseRes.code === '0' && normalExceptionMatchGoodsSubWarehouseRes.code === '0') {
    //   try {
    //     yield this.changeData({
    //       fpSubWarehouseList: fpSubWarehouseRes.info.configValue.split(',').map((i) => +i),
    //       normalExceptionMatchGoodsSubWarehouseList: normalExceptionMatchGoodsSubWarehouseRes.info.configValue.split(',').map((i) => +i) || [],
    //     });
    //   } catch (error) {
    //     console.log(error);
    //   }
    // } else {
    //   handleListMsg([fpSubWarehouseRes, normalExceptionMatchGoodsSubWarehouseRes]);
    // }
    const systemConfigObj = yield getSystemConfig([
      'FP_WELLEN_ISSUE_SUB_WAREHOUSE',
      'NORMAL_EXCEPTION_MATCH_GOODS_SUB_WAREHOUSE',
    ]);
    try {
      yield this.changeData({
        fpSubWarehouseList: systemConfigObj.FP_WELLEN_ISSUE_SUB_WAREHOUSE?.split(',').map((i) => +i),
        normalExceptionMatchGoodsSubWarehouseList: systemConfigObj.NORMAL_EXCEPTION_MATCH_GOODS_SUB_WAREHOUSE?.split(',').map((i) => +i) || [],
      });
    } catch (error) {
      console.error(error);
    }
  },
  // 搜索
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    const warehouseIds = warehouseId ? [warehouseId] : [];
    const { limit, pageInfo } = this.state;
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      areaIds: typeof limit.areaIds === 'number' ? [limit.areaIds] : limit.areaIds,
    };
    delZero(param);
    markStatus('loading');
    const data = yield getGoodsGatherList({ ...param, warehouseIds });
    if (data.code === '0') {
      yield this.changeData({
        selectedRows: [],
        list: data.info.data,
        exportVisible: false,
        pageInfo: {
          ...this.state.pageInfo,
          count: data.info.meta.count,
        },
      });
    } else {
      Modal.error({ title: data.msg });
    }
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = this.state;
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  * updateAddress(obj) {
    const {
      detailAddress,
      provinceId,
      cityId,
      districtId,
    } = obj;
    if (!(detailAddress && provinceId && cityId && districtId)) {
      Modal.error({ title: t('省市区以及详细地址必填') });
      return;
    }
    const res = yield editAddress(obj);
    if (res.code === '0') {
      Message.success(t('更新成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
    yield this.changeData({
      editAddressModal: false,
    });
  },

  // 注销
  * off(ids) {
    const res = yield offSubWarehouse({ ids });
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },

  * editOrAddShow(param) {
    const { modalType, id } = param;
    yield this.changeData({
      modalType,
    });
    yield this.changeData({
      addModal: true,
    });
    if (modalType) {
      // 回填数据
      const [data] = yield Promise.all([
        getGoodsGatherById(String(id)),
      ]);
      if (data.code === '0') {
        if (data.info.areaId === -1) {
          data.info.areaId = ''; // 后端库区id返回-1代表为空
        }

        yield this.changeAddInfoData({
          ...data.info,
          childArea: data.info?.childArea === 0 ? '' : data.info?.childArea,
        });
        yield this.getAreaAndFloor(data);
        const params = {
          warehouseId: data.info.warehouseId,
        };
        // 请求子仓列表
        yield this.getModalSubWarehouse(params);
      } else {
        Modal.error({ title: data.msg });
      }
    }
  },
  * getAreaAndFloor(data) {
    const floorList = yield getFloorSelectList({ subWarehouseId: data.info.subWarehouseId, areaId: data.info.areaId });
    yield this.changeData({
      floorList,
    });
    const areaList = yield getAreaSelectList({ subWarehouseId: data.info.subWarehouseId, floor: data.info.floor });
    yield this.changeData({
      areaList,
    });
  },
  * clearLocationGroupNoValidate() {
    const { modalFormRef } = this.state;
    yield this.changeData({
      isCheckLocationGroupNo: {
        msg: '',
        isPass: true,
      },
    });
    if (modalFormRef && modalFormRef.clearValidate) modalFormRef.clearValidate();
  },

  // 点击保存 - 即新增
  * commitData() {
    const { modalType, modalFormRef } = this.state;

    yield this.changeData({
      isCheckLocationGroupNo: {
        msg: '',
        isPass: true,
      },
    });
    if (modalFormRef && modalFormRef.clearValidate) modalFormRef.clearValidate();

    // 数据处理
    // delZero(obj);
    const postObj = !modalType ? assign({}, this.state.addInfo, {
      locationOrder: Number(this.state.addInfo.locationOrder),
    }) : assign({}, this.state.addInfo);
    const params = paramTrim(postObj);
    params.nationalLineType = this.state.addInfo.nationalLineType;
    let res = '';
    markStatus('modalLoading');
    if (!modalType) {
      res = yield addGoods(params);
    } else {
      res = yield editGoods(params);
    }
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
      yield this.closeModal();
    } else if (res.code === '482180') {
      yield this.changeData({
        isCheckLocationGroupNo: {
          msg: res.errMsg,
          isPass: false,
        },
      });
      if (modalFormRef && modalFormRef.validateFields) modalFormRef.validateFields('locationGroupNo');
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * closeModal() {
    const { modalFormRef } = this.state;
    yield this.changeData({
      addModal: false,
      addInfo: {
        location: '',
        locationOrder: '',
        subWarehouseId: '',
        warehouseId: '',
        locationType: '',
        floor: '', // 楼层id
        areaId: '', // 库区id
        enabled: 1,
        nationalLineType: 0, // 默认无国家线
        childArea: '',
        locationGroupNo: '',
      },
      modalSubWarehouseList: [],
      floorList: [],
      areaList: [],
    });
    if (modalFormRef && modalFormRef.clearValidate) modalFormRef.clearValidate();
  },
  // 导出
  * exportData() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    const warehouseIds = warehouseId ? [warehouseId] : [];
    const { limit } = this.state;
    markStatus('loading');
    const data = yield exportFile({ ...limit, warehouseIds });
    if (data.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: data.msg });
    }
  },
  // 打印
  * print() {
    const { selectedRows } = this.state;
    const locationIds = selectedRows.map((v) => v.id);
    const res = yield printAip({ locationIds });
    if (res.code === '0') {
      yield this.changeData({
        selectedRows: [],
      });

      const list = res.info.data.map((v) => ({ code: v.location, barcode: textToBarcode(v.location, { width: 2, height: 100 }) }));
      transformToPdfUrl(tpl({ list }), 50, 80, { landscape: 1 }, 1);
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 获取楼层下拉数据
  * getFloorArr(param) {
    const data = yield getFloorSelectList(param);
    yield this.changeData({
      floorList: data,
    });
  },
  // 获取库区下拉数据
  * getAreaId(param) {
    const list = yield getAreaSelectList(param);
    yield this.changeData({
      areaList: list,
    });
  },
  * uploadFile(formData) {
    markStatus('loading');
    formData.append('function_node', '20');
    const res = yield formdataPost(collectionLocationImportURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 获取库区
  * getHeaderFormAreaList({ subWarehouseIds }) {
    if (subWarehouseIds.length === 0) {
      yield this.changeData({
        headerFormAreaList: [],
      });
      return;
    }

    markStatus('loading');
    const { code, info, msg } = yield selectArea({
      subWarehouseIdList: subWarehouseIds,
      enabled: 1,
    });
    if (code === '0') {
      yield this.changeData({
        headerFormAreaList: info.data || [],
      });
      return;
    }
    Modal.error({ title: msg });
    yield this.changeData({
      headerFormAreaList: [],
    });
  },
  // 下载模板
  * downloadTemplate() {
    try {
      markStatus('loading');
      const res = yield downloadTemplate();
      const b = yield res.blob();
      const blob = new Blob([b], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('模版下载')}.xlsx`);
    } catch (e) {
      Modal.error({ title: e.reason.message });
    }
  },
};
