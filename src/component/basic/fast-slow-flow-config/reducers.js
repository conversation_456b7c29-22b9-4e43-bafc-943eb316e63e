import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import fileSaver from 'file-saver';
import { formdataPost } from '@src/server/common/fileFetch';
import {
  getListAPI, exportListAPI, saveAPI, downloadTemplateAPI, uploadFileURL, batchAbleAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  fastSlowFlowTagType: '', // 快慢流分级维度
};

export const defaultAddObj = {
  fastSlowFlowTagType: '', // 快慢流分级维度
  nationalLineType: '', // 包裹国家线
  isAble: 1, // 是否启用
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域

  limit: defaultLimit,
  /** 配置类型，1-历史销量标签组合 2-快流个数上限 3-算法预测销量(旧)，4-算法预测销量（新） */
  configTypeList: [],
  // 1-是 0-否
  isAbleList: [],
  exceptionTypeList: [],
  operatorType: 0, // 0关闭 1新增 2编辑
  addEditObj: defaultAddObj,
  selectedRows: [],
  canBatchOperateAble: false, // 是否可操作批量启用/禁用按钮
  sortCodeList: [], // 快慢流分级维度下拉
  nationalLineTypeList: [], // 国家线下拉
  processFlowData: [], // 详情数据
  detailModalVisiable: 0, // 详情弹框
  file: '',
  showImportCenter: false,
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const selectData = yield dictSelect({ catCode: ['FAST_SLOW_FLOW_TAG_TYPE', 'NATIONAL_LINE_TYPE', 'FAST_SLOW_FLOW_CONFIG_IS_ABLE'] });
    if (selectData.code === '0') {
      yield this.changeData({
        nationalLineTypeList: selectData.info.data.find((x) => x.catCode === 'NATIONAL_LINE_TYPE').dictListRsps,
        configTypeList: selectData.info.data.find((item) => item.catCode === 'FAST_SLOW_FLOW_TAG_TYPE').dictListRsps,
        isAbleList: selectData.info.data.find((x) => x.catCode === 'FAST_SLOW_FLOW_CONFIG_IS_ABLE').dictListRsps,
      });
    } else {
      Modal.error({ title: selectData.msg });
    }
  },
  // 搜索
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        selectedRows: [],
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
      yield this.changeLimitData({
        fastSlowFlowTagType: (info.data || [])[0]?.fastSlowFlowTagType || limit.fastSlowFlowTagType,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 导出
  * exportData() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    // 校验通过后执行
    const { limit, pageInfo } = yield '';
    // 一般导出接口不用传页数页码，因为是要导出全部数据：部分旧页面有传就与旧页面一致；新页面跟后端统一按规范不用传页数页码
    // 实际开发时，根据情况决定是否加仓库id，页数页码等字段
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
    };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(param);
    if (code === '0') {
      window.open('#/statistical/download');
    } else {
      Modal.error({ title: msg });
    }
  },
  // 批量操作启用/禁用
  * batchOperateStatus() {
    const { selectedRows } = yield '';
    const ids = selectedRows.map((item) => item.id);
    const { code, msg } = yield batchAbleAPI({ ids });
    if (code === '0') {
      Message.success(selectedRows[0]?.isAble === 1 ? t('批量禁用成功') : t('批量启用成功'));
      yield this.changeData({
        canBatchOperateAble: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 快慢流分级配置-新增或编辑
  * addOrEdit() {
    markStatus('loading');
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { addEditObj, file, operatorType } = yield '';
    // 新增 历史标签配置  校验导入文件
    if (addEditObj.fastSlowFlowTagType === 1 && file.length === 0 && operatorType === 1) {
      Modal.error({ title: t('请导入文件') });
      return;
    }
    const { code, msg, info } = yield saveAPI({ ...addEditObj, warehouseId });
    if (code === '0') {
      Message.success(operatorType === 1 ? t('新增成功') : t('编辑成功'));
      if (addEditObj.fastSlowFlowTagType === 1 && file.length > 0) {
        const formData = new FormData();
        formData.append('file', file[0]);
        yield this.uploadFile({ formData, id: info.id });
      } else {
        yield this.changeData({
          operatorType: 0,
        });
      }
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  // 下载模板
  * downloadTemplate() {
    const res = yield downloadTemplateAPI();
    const b = yield res.blob();
    const blob = new Blob([b], { type: 'application/octet-stream' });
    fileSaver.saveAs(blob, `${t('导入模板')}.xlsx`);
  },
  /**
   * 导入
   */
  * uploadFile({ formData, id }) {
    markStatus('loading');
    formData.append('function_node', '89');
    formData.append('request_json', JSON.stringify({ fast_slow_sub_config_id: id }));
    const res = yield formdataPost(uploadFileURL, formData);
    if (res.code === '0') {
      yield this.changeData({
        showImportCenter: true,
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
