import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';

// 导出
export const exportListAPI = (param) => sendPostRequest({
  url: '/fast_slow_flow_sub_level_config/export',
  param,
}, process.env.WWS_URI);

// 获取列表数据
export const getListAPI = (param) => sendPostRequest({
  url: '/fast_slow_flow_sub_level_config/query',
  param,
}, process.env.WWS_URI);

// 快慢流分级配置-下载模板
export const downloadTemplateAPI = () => {
  const uri = `${process.env.WWS_URI}/fast_slow_flow_sub_level_config/download_template`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
  });
};

// 快慢流分级配置-新增或编辑
export const saveAPI = (param) => sendPostRequest({
  url: '/fast_slow_flow_sub_level_config/edit',
  param,
}, process.env.WWS_URI);

// 批量启用/禁用
export const batchAbleAPI = (param) => sendPostRequest({
  url: '/fast_slow_flow_sub_level_config/batch_able',
  param,
}, process.env.WWS_URI);

export const uploadFileURL = `${process.env.WGS_FRONT}/file_import/record/wws/fast_slow_flow_sub_level_history_actuator`;
