import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Modal } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';
import styles from '../style.less';
// 配置类型，1-历史销量标签组合 2-快流个数上限 3-算法预测销量（旧），4-算法预测（新）
const mapList = new Map([
  [1, t('历史标签配置')],
  [2, t('快流SKU个数上限')],
  [3, t('标签')],
  [4, t('标签')],
]);

class List extends React.Component {
  render() {
    const {
      limit,
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
      processFlowData,
      detailModalVisiable,
    } = this.props;

    let tableLabel = t('标签');
    switch (limit.fastSlowFlowTagType) {
      case 1:
        tableLabel = t('历史标签配置');
        break;
      case 2:
        tableLabel = t('快流SKU个数上限');
        break;
      case 3:
      case 4:
        tableLabel = t('标签');
        break;
      default:
        tableLabel = t('标签');
    }

    const firstRow = processFlowData[0] || {};
    const previewColumns = [
      firstRow.preSixDayTagName && {
        title: t('前6天'),
        render: 'preSixDayTagName',
        width: 100,
      },
      firstRow.preFiveDayTagName && {
        title: t('前5天'),
        width: 100,
        render: 'preFiveDayTagName',
      },
      firstRow.preFourDayTagName && {
        title: t('前4天'),
        render: 'preFourDayTagName',
        width: 100,
      },
      firstRow.preThreeDayTagName && {
        title: t('前3天'),
        width: 100,
        render: 'preThreeDayTagName',
      },
      firstRow.preTwoDayTagName && {
        title: t('前2天'),
        render: 'preTwoDayTagName',
        width: 100,
      },
      firstRow.preOneDayTagName && {
        title: t('前1天'),
        width: 100,
        render: 'preOneDayTagName',
      },
      {
        title: t('算法当日预测'),
        render: 'calcTagName',
        width: 140,
      },
      {
        title: t('业务配置结果'),
        render: 'bizTagName',
        width: 140,
      },
    ].filter((e) => !!e);
    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 150,
      },
      {
        title: t('国家线'),
        width: 180,
        render: 'nationalLineTypeName',
      },
      {
        title: t('状态'),
        render: 'isAbleName',
        width: 120,
      },
      {
        title: (row) => mapList.get(row[0]?.fastSlowFlowTagType || 3),
        keygen: tableLabel,
        width: 150,
        render: (row) => {
          let showValueEle = '';
          switch (row.fastSlowFlowTagType) {
            // 历史
            case 1:
              // eslint-disable-next-line no-case-declarations
              const configHistoryList = row.configHistoryList || [];
              // eslint-disable-next-line no-case-declarations
              const bizTagNameList = configHistoryList.map((e) => e.bizTagName || t('空'));
              showValueEle = [...new Set(bizTagNameList)].map((e, index) => (
                <div key={JSON.stringify(index)}>
                  {e}
                </div>
              ));
              break;
              // 快流SKU个数上限
            case 2:
              showValueEle = row.fastSkuThreshold;
              break;
              // 算法预测
            case 3:
            case 4:
              showValueEle = row.fastSlowFlowTagTypeName;
              break;
            default:
              break;
          }
          return showValueEle;
        },
      },
      {
        title: t('更新时间'),
        width: 180,
        render: 'lastUpdateTime',
      },
      {
        title: t('更新人'),
        width: 180,
        render: 'updateUserName',
      },
      {
        title: t('操作'),
        width: 120,
        render: (record) => (
          <>
            {
           record.fastSlowFlowTagType === 1 && (
           <Button
             size="small"
             text
             type="primary"
             className={styles.recordButton}
             onClick={() => store.changeData({ detailModalVisiable: 1, processFlowData: record?.configHistoryList || [] })}
           >
             {t('查看详情')}
           </Button>
           )
          }
            <Button
              size="small"
              text
              type="primary"
              className={styles.recordButton}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
            onRowSelect={(rows) => {
              // 勾选数据为同一种状态时可交互批量启用/禁用按钮
              const canBatchOperateAble = rows.every((item) => item.isAble === rows[0].isAble);
              store.changeData({ selectedRows: rows, canBatchOperateAble });
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'FAST_SLOW_FLOW_SUB_LEVEL_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
        <Modal
          destroy
          visible={detailModalVisiable}
          maskCloseAble={false}
          title={t('历史销量标签组合-详情')}
          width={800}
          onClose={() => store.changeData({ detailModalVisiable: 0 })}
          footer={(
            <div>
              <Button key="cancel" onClick={() => store.changeData({ detailModalVisiable: 0 })}>{t('确认')}</Button>
            </div>
              )}
        >
          <Table
            keygen="id"
            bordered
            style={{ maxHeight: 300 }}
            empty={t('暂无数据')}
            columns={previewColumns}
            data={processFlowData}
            pagination={false}
          />
        </Modal>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  processFlowData: PropTypes.arrayOf(PropTypes.shape()),
  detailModalVisiable: PropTypes.arrayOf(PropTypes.shape()),
  limit: PropTypes.shape(),
};

export default List;
