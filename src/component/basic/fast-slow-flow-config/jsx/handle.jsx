import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Modal, Select, Rule, Input, Message,
} from 'shineout';
import { uploadFileURL } from '@src/server/basic/upload';
import UploadPlus from '@shein-components/upload_plus';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import store, { defaultAddObj } from '../reducers';
import style from '../style.less';

const rule = Rule();

class Handle extends React.Component {
  render() {
    const {
      loading,
      selectedRows,
      canBatchOperateAble,
      operatorType,
      addEditObj,
      nationalLineTypeList,
      configTypeList,
      isAbleList,
      file,
      showImportCenter,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          onClick={() => store.changeData({
            operatorType: 1,
            addEditObj: defaultAddObj,
            showImportCenter: false,
            file: '',
          })}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={selectedRows.length !== 1 || !loading}
          onClick={() => store.changeData({
            operatorType: 2,
            addEditObj: selectedRows[0],
            showImportCenter: false,
            file: '',
          })}
        >
          {t('编辑')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.exportData();
          }}
        >
          {t('导出')}
        </Button>
        <Button
          type="primary"
          disabled={!selectedRows.length || !canBatchOperateAble || !loading}
          onClick={() => {
            store.batchOperateStatus();
          }}
        >
          {t('批量启用/禁用')}
        </Button>
        <Modal
          destroy
          visible={operatorType}
          bodyStyle={{ maxHeight: 550, overflow: 'auto' }}
          width={800}
          maskCloseAble={false}
          title={operatorType === 1 ? t('快慢流分级模式配置-新增') : t('快慢流分级模式配置-编辑')}
          onClose={() => store.changeData({ operatorType: 0 })}
          footer={(
            <div>
              <Button key="cancel" onClick={() => store.changeData({ operatorType: 0 })}>{t('取消')}</Button>
              <Modal.Submit
                key="confirm"
                disabled={!loading}
              >
                {t('确认')}
              </Modal.Submit>
            </div>
              )}
        >
          <Form
            labelWidth={130}
            labelAlign="right"
            value={addEditObj}
            onSubmit={() => {
              store.addOrEdit();
            }}
            onChange={(value) => {
              store.changeData({
                addEditObj: value,
              });
            }}
            inline
            formRef={(f) => store.changeData({ addOrEditFormRef: f })}
          >
            <Form.Item required label={t('包裹国家线')}>
              <Select
                name="nationalLineType"
                keygen="dictCode"
                format="dictCode"
                renderItem={(w) => w.dictNameZh}
                data={nationalLineTypeList}
                onFilter={(text) => (d) => d.dictNameZh.indexOf(text) >= 0}
                clearable
                width={200}
                absolute
                compressed
                placeholder={t('请选择')}
                rules={[rule.required(t('请选择包裹国家线'))]}
              />
            </Form.Item>
            <h3>{t('快慢流分级')}</h3>
            <Form.Item required label={t('快慢流分级维度')}>
              <Select
                name="fastSlowFlowTagType"
                data={configTypeList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                compressed
                clearable
                absolute
                width={200}
                placeholder={t('请选择')}
                rules={[rule.required(t('请选择快慢流分级维度'))]}
                onChange={(val) => {
                  store.changeData({
                    addEditObj: {
                      ...addEditObj,
                      fastSlowFlowTagType: val,
                      file: '',
                      fastSkuThreshold: '',
                    },
                  });
                }}
              />
            </Form.Item>
            <Form.Item required label={t('状态')}>
              <Select
                name="isAble"
                data={isAbleList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                compressed
                clearable
                absolute
                placeholder={t('请选择')}
                rules={[rule.required(t('请选择状态'))]}
                width={200}
              />
            </Form.Item>
            {
             [2, 4].includes(addEditObj.fastSlowFlowTagType)
             && (
             <Form.Item required label={t('快流SKU个数上限')}>
               <Input.Number
                 width={200}
                 name="fastSkuThreshold"
                 digits={0}
                 type="number"
                 min={1}
                 delay={0}
                 allowNull
                 rules={[rule.required(t('请填写'))]}
                 onChange={(val) => {
                   if (val <= 10000000 || val === '') {
                     store.changeData({
                       addEditObj: {
                         ...addEditObj,
                         fastSkuThreshold: val,
                       },
                     });
                   } else {
                     store.changeData({
                       addEditObj: {
                         ...addEditObj,
                         fastSkuThreshold: addEditObj.fastSkuThreshold,
                       },
                     });
                   }
                 }}
               />
             </Form.Item>
             )
           }
            {
             addEditObj.fastSlowFlowTagType === 1 && (
               <div className={style.importBtnDiv}>
                 <Button
                   size="default"
                   onClick={() => store.downloadTemplate()}
                 >
                   <Icon name="download" style={{ marginRight: 6 }} />
                   {t('下载导入模板')}
                 </Button>
                 <div className={style.importBtn}>
                   <UploadPlus
                     accept=".xls,.xlsx"
                     action={uploadFileURL}
                     autoUpload={false}
                     fileList={file || []}
                     maxSize={5}
                     autoUploadKeyName="file"
                     filePathKeyName="imageUrl"
                     onFailUpload={async (_, info) => Message.error(info)}
                     onDelete={async (removeItem) => {
                       const newFiles = file.filter((v) => v.fileUrl !== removeItem.fileUrl);
                       store.changeData({
                         file: newFiles,
                       });
                     }}
                     onChange={(f) => {
                       store.changeData({
                         file: f,
                       });
                     }}
                   />
                 </div>
               </div>
             )
           }
            {
             showImportCenter && (
               <div className={style.importCenter} onClick={() => window.open(STATISTICAL_IMPORT_CENTER)}>{t('查看本次导入详情')}</div>
             )
           }
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  canBatchOperateAble: PropTypes.bool,
  operatorType: PropTypes.number,
  addEditObj: PropTypes.shape(),
  nationalLineTypeList: PropTypes.arrayOf(PropTypes.shape()),
  configTypeList: PropTypes.arrayOf(PropTypes.shape()),
  isAbleList: PropTypes.arrayOf(PropTypes.shape()),
  file: PropTypes.shape(),
  showImportCenter: PropTypes.bool,
};
export default Handle;
