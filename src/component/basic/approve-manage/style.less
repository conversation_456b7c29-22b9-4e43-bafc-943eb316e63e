/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin-top: 12px;
    padding-top: 12px;
    background-color: #fff;
}

.handleExportButton{
    margin-right: 6px;
}

.handleModalTitle {
    border-bottom: 1px dashed #333;
    padding-bottom: 5px;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: bold;
}

.handleModalItem {
    flex: 1;
    display: flex;
    margin-bottom: 5px;
    .titleWidth{
        width:283px
    }
}

/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect{
    width: 140px;
 }

 .headerUnmatchedText{
     color: #bbb;
 }

.headerSearchTimeLine{
    display: flex;
}

/* list.jsx
---------------------------------------------------------------- */
.listOperationButton{
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}
.listSection{
    height: 0px; /* 必写为了子domheight - 100%有效 */
    flex: 1;
}
.listOperationLink{
    font-size: 14px;
    cursor: pointer;
    color: var(--primary-color, #1890ff)
}

.newTitleWidth{
    min-width:70px
}

.handlePre{
    white-space: pre-wrap;
    word-break: break-word;
    margin: 0;
}