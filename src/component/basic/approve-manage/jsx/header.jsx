import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import {
  Input, Select, Rule,
} from 'shineout';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import FilterSearchSelect from '@src/component/public-component/select/filter-search-select';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';
import styles from '../style.less';

const rule = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      // 当审批单，发起人，审批人，任意一个控件有值，允许时间清空
      if (formData.approvalDocumentCode || formData.creator || formData.currentApprovalUser) {
        return callback(true);
      }

      if (!formData.startTime || !formData.endTime) {
        callback(new Error(t('开始时间或结束时间必选')));
      }
      // 时间范围不能超过一个月
      if (moment(formData.endTime)
        .diff(moment(formData.startTime), 'days', true) > 30) {
        callback(new Error(t('时间范围不能超过{}天', 30)));
      }
      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      approvalTypeList,
      relateFunctionList,
      currentApprovalStatusList,
      parkList,
      subWarehouseList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('审批单'), t('发起人'), t('审批人'), t('发起时间')]}
        >
          <Input
            label={t('审批单')}
            name="approvalDocumentCode"
            required
            placeholder={t('请输入')}
            clearable
          />
          <FilterSearchSelect
            label={t('发起人')}
            name="creator"
            required
            placeholder={t('请输入')}
            clearable
          />
          <Select
            label={t('审批类型')}
            name="approvalType"
            data={approvalTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('关联功能')}
            name="relateFunction"
            data={relateFunctionList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <FilterSearchSelect
            label={t('审批人')}
            name="currentApprovalUser"
            required
            placeholder={t('请输入')}
            clearable
          />
          <Select
            label={t('当前状态')}
            name="currentApprovalStatus"
            data={currentApprovalStatusList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('园区')}
            name="parkId"
            data={parkList}
            keygen="parkType"
            format="parkType"
            placeholder={t('请选择')}
            renderItem="parkName"
            clearable
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(val) => {
              if (!['', undefined, null].includes(val)) {
                const { parkSubWarehouseList } = fliterSubwarehouse([val]);
                store.changeData({
                  subWarehouseList: parkSubWarehouseList,
                });
              }
              store.changeLimitData({
                parkId: val,
                subWarehouseIds: [],
              });
            }}
          />
          <Select
            label={t('子仓')}
            name="subWarehouseIds"
            data={subWarehouseList}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
          />
          <DateRangePicker
            label={t('发起时间')}
            name={['startTime', 'endTime']}
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[rule.timeRange()]}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  approvalTypeList: PropTypes.arrayOf(PropTypes.shape()),
  relateFunctionList: PropTypes.arrayOf(PropTypes.shape()),
  currentApprovalStatusList: PropTypes.arrayOf(PropTypes.shape()),
  parkList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
