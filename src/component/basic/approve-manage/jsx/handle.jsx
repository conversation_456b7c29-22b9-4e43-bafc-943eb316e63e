import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Table, Textarea,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '../style.less';
import store, { currentApprovalStatusMap } from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
      previewVisible,
      approveVisible,
      approveObj,
      approveObjAdvice,
      processFlowData,
      hasForceRejectBtn,
    } = this.props;

    const previewColumns = [
      {
        title: t('流程顺序'),
        render: 'approvalOrder',
        width: 120,
      },
      {
        title: t('环节处理人'),
        render: 'approvalUser',
        width: 140,
      },
      {
        title: t('状态'),
        render: 'approvalStatusName',
        width: 120,
      },
    ];

    const approveColumns = [
      {
        title: t('流程顺序'),
        render: 'approvalOrder',
        width: 120,
      },
      {
        title: t('环节处理人'),
        render: 'approvalUser',
        width: 140,
      },
      {
        title: t('状态'),
        render: 'approvalStatusName',
        width: 120,
      },
      {
        title: t('审批意见'),
        render: 'approvalAdvice',
        width: 120,
      },
    ];

    const getButtons = () => {
      const buttons = [];
      if (hasForceRejectBtn) {
        buttons.push(
          <Button
            type="danger"
            disabled={!loading}
            onClick={() => {
              store.approve({
                operateType: '4', // 1,审批完成;2,驳回;3,确认生效; 4,强制驳回
              });
            }}
          >
            {t('强制驳回')}
          </Button>,
        );
      }
      if (approveObj.currentApprovalStatus === currentApprovalStatusMap.get(t('待审批'))) {
        buttons.push(
          <Button
            disabled={!loading}
            onClick={() => {
              store.approve({
                operateType: '2', // 1,审批完成;2,驳回;,3确认生效
              });
            }}
          >
            {t('驳回')}
          </Button>,
        );
        buttons.push(
          <Button
            type="primary"
            disabled={!loading}
            onClick={() => {
              store.approve({
                operateType: '1', // 1,审批完成;2,驳回;,3确认生效
              });
            }}
          >
            {t('审批通过')}
          </Button>,
        );
      }
      if (approveObj.currentApprovalStatus === currentApprovalStatusMap.get(t('待确认')) && approveObj.approvalType !== 2) {
        buttons.push(
          <Button
            type="primary"
            disabled={!loading}
            onClick={() => {
              store.approve({
                operateType: '3', // 1,审批完成;2,驳回;,3确认生效
              });
            }}
          >
            {t('确认生效')}
          </Button>,
        );
      }
      buttons.push(
        <Button
          onClick={() => {
            store.changeData({ approveVisible: false });
          }}
        >
          {t('返回')}
        </Button>,
      );

      return buttons;
    };

    return (
      <section className={styles.handleHandle}>
        <Modal
          title={t('查看流程')}
          visible={previewVisible}
          onClose={() => store.changeData({ previewVisible: false })}
          footer={[
            <Button
              onClick={() => {
                store.changeData({ previewVisible: false });
              }}
            >
              {t('返回')}
            </Button>,
          ]}
        >
          <Table
            keygen="id"
            bordered
            style={{ maxHeight: 300 }}
            empty={t('暂无数据')}
            columns={previewColumns}
            data={processFlowData}
            pagination={false}
          />
        </Modal>
        <Modal
          title={t('审批')}
          visible={approveVisible}
          onClose={() => store.changeData({ approveVisible: false })}
          footer={getButtons()}
          width={700}
        >
          <div>
            <div className={styles.handleModalTitle}>{t('基础信息')}</div>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
            }}
            >
              <div style={{ display: 'flex' }}>
                <div className={styles.handleModalItem}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  <span>
                    {t('审批单号')}
                    ：
                  </span>
                  <span>{approveObj.approvalDocumentCode}</span>
                </div>
                <div className={styles.handleModalItem}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  <span>
                    {t('发起人')}
                    ：
                  </span>
                  <span>{approveObj.creator}</span>
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <div className={styles.handleModalItem}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  <span>
                    {t('审批类型')}
                    ：
                  </span>
                  <span>{approveObj.approvalTypeName}</span>
                </div>
                <div className={styles.handleModalItem}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  <span>
                    {t('关联功能')}
                    ：
                  </span>
                  <span>{approveObj.relateFunctionName}</span>
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <div className={styles.handleModalItem}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  <span>
                    {t('当前状态')}
                    ：
                  </span>
                  <span>{approveObj.currentApprovalStatusName}</span>
                </div>
                <div className={styles.handleModalItem}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  <span>
                    {t('审批人')}
                    ：
                  </span>
                  <span>{approveObj.currentApprovalUser}</span>
                </div>
              </div>
              <div className={styles.handleModalItem}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                <span>
                  {t('发起时间')}
                  ：
                </span>
                <span>{approveObj.createTime}</span>
              </div>
              <div style={{ display: 'flex' }}>
                <div className={styles.handleModalItem}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  <span>
                    {t('园区')}
                    ：
                  </span>
                  <span>{approveObj.parkName}</span>
                </div>
                <div className={styles.handleModalItem}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  <span>
                    {t('子仓')}
                    ：
                  </span>
                  <span>{approveObj.subWarehouseName}</span>
                </div>
              </div>
              <div className={styles.handleModalItem}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                <span className={styles.newTitleWidth}>
                  {t('审批备注')}
                  ：
                </span>
                <pre className={styles.handlePre}>{approveObj.approvalRemark}</pre>
              </div>
              {
                approveObj.currentApprovalStatus === currentApprovalStatusMap.get(t('待审批')) && (
                  <div style={{ display: 'flex' }}>
                    <span style={{ flexShrink: 0, marginLeft: '12px' }}>
                      {t('审批意见')}
                      ：
                    </span>
                    <Textarea
                      rows={6}
                      placeholder={t('请输入')}
                      info={200}
                      value={approveObjAdvice}
                      onChange={(val) => {
                        store.changeData({
                          approveObjAdvice: val,
                        });
                      }}
                    />
                  </div>
                )
              }
            </div>
          </div>
          {
            approveObj.approvalType !== 2 && (
            <div style={{ marginTop: 10 }}>
              <div className={styles.handleModalTitle}>{t('附件')}</div>
              <a
                rel="noopener noreferrer"
                target="new"
                href={approveObj.approvalAppendix}
                download={approveObj.approvalAppendixName}
              >
                {approveObj.approvalAppendixName || t('暂无附件')}
              </a>
            </div>
            )
          }

          <div style={{ marginTop: 10 }}>
            <div className={styles.handleModalTitle}>{t('流程记录')}</div>
            <div>
              <Table
                keygen="id"
                bordered
                style={{ maxHeight: 300 }}
                empty={t('暂无数据')}
                columns={approveColumns}
                data={processFlowData}
                pagination={false}
              />
            </div>
          </div>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  previewVisible: PropTypes.bool,
  processFlowData: PropTypes.shape(),
  approveVisible: PropTypes.bool,
  approveObj: PropTypes.shape(),
  approveObjAdvice: PropTypes.string,
  hasForceRejectBtn: PropTypes.bool,
};
export default Handle;
