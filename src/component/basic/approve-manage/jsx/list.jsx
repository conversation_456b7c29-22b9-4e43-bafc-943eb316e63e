import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyle from '@src/component/style.less';
import styles from '../style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
    } = this.props;

    const columns = [
      {
        title: t('审批单号'),
        width: 120,
        render: (record) => (
          <div
            className={styles.listOperationLink}
            onClick={() => store.openModal({
              approvalDocumentCode: record.approvalDocumentCode,
              type: 'edit',
            })}
          >
            {record.approvalDocumentCode}
          </div>
        ),
      },
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 120,
      },
      {
        title: t('园区'),
        width: 150,
        render: 'parkName',
      },
      {
        title: t('子仓'),
        width: 140,
        render: 'subWarehouseName',
      },
      {
        title: t('发起人'),
        width: 100,
        render: 'creator',
      },
      {
        title: t('审批类型'),
        width: 120,
        render: 'approvalTypeName',
      },
      {
        title: t('关联功能'),
        width: 120,
        render: 'relateFunctionName',
      },
      {
        title: t('当前状态'),
        render: 'currentApprovalStatusName',
        width: 120,
      },
      {
        title: t('审批人'),
        render: 'currentApprovalUser',
        width: 100,
      },
      {
        title: t('流程环节'),
        width: 120,
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            className={styles.listOperationButton}
            onClick={() => store.openModal({
              approvalDocumentCode: record.approvalDocumentCode,
              type: 'preview',
            })}
          >
            {t('查看流程')}
          </Button>
        ),
      },
      {
        title: t('发起时间'),
        render: 'createTime',
        width: 120,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 80,
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            className={styles.listOperationButton}
            onClick={() => store.changeData({
              recordVisible: true,
              recordId: record.id,
            })}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={globalStyle.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              className: globalStyle.pagination,
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'WGS_GENERAL_APPROVAL_DOCUMENT',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
};

export default List;
