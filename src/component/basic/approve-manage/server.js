import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/approval_document/list',
  param,
}, process.env.WGS_FRONT);

/**
 * 查看流程
 */
export const getProcessFlowAPI = (param) => sendPostRequest({
  url: '/approval_document/get_process_flow',
  param,
}, process.env.WGS_FRONT);

/**
 * 审批
 */
export const approveAPI = (param) => sendPostRequest({
  url: '/approval_document/approve',
  param,
}, process.env.WGS_FRONT);

/**
 * 强制驳回审批
 */
export const forceRejectAPI = (param) => sendPostRequest({
  url: '/approval_document/force_reject_approve',
  param,
}, process.env.WGS_FRONT);
