import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import moment from 'moment';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
// import { paramTrim } from '@src/lib/deal-func';
import { handleListMsg, queryParkList } from '@src/lib/dealFunc';
import { userNameSessionStorage } from '@src/lib/storage-new';
import { checkUrlPermissionAPI } from '@src/server/common/common';
import {
  getListAPI, getProcessFlowAPI, approveAPI, forceRejectAPI,
} from './server';

export const currentApprovalStatusMap = new Map([
  [t('待审批'), 10],
  [t('待确认'), 20],
]);

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  approvalDocumentCode: '', // 审批单
  creator: '', // 发起人
  approvalType: '', // 审批类型
  relateFunction: '', // 关联功能
  parkId: '', // 园区
  subWarehouseIds: [], // 子仓
  currentApprovalUser: userNameSessionStorage.getItem() || '', // 审批人 默认：登录人本人
  currentApprovalStatus: [currentApprovalStatusMap.get(t('待审批')), currentApprovalStatusMap.get(t('待确认'))], // 当前状态 默认: 10 待审批，20 待确认
  startTime: moment().subtract(29, 'days').format('YYYY-MM-DD 00:00:00'), // 发起时间 开始时间
  endTime: moment().format('YYYY-MM-DD 23:59:59'), // 发起时间 结束时间
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  approvalTypeList: [], // 审批类型 下拉 GENERAL_APPROVE_TYPE
  relateFunctionList: [], // 关联功能 下拉 APPROVAL_RELATE_FUNCTION
  currentApprovalStatusList: [], // 当前状态 下拉 APPROVAL_STATUS
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  approveVisible: false, // 审批弹窗 是否可见
  approveObj: {}, // 审批弹窗 数据
  approveObjAdvice: '', // 审批弹窗 审批意见
  previewVisible: false, // 查看流程 是否可见
  processFlowData: {}, // 查看流程 数据
  subWarehouseList: [],
  parkList: [],
  hasForceRejectBtn: false, // 是否有强制驳回按钮
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['GENERAL_APPROVE_TYPE', 'APPROVAL_RELATE_FUNCTION', 'APPROVAL_STATUS'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        approvalTypeList: selectData.info.data.find((item) => item.catCode === 'GENERAL_APPROVE_TYPE').dictListRsps,
        relateFunctionList: selectData.info.data.find((item) => item.catCode === 'APPROVAL_RELATE_FUNCTION').dictListRsps,
        currentApprovalStatusList: selectData.info.data.find((item) => item.catCode === 'APPROVAL_STATUS').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }
    const { warehouseId } = yield 'nav';
    yield this.warehouseChange({
      warehouseId,
    });
    // 校验按钮权限
    yield this.checkPermission();
  },
  /**
   * 校验按钮权限
   */
  * checkPermission() {
    const { code: confirmCode } = yield checkUrlPermissionAPI({ url: `${process.env.WGS_FRONT}/approval_document/force_reject_approve` });
    yield this.changeData({
      hasForceRejectBtn: confirmCode === '0',
    });
  },
  /**
   * 搜索
   */
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  /**
   * 点击数据，展示弹窗
   */
  * openModal(state) {
    const { list } = yield '';
    const { approvalDocumentCode, type } = state;

    markStatus('loading');
    const { code, info, msg } = yield getProcessFlowAPI({
      approvalDocumentCode,
      isFinish: type !== 'preview', // 是否完成 查看流程传false; 审批传true
    });
    if (code === '0') {
      switch (type) {
        case 'preview':
          yield this.changeData({
            previewVisible: true,
            processFlowData: info,
          });
          break;
        case 'edit':
        default:
          yield this.changeData({
            approveVisible: true,
            approveObj: list.find((x) => x.approvalDocumentCode === approvalDocumentCode) || {},
            processFlowData: info,
          });
          break;
      }
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 审批
   */
  * approve(state) {
    const { approveObj, approveObjAdvice } = yield '';
    const { operateType } = state;
    const { warehouseId } = yield 'nav';

    if (approveObjAdvice.length > 200) {
      Modal.error({ title: t('审批意见不能超过200个字符') });
      return;
    }

    markStatus('loading');
    let reqUrl = approveAPI;
    let reqParams = {
      approvalDocumentCode: approveObj.approvalDocumentCode,
      approvalAdvice: approveObjAdvice,
      operateType,
      warehouseId,
    };
    // 4强制驳回审批
    if (operateType === '4') {
      reqUrl = forceRejectAPI;
      reqParams = {
        approvalDocumentCode: approveObj.approvalDocumentCode,
        approvalAdvice: approveObjAdvice,
      };
    }
    const { code, msg } = yield reqUrl(reqParams);
    if (code === '0') {
      switch (operateType) { // 1,审批完成;2,驳回;,3确认生效
        case '1':
          Message.success(t('操作成功, 已审批完成'));
          break;
        case '2':
          Message.success(t('操作成功, 已驳回'));
          break;
        case '3':
          Message.success(t('操作成功, 已确认生效'));
          break;
        case '4':
          Message.success(t('强制驳回成功'));
          break;
        default:
          break;
      }
      yield this.changeData({
        approveVisible: false, // 审批弹窗 是否可见
        approveObj: {}, // 审批弹窗 数据
        approveObjAdvice: '', // 审批弹窗 审批意见
        processFlowData: {}, // 查看流程 数据
      });
      yield this.search();
    } else {
      Modal.error({ title: msg });
    }
  },
  * warehouseChange({ warehouseId }) {
    yield this.changeLimitData({
      parkId: '', // 园区
      subWarehouseIds: [], // 子仓
    });
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      subWarehouseList: [],
      parkList,
    });
  },
};
