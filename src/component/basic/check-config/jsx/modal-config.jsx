import React from 'react';
import PropTypes from 'prop-types';
import {
  Modal, Button, Select, Radio, Input,
} from 'shineout';
import RuleInput from '@shein-components/WmsInput';
import { t } from '@shein-bbl/react';
import store, { CUR_MONTH } from '../reducers';
import styles from '../../../style.less';

function ModalConfig(props) {
  const {
    configModalVisible,
    checkTypeList,
    statusList,
    exceptionList,
    businessTypeList,
    modalData,
    loading,
  } = props;
  const valStyle = {
    margin: '0 10px',
    display: 'inline-block',
  };
  // 数量校验
  const getErrorMsg = () => {
    const {
      lowWaterMin,
      lowWaterMax,
      dpMin,
      dpMax,
    } = modalData;
    if (+modalData.checkType === 15 && lowWaterMin >= lowWaterMax) {
      return t('低水位件数下限不能大于上限');
    }
    if (+modalData.checkType === 12 && dpMin >= dpMax) {
      return t('动碰件数下限不能大于上限');
    }
    return '';
  };

  return (
    <Modal
      maskCloseAble={false}
      visible={configModalVisible}
      title={t('盘点配置')}
      width={750}
      onClose={() => store.changeData({ configModalVisible: false })}
      footer={[
        <Button
          key="cancel"
          onClick={() => {
            store.changeData({ configModalVisible: false });
          }}
        >
          {t('取消')}
        </Button>,
        <Button
          key="ok"
          type="primary"
          disabled={!loading}
          onClick={() => {
            const msg = getErrorMsg();
            if (msg) {
              Modal.error({ title: msg });
              return;
            }
            const { checkType, curMonth } = modalData;
            // 时间间隔设值
            const checkTimeRange = curMonth === CUR_MONTH ? CUR_MONTH : (modalData.checkTimeRange || '');
            store.checkConfigEdit({
              params: {
                checkType,
                checkTimeRange,
                dpMax: modalData.dpMax,
                dpMin: modalData.dpMin,
                dpType: modalData.dpType,
                exType: modalData.exType,
                lowWaterMax: modalData.lowWaterMax,
                lowWaterMin: modalData.lowWaterMin,
                lowWaterRatio: modalData.lowWaterRatio,
                enabled: modalData.enabled,
                id: modalData.id,
              },
            });
          }}
        >
          {t('确认')}
        </Button>,
      ]}
    >
      <div>
        <div className={styles.inner_list}>
          <span className={styles.labWidth}>
            {t('盘点类型')}
            :
          </span>
          <Select
            style={valStyle}
            data-bind="modalData.checkType"
            width={200}
            data={checkTypeList}
            datum={{ format: 'dictCode' }}
            keygen="dictCode"
            renderItem={(w) => w.dictNameZh}
            placeholder={t('请选择')}
          />
        </div>
        <div className={styles.inner_list}>
          <span className={styles.labWidth}>
            {t('状态')}
            :
          </span>
          <div
            style={valStyle}
            className={styles.inputWidth}
          >
            <Radio.Group
              keygen="id"
              data-bind="modalData.enabled"
            >
              {(statusList || []).map((d) => (
                <Radio key={d.value} htmlValue={d.value}>
                  {d.label}
                </Radio>
              ))}
            </Radio.Group>
          </div>
        </div>
        <div className={styles.inner_list}>
          <span className={styles.labWidth}>
            {t('异常指标')}
            :
          </span>
          <div style={valStyle} className={styles.inputWidth}>
            <Radio.Group
              keygen="value"
              data-bind="modalData.exType"
            >
              {(exceptionList || []).map((d) => (
                <Radio key={d.value} htmlValue={d.value}>
                  {d.label}
                </Radio>
              ))}
            </Radio.Group>
          </div>
        </div>
        <div className={styles.inner_list}>
          <span className={styles.labWidth}>
            {t('盘点间隔时间')}
            :
          </span>
          <div style={{ display: 'inline-block' }}>
            <Select
              style={valStyle}
              data-bind="modalData.curMonth"
              width={100}
              data={[{
                label: CUR_MONTH,
                value: CUR_MONTH,
              }]}
              datum={{ format: 'value' }}
              keygen="value"
              renderItem={(w) => w.label}
              placeholder={t('请选择')}
              clearable
            />
            <Input.Number
              min={1}
              max={30}
              style={valStyle}
              data-bind="modalData.checkTimeRange"
              className={styles.inputWidth}
            />
          </div>
        </div>
        {/* 低水位盘点才展示 */}
        {+modalData.checkType === 15 && (
          <>
            <div className={styles.inner_list}>
              <span className={styles.labWidth}>
                {t('低水位件数')}
                :
              </span>
              <div style={{ display: 'inline-block' }}>
                <RuleInput.Number
                  data-bind="modalData.lowWaterMin"
                  className={styles.inputWidth}
                  style={valStyle}
                  min={0}
                />
                ~
                <RuleInput.Number
                  data-bind="modalData.lowWaterMax"
                  className={styles.inputWidth}
                  style={valStyle}
                  min={0}
                />
                {t('件')}
              </div>
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labWidth}>
                {t('低水位比例')}
                :
              </span>
              <div style={{ display: 'inline-block' }}>
                <Input.Number
                  min={0}
                  max={100}
                  style={valStyle}
                  data-bind="modalData.lowWaterRatio"
                  className={styles.inputWidth}
                />
                &nbsp;%
              </div>
            </div>
          </>
        )}
        {/* 动碰盘点才展示 */}
        {+modalData.checkType === 12 && (
          <>
            <div className={styles.inner_list}>
              <span className={styles.labWidth}>
                {t('动碰次数')}
                :
              </span>
              <div style={{ display: 'inline-block' }}>
                <RuleInput.Number
                  data-bind="modalData.dpMin"
                  className={styles.inputWidth}
                  style={valStyle}
                  min={0}
                />
                ~
                <RuleInput.Number
                  data-bind="modalData.dpMax"
                  className={styles.inputWidth}
                  style={valStyle}
                  min={0}
                />
                {t('次')}
              </div>
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labWidth}>
                {t('动碰业务')}
                :
              </span>
              <Select
                style={{ margin: '0 10px' }}
                data-bind="modalData.dpType"
                width={200}
                data={businessTypeList}
                datum={{ format: 'value' }}
                keygen="value"
                renderItem={(w) => w.label}
                placeholder={t('请选择')}
                onFilter={(text) => (v) => v.label.toLowerCase()
                  .indexOf(text.toLowerCase()) > -1}
              />
            </div>
          </>
        )}

      </div>
    </Modal>
  );
}

ModalConfig.propTypes = {
  configModalVisible: PropTypes.bool,
  checkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  exceptionList: PropTypes.arrayOf(PropTypes.shape()),
  businessTypeList: PropTypes.arrayOf(PropTypes.shape()),
  modalData: PropTypes.shape(),
  loading: PropTypes.number,
};

export default ModalConfig;
