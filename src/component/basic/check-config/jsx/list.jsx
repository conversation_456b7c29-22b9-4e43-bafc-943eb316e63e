import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store, { CUR_MONTH } from '../reducers';
import ModalConfig from './modal-config';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
    } = this.props;

    const columns = [
      {
        title: t('盘点类型'),
        width: 100,
        render: 'checkTypeName',
      },
      {
        title: t('异常指标'),
        width: 130,
        render: 'exTypeName',
      },
      {
        title: t('盘点间隔(天)'),
        width: 120,
        render: 'checkTimeRange',
      },
      {
        title: t('状态'),
        width: 100,
        render: 'enabledName',
      },
      {
        title: t('更新人'),
        width: 130,
        render: 'lastUpdateName',
      },
      {
        title: t('更新时间'),
        width: 130,
        render: 'lastUpdateTime',
      },
      {
        title: t('操作'),
        width: 200,
        fixed: 'right',
        render: (record) => (
          <>
            <Button
              key="enable"
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              disabled={record.enabled === 1}
              onClick={() => {
                // 启用
                store.checkConfigBan({
                  params: {
                    enabled: '1',
                    ids: [record.id],
                  },
                });
              }}
            >
              {t('启用')}
            </Button>
            <Button
              key="disable"
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              disabled={record.enabled === 2}
              onClick={() => {
                // 禁用
                store.checkConfigBan({
                  params: {
                    enabled: '2',
                    ids: [record.id],
                  },
                });
              }}
            >
              {t('禁用')}
            </Button>
            <Button
              key="edit"
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => {
                const { checkTimeRange } = record;
                // 编辑盘点
                store.changeData({
                  configModalVisible: true,
                  modalData: {
                    ...record,
                    curMonth: checkTimeRange === CUR_MONTH ? CUR_MONTH : '',
                    checkTimeRange: checkTimeRange !== CUR_MONTH ? checkTimeRange : '',
                    modalCheckType: '',
                  },
                });
              }}
            >
              {t('编辑')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={[styles.tableSection, styles.listArea].join(' ')}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        <ModalConfig {...this.props} />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
};

export default List;
