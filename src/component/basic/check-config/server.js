import { sendPostRequest } from '@src/server/common/public';

/**
 * 获取盘点配置
 * @param param
 * @returns {*}
 */
export const checkConfigQueryAPI = (param) => sendPostRequest({
  url: '/check_config/query',
  param,
}, process.env.WWS_URI);

/**
 * 状态禁用启用
 * @param param
 * @returns {*}
 */
export const checkConfigBanAPI = (param) => sendPostRequest({
  url: '/check_config/ban',
  param,
}, process.env.WWS_URI);

/**
 * 盘点配置编辑
 * @param param
 * @returns {*}
 */
export const checkConfigEditAPI = (param) => sendPostRequest({
  url: '/check_config/edit',
  param,
}, process.env.WWS_URI);
