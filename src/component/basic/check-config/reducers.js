import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { paramTrim, clearEmpty } from '@src/lib/deal-func';
import { handleListMsg } from '@src/lib/dealFunc';
import { checkConfigQueryAPI, checkConfigBanAPI, checkConfigEditAPI } from './server';

// 根据条件进行过滤
const getListByLimit = (list = [], limit = {}) => {
  if (!list.length) {
    return [];
  }
  const keys = Object.keys(clearEmpty(limit));
  if (!keys.length) {
    return list;
  }
  return keys.reduce((res, keyItem) => res.filter((i) => i[keyItem] === limit[keyItem]), list);
};

export const CUR_MONTH = t('本月');
// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  checkType: '',
  enabled: '',
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  checkTypeList: [], // 盘点类型 循环盘点-1，动碰盘点-12，低水位盘点-15
  configModalVisible: false, // 配置弹窗展示
  // 状态列表
  // 状态列表
  statusList: [{
    label: t('启用'),
    value: 1,
  }, {
    label: t('停用'),
    value: 2,
  }],
  exceptionList: [{
    label: t('短拣率'),
    value: 3,
  }, {
    label: t('库存准确率'),
    value: 2,
  }, {
    label: t('盘点覆盖率'),
    value: 1,
  }], // 异常指标列表
  modalData: {
    checkType: '', // 盘点类型 1循环 12动碰 15低水位
    checkTimeRange: '', // 盘点间隔
    dpMax: '', // 动碰次数上界
    dpMin: '', // 动碰次数下界
    dpType: '', // 动碰业务类型1入库2库内3出库
    exType: '', // 异常指标1盘点覆盖率2库存准确率3短拣率
    lowWaterMax: '', // 低水位件数上界
    lowWaterMin: '', // 低水位件数下界
    curMonth: '', // 当月
  },
  businessTypeList: [{
    label: t('入库'),
    value: 1,
  }, {
    label: t('库内'),
    value: 2,
  }, {
    label: t('出库'),
    value: 3,
  }], // 动碰业务列表
  orginList: [],
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    yield this.search();
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['CHECK_TYPE'] }),
    ]);
    if (selectData.code === '0') {
      const datas = selectData.info.data || [];
      yield this.changeData({
        // 盘点类型，只展示三个，循环盘点，动碰盘点，低水位盘点
        checkTypeList: datas.find((item) => item.catCode === 'CHECK_TYPE')
          .dictListRsps
          .filter((i) => [1, 12, 15].includes(i.dictCode)),
      });
    } else {
      handleListMsg([selectData]);
    }
  },
  * handleSearch() {
    const { orginList, limit } = yield '';
    const resList = getListByLimit(orginList, limit);
    yield this.changeData({ list: resList });
  },
  /**
   * 搜索
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield checkConfigQueryAPI(paramTrim(param));
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        // 原始列表，用来做本地查询
        orginList: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 状态禁用启用
  * checkConfigBan(action) {
    // 获取列表
    const res = yield checkConfigBanAPI(paramTrim(action.params));
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.search();
      yield this.handleSearch();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 状态禁用启用
  * checkConfigEdit(action, ctx) {
    markStatus('loading');
    // 获取列表
    const res = yield checkConfigEditAPI(paramTrim(action.params));
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.search();
      yield this.handleSearch();
      yield ctx.changeData({ configModalVisible: false });
    } else {
      Modal.error({ title: res.msg });
    }
  },
};
