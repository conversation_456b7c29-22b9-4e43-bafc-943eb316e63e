import moment from 'moment';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { getSubWarehouseApi } from '@src/server/basic/sub-warehouse';
import { queryLocationAPI, exportLocationAPI } from '@src/server/common/common';
import { selectArea } from '@src/server/basic/area';
import { STATISTICAL_DOWNLOAD } from '@src/lib/jump-url';
import { queryConfigAPI } from './server';

const format = 'YYYY-MM-DD HH:mm:ss';

// 搜索默认值
export const defaultLimit = {
  parkTypeList: [], // 园区
  subWarehouseIds: [], //  子仓
  areaIds: '', // 库区
  locations: '', // 库位
  containerCodes: '', // 周转箱
  status: '', // 周转箱状态
  releaseUser: '', // 释放人
  upperUser: '', // 上架人
  beginTime: moment(new Date()
    .setHours(0, 0, 0, 0))
    .format(format),
  endTime: moment(new Date()
    .setHours(23, 59, 59, 999))
    .format(format),
};
// 其他默认值
const defaultState = {
  formRef: {},
  loading: 0, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  list: [],
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },

  parkTypeList: [],
  subWarehouseList: [],
  areaList: [],
  statusList: [],
  timeRequiredConfig: false,
};

export default {
  state: defaultState,
  // 改变state的值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },

  /**
   * 初始化数据
   */
  * init() {
    markStatus('loading');
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['LOCATION_CONTAINER_STATUS'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        statusList: selectData.info.data.find((v) => v.catCode === 'LOCATION_CONTAINER_STATUS').dictListRsps,
      });
    } else {
      Modal.error({
        title: selectData.msg || t('请求失败,请稍后再试'),
        autoFocusButton: 'ok',
      });
    }
    const { warehouseId, permissionSubWarehouseList } = yield 'nav';
    yield this.changeData({
      subWarehouseList: permissionSubWarehouseList,
    });
    yield this.getParkTypeList(warehouseId);
    yield this.queryRequiredConfig();
  },

  * queryRequiredConfig() {
    const res = yield queryConfigAPI({ key: 'GOODS_LOCATION_CONTAINER_MAP_QUERY_BROADEN_TIME' });
    if (res.code === '0') {
      yield this.changeData({
        timeRequiredConfig: res.info?.value,
      });
    } else {
      Modal.error({
        title: res.msg,
      });
    }
  },
  /**
   * 右上角更换仓库触发
   * @param {*} action
   */
  * changeSubWarehouseList(action) {
    const { warehouseId, permissionSubWarehouseList } = action;
    yield this.changeData({
      subWarehouseList: permissionSubWarehouseList,
      limit: {
        ...this.state.limit,
        parkTypeList: [],
        subWarehouseIds: [],
        areaIds: '',
      },
    });
    yield this.getParkTypeList(warehouseId);
  },

  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },

  /**
   * 搜索操作
   */
  * search() {
    const { limit, pageInfo } = this.state;
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const param = {
      ...limit,
      containerCodes: limit.containerCodes ? limit.containerCodes.split(',') : '',
      locations: limit.locations ? limit.locations.split(',') : '',
      areaIds: limit.areaIds.length !== 0 ? [limit.areaIds] : '',
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseIds: [warehouseId],
    };
    markStatus('loading');
    const { code, info, msg } = yield queryLocationAPI(clearEmpty(paramTrim(param), [0, '0']));
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },

  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(arg = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },

  /**
   * 校验Header
   */
  * handleFormValidate() {
    const { formRef } = this.state;
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 按钮操作-导出
   */
  * export() {
    const { limit, list } = this.state;
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    // 无数据不导出
    if (!list.length) {
      Modal.error({ title: t('无数据，无法导出') });
      return;
    }
    if (!limit.areaIds && !limit.locations && !limit.containerCodes) {
      Modal.error({ title: t('库区、库位、周转箱三者其一必选') });
      return;
    }
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    const param = {
      ...limit,
      containerCodes: limit.containerCodes ? limit.containerCodes.split(',') : '',
      locations: limit.locations ? limit.locations.split(',') : '',
      areaIds: limit.areaIds.length !== 0 ? [limit.areaIds] : '',
      warehouseIds: [warehouseId],
    };
    markStatus('loading');
    const data = yield exportLocationAPI(clearEmpty(paramTrim(param), [0, '0']));
    if (data.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: data.msg || t('后台数据出错') });
    }
  },
  /**
   * 获取园区
   * @param {String} warehouseId 仓库id
   */
  * getParkTypeList(warehouseId) {
    markStatus('loading');
    const { code, info, msg } = yield getSubWarehouseApi({ warehouseId });
    if (code === '0') {
      const parkList = info.data
        .filter((x) => ![undefined, null].includes(x.parkType))
        .map((x) => ({
          dictCode: x.parkType,
          dictNameZh: x.parkName,
        }))
        .sort((a, b) => a.dictCode - b.dictCode);

      // 去重
      const parkMap = new Map();
      parkList.forEach((item) => {
        if (!parkMap.has(item.dictCode)) {
          parkMap.set(item.dictCode, item);
        }
      });

      yield this.changeData({
        parkTypeList: parkMap instanceof Map ? [...parkMap.values()] : [],
      });
    } else {
      Modal.error({
        title: msg,
      });

      yield this.changeData({
        parkTypeList: [],
      });
    }
  },
  /**
   * 获取库区
   * @param {String} subWarehouseId 子仓id
   */
  * getAreaList(subWarehouseIds) {
    markStatus('loading');
    const { code, info, msg } = yield selectArea({
      subWarehouseIdList: subWarehouseIds,
      enabled: 1,
    });
    if (code === '0') {
      yield this.changeData({
        areaList: info.data || [],
      });
    } else {
      Modal.error({ title: msg });
      yield this.changeData({
        areaList: [],
      });
    }
  },
};
