import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Button } from 'shineout';
import styles from '@src/component/style.less';
import store from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.export();
          }}
        >
          {t('导出')}
        </Button>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
};

export default Handle;
