import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
    } = this.props;

    const columns = [
      {
        title: t('园区'),
        render: 'parkTypeName',
        width: 100,
      },
      {
        title: t('子仓'),
        render: 'subWarehouse',
        width: 100,
      },
      {
        title: t('库区'),
        render: 'area',
        width: 100,
      },
      {
        title: t('库位'),
        render: 'location',
        width: 120,
      },
      {
        title: t('周转箱'),
        render: 'containerCode',
        width: 100,
      }, {
        title: t('周转箱状态'),
        render: 'statusName',
        width: 120,
      }, {
        title: t('释放人'),
        render: 'releaseUser',
        width: 100,
      }, {
        title: t('上架人'),
        render: 'upperUser',
        width: 100,
      }, {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 160,
      },
      {
        title: t('上架时间'),
        render: 'createTime',
        width: 160,
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => t('共{}条', String(total)), 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
};

export default List;
