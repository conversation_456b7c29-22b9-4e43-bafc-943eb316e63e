import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import { Select, Rule, Modal } from 'shineout';
import InputMore from '@shein-components/inputMore';
import { formatSearchData } from '@public-component/search-queries/utils';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import DateRangePicker from '@shein-components/dateRangePicker2';
import FilterSearchSelect from '@src/component/public-component/select/filter-search-select';
import store, { defaultLimit } from '../reducers';

const rule = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      if (!formData.containerCodes && !formData.locations) {
        if (!formData.beginTime || !formData.endTime) {
          callback(new Error(t('开始时间或结束时间必选')));
        }
        // 限制可选时间段最大不超过1周
        if (moment(formData.endTime)
          .customDiff(moment(formData.beginTime), 'weeks', true) > 1) {
          callback(new Error(t('选择时间段不能大于{}周', 1)));
        }
      }
      callback(true);
    },
  },
  timeRequiredRange: {
    func: (val, formData, callback) => {
      if (!formData.containerCodes && !formData.locations && Number(formData.status) !== 1) {
        if (!formData.beginTime || !formData.endTime) {
          callback(new Error(t('开始时间或结束时间必选')));
        }
        // 限制可选时间段最大不超过1周
        if (moment(formData.endTime)
          .customDiff(moment(formData.beginTime), 'weeks', true) > 1) {
          callback(new Error(t('选择时间段不能大于{}周', 1)));
        }
      }
      callback(true);
    },
  },
});

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      parkTypeList,
      subWarehouseList,
      areaList,
      statusList,
      timeRequiredConfig,
    } = this.props;

    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 70 }}
          searching={!loading}
          value={limit}
          onChange={(val) => {
            if (limit.beginTime !== val.beginTime && !val.beginTime && !val.endTime) {
              store.queryRequiredConfig();
            }
            store.changeLimitData(formatSearchData(defaultLimit, val));
          }}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            if (!limit.areaIds && !limit.locations && !limit.containerCodes) {
              Modal.error({ title: t('库区、库位、周转箱三者其一必选') });
              return;
            }
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('园区')}
            name="parkTypeList"
            data={parkTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            // multiple
            compressed
            clearable
            placeholder={t('全部')}
            onChange={(val) => {
              store.changeLimitData({
                parkTypeList: val ? [val] : [],
              });
            }}
          />
          <Select
            label={t('子仓')}
            name="subWarehouseIds"
            data={subWarehouseList}
            keygen="id"
            format="id"
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            // multiple
            compressed
            clearable
            placeholder={t('全部')}
            onChange={(val) => {
              // reset
              store.changeData({
                areaList: [],
              });
              store.changeLimitData({
                areaIds: '',
                subWarehouseIds: val ? [val] : [],
              });
              if (val) {
                store.getAreaList(val ? [val] : []);
              }
            }}
          />
          <Select
            label={t('库区')}
            name="areaIds"
            data={areaList}
            disabled={limit.subWarehouseIds.length === 0}
            keygen="id"
            format="id"
            renderItem="area"
            onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <InputMore
            label={t('库位')}
            title={t('添加多个库位,以回车键隔开')}
            placeholder={t('请输入')}
            modalplaceholder={t('支持输入多个库位')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            name="locations"
            max={100}
            maskCloseAble={false}
            overDisabled
            clearable
            required
          />
          <InputMore
            label={t('周转箱')}
            title={t('添加多个周转箱,以回车键隔开')}
            placeholder={t('请输入')}
            modalplaceholder={t('支持输入多个周转箱')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            name="containerCodes"
            max={100}
            maskCloseAble={false}
            overDisabled
            clearable
            required
          />
          <Select
            label={t('周转箱状态')}
            name="status"
            data={statusList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            onChange={(v) => {
              // 产品要求占用中时，支持不传时间，
              // 此时后端可能慢接口，前端需要根据配置决定是否放开时间限制
              if (v === 1) {
                store.queryRequiredConfig();
              }
            }}
          />
          <FilterSearchSelect
            label={t('释放人')}
            name="releaseUser"
            clearable
            placeholder={t('请输入')}
          />
          <FilterSearchSelect
            label={t('上架人')}
            name="upperUser"
            clearable
            placeholder={t('请输入')}
          />
          <DateRangePicker
            label={t('更新时间')}
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['beginTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[timeRequiredConfig ? rule.timeRequiredRange() : rule.timeRange()]}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  areaList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  timeRequiredConfig: PropTypes.bool,
};

export default Header;
