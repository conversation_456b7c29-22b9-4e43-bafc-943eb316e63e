import { sendPostRequest } from '@src/server/common/public';

// 搜索
// export const queryAPI = (param) => sendPostRequest({
//   url: '/goods_location_container_map/query',
//   param,
// }, process.env.BASE_URI_WMD);

// 导出
// export const exportAPI = (param) => sendPostRequest({
//   url: '/goods_location_container_map/export',
//   param,
// }, process.env.BASE_URI_WMD);

// 根据仓库id 查询子仓和园区信息(级联)
// export const getParkTypeListAPI = param => sendPostRequest({
//   url: '/sub_warehouse/query_park',
//   param,
// }, process.env.BASE_URI_WMD);

// 返回true，才按prd上面的来放开时间
// eslint-disable-next-line import/prefer-default-export
export const queryConfigAPI = (param) => sendPostRequest({
  url: '/wmd_apollo/query_boolean_by_key',
  param,
}, process.env.BASE_URI_WMD);
