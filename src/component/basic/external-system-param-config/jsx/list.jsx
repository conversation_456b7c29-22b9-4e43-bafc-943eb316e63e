import React from 'react';
import PropTypes from 'prop-types';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import styles from '@src/component/style.less';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
    } = this.props;

    const columns = [
      {
        title: t('配置来源方'),
        width: 120,
        render: 'configSource',
      },
      {
        title: t('业务类型'),
        width: 100,
        render: 'businessTypeName',
      },
      {
        title: t('配置项目'),
        width: 100,
        render: 'configCategoryName',
      },
      {
        title: t('配置内容'),
        width: 100,
        render: 'configContent',
      },
      {
        title: t('更新时间'),
        width: 100,
        render: 'lastUpdateTime',
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            style={{ height: '100%' }}
            rowClassName={() => styles.borderInner}
            bordered
            fixed="both"
            loading={!loading}
            data={list}
            columns={columns}
            keygen="id"
            empty={t('暂无数据')}
            size="small"
            width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape).isRequired,
  pageInfo: PropTypes.shape().isRequired,
};

export default List;
