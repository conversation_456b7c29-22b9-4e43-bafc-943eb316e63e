import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store from '../reducers';

class Handle extends Component {
  render() {
    const {
      loading,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.search();
          }}
        >
          {t('搜索')}
        </Button>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.bool,
};
export default Handle;
