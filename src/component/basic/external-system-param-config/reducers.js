import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import moment from 'moment';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { queryAPI } from './server';

export const defaultLimit = {

};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  loading: 0, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
};

export default {
  state: defaultState,
  * init() {
    markStatus('loading');
    yield this.search();
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 编辑
  changeConfigObjData(state, data) {
    Object.assign(state.editObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    // 清空校验信息
    if (formRef && formRef.clearValidate) {
      formRef.clearValidate();
    }
    yield this.changeLimitData({
      ...defaultLimit,
    });
  },
  // 查询
  * search() {
    const { pageInfo } = this.state;
    const param = {
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');

    const { code, info, msg } = yield queryAPI(param);
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 页签改变
  * handlePaginationChange(arg = {}) {
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
};
