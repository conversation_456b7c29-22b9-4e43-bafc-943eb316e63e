import fileFetch from '@src/server/common/fileFetch';
import { sendPostRequest } from '@src/server/common/public';

/**
 * 列表查询接口
 * @param param
 * @returns {*}
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/user_auth_board/query',
  param,
}, process.env.WKB_FRONT);

/**
 * 编辑新增接口
 * @param param
 * @returns {*}
 */
export const updateAPI = (param) => sendPostRequest({
  url: '/user_auth_board/modify',
  param,
}, process.env.WKB_FRONT);

/**
 * 导入拣货标签
 * @type {string}
 */
export const importURL = `${process.env.WGS_FRONT}/file_import/record/wkb/user_auth_board_import`;

/**
 * 下载模板
 */
export const downloadTemplateAPI = () => fileFetch(`${process.env.WKB_FRONT}/user_auth_board/download_template`, {
  method: 'POST',
  credentials: 'include',
});

/**
 * 启用 禁用
 * @param param
 * @returns {*}
 */
export const enableOrNotAPI = (param) => sendPostRequest({
  url: '/user_auth_board/enabled',
  param,
}, process.env.WKB_FRONT);

/**
 * 获取权限节点菜单
 * @param param
 * @returns {*}
 */
export const authListAPI = (param) => sendPostRequest({
  url: '/auth_rule/query_menu_tree',
  param,
}, process.env.WAS_URI);
