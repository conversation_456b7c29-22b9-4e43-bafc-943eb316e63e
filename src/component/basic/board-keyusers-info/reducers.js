import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { handleListMsg } from '@src/lib/dealFunc';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty } from '@src/lib/deal-func';
import { formdataPost } from '@src/server/common/fileFetch';
import fileSaver from 'file-saver';
import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import {
  getListAPI, updateAPI, importURL,
  downloadTemplateAPI, authListAPI, enableOrNotAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  warehouseId: '', // 仓库
  boardAuthIdList: [], // 看板菜单
  enabled: '', // 是否启用
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  selectedRows: [], // 选中行数据
  showEditModal: false, // 显示新增、修改弹窗
  editObj: {
    warehouseId: '', // 仓库
    enabled: 1, // 1-启用，0-禁用
    boardAuthId: '', // 看板权限id
    extendsList: [
      {
        parentUserName: '', // 上级用户
        userName: '', // 关键用户
        warningNum: 7,
      },
    ],
  }, // 新增、修改弹窗数据
  isEdit: false, // 是否编辑弹窗
  recordId: '', // 操作记录弹窗
  importModalVisible: false, // 导入弹框
  file: null,
  boardListOptions: [], // 菜单
  enabledOptions: [
    {
      dictNameZh: t('启用'),
      dictCode: 1,
    },
    {
      dictNameZh: t('禁用'),
      dictCode: 0,
    }],
};

// 去掉最后一层children: [],变成children: undefined,
const deleteChildren = (list) => list.map((item) => {
  const children = item.children.length ? deleteChildren(item.children)
    : undefined;
  return { ...item, children };
});

// 转义rule里面的&amp;等转义字符
const transform = (list) => list.map((item) => {
  const parser = new DOMParser();
  return {
    ...item,
    children: item.children ? transform(item.children) : undefined,
    rule: parser.parseFromString(item.rule, 'text/html').body.textContent,
  };
});

// 创建树形下拉
export const createTreeData = (data) => data.map((item) => {
  if (item.children) {
    return {
      ...item,
      value: item.id,
      label: item.title,
      children: createTreeData(item.children),
      disabled: item.status !== '1',
    };
  }
  return {
    ...item,
    value: item.id,
    label: item.title,
    disabled: item.status !== '1',
  };
});

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改搜索条件新增/编辑对象属性值
  changeEdit: (state, data) => {
    Object.assign(state.editObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    // 请求下拉框数据
    markStatus('loading');
    const [authRes] = yield Promise.all([
      authListAPI({}),
    ]);
    if (authRes.code === '0') {
      yield this.changeData({
        boardListOptions: transform(deleteChildren((authRes?.info || []).find((li) => (li.id === '110'))?.children || [])),
      });
    } else {
      handleListMsg([authRes], false);
    }
  },
  /**
   * 搜索
   */
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(clearEmpty(param, [0]));
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 启用/禁用
  * enableOrNot(params) {
    markStatus('loading');
    const res = yield enableOrNotAPI(params);
    if (res.code === '0') {
      yield this.changeData({ showEditModal: false });
      Message.success(Number(params.enabled) === 1 ? t('启用成功！') : t('禁用成功！'));
      // 重新获取数据
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg || t('后台数据出错') });
    }
  },
  // 新增/修改
  * update() {
    const {
      editObj,
      isEdit,
    } = yield '';
    markStatus('loading');
    const res = yield updateAPI(editObj);
    if (res.code === '0') {
      yield this.changeData({ showEditModal: false });
      Message.success(!isEdit ? t('新增成功！') : t('修改成功！'));
      // 重新获取数据
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg || t('后台数据出错') });
    }
  },
  // 下载模板
  * downloadTemplate() {
    try {
      markStatus('loading');
      const res = yield downloadTemplateAPI();
      const b = yield res.blob();
      const blob = new Blob([b], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('看板关键用户信息')}.xlsx`);
    } catch (e) {
      Modal.error({ title: e.reason.message });
    }
  },
  * uploadFile({ formData }) {
    markStatus('loading');
    formData.append('function_node', 88);
    const res = yield formdataPost(importURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
