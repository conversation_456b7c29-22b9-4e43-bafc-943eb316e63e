import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Popover, Table } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import styles from '../style.less';
import store from '../reducers';
// 表格行选中
const rowSelectHandle = (rows) => {
  store.changeData({ selectedRows: rows });
};
class List extends React.Component {
  render() {
    const {
      loading,
      list,
      selectedRows,
      pageInfo,
    } = this.props;

    const columns = [
      {
        title: t('监控看板'),
        render: 'boardAuthName',
        width: 160,
      },
      {
        title: t('所属菜单'),
        render: 'menuAuthName',
        width: 160,
      },
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 140,
      },
      {
        title: t('关键人员'),
        render: (r) => {
          const extendsList = r.extendsList || [];
          return (
            <>
              <Popover
                trigger="click"
                position="top"
                content={(
                  <div style={{ height: 180, width: 150, overflowY: 'auto' }} />
              )}
              >
                {
                  (r.extendsList || []).map((item) => (item.userName)).join('、')
                }
              </Popover>
              {
              extendsList.length > 10
                ? (
                  <div>
                    {(r.extendsList || []).slice(0, 10).map((item) => (item.userName)).join('、')}
                    <span className={styles.more}>{t('更多')}</span>
                  </div>
                ) : (r.extendsList || []).map((ei) => (ei.userName)).join('、')
            }
            </>
          );
        },
        width: 200,
      },
      {
        title: t('状态'),
        render: 'enabledName',
        width: 140,
      },
      {
        title: t('更新人'),
        render: 'operator',
        width: 140,
      },
      {
        title: t('更新时间'),
        width: 180,
        render: 'lastUpdateTime',
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
            onRowSelect={rowSelectHandle}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  pageInfo: PropTypes.shape(),
};

export default List;
