import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Button, Modal, Select, Form, Rule, Input, TreeSelect,
} from 'shineout';
import Icon from '@shein-components/Icon';
import FilterSearchSelect from '@public-component/select/filter-search-select';
import { warehouseIdLocalStorage } from '@src/lib/storage-new';
import styles from '@src/component/style.less';
import store, { createTreeData } from '../reducers';
import style from '../style.less';

const rules = Rule();

class Handle extends React.Component {
  render() {
    const {
      showEditModal,
      editObj,
      loading,
      isEdit,
      boardListOptions,
      enabledOptions,
      selectedRows,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          loading={loading === 0}
          onClick={() => {
            store.changeData({
              showEditModal: true,
              isEdit: false,
              editObj: {
                enabled: 1, // 1-启用，0-禁用
                boardAuthId: '', // 看板权限id
                warehouseId: +warehouseIdLocalStorage.getItem(),
                extendsList: [
                  {
                    parentUserName: '', // 上级用户
                    userName: '', // 关键用户
                    warningNum: 7,
                    id: +new Date(),
                  },
                ],
              },
            });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          loading={loading === 0}
          disabled={selectedRows.length !== 1}
          onClick={() => {
            store.changeData({
              showEditModal: true,
              isEdit: true,
              editObj: { ...(selectedRows[0] || {}), boardAuthId: String((selectedRows[0] || {}).boardAuthId) },
            });
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="primary"
          loading={loading === 0}
          onClick={() => {
            store.changeData({
              importModalVisible: true, // 导入弹框
            });
          }}
        >
          {t('导入')}
        </Button>
        <Button
          type="primary"
          loading={loading === 0}
          onClick={() => {
            store.downloadTemplate();
          }}
        >
          {t('下载导入模板')}
        </Button>
        <Button
          type="primary"
          loading={loading === 0}
          disabled={!selectedRows.length}
          onClick={() => {
            store.enableOrNot({
              enabled: 1, // 1 启用 0 禁用
              idList: selectedRows.map((si) => (si.id)),
            });
          }}
        >
          {t('启用')}
        </Button>
        <Button
          type="primary"
          loading={loading === 0}
          disabled={!selectedRows.length}
          onClick={() => {
            store.enableOrNot({
              enabled: 0, // 1 启用 0 禁用
              idList: selectedRows.map((si) => (si.id)),
            });
          }}
        >
          {t('禁用')}
        </Button>
        <Modal
          key={showEditModal}
          visible={showEditModal}
          maskCloseAble={false}
          onClose={() => store.changeData({ showEditModal: false })}
          width={820}
          title={isEdit ? t('编辑') : t('新增')}
          bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ showEditModal: false })}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading || !(editObj.extendsList || []).every((ele) => ele.parentUserName && ele.userName && ele.warningNum)}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={150}
            labelAlign="right"
            onChange={(value) => {
              store.changeData({
                editObj: value,
              });
            }}
            onSubmit={() => {
              store.update();
            }}
            value={editObj}
          >
            <div className={style.compileModalCont}>
              <div className={style.itemWrap}>
                <div className={style.itemTitle}>
                  {t('基础信息')}
                </div>
                <div className={style.selectWrap}>
                  <div>
                    <Form.Item required label={t('监控看板')}>
                      {isEdit ? (
                        <Input
                          absolute
                          name="boardAuthName"
                          clearable
                          disabled={isEdit}
                          width={200}
                        />
                      ) : (
                        <TreeSelect
                          absolute
                          label={t('监控看板')}
                          name="boardAuthId"
                          mode={2}
                          keygen={(d) => d.value}
                          renderItem="label"
                          onFilter={(text) => (d) => (d.label && d.label.indexOf(text) > -1)}
                          data={createTreeData(boardListOptions)}
                          width={200}
                          clearable
                          rules={[rules.required(t('请选择'))]}
                        />
                      )}
                    </Form.Item>
                  </div>
                  <div>
                    <Form.Item label={t('状态')}>
                      <Select
                        absolute
                        label={t('状态')}
                        name="enabled"
                        width={200}
                        data={enabledOptions}
                        keygen="dictCode"
                        format="dictCode"
                        renderItem="dictNameZh"
                        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        placeholder={t('全部')}
                      />
                    </Form.Item>
                  </div>
                </div>
                <div className={style.itemTitle}>
                  {t('人员信息')}
                </div>
                <div className={style.extendsListItem}>
                  <div className={style.extendsListSubItem} />
                  <div className={style.extendsListSubItem}>{t('关键用户')}</div>
                  <div className={style.extendsListSubItem}>{t('上级用户')}</div>
                  <div className={style.extendsListSubItem}>{t('预警次数')}</div>
                </div>
                <div>
                  {(editObj.extendsList || []).map((ei, eIdx) => (
                    <div className={style.extendsListItem}>
                      <div className={style.extendsListSubItem} style={{ textAlign: 'right' }}>
                        {eIdx + 1}
                        :
                      </div>
                      <FilterSearchSelect
                        absolute
                        width={150}
                        className={style.extendsListSubItem}
                        label={t('关键用户')}
                        value={ei.spliceName}
                        clearable
                        placeholder={t('请输入')}
                        onChange={(val, dataItem) => {
                          store.changeData({
                            editObj: {
                              ...editObj,
                              extendsList: (editObj.extendsList || []).map((sei, seIdx) => {
                                if (seIdx === eIdx) {
                                  return {
                                    ...sei,
                                    userName: val,
                                    spliceName: dataItem.splicedName,
                                  };
                                }
                                return sei;
                              }),
                            },
                          });
                        }}
                      />
                      <FilterSearchSelect
                        absolute
                        width={150}
                        className={style.extendsListSubItem}
                        label={t('上级用户')}
                        value={ei.parentSpliceName}
                        clearable
                        placeholder={t('请输入')}
                        onChange={(val, dataItem) => {
                          store.changeData({
                            editObj: {
                              ...editObj,
                              extendsList: (editObj.extendsList || []).map((sei, seIdx) => {
                                if (seIdx === eIdx) {
                                  return {
                                    ...sei,
                                    parentUserName: val,
                                    parentSpliceName: dataItem.splicedName,
                                  };
                                }
                                return sei;
                              }),
                            },
                          });
                        }}
                      />
                      <Input.Number
                        absolute
                        value={ei.warningNum}
                        delay={0}
                        digits={0}
                        allowNull
                        hideArrow
                        width={150}
                        min={1}
                        max={99}
                        clearable
                        className={style.extendsListSubItem}
                        onChange={(val) => {
                          store.changeData({
                            editObj: {
                              ...editObj,
                              extendsList: (editObj.extendsList || []).map((sei, seIdx) => {
                                if (seIdx === eIdx) {
                                  return {
                                    ...sei,
                                    warningNum: val,
                                  };
                                }
                                return sei;
                              }),
                            },
                          });
                        }}
                      />
                      <div>
                        <Button
                          type="success"
                          text
                          size="large"
                          onClick={() => {
                            store.changeData({
                              editObj: {
                                ...editObj,
                                extendsList: [
                                  ...editObj.extendsList || [],
                                  {
                                    parentUserName: '', // 上级用户
                                    userName: '', // 关键用户
                                    warningNum: 7,
                                  },
                                ],
                              },
                            });
                          }}
                        >
                          <Icon name="plus-o" />
                        </Button>
                        <Button
                          type="danger"
                          text
                          disabled={(editObj.extendsList || []).length === 1}
                          size="large"
                          onClick={() => {
                            store.changeData({
                              editObj: {
                                ...editObj,
                                extendsList: (editObj.extendsList || []).filter((sei, seIdx) => (seIdx !== eIdx)),
                              },
                            });
                          }}
                        >
                          <Icon name="minus-o" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  showEditModal: PropTypes.bool.isRequired,
  editObj: PropTypes.shape().isRequired,
  loading: PropTypes.number.isRequired,
  isEdit: PropTypes.bool.isRequired,
  boardListOptions: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  enabledOptions: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
};

export default Handle;
