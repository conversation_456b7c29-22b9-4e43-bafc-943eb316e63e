import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import {
  Modal, Button,
} from 'shineout';
import FileDelayUpload from '@public-component/upload/fileDelayUpload';
import store from '../reducers';

function ImportModal(props) {
  const {
    loading,
    file,
    importModalVisible, // 导入弹框
  } = props;

  return (
    <Modal
      key={importModalVisible}
      maskCloseAble={null}
      title={t('导入看板关键用户信息')}
      visible={importModalVisible}
      maskClosable={false}
      onClose={() => {
        store.changeData({
          file: '',
          importModalVisible: false,
        });
      }}
      footer={(
        <div>
          <Button
            onClick={() => {
              store.changeData({
                file: '',
                importModalVisible: false,
              });
            }}
          >
            {t('取消')}
          </Button>
          <Button
            type="primary"
            disabled={!file}
            loading={loading === 0}
            style={{ marginRight: 15 }}
            onClick={() => {
              const formData = new FormData();
              formData.append('file', file);
              store.uploadFile({
                formData,
              });
            }}
          >
            {t('确定')}
          </Button>
        </div>
      )}
    >
      <div style={{ padding: '20px 0', display: 'flex' }}>
        <span style={{ display: 'inline-block', marginRight: 20 }}>
          {t('选择文件')}
          :
        </span>
        <FileDelayUpload
          value={file}
          accept=".xls,.xlsx"
          title={t('导入文件')}
          onChange={(f) => {
            if (f) {
              store.changeData({
                file: f,
              });
            } else {
              store.changeData({
                file: '',
              });
            }
          }}
        />
      </div>
    </Modal>
  );
}

ImportModal.propTypes = {
  loading: PropTypes.number,
  file: PropTypes.shape(),
  importModalVisible: PropTypes.bool, // 导入弹框
};

export default ImportModal;
