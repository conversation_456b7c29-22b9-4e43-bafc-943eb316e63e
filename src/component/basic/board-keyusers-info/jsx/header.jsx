import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select, TreeSelect } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit, createTreeData } from '../reducers';
import ImportModal from './import-modal';

class Header extends Component {
  render() {
    const {
      limit,
      loading,
      boardListOptions,
      enabledOptions,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <TreeSelect
            label={t('监控看板')}
            name="boardAuthIdList"
            mode={2}
            keygen={(d) => d.value}
            renderItem="label"
            onFilter={(text) => (d) => (d.label && d.label.indexOf(text) > -1)}
            data={createTreeData(boardListOptions)}
            multiple
            clearable
            compressed
          />
          <Select
            label={t('状态')}
            name="enabled"
            data={enabledOptions}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
        </SearchAreaContainer>
        <ImportModal {...this.props} />
      </section>
    );
  }
}

Header.propTypes = {
  limit: PropTypes.shape().isRequired,
  loading: PropTypes.number.isRequired,
  boardListOptions: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  enabledOptions: PropTypes.arrayOf(PropTypes.shape()).isRequired,
};
export default Header;
