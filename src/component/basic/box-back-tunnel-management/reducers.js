import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
// import moment from 'moment';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { getSubWarehouseApi } from '@src/server/basic/sub-warehouse';
import {
  getListAPI, addStrategyAPI, modifyStrategyAPI, deleteStrategyAPI, slotConfAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  parkType: '',
  subWarehouseIds: [],
};

export const defaultEditObj = {
  warehouseId: '',
  parkType: '',
  subWarehouseId: '',
  slot: '',
  containerType: '',
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  editObj: defaultEditObj,
  editObjVisible: 0, // 1 新增，2 编辑
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  warehouseId: '', // 右上角控制
  warehouseList: [], // 仓库列表
  parkList: [],
  subWarehouseList: [], // 搜索的子仓列表
  modalSubWarehouseList: [], // 编辑的子仓列表
  containerTypeList: [],
  slotList: [],
  list: [],
  selectedRows: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改搜索条件即state.limit属性值
  changeEditObjData(state, data) {
    Object.assign(state.editObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init(args, ctx) {
    const { warehouseList, warehouseId } = yield 'nav';
    yield ctx.changeData({ warehouseId, warehouseList });
    const [dictRes] = yield Promise.all([
      dictSelect({ catCode: ['CONTAINER_TYPE'] }),
    ]);
    if (dictRes.code === '0') {
      yield ctx.changeData({
        containerTypeList: dictRes.info.data.find((x) => x.catCode === 'CONTAINER_TYPE').dictListRsps,
      });
    } else {
      Modal.error({ title: dictRes.msg });
    }
    yield ctx.getSubWarehouse({ warehouseId });
  },
  // 搜索
  * search() {
    const { warehouseId } = yield 'nav';
    const { limit, pageInfo } = this.state;
    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        selectedRows: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = this.state;
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { warehouseId, subWarehouseList } = data;
    yield this.changeData({ warehouseId, subWarehouseList });
    yield this.getSubWarehouse({ warehouseId });
  },
  /**
   * @description 获取子仓和园区数据
   * @param warehouseId {number} 右上角的仓库
   * @returns {Generator<void|*, void, *>}
   */
  * getSubWarehouse({ warehouseId }) {
    let parkList = [];
    // 有仓库id才请求
    if (warehouseId) {
      const { code, info, msg } = yield getSubWarehouseApi({ warehouseId });
      if (code === '0') {
        parkList = Object.values((info.data || []).reduce((result, curr) => {
          const { parkName, parkType } = curr;
          if (result[parkType]) {
            result[parkType].subWarehouseList.push(curr);
          } else {
            result[parkType] = {
              parkType,
              parkName,
              subWarehouseList: [{ ...curr }],
            };
          }
          return result;
        }, {}));
      } else {
        Modal.error({ title: msg });
      }
    }
    yield this.changeLimitData({ parkType: '' });
    // 修改园区下拉数据
    yield this.changeData({ parkList, subWarehouseList: [], modalSubWarehouseList: [] });
  },
  // 1 新增 2 编辑
  * confirmEdit() {
    markStatus('loading');
    const api = this.state.editObjVisible === 1 ? addStrategyAPI : modifyStrategyAPI;
    const tip = this.state.editObjVisible === 1 ? t('新增成功') : t('修改成功');
    const { code, msg } = yield api({ ...this.state.editObj });
    yield this.changeData({ editObjVisible: 0 });
    if (code === '0') {
      Message.success(tip);
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 删除
  * deleteData() {
    markStatus('loading');
    const ids = this.state.selectedRows.map((item) => item.id);
    const { code, msg } = yield deleteStrategyAPI({ idList: ids });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取分拣口配置信息
  * querySlotConf(args = {}) {
    const {
      warehouseId,
      subWarehouseId,
    } = args;
    if (!warehouseId || !subWarehouseId) {
      return;
    }
    markStatus('loading');
    const { code, info, msg } = yield slotConfAPI({
      ...args,
      slotType: 3, // 空箱
      slotStatus: 1, // 启用
    });
    if (code === '0') {
      yield this.changeData({
        slotList: info || [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },
};
