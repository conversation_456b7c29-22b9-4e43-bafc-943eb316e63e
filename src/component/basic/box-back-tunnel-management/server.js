import { sendPostRequest } from '@src/server/common/public';

// 搜索
export const getListAPI = (param) => sendPostRequest({
  url: '/reflux_config/crossing/query',
  param,
}, process.env.BASE_URI_WMD);

// 新增
export const addStrategyAPI = (param) => sendPostRequest({
  url: '/reflux_config/crossing/add',
  param,
}, process.env.BASE_URI_WMD);

// 编辑
export const modifyStrategyAPI = (param) => sendPostRequest({
  url: '/reflux_config/crossing/modify',
  param,
}, process.env.BASE_URI_WMD);

// 删除
export const deleteStrategyAPI = (param) => sendPostRequest({
  url: '/reflux_config/crossing/delete',
  param,
}, process.env.BASE_URI_WMD);

// 查询分拣口配置信息
export const slotConfAPI = (param) => sendPostRequest({
  url: '/query_slot_conf',
  param,
}, process.env.BASE_URI_WMD);
