import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/menu/carousel/query',
  param,
}, process.env.WGS_FRONT);

/**
 * 新增编辑
 * @param param
 * @returns {*}
 */
export const editAddConfigAPI = (param) => sendPostRequest({
  url: '/menu/carousel/modify',
  param,
}, process.env.WGS_FRONT);

/**
 * 删除
 * @param param
 * @returns {*}
 */
export const deleteAPI = (param) => sendPostRequest({
  url: '/menu/carousel/delete',
  param,
}, process.env.WGS_FRONT);
