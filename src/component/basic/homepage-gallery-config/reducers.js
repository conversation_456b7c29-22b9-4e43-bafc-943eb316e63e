import { markStatus } from 'rrc-loader-helper';
import { Message, Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { t } from '@shein-bbl/react';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import { getListAPI, editAddConfigAPI, deleteAPI } from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  imageName: '', // 图片名称
  menuStatus: [], // 状态
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  editAddModalVisible: false,
  editAddModalObj: {
    imageName: '',
    imageUrl: '',
    priority: '',
    name: '',
    menuStatus: '',
  },
  selectedRows: [],
  systemList: [], // 系统下拉框
  isEdit: false,
  imageCropperObj: { // 图片剪裁用到的参数
    crop: { // 默认选中全部
      aspect: 3 / 1,
      unit: '%',
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    },
    src: '',
    blobSrc: '', // blob:url
  },
  enabledList: [], // 状态下拉
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 修改新增编辑弹框数据
  changeEditAddModalObj(state, data) {
    Object.assign(state.editAddModalObj, data);
  },
  // 图片剪裁数据修改
  changeImageCropperObj(state, data) {
    Object.assign(state.imageCropperObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['ENABLED'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        enabledList: selectData.info.data.find((item) => item.catCode === 'ENABLED').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }
  },
  /**
   * 搜索
   */
  * search() {
    const { warehouseId } = yield 'nav';
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      warehouseId: warehouseId || 0,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        selectedRows: [],
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  /**
   * 新增编辑的提交
   * @returns
   */
  * handleModalSubmit(params = {}) {
    const { warehouseId } = yield 'nav';
    const { editAddModalObj, isEdit } = yield '';
    const { name, ...restEditAddModalObj } = editAddModalObj || {};
    markStatus('loading');
    const { code, msg } = yield editAddConfigAPI({
      ...restEditAddModalObj,
      ...params,
      warehouseId: warehouseId || 0,
    });
    if (code === '0') {
      Message.success(isEdit ? t('编辑成功') : t('新增成功'));
      yield this.changeData({
        editAddModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 删除
   * @param action
   */
  * delete(action) {
    markStatus('loading');
    const res = yield deleteAPI(action.param);
    if (res.code === '0') {
      Modal.success({ title: t('删除成功!') });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },
};
