import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Input, Message, Upload, Select,
} from 'shineout';
import { t } from '@shein-bbl/react';
import UploadPlus from '@shein-components/upload_plus';
import { uploadFileURL } from '@src/server/basic/upload';
import ImageCropper from '@shein-components/ImageCropper';
import { formdataPost } from '@src/server/common/fileFetch';
import globalStyles from '@src/component/style.less';
import styles from '../style.less';
import store from '../reducers';

class Handle extends React.Component {
  constructor(props) {
    super(props);
    this.onSelectFile = this.onSelectFile.bind(this);
    this.getImageWidth = this.getImageWidth.bind(this);
  }

  /**
   * 选择图片
   * @param options
   */
  // eslint-disable-next-line class-methods-use-this
  async onSelectFile(options) {
    const f = options.file;
    if (f) {
      const fileLink = URL.createObjectURL(options.file);
      const { width, height, unit } = await this.getImageWidth(fileLink);
      store.changeImageCropperObj({
        src: fileLink,
        crop: { // 默认选中全部
          aspect: 3 / 1,
          unit,
          x: 0,
          y: 0,
          width,
          height,
        },
      });
    }
    setTimeout(() => {
      options.onLoad({ status: 200 });
    });
  }

  // eslint-disable-next-line class-methods-use-this
  async getImageWidth(url) {
    const img = new Image();
    img.src = url;

    return new Promise((resolve) => {
      // 图片加载成功
      img.onload = () => {
        let height;
        // 固定宽度是为200
        const { width, height: realHeigt } = img;
        // ! 若展示图片宽度变化，需同步改动当前的数据
        const scaleNum = 200 / width;
        if (width / realHeigt < (3 / 1)) {
          height = width * (1 / 3) * scaleNum;
        }
        resolve({
          width: 200,
          height,
          unit: 'px',
        });
      };

      img.onerror = () => {
        resolve({
          width: 100,
          height: 100,
          unit: '%',
        });
      };
    });
  }

  render() {
    const {
      loading,
      editAddModalVisible,
      editAddModalObj,
      isEdit,
      imageCropperObj: {
        src,
        blobSrc,
        crop,
        croppedImage,
      },
      enabledList,
    } = this.props;

    // 校验编辑新增
    const validEditAdd = () => {
      const {
        imageName, imageUrl, priority, menuStatus,
      } = editAddModalObj;
      if (!imageName || (!imageUrl && !this.props.imageCropperObj.src) || !priority || !menuStatus) {
        return true;
      }
      return false;
    };

    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              editAddModalVisible: true,
              isEdit: false,
              editAddModalObj: {},
              imageCropperObj: { // 图片剪裁用到的参数
                crop: { // 默认选中全部
                  aspect: 3 / 1,
                  unit: '%',
                  x: 0,
                  y: 0,
                  width: 100,
                  height: 100,
                },
                src: '',
                blobSrc: '', // blob:url
              },
            });
          }}
        >
          {t('新增')}
        </Button>
        <Modal
          visible={editAddModalVisible}
          width={800}
          maskCloseAble={false}
          title={isEdit ? t('编辑') : t('新增')}
          onClose={() => store.changeData({
            editAddModalVisible: false,
          })}
          footer={(
            <div>
              <Button onClick={() => store.changeData({
                editAddModalVisible: false,
              })}
              >
                {t('取消')}
              </Button>
              <Modal.Submit disabled={!loading || validEditAdd()}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={120}
            labelAlign="right"
            value={editAddModalObj}
            formRef={(f) => store.changeData({ editFormRef: f })}
            onSubmit={() => {
              // 裁剪后提交
              if (this.props.imageCropperObj.src) {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', blobSrc);
                xhr.responseType = 'blob';
                xhr.onload = async () => {
                  if (xhr.status === 200) {
                    const blobData = xhr.response;
                    const formData = new FormData();
                    const randomNum = Math.floor(Math.random() * 10000);
                    const randomName = `${Date.now()}${randomNum}`;
                    formData.append('file', blobData, `${randomName}_${editAddModalObj.name}`);
                    formData.append('is_use_origin_name', 'false');
                    formData.append('is_compress', 'true');
                    const rps = await formdataPost(uploadFileURL, formData);
                    store.changeEditAddModalObj({
                      imageUrl: rps.info?.image_url,
                    });
                    store.handleModalSubmit({ imageUrl: rps.info.image_url });
                  }
                };
                xhr.send();
              } else {
                store.handleModalSubmit();
              }
            }}
            onChange={(value) => {
              store.changeData({
                editAddModalObj: value,
              });
            }}
          >
            <div>
              <Form.Item required label={t('图片名称')}>
                <Input clearable maxLength={50} delay={0} name="imageName" placeholder={t('请输入')} />
              </Form.Item>
              <Form.Item required label={t('优先级')}>
                <Input.Number
                  clearable
                  placeholder={t('请输入')}
                  min={1}
                  max={20}
                  name="priority"
                />
              </Form.Item>
              <Form.Item required label={t('图片')}>
                <Upload.Button
                  accept="image/png, image/jpeg, image/jpg"
                  onSuccess={(res, file) => {
                    store.changeEditAddModalObj({
                      name: file.name,
                    });
                    return file.name;
                  }}
                  limit={1}
                  validator={{
                    ext: (ext) => {
                      if (!['jpg', 'jpeg', 'png'].includes(ext)) {
                        Message.error(t('支持文件格式JPG、JPEG、PNG'));
                        return false;
                      }
                    },
                    size: (size) => (size > 50 * 1024 * 1024 ? new Error(t('文件需小于{}MB', 50)) : undefined),
                  }}
                  style={{ width: 100, display: 'inline-block' }}
                  request={this.onSelectFile}
                  loading={t('正在上传...')}
                  placeholder={t('选择上传图片')}
                  type="primary"
                />
                <span className={styles.tip}>{t('仅支持显示长宽比为{}的图片，支持上传后手动裁切图片至{}', '3:1', '3:1')}</span>
                <div style={{ paddingTop: '5px', overflow: 'auto' }}>
                  <ImageCropper
                    width="200px"
                    src={src}
                    crop={crop}
                    onChange={(v) => {
                      // 如果宽和高都没有，则不处理
                      if (!v?.width && !v?.height) {
                        return;
                      }
                      // 如果图片宽度是高度的3倍以上，则截取宽度为高度3倍
                      if (v.width / v.height > (3 / 1)) {
                        store.changeImageCropperObj({
                          crop: { ...v, width: v.height * (3 / 1) },
                        });
                      } else {
                        store.changeImageCropperObj({
                          crop: { ...v, height: v.width * (1 / 3) },
                        });
                      }
                    }}
                    onCropComplete={(image) => {
                      store.changeImageCropperObj({
                        croppedImage: image.base64,
                        blobSrc: image.url,
                      });
                    }}
                  />
                  {croppedImage && (
                    <img alt={t('剪裁后预览图')} style={{ maxWidth: '100%', paddingTop: '5px' }} src={croppedImage} />
                  )}
                </div>
                {/* 仅做编辑回显预览用 */}
                {isEdit && editAddModalObj.imageUrl && !this.props.imageCropperObj.src && (
                <UploadPlus
                  accept=".jpg,.jpeg,.png"
                  autoUpload
                  action={uploadFileURL}
                  fileList={editAddModalObj.imageUrl ? [{
                    imageUrl: editAddModalObj.imageUrl,
                    name: editAddModalObj.name,
                  }] : []}
                  autoUploadKeyName="file"
                  filePathKeyName="imageUrl"
                  data={{
                    is_use_origin_name: true,
                  }}
                  onFailUpload={async (_, info) => Message.error(info)}
                  onDelete={async () => {
                    store.changeEditAddModalObj({
                      imageUrl: '',
                      name: '',
                    });
                  }}
                  onSuccessUpload={async ({ file, info }) => {
                    store.changeEditAddModalObj({
                      imageUrl: info.image_url,
                      name: file.name,
                    });
                  }}
                />
                )}
              </Form.Item>
              <Form.Item required label={t('状态')}>
                <Select
                  label={t('状态')}
                  name="menuStatus"
                  keygen="dictCode"
                  format="dictCode"
                  renderItem={(w) => w.dictNameZh}
                  data={enabledList}
                  clearable
                  placeholder={t('全部')}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  editAddModalVisible: PropTypes.bool,
  editAddModalObj: PropTypes.shape(),
  isEdit: PropTypes.bool,
  imageCropperObj: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
