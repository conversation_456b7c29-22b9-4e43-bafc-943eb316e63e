import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Modal } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import ImagesPreviewer from '@shein-components/ImagesPreviewer';
import globalStyles from '@src/component/style.less';
import store from '../reducers';
import styles from '../style.less';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('ID'),
        render: 'id',
        width: 100,
      },
      {
        title: t('图片名称'),
        width: 140,
        render: 'imageName',
      },
      {
        title: t('图片'),
        width: 140,
        render: (d) => (
          <div>
            {d.imageUrl
              ? (
                <ImagesPreviewer
                  width="180px"
                  height="60px"
                  showPopover={false}
                  showRotate
                  dataSource={{
                    thumb: [d.imageUrl],
                    origin: [d.imageUrl],
                  }}
                />
              ) : (
                <span>
                  {t('无')}
                </span>
              )}
          </div>
        ),
      },
      {
        title: t('状态'),
        render: 'menuStatusName',
        width: 120,
      },
      {
        title: t('优先级'),
        render: 'priority',
        width: 80,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 160,
      },
      {
        title: t('更新人'),
        render: 'operator',
        width: 120,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 210,
        render: (record) => (
          <div>
            <Button
              size="small"
              text
              type="primary"
              className={styles.listOperationButton}
              onClick={() => {
                store.changeData({
                  editAddModalVisible: true,
                  isEdit: true,
                  editAddModalObj: {
                    id: record.id,
                    imageName: record.imageName,
                    imageUrl: record.imageUrl,
                    priority: record.priority,
                    name: record.imageUrl ? `${record.imageUrl}`.substring(`${record.imageUrl}`.lastIndexOf('/') + 1) : '',
                    menuStatus: record.menuStatus,
                  },
                  imageCropperObj: { // 图片剪裁用到的参数
                    crop: { // 默认选中全部
                      aspect: 3 / 1,
                      unit: '%',
                      x: 0,
                      y: 0,
                      width: 100,
                      height: 100,
                    },
                    src: '',
                    blobSrc: '', // blob:url
                  },
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              className={styles.listOperationButton}
              onClick={() => {
                Modal.confirm({
                  title: t('是否删除该数据?'),
                  onOk: () => {
                    store.delete({
                      param: { id: [record.id] },
                    });
                  },
                  text: { ok: t('确认'), cancel: t('取消') },
                });
              }}
            >
              {t('删除')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              className={styles.listOperationButton}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'WGS_MENU_CAROUSEL_DOCUMENT',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
