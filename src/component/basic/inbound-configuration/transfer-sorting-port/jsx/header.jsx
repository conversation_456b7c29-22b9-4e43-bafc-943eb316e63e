import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      outSubWarehouseList, // 出库子仓下拉数据
      storageTypeList, // 存储属性
      orderTypeList, // 单据类型
      orderSubTypeList, // 单据子类型
      slotList, // 状态
      upperParkTypeList, // 上架园区 下拉
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 80 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('出库子仓')}
            name="outSubWarehouseId"
            data={outSubWarehouseList}
            keygen="id"
            format="id"
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('上架园区')}
            name="upperParkType"
            data={upperParkTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('单据类型')}
            name="billType"
            data={orderTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
          />
          <Select
            label={t('单据子类型')}
            name="billSubType"
            data={orderSubTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
          />
          <Select
            label={t('存储属性')}
            name="storeTypes"
            data={storageTypeList}
            keygen="id"
            format="id"
            placeholder={t('全部')}
            renderItem="name"
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
          />
          <Select
            label={t('分拣口')}
            name="sortPort"
            data={slotList}
            keygen="dictNameZh"
            format="dictNameZh"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  outSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  storageTypeList: PropTypes.arrayOf(PropTypes.shape()),
  orderTypeList: PropTypes.arrayOf(PropTypes.shape()),
  orderSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
  slotList: PropTypes.arrayOf(PropTypes.shape()),
  upperParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
