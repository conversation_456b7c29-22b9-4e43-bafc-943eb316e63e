import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Popover } from 'shineout';
import OperationModal from '@public-component/modal/operation-modal';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouse',
        width: 160,
      },
      {
        title: t('出库子仓'),
        width: 100,
        render: (row) => (
          <span
            style={{ color: 'rgba(51, 153, 255,1)', textDecorationLine: 'underline', cursor: 'pointer' }}
            onClick={() => { store.editData(row); }}
          >
            {row.outSubWarehouse}
          </span>
        ),
      },
      {
        title: t('上架园区'),
        render: 'upperParkTypeName',
        width: 160,
      },
      {
        title: t('单据类型'),
        render: 'billTypeName',
        width: 120,
      },
      {
        title: t('单据子类型'),
        render: 'billSubTypeName',
        width: 120,
      },
      {
        title: t('存储属性'),
        render: 'storeTypesName',
        width: 140,
      },
      {
        title: t('分拣口'),
        render: 'sortPort',
        width: 120,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 140,
        render: (record) => (
          <div>
            <Button
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作查询')}
            </Button>
            <Button
              size="small"
              type="primary"
              text
              style={{ marginLeft: 8 }}
            >
              <Popover.Confirm
                onOk={() => store.delData({ id: record.id })}
                type="warning"
                okType="primary"
              >
                {t('是否确定删除？')}
              </Popover.Confirm>
              {t('删除')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'SORT_PORT_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
};

export default List;
