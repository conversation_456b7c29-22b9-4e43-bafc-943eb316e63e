import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Select,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store from '../reducers';

// 校验弹窗数据
const validSubmit = (obj = {}) => {
  const singleKeyList = ['billType', 'outSubWarehouseId', 'upperParkType'];
  // 判断单选与多选是否都有值：单据子类型非必填
  const validSuccess = singleKeyList.every((key) => obj[key] || obj[key] === 0)
    && ['storeTypes'].every((key) => obj[key] && obj[key].length);
  return validSuccess;
};
class Handle extends React.Component {
  render() {
    const {
      loading,
      modalVisible,
      configData,
      outSubWarehouseListModal,
      storageTypeList,
      orderTypeList,
      orderSubTypeList,
      slotList,
      modalLoading,
      upperParkTypeList,
    } = this.props;

    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.addData();
          }}
        >
          {t('新增')}
        </Button>
        <Modal
          maskCloseAble={false}
          visible={modalVisible}
          width={600}
          title={modalVisible === 1 ? t('新增') : t('编辑')}
          onClose={() => { store.changeData({ modalVisible: 0 }); }}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ modalVisible: 0 })}>{t('取消')}</Button>
              <Modal.Submit disabled={!validSubmit(configData) || !modalLoading}>{t('确定')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={150}
            value={configData}
            labelAlign="right"
            style={{ maxWidth: 400 }}
            onSubmit={(data) => {
              store.submitConfigData(data);
            }}
          >
            <Form.Item label={t('出库子仓')} required>
              <Select
                disabled={modalVisible !== 1}
                data-bind="configData.outSubWarehouseId"
                data={outSubWarehouseListModal}
                keygen="id"
                format="id"
                renderItem="nameZh"
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                placeholder={t('请选择')}
              />
            </Form.Item>
            <Form.Item label={t('上架园区')} required>
              <Select
                data-bind="configData.upperParkType"
                data={upperParkTypeList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                placeholder={t('请选择')}
              />
            </Form.Item>
            <Form.Item label={t('单据类型')} required>
              <Select
                data-bind="configData.billType"
                data={orderTypeList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                placeholder={t('请选择')}
              />
            </Form.Item>
            <Form.Item label={t('单据子类型')}>
              <Select
                clearable
                data-bind="configData.billSubType"
                data={orderSubTypeList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                placeholder={t('请选择')}
              />
            </Form.Item>
            <Form.Item label={t('存储属性')} required>
              <Select
                data-bind="configData.storeTypes"
                data={storageTypeList}
                keygen="id"
                format="id"
                renderItem="name"
                onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                multiple
                compressed
                placeholder={t('请选择')}
              />
            </Form.Item>
            <Form.Item label={t('分拣口')} required>
              <Select
                data-bind="configData.sortPort"
                data={slotList}
                keygen="dictNameZh"
                format="dictNameZh"
                renderItem="dictNameZh"
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                placeholder={t('请选择')}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalVisible: PropTypes.number,
  configData: PropTypes.shape(),
  outSubWarehouseListModal: PropTypes.arrayOf(PropTypes.shape()),
  storageTypeList: PropTypes.arrayOf(PropTypes.shape()),
  orderTypeList: PropTypes.arrayOf(PropTypes.shape()),
  orderSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
  slotList: PropTypes.arrayOf(PropTypes.shape()),
  modalLoading: PropTypes.number,
  upperParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
