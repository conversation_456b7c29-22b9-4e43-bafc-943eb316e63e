import { sendPostRequest } from '@src/server/common/public';

// 查询列表数据
export const getList = (param) => sendPostRequest({
  url: '/sort_port/query',
  param,
}, process.env.WWS_URI);

// 新增
export const addData = (param) => sendPostRequest({
  url: '/sort_port/add',
  param,
}, process.env.WWS_URI);

// 编辑
export const editData = (param) => sendPostRequest({
  url: '/sort_port/update',
  param,
}, process.env.WWS_URI);

// 删除
export const removeData = (param) => sendPostRequest({
  url: '/sort_port/remove',
  param,
}, process.env.WWS_URI);
