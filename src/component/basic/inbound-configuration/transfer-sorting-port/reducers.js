import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Message, Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect, getSubWarehouseSelectList, getStoreAttr } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import {
  getList, addData, editData, removeData,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  outSubWarehouseId: [], // 出库子仓,
  billType: [], // 单据类型,
  billSubType: [], // 单据子类型,
  storeTypes: [], // 存储属性,
  sortPort: [], // 分拣口,
  upperParkType: [], // 上架园区
};

export const defaultConfigData = {
  billSubType: '', // 单据子类型
  billType: '', // 单据类型
  outSubWarehouseId: '', // 出库子仓id
  sortPort: '', // 分拣口名称
  storeTypes: [], // 存储属性
  upperParkType: '', // 上架园区
};

const defaultState = {
  warehouseIds: '',
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  configData: defaultConfigData,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  modalVisible: 0,
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  //* 搜索数据
  outSubWarehouseList: [], // 出库子仓下拉数据[全自动化子仓]
  storageTypeList: [], // 存储属性
  orderTypeList: [], // 单据类型
  orderSubTypeList: [], // 单据子类型
  slotList: [], // 分拣口下拉
  // 弹窗下拉
  outSubWarehouseListModal: [], // 出库子仓
  modalLoading: 1,
  upperParkTypeList: [], // 上架园区 下拉
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const [selectData, attrListData] = yield Promise.all([
      dictSelect({ catCode: ['WL_ORDER_TYPE', 'SubType', 'WL_AUTO_SLOT', 'PARK_ENUM'] }),
      getStoreAttr({ enabled: 1 }),
    ]);
    if (selectData.code === '0' && attrListData.code === '0') {
      yield this.changeData({
        orderTypeList: selectData.info.data.find((item) => item.catCode === 'WL_ORDER_TYPE').dictListRsps,
        orderSubTypeList: selectData.info.data.find((item) => item.catCode === 'SubType').dictListRsps,
        slotList: selectData.info.data.find((item) => item.catCode === 'WL_AUTO_SLOT').dictListRsps,
        upperParkTypeList: selectData.info.data.find((item) => item.catCode === 'PARK_ENUM').dictListRsps,
        storageTypeList: attrListData.info.data,
      });
    } else {
      handleListMsg([selectData, attrListData]);
    }
    const { warehouseId } = yield 'nav';
    const { warehouseIds } = this.state;
    if (warehouseIds !== warehouseId) {
      yield this.warehouseChange({ warehouseId });
    }
  },
  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { warehouseId } = data;
    yield this.changeData({
      warehouseIds: warehouseId,
      outSubWarehouseList: [],
      outSubWarehouseListModal: [],
      limit: {
        ...this.state.limit,
        outSubWarehouseId: [],
      },
    });
    // 右上角仓库改变，且有仓库值时，获取页面出库子仓下拉数据
    if (warehouseId) {
      yield this.querySubWarehouse({ warehouseId, type: 1 });
    }
  },
  // 搜索
  * search() {
    const { warehouseId } = yield 'nav';
    const { limit, pageInfo } = this.state;
    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getList(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = this.state;
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  * addData() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请先在右上角选择仓库') });
      return;
    }
    yield this.changeData({
      modalVisible: 1,
      configData: {
        ...defaultConfigData,
        warehouseId,
      },
    });
  },
  * editData(row) {
    const { warehouseId, id } = row;
    if (!warehouseId) {
      Modal.error({ title: t('当前编辑的数据仓库为空！') });
      return;
    }
    // 获取编辑仓库对应上架子仓和出库子仓下拉数据
    // 编辑时，出库子仓不可编辑，就不需要获取下拉值
    yield this.querySubWarehouse({ warehouseId, type: 2 });
    yield this.changeData({
      modalVisible: 2,
      configData: {
        id,
        warehouseId, // 仓库ID
        billSubType: row.billSubType,
        billType: row.billType,
        outSubWarehouseId: row.outSubWarehouseId,
        sortPort: row.sortPort,
        storeTypes: row.storeTypes,
        upperParkType: row.upperParkType, // 上架园区
      },
    });
  },
  // 新增/编辑提交
  * submitConfigData() {
    const { configData, modalVisible } = this.state;
    // 编辑时，单据子类型或分拣口为空，后端要传0和''
    if (modalVisible === 2) {
      if (!configData.billSubType) {
        configData.billSubType = 0;
      }
      if (!configData.sortPort) {
        configData.sortPort = '';
      }
    }
    markStatus('modalLoading');
    // eslint-disable-next-line max-len
    const { code, msg } = modalVisible === 1 ? yield addData(configData) : yield editData(configData);
    if (code === '0') {
      yield this.changeData({
        modalVisible: 0,
      });
      Message.success(modalVisible === 1 ? t('新增成功') : t('编辑成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 删除
  * delData(data) {
    markStatus('loading');
    const { code, msg } = yield removeData(data);
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取子仓下拉数据：编辑弹窗和出库子仓
  * querySubWarehouse({ warehouseId, type }) {
    // type: 1-页面出库子仓，2-弹窗出库子仓，【不传则默认弹窗上架子仓】
    const param = { warehouseId, enabled: 1 };
    // 出库子仓要全自动化子仓数据（已删除）
    // if (type === 1 || type === 2) {
    //   param.subWarehouseTypeList = [8];
    // }
    const { code, info, msg } = yield getSubWarehouseSelectList(param);
    if (code === '0') {
      let key = '';
      if (type === 1) {
        key = 'outSubWarehouseList';
      } else if (type === 2) {
        key = 'outSubWarehouseListModal';
      }
      yield this.changeData({
        [key]: info.data || [],
      });
      // 如果是获取页面出库子仓，则代表是刚进页面或切换右上角仓库；此时也给弹窗出库子仓赋值，用于新增
      if (key === 'outSubWarehouseList') {
        yield this.changeData({
          outSubWarehouseListModal: info.data || [],
        });
      }
    } else {
      Modal.error({ title: msg });
    }
  },
};
