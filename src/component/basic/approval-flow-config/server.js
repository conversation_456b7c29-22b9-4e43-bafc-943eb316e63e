import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/approval_process/query',
  param,
}, process.env.WGS_FRONT);

/**
 * 新增
 * @param {*} param
 * @returns
 */
export const addAPI = (param) => sendPostRequest({
  url: '/approval_process/insert',
  param,
}, process.env.WGS_FRONT);

/**
 * 编辑
 * @param {*} param
 * @returns
 */
export const editAPI = (param) => sendPostRequest({
  url: '/approval_process/modify',
  param,
}, process.env.WGS_FRONT);

/**
 * 调拨计划规则弹框搜索
 * @param param
 */
export const queryListAPI = (param) => sendPostRequest({
  url: '/allocation_config/query_list',
  param,
}, process.env.WTS_FRONT);

/**
 * 查询调拨单是否可用
 * @param {*} param
 * @returns
 */
export const checkAllocationConfigAPI = (param) => sendPostRequest({
  url: '/approval_process/checkAllocationConfig',
  param,
}, process.env.WGS_FRONT);

/**
 * 删除审批流配置
 * @param {*} param
 * @returns
 */
export const deleteApi = (param) => sendPostRequest({
  url: '/approval_process/delete',
  param,
}, process.env.WGS_FRONT);
