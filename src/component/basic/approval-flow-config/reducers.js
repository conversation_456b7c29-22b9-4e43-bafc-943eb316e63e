import React from 'react';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg, queryParkList, fliterSubwarehouse } from '@src/lib/dealFunc';
import { getPermissionSubWarehouse } from '@src/component/nav/server';
import error from '@src/source/audio/delete.mp3';
import {
  getListAPI,
  addAPI,
  editAPI,
  queryListAPI,
  checkAllocationConfigAPI,
  deleteApi,
} from './server';
// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  approvalProcessName: '', // 流程名称
  approvalType: [], // 审批类型
  parkId: '', // 园区
  subWarehouseIds: [], // 子仓
  enableStatus: '', // 启用状态
  isPush: '', // 是否开启待审批提醒推送
};

// 发出异常声音
const audio = new Audio(error);
audio.load();

// 弹窗表单数据
export const defaultModalInfo = {
  approvalProcessCode: '', // 流程编码
  approvalProcessName: '', // 流程名称
  warehouseId: '', // 仓库
  approvalType: '', // 审批类型
  enableStatus: 1, // 启用状态
  needConfirm: 1, // 是否本人确认
  approverList: [], // 审批人
  parkId: '', // 园区
  subWarehouseId: '', // 子仓
  isPush: 0, // 是否开启待审批提醒推送
  pushTimeList: [], // 微信推送时间
};

export const defaultModalLimit = {
  confNo: '',
  destSubWarehouseIdList: [],
  destWarehouseIdList: [],
  srcSubWarehouseIdList: [],
  srcWarehouseIdList: [],
  pageNum: 1,
  pageSize: 50,
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  approvalTypeList: [], // 审批类型 下拉
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  selectedRows: [], // 勾选的数据
  modalFormRef: {}, // 用于校验弹窗内的表单项
  modalType: '', // 弹窗类型 1:新增 0:编辑
  modalInfo: defaultModalInfo,
  warehouseList: [], // 仓库 下拉
  enableTypeList: [// 启用状态
    { dictCode: 1, dictNameZh: t('启用') },
    { dictCode: 0, dictNameZh: t('禁用') },
  ],
  needTypeList: [// 是否本人确认
    { dictCode: 1, dictNameZh: t('需要') },
    { dictCode: 0, dictNameZh: t('不需要') },
  ],
  statusList: [ // 状态下拉
    { dictNameZh: t('启用'), dictCode: true },
    { dictNameZh: t('禁用'), dictCode: false },
  ],
  currentApprover: [''], // 弹窗 审批人
  canAdd: false,
  canDel: false,
  subWarehouseList: [],
  parkList: [],
  modalLimit: defaultModalLimit,
  modalPageSizeList: [20, 50, 100], // 表格页显示条数
  modalList: [],
  modalCount: 0,
  modalRecord: [],
  cacheModalRecord: [],
  cacheRecord: [],
  selectModalVisible: false,
  yesOrNoList: [],
  pushTimeList: [], // 微信推送时间
};

// 是否类型是调拨单
export const isAllocation = 2;

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  changeModalnfoData(state, data) {
    Object.assign(state.modalInfo, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['GENERAL_APPROVE_TYPE', 'APPROVAL_PROCESS_IS_PUSH', 'APPROVAL_PROCESS_PUSH_TIMES'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        approvalTypeList: selectData.info.data.find((item) => item.catCode === 'GENERAL_APPROVE_TYPE').dictListRsps,
        yesOrNoList: selectData.info.data.find((item) => item.catCode === 'APPROVAL_PROCESS_IS_PUSH').dictListRsps,
        pushTimeList: selectData.info.data.find((item) => item.catCode === 'APPROVAL_PROCESS_PUSH_TIMES').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }
    const { warehouseList, warehouseId } = yield 'nav';
    yield this.changeData({
      warehouseList,
    });
    yield this.warehouseChange({
      warehouseId,
    });
  },

  /**
   * 搜索
   */
  * search() {
    // 获取仓库
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  /**
   * 打开弹窗
   * @returns
   */
  * openModal(modalType) {
    // 请求弹窗数据 + 编辑赋值
    const { selectedRows } = yield '';
    if (modalType === 0) {
      yield this.changeData({
        modalInfo: {
          ...selectedRows[0],
          enableStatus: selectedRows[0].enableStatus ? 1 : 0,
          needConfirm: selectedRows[0].needConfirm ? 1 : 0,
        },
        currentApprover: selectedRows[0].approverList,
        canAdd: !selectedRows[0].approverList.some((item) => item === ''),
        canDel: true,
      });
      if (selectedRows[0].approvalType === isAllocation) {
        yield this.changeData({
          cacheRecord: selectedRows[0].confNo.map((v) => ({
            confNo: v,
          })),
        });
      }
      if (selectedRows[0]?.warehouseId) {
        const { parkList } = yield queryParkList(selectedRows[0].warehouseId);
        yield this.changeData({
          modalParkList: parkList,
          modalSubWarehouseList: [],
        });
      }
      if (!['', undefined, null].includes(selectedRows[0]?.parkId)) {
        const { parkSubWarehouseList } = fliterSubwarehouse([selectedRows[0].parkId]);
        yield this.changeData({
          modalSubWarehouseList: parkSubWarehouseList,
        });
      }
    }

    yield this.changeData({
      modalType,
    });
  },

  * deleteData() {
    const { selectedRows } = yield '';
    const param = selectedRows.map((v) => v.id);
    markStatus('loading');
    const { code, msg } = yield deleteApi({ ids: param });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * @description: 审批人 增改删
   * @param {string} current 用户输入
   * @param {number} index 第几组
   * @param {string} type  add/edit 新增组/修改组
   * @returns {varType}
   */
  * changeApproverList({ current, index, type }) {
    const { currentApprover } = yield '';
    const list = currentApprover.slice();
    let canDel = true;
    let canAdd = false;

    switch (type) {
      case 'add':
        list.splice(index, 0, '');
        break;
      case 'change':
        list.splice(index, 1, current);
        canAdd = !list.some((item) => !item);
        break;
      case 'delete':
        list.splice(index, 1);
        canAdd = !list.some((item) => !item);
        break;
      default:
        throw new Error(`unknown param type: ${type}`);
    }
    // 上限为10
    if (list.length === 10) {
      canAdd = false;
    }
    if (list.length === 1) {
      canDel = false;
    }
    yield this.changeData({
      currentApprover: list,
      canAdd,
      canDel,
    });
  },

  /**
   * 关闭弹窗-清空数据及表单验证
   * @returns
   */
  * closeModal() {
    const { modalFormRef } = yield '';
    yield this.changeData({
      modalType: '',
      modalInfo: defaultModalInfo,
      currentApprover: [''],
    });
    if (modalFormRef && modalFormRef.clearValidate) modalFormRef.clearValidate();
  },

  /**
   * 保存
   * @returns
   */
  * saveData() {
    const {
      modalInfo, modalType, currentApprover, cacheRecord,
    } = yield '';
    const {
      approvalProcessCode, approvalProcessName, warehouseId, approvalType, enableStatus, needConfirm, parkId, subWarehouseId, isPush, pushTimeList,
    } = modalInfo;
    let param = {
      approvalProcessCode: approvalProcessCode.trim(),
      approvalProcessName: approvalProcessName.trim(),
      approvalType,
      enableStatus,
      approverList: currentApprover,
      id: modalType ? undefined : modalInfo.id,
      isPush,
      pushTimeList,
    };
    // 是否推送=否时、推送时间为空
    if (isPush === 0) {
      param.pushTimeList = [];
    }
    if (approvalType === isAllocation) {
      param = {
        ...param,
        confNoList: cacheRecord.map((v) => v.confNo),
      };
    } else {
      param = {
        ...param,
        warehouseId,
        needConfirm,
        parkId: parkId || 0,
        subWarehouseId: subWarehouseId || 0,
      };
    }
    markStatus('loading');
    const API = modalType ? addAPI : editAPI;
    const { code, msg } = yield API(param);
    if (code === '0') {
      Message.success(modalType ? t('新增成功') : t('编辑成功！'));
      yield this.changeData({
        cacheRecord: [],
      });
      yield this.closeModal();
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 获取弹窗 园区、子仓数据
   */
  * getModalParkList(warehouseId) {
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      modalParkList: parkList,
      modalSubWarehouseList: [],
    });
  },
  * warehouseChange({ warehouseId }) {
    yield this.changeLimitData({
      parkId: '', // 园区
      subWarehouseIds: [], // 子仓
    });
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      subWarehouseList: [],
      parkList,
    });
  },
  * getSubWarehouse({ warehouseId, isSrc }) {
    markStatus('loading');
    const res = yield getPermissionSubWarehouse({ warehouseId, enabled: 1 });
    if (res.code === '0') {
      if (isSrc) {
        yield this.changeData({
          modalSrcSubWarehouseList: res.info.data,
        });
      } else {
        yield this.changeData({
          modalDestSubWarehouseList: res.info.data,
        });
      }
    } else {
      Modal.error({
        title: res.msg,
        autoFocusButton: 'ok',
      });
    }
  },
  * modalSearch() {
    const { modalLimit, cacheRecord } = yield '';
    markStatus('loading');
    const res = yield queryListAPI(modalLimit);
    if (res.code === '0') {
      const modalRecord = res.info.data.filter((v) => cacheRecord.map((item) => item.confNo).includes(v.confNo));
      yield this.changeData({
        modalList: res.info.data,
        modalCount: res.info.meta.count,
        modalRecord,
        cacheModalRecord: modalRecord,
      });
    } else {
      Modal.error({ title: res.msg || t('后台数据出错') });
    }
  },
  // 页签改变
  * modalHandlePaginationChange(arg = {}) {
    const { modalLimit } = yield '';
    yield this.changeData({
      modalLimit: {
        ...modalLimit,
        ...arg,
      },
    });
    yield this.modalSearch();
  },
  * clearModalSearchData() {
    yield this.changeData({
      modalLimit: defaultModalLimit,
      modalPageSizeList: [20, 50, 100], // 表格页显示条数
      modalList: [],
      modalCount: 0,
      modalRecord: [],
      modalSrcSubWarehouseList: [],
      modalDestSubWarehouseList: [],
    });
  },
  * checkAllocationConfig() {
    const {
      modalType, modalInfo, cacheRecord,
    } = yield '';
    const param = {
      confNoList: cacheRecord.map((v) => v.confNo),
    };
    if (!modalType) {
      param.approvalProcessId = modalInfo.id;
      param.id = modalInfo.id;
    }
    const res = yield checkAllocationConfigAPI(param);
    if (res.code === '0') {
      if (res.info?.length) {
        audio.play();
        Modal.error({
          title:
  <div>
    <div>{t('调拨计划规则已被其他审批流程关联，提交失败！')}</div>
    <div style={{ maxHeight: '250px', overflow: 'auto' }}>
      {
              res.info.map((v) => (<div>{v}</div>))
            }
    </div>
  </div>,
        });
      } else {
        yield this.changeData({
          selectModalVisible: false,
        });
        yield this.clearModalSearchData();
      }
    } else {
      Modal.error({ title: res.msg || t('后台数据出错') });
    }
  },
};
