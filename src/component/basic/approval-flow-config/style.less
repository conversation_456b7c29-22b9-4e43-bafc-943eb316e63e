/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.handleExportButton {
    margin-right: 6px;
}


/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect {
    width: 140px;
}

.headerUnmatchedText {
    color: #bbb;
}

.headerSearchTimeLine {
    display: flex;
}

/* list.jsx
---------------------------------------------------------------- */
.listOperationButton {
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}

.listSection {
    height: 0px;
    /* 必写为了子domheight - 100%有效 */
    flex: 1;
}


.modalBox {
    padding-top: 10px;
}

.itemTitle {
    margin-bottom: 10px;
    position: relative;
    padding-left: 12px;
}

.itemTitle:before {
    content: '';
    position: absolute;
    width: 3px;
    height: 18px;
    background: #197afa;
    border-radius: 2px;
    left: 0;
}

.itemTitle:after {
    content: '';
    position: absolute;
    width: 100px;
    height: 2px;
    border-radius: 2px;
    width: 900px;
    height: 2px;
    right: 0;
    top: 10px;
    background-color: #197afaa3;
}


.rightDown {
    position: fixed;
    bottom: 5px;
    right: 5px;
    text-align: right;
    margin-top: 10px;
}

/*
* search header
*/
/*列表页搜索栏目样式*/
.header_wrap_view {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding-top: 10px;
}

/*详情,查看,新增页面样式*/
.header_wrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: 20px 10px 10px 10px;
    border: 1px solid #ccc;
}

.detail_title {
    background-color: #efefef;
    height: 32px;
    line-height: 32px;
    padding-left: 5px;
    border: 1px solid #ccc;
    margin-bottom: -1px;
    border-radius: 5px 5px 0 0;
    color: #337ab7;
}

/*详情页面样式*/
.inner_detail {
    margin-bottom: 10px;
    margin-right: 10px;
    line-height: 32px;
}

/*列表页搜索栏*/
.inner_list {
    margin-bottom: 10px;
    line-height: 32px;
}

/*新增跟修改页面*/
.inner_add {
    line-height: 32px;
    margin-right: 10px;
}

/*专门给预设资料用的*/
.inner {
    margin-bottom: 10px;
    line-height: 32px;
    margin-right: 10px;
}

.title {
    font-size: 16px;
}

.lab {
    margin-right: 5px;
    display: inline-flex;
    justify-content: flex-end;
    vertical-align: top;
}

.labWidth {
    margin-right: 5px;
    min-width: 110px;
    display: inline-flex;
    justify-content: flex-end;
    vertical-align: top;
}

/*解决form表单内select,input宽度bug*/
form .inputWidth {
    width: 250px;
}

form .inputMaterialWidth {
    width: 250px;
}

.inputWidth {
    width: 250px;
}

/*详情页面备注样式*/
.remarkCon {
    display: inline-flex;
    line-height: 16px;
    width: 250px;
    word-wrap: break-word;
    word-break: break-all;
}

.remarkConLarge {
    display: inline-flex;
    line-height: 16px;
    width: 260px;
    word-wrap: break-word;
    word-break: break-all;
}

/*预设资料列表页一排按钮的样式*/

.buttonItemWrap {

}

.buttonItem {
    margin-right: 10px;
    margin-bottom: 10px;
}

/*是否再次新增*/
.spanBox {
    display: inline-block;
    line-height: 30px;
}

/*
* table
*/
.rightDown {
    text-align: right;
    margin-top: 10px;
}

/*
* index css
*/

.h2 {
    font-size: 30px;
    text-align: center;
    color: #00345c;
    text-shadow: 4px 4px 10px #aaa;
}

.warnMsg {
    text-align: right;
    color: #f00;
    line-height: 20px;
    height: 20px;
    margin-right: 10px;
}

.redStar {
    color: #f00;
    margin-left: 5px;
}

.backgroundStar {
    color: #fff;
    margin-left: 5px;
}

/*组件内部样式*/


/*备注*/
.remark {
    display: block;
    width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0 auto;
}

.wrap {
}

.checkAll {
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
}

.checkValue {

}

.modelError {
    width: 200px;
}

.spinStyle {
    display: none;
    position: absolute;
    left: 50%;
    top: calc(50% + 44px);
    transform: translate(-50%, -50%);
    z-index: 999;
}

/*listImage*/

.head {
    position: relative;
    text-align: center;
    display: flex;
    padding: 5px 0;
}

.bigImg {
    flex: 1;
    overflow: auto;
}

.zoom {
    position: absolute;
    width: 80px;
    height: 30px;
    background-color: #000;
    opacity: 0.8;
    left: 50%;
    margin-left: -40px;
    bottom: 8px;
}

.zoom span {
    width: 20px;
    height: 20px;
    display: inline-block;
    margin: 5px 0;
    cursor: pointer;
}

.head img {
    width: 300px;
    vertical-align: middle;
    cursor: move;
}

.icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
}

.pic {
    margin-top: 10px;
    padding: 5px;
    background-color: #27282b;
}

.pic img {
    width: 55px;
    height: 55px;
    object-fit: cover;
    margin-right: 5px;
}

.pic .on {
    width: 65px;
    height: 65px;
}


.imgModal {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: hidden;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99999;
}

.imgModal > div {
    position: absolute;
    background-color: #ffffff;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

.uploadBox {
    display: inline-block;
    border-radius: 4px;
    min-width: 250px;
    height: 30px;
    line-height: 30px;
    padding-left: 3px;
    padding-right: 3px;
    vertical-align: middle;
    border: 1px solid rgb(217, 217, 217);
    margin-right: 5px;
    cursor: pointer;
}

.flexItem {
    display: flex;
    justify-content: center;
}

.focusInput {

}

.tagSpan {
    display: inline-block;
    margin-right: 10px;
    padding: 2px 5px;
    border-radius: 4px;
    cursor: pointer;
    line-height: 22px;
    margin-bottom: 10px;
    border: 1px dashed #999;
}

.addPrintInput, .onlyPrintInput {
    white-space: normal;
}

.compileModalCont {
    padding-top: 10px;
}

.compileModalCont > div {
    margin-bottom: 10px;
}

.compileModalCont > div > span {
    vertical-align: middle;
}

.handle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.required:before {
    margin-right: 4px;
    color: #ff4d50;
    content: "*";
    font-family: SimSun;
}

.lineHeight{
    line-height: 29px;
}

.inputWidth{
    width: 220px;
}

.modalHeader {
  display: flex;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.inputWrap {
  display: flex;
  margin-right: 10px;
  margin-bottom: 10px;
}

.modalInupt {
  width: 100px;
}

.modalLab {
  height: 32px;
  line-height: 32px;
  padding-right: 5px;
}

.rightDown {
  text-align: right
}


.modalImportFileLabel {
  line-height: 45px;
  margin-right: 15px;
  width: 70px;
}

.modalTip {
  color: gray;
  margin: -10px 0 0 75px;
}
  