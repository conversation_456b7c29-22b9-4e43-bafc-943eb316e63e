import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('流程编码'),
        width: 120,
        render: (record) => (
          <div
            style={{ color: 'blue', cursor: 'pointer' }}
            onClick={() => {
              store.changeData({
                selectedRows: [record],
              });
              store.openModal(0);
            }}
          >
            {record.approvalProcessCode}
          </div>
        ),
      },
      {
        title: t('流程名称'),
        render: 'approvalProcessName',
        width: 120,
      },
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 120,
      },
      {
        title: t('园区'),
        width: 150,
        render: 'parkName',
      },
      {
        title: t('子仓'),
        width: 140,
        render: 'subWarehouseName',
      },
      {
        title: t('审批类型'),
        render: 'approvalTypeName',
        width: 120,
      },
      {
        title: t('是否开启待审批提醒推送'),
        render: 'isPushName',
        width: 140,
      },
      {
        title: t('推送时间'),
        render: 'pushTimes',
        width: 120,
      },
      {
        title: t('状态'),
        render: 'enableStatusName',
        width: 80,
      },
      {
        title: t('创建人'),
        render: 'creator',
        width: 180,
      },
      {
        title: t('创建时间'),
        render: 'createTime',
        width: 180,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 80,
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              className: styles.pagination,
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'WGS_GENERAL_APPROVAL_PROCESS',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
};

export default List;
