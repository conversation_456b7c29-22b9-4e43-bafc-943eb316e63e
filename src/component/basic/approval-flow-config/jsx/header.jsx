import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Input, Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      approvalTypeList,
      parkList,
      subWarehouseList,
      statusList,
      yesOrNoList,
    } = this.props;

    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('流程名称'), t('审批类型')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Input label={t('流程名称')} name="approvalProcessName" placeholder={t('请输入')} />
          <Select
            label={t('审批类型')}
            name="approvalType"
            data={approvalTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />

          <Select
            label={t('园区')}
            name="parkId"
            data={parkList}
            keygen="parkType"
            format="parkType"
            placeholder={t('请选择')}
            renderItem="parkName"
            clearable
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(val) => {
              if (!['', undefined, null].includes(val)) {
                const { parkSubWarehouseList } = fliterSubwarehouse([val]);
                store.changeData({
                  subWarehouseList: parkSubWarehouseList,
                });
              }
              store.changeLimitData({
                parkId: val,
                subWarehouseIds: [],
              });
            }}
          />
          <Select
            label={t('子仓')}
            name="subWarehouseIds"
            data={subWarehouseList}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('状态')}
            name="enableStatus"
            data={statusList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('是否开启待审批提醒推送')}
            name="isPush"
            data={yesOrNoList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  approvalTypeList: PropTypes.arrayOf(PropTypes.shape()),
  parkList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  yesOrNoList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
