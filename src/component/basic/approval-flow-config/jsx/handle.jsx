import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Input, Modal, Select, Radio, Table, Popover, Rule,
} from 'shineout';
import Icon from '@shein-components/Icon';
import FilterSearchSelect from '@src/component/public-component/select/filter-search-select';
import { t } from '@shein-bbl/react';
import globalStyle from '@src/component/style.less';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import styles from '../style.less';
import store, { isAllocation } from '../reducers';

const rules = new Rule({});

const columns = [
  {
    title: t('调拨计划规则编码'),
    render: 'confNo',
    width: 140,
  },
  {
    title: t('出发仓'),
    render: 'srcWarehouseName',
    width: 70,
  },
  {
    title: t('出发子仓'),
    render: 'srcSubWarehouseName',
    width: 70,
  },
  {
    title: t('到达仓'),
    render: 'destWarehouseName',
    width: 80,
  },
  {
    title: t('到达子仓'),
    render: 'destSubWarehouseName',
    width: 120,
  },
  {
    title: t('优先级'),
    render: 'priorityName',
    width: 120,
  },
  {
    title: t('状态'),
    render: 'statusName',
    width: 120,
  },
];

class Handle extends React.Component {
  render() {
    const {
      loading,
      modalType,
      modalInfo,
      warehouseList,
      enableTypeList,
      needTypeList,
      approvalTypeList,
      currentApprover,
      canAdd,
      canDel,
      modalParkList,
      modalSubWarehouseList,
      selectModalVisible,
      modalRecord,
      modalLimit,
      modalSrcSubWarehouseList,
      modalDestSubWarehouseList,
      modalList,
      modalPageSizeList,
      modalCount,
      cacheRecord,
      cacheModalRecord,
      selectedRows,
      yesOrNoList,
      pushTimeList,
    } = this.props;

    // 展示调拨计划规则编码
    const showConfNo = () => {
      if (cacheRecord.length) {
        if (cacheRecord.length === 1) {
          return cacheRecord[0].confNo;
        }
        return `${cacheRecord[0].confNo}...`;
      }
      return t('请选择');
    };

    const validate = () => {
      const {
        approvalProcessCode, approvalProcessName, warehouseId, approvalType,
      } = modalInfo;
      if (!approvalProcessCode || !approvalProcessName || !approvalType) {
        return true;
      }
      if (approvalType === isAllocation) {
        if (!cacheRecord.length) {
          return true;
        }
      } else if (!warehouseId) {
        return true;
      }
      if (currentApprover.some((item) => !item)) {
        return true;
      }
      return false;
    };

    // 调拨规则计划弹窗判断新增调拨单号 record 本次选择的数据
    const addCacheRecord = (record) => {
      const modalRecordConfNoList = modalRecord.map((v) => v.confNo);
      const newCacheRecord = [
        ...cacheRecord,
      ];
      while (record.find((v) => !modalRecordConfNoList.includes(v.confNo))) {
        const { confNo } = record.find((v) => !modalRecordConfNoList.includes(v.confNo));
        newCacheRecord.push({ confNo });
        modalRecordConfNoList.push(confNo);
      }
      store.changeData({
        cacheRecord: newCacheRecord,
        cacheModalRecord: record,
      });
    };

    // 调拨规则计划弹窗判断减少调拨单号 record 本次选择的数据
    const delCacheRecord = (record) => {
      const recordConfNoList = record.map((v) => v.confNo);
      let newCacheModalRecord = [
        ...cacheModalRecord,
      ];
      let newCacheRecord = [
        ...cacheRecord,
      ];
      while (newCacheModalRecord.find((v) => !recordConfNoList.includes(v.confNo))) {
        const { confNo } = newCacheModalRecord.find((v) => !recordConfNoList.includes(v.confNo));
        newCacheModalRecord = newCacheModalRecord.filter((v) => v.confNo !== confNo);
        newCacheRecord = newCacheRecord.filter((v) => v.confNo !== confNo);
      }
      store.changeData({
        cacheRecord: newCacheRecord,
        cacheModalRecord: newCacheModalRecord,
      });
    };

    return (
      <section className={[globalStyle.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          loading={!loading}
          onClick={() => {
            store.openModal(1);
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          loading={!loading}
          disabled={!selectedRows.length || selectedRows.filter((v) => v.enableStatus).length}
          onClick={() => {
            Modal.confirm({
              title: t('确认删除？'),
              onOk: () => {
                store.deleteData();
              },
            });
          }}
        >
          {t('删除')}
        </Button>

        {/* 新增、编辑弹窗 */}
        <Modal
          maskCloseAble={null}
          visible={[0, 1].includes(modalType) && !selectModalVisible}
          width={1020}
          title={modalType ? t('新增') : t('编辑')}
          bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
          onClose={() => {
            store.closeModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading || validate()}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={130}
            labelAlign="right"
            style={{ maxWidth: 1000 }}
            onSubmit={() => {
              store.saveData();
            }}
            onChange={(value) => {
              store.changeData({
                modalInfo: value,
              });
            }}
            value={modalInfo}
            inline
            formRef={(f) => store.changeData({ modalFormRef: f })}
          >
            <div className={styles.modalBox}>
              <div className={styles.itemTitle}>
                {t('基础信息')}
              </div>
              <Form.Item required label={t('流程编码')}>
                <Input name="approvalProcessCode" maxLength={50} style={{ width: 280 }} />
              </Form.Item>
              <Form.Item required label={t('流程名称')}>
                <Input name="approvalProcessName" maxLength={50} style={{ width: 280 }} />
              </Form.Item>
              {
                modalInfo.approvalType !== 2 && (
                  <>
                    <Form.Item required label={t('仓库')}>
                      <Select
                        name="warehouseId"
                        data={warehouseList}
                        keygen="id"
                        format="id"
                        placeholder={t('请选择')}
                        renderItem="nameZh"
                        style={{ width: 280 }}
                        renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                        onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        onChange={(val) => {
                          store.getModalParkList(val);
                          store.changeData({
                            modalInfo: {
                              ...modalInfo,
                              warehouseId: val,
                              parkId: '',
                              subWarehouseId: '',
                            },
                          });
                        }}
                      />
                    </Form.Item>
                    <Form.Item label={t('园区')}>
                      <Select
                        name="parkId"
                        data={modalParkList}
                        keygen="parkType"
                        format="parkType"
                        placeholder={t('请选择')}
                        renderItem="parkName"
                        width={280}
                        absolute
                        clearable
                        renderUnmatched={(r) => r.parkName || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                        onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        onChange={(val) => {
                          if (!['', undefined, null].includes(val)) {
                            const { parkSubWarehouseList } = fliterSubwarehouse([val]);
                            store.changeData({
                              modalSubWarehouseList: parkSubWarehouseList,
                            });
                          }
                          store.changeData({
                            modalInfo: {
                              ...modalInfo,
                              parkId: val,
                              subWarehouseId: '',
                            },
                          });
                        }}
                      />
                    </Form.Item>
                    <Form.Item label={t('子仓')}>
                      <Select
                        name="subWarehouseId"
                        data={modalSubWarehouseList}
                        keygen="id"
                        format="id"
                        placeholder={t('请选择')}
                        renderItem="nameZh"
                        width={280}
                        absolute
                        clearable
                        renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                        onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                      />
                    </Form.Item>
                  </>
                )
              }
              <Form.Item required label={t('审批类型')}>
                <Select
                  absolute
                  disabled={modalType === 0}
                  name="approvalType"
                  data={approvalTypeList}
                  keygen="dictCode"
                  format="dictCode"
                  placeholder={t('请选择')}
                  renderItem="dictNameZh"
                  style={{ width: 280 }}
                  renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  onChange={(val) => {
                    if (val === isAllocation) {
                      store.changeData({
                        cacheRecord: [],
                        modalInfo: {
                          ...modalInfo,
                          parkId: '',
                          subWarehouseId: '',
                          warehouseId: '',
                          approvalType: val,
                        },
                        modalSubWarehouseList: [],
                        modalParkList: [],
                      });
                    }
                  }}
                />
              </Form.Item>
              {
                modalInfo.approvalType === isAllocation && (
                  <Form.Item required label={t('调拨计划规则编码')}>
                    <Button
                      style={{ width: 280 }}
                      onClick={() => {
                        store.changeData({
                          selectModalVisible: true,
                        });
                      }}
                    >
                      {
                        !!cacheRecord.length && (
                          <Popover
                            style={{ padding: '4px 8px' }}
                            position="top"
                          >
                            <div>
                              {
                              cacheRecord.map((v) => <div>{v.confNo}</div>)
                            }
                            </div>
                          </Popover>
                        )
                      }
                      {showConfNo()}
                    </Button>
                  </Form.Item>
                )
              }
              <Form.Item required label={t('启用状态')}>
                <Radio.Group name="enableStatus" keygen="dictCode" data={enableTypeList} format="dictCode" renderItem="dictNameZh" />
              </Form.Item>
            </div>

            <div className={styles.modalBox}>
              <div className={styles.itemTitle}>
                {t('流程配置')}
              </div>
              {
                modalInfo.approvalType !== 2 && (
                  <Form.Item required label={t('是否本人确认')}>
                    <Radio.Group name="needConfirm" keygen="dictCode" data={needTypeList} format="dictCode" renderItem="dictNameZh" />
                  </Form.Item>
                )
              }
              {currentApprover.map((item, i) => (
                // eslint-disable-next-line react/no-array-index-key
                <div key={i}>
                  <Form.Item required label={`${t('审批人')}(${i + 1})`}>
                    <FilterSearchSelect
                      absolute
                      value={item}
                      clearable
                      style={{ width: 280 }}
                      placeholder={t('请输入')}
                      onChange={(value) => {
                        store.changeApproverList({
                          current: value,
                          index: i,
                          type: 'change',
                        });
                      }}
                    />
                  </Form.Item>
                  <Button
                    style={{ fontSize: 18 }}
                    type="primary"
                    text
                    loading={!loading}
                    disabled={!canAdd}
                    onClick={() => {
                      store.changeApproverList({
                        current: '',
                        index: i + 1,
                        type: 'add',
                      });
                    }}
                  >
                    <Icon name="plus-o" />
                  </Button>
                  <Button
                    style={{ marginLeft: 0, fontSize: 18 }}
                    type="danger"
                    text
                    loading={!loading}
                    disabled={!canDel}
                    onClick={() => {
                      store.changeApproverList({
                        current: '',
                        index: i,
                        type: 'delete',
                      });
                    }}
                  >
                    <Icon name="minus-o" />
                  </Button>
                </div>
              ))}
            </div>
            <div className={styles.modalBox}>
              <div className={styles.itemTitle}>
                {t('推送配置')}
              </div>
              <Form.Item label={t('是否开启待审批提醒推送')} labelWidth={160}>
                <Select
                  name="isPush"
                  width={200}
                  data={yesOrNoList}
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  clearable
                  absolute
                  placeholder={t('全部')}
                  onChange={(val) => {
                    // 若选择了否、则清空推送时间
                    if (val === 0) {
                      store.changeModalnfoData({
                        pushTimeList: [],
                      });
                    }
                  }}
                />
              </Form.Item>
              <Form.Item label={t('企微推送时间')} labelWidth={160} required={modalInfo.isPush === 1}>
                <Select
                  name="pushTimeList"
                  width={200}
                  data={pushTimeList}
                  disabled={modalInfo.isPush === 0}
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  clearable
                  placeholder={t('')}
                  multiple
                  compressed
                  rules={[modalInfo.isPush === 1 ? rules.required(t('请选择企微推送时间')) : null]}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
        <Modal
          visible={selectModalVisible}
          width={1200}
          onClose={() => {
            store.changeData({
              selectModalVisible: false,
            });
            store.clearModalSearchData();
          }}
          maskOpacity={0}
          maskCloseAble={false}
          title={t('调拨计划规则')}
          footer={[
            <Button
              onClick={() => {
                store.changeData({
                  selectModalVisible: false,
                });
                store.clearModalSearchData();
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              disabled={!cacheRecord.length}
              type="primary"
              onClick={() => {
                store.checkAllocationConfig();
              }}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div>
            <div className={styles.modalHeader}>
              <div className={styles.inputWrap}>
                <div className={styles.modalLab}>
                  {t('编码')}
                  :
                </div>
                <Input
                  style={{ width: '200px' }}
                  value={modalLimit.confNo}
                  size="middle"
                  className={styles.modalInupt}
                  onChange={(val) => {
                    store.changeData({
                      modalLimit: {
                        ...modalLimit,
                        confNo: val,
                      },
                    });
                  }}
                />
              </div>
              <div className={styles.inputWrap}>
                <div className={styles.modalLab}>
                  {t('出发仓')}
                  :
                </div>
                <Select
                  style={{ width: '200px' }}
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  className={styles.modalInupt}
                  value={modalLimit.srcWarehouseIdList}
                  clearable
                  compressed
                  multiple
                  data={warehouseList}
                  placeholder={t('全部')}
                  onChange={(warehouseId) => {
                    store.changeData({
                      modalLimit: {
                        ...modalLimit,
                        srcWarehouseIdList: warehouseId,
                        srcSubWarehouseIdList: [],
                      },
                    });
                    if (warehouseId.length === 1) {
                      store.getSubWarehouse({ warehouseId: warehouseId[0], isSrc: true });
                    } else {
                      store.changeData({
                        modalSrcSubWarehouseList: [],
                      });
                      store.changeLimitData({
                        modalSrcSubWarehouseId: [],
                      });
                    }
                  }}
                  onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                />
              </div>
              <div className={styles.inputWrap}>
                <div className={styles.modalLab}>
                  {t('出发子仓')}
                  :
                </div>
                <Select
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  style={{ width: '200px' }}
                  className={styles.modalInupt}
                  value={modalLimit.srcSubWarehouseIdList}
                  clearable
                  compressed
                  multiple
                  data={modalSrcSubWarehouseList}
                  placeholder={t('全部')}
                  onChange={(subWarehouseId) => {
                    store.changeData({
                      modalLimit: {
                        ...modalLimit,
                        srcSubWarehouseIdList: subWarehouseId,
                      },
                    });
                  }}
                  onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                />
              </div>
              <div className={styles.inputWrap}>
                <div className={styles.modalLab}>
                  {t('到达仓')}
                  :
                </div>
                <Select
                  style={{ width: '200px' }}
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  className={styles.modalInupt}
                  value={modalLimit.destWarehouseIdList}
                  clearable
                  compressed
                  multiple
                  data={warehouseList}
                  placeholder={t('全部')}
                  onChange={(warehouseId) => {
                    store.changeData({
                      modalLimit: {
                        ...modalLimit,
                        destWarehouseIdList: warehouseId,
                        destSubWarehouseIdList: [],
                      },
                    });
                    if (warehouseId.length === 1) {
                      store.getSubWarehouse({ warehouseId: warehouseId[0], isSrc: false });
                    } else {
                      store.changeData({
                        modalDestSubWarehouseList: [],
                      });
                      store.changeLimitData({
                        modalDestSubWarehouseList: [],
                      });
                    }
                  }}
                  onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                />
              </div>
              <div className={styles.inputWrap}>
                <div className={styles.modalLab}>
                  {t('到达子仓')}
                  :
                </div>
                <Select
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  style={{ width: '200px' }}
                  className={styles.modalInupt}
                  data={modalDestSubWarehouseList}
                  clearable
                  compressed
                  multiple
                  value={modalLimit.destSubWarehouseIdList}
                  placeholder={t('全部')}
                  onChange={(subWarehouseId) => {
                    store.changeData({
                      modalLimit: {
                        ...modalLimit,
                        destSubWarehouseIdList: subWarehouseId,
                      },
                    });
                  }}
                  onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                />
              </div>
              <div className={styles.inputWrap}>
                <Button
                  type="primary"
                  icon="search"
                  onClick={() => {
                    store.modalHandlePaginationChange({
                      pageNum: 1,
                      pageSize: modalLimit.pageSize,
                    });
                  }}
                >
                  {t('搜索')}
                </Button>
              </div>
            </div>
            <Table
              data={modalList}
              columns={columns}
              value={modalRecord}
              empty={t('暂无数据')}
              onRowSelect={(record) => {
                // 判断是新增还是减少调用对应的方法
                if (cacheModalRecord.length < record.length) {
                  addCacheRecord(record);
                } else {
                  delCacheRecord(record);
                }
                store.changeData({
                  modalRecord: record,
                });
              }}
              prediction={(v, d) => (v.id ? v.id : v) === d.id}
              keygen="id"
              pagination={{
                align: 'right',
                current: modalLimit.pageNum,
                pageSize: modalLimit.pageSize,
                layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
                onChange: (page, size) => {
                  store.modalHandlePaginationChange({
                    pageNum: page,
                    pageSize: size,
                  });
                },
                pageSizeList: modalPageSizeList,
                total: modalCount,
              }}
              loading={!loading}
              height={300}
              fixed="both"
              key=""
            />
          </div>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalInfo: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  enableTypeList: PropTypes.arrayOf(PropTypes.shape),
  needTypeList: PropTypes.arrayOf(PropTypes.shape),
  approvalTypeList: PropTypes.arrayOf(PropTypes.shape),
  currentApprover: PropTypes.arrayOf(PropTypes.string),
  canAdd: PropTypes.bool,
  canDel: PropTypes.bool,
  modalParkList: PropTypes.arrayOf(PropTypes.shape),
  modalSubWarehouseList: PropTypes.arrayOf(PropTypes.shape),
  selectModalVisible: PropTypes.bool,
  modalRecord: PropTypes.arrayOf(PropTypes.shape()),
  modalLimit: PropTypes.shape(),
  modalSrcSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  modalDestSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  modalList: PropTypes.arrayOf(PropTypes.shape()),
  modalPageSizeList: PropTypes.arrayOf(PropTypes.number),
  modalCount: PropTypes.number,
  cacheRecord: PropTypes.arrayOf(PropTypes.shape()),
  cacheModalRecord: PropTypes.arrayOf(PropTypes.shape()),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  yesOrNoList: PropTypes.arrayOf(PropTypes.shape),
  pushTimeList: PropTypes.arrayOf(PropTypes.shape),
};
export default Handle;
