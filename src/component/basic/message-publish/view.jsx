import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  Button, Spin, Input, Textarea,
} from 'shineout';
import { getLoginInfo, handleDelPage } from '@src/lib/dealFunc';
import store from './reducers';
import styles from '../../style.less';
import style from './style.css';
import EmojiPanel from './emoji-panel';

class Container extends React.Component {
  componentDidMount() {
    handleDelPage();

    // 初始化数据
    // store.init();
  }

  render() {
    const {
      ready,
      title,
      content,
      inputPosition,
    } = this.props;

    if (ready) {
      return (
        <div className={style.container}>
          <div style={{ minWidth: '660px' }}>
            <div className={styles.inner_list}>
              <span className={style.lab}>
                {t('标题')}
                <span className={styles.redStar}>*</span>
              </span>
              <Input
                onFocus={() => store.changeData({ data: { inputPosition: 1 } })}
                data-bind="title"
                className={styles.inputWidth}
                style={{ width: 600 }}
                maxLength={50}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={style.lab}>
                {t('内容')}
                <span className={styles.redStar}>*</span>
              </span>
              <Textarea
                onFocus={() => store.changeData({ data: { inputPosition: 2 } })}
                data-bind="content"
                rows={20}
                className={styles.inputWidth}
                style={{
                  width: 600,
                  // height: 400,
                }}
              />
            </div>
            <div style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                disabled={!title || !content}
                onClick={() => {
                  store.publish({
                    params: {
                      title,
                      content,
                      // targetList: [],
                      // 广播
                      publishType: 'broadcast',
                      store: false,
                    },
                  });
                }}
              >
                {t('发送消息')}
              </Button>
              <Button
                type="primary"
                disabled={!title || !content}
                onClick={() => {
                  const info = getLoginInfo();
                  store.publish({
                    params: {
                      title,
                      content,
                      targetList: [info && info.name],
                      // 只发给自己
                      publishType: 'unicast',
                      stay: true,
                    },
                  });
                }}
              >
                {t('测试一下')}
              </Button>
            </div>
          </div>
          <div style={{ padding: '0px 10px' }}>
            <EmojiPanel
              onClick={(v) => {
                const data = {};
                // inputPosition 1表示在title中插入，否则在content中插入
                if (inputPosition === 1) {
                  data.title = `${title}${v}`;
                } else {
                  data.content = `${content}${v}`;
                }
                store.changeData({
                  data,
                });
              }}
            />
          </div>
        </div>
      );
    }
    return (
      <div style={{ textAlign: 'center' }}>
        <Spin size={50} name="default" />
      </div>
    );
  }
}

Container.propTypes = {
  ready: PropTypes.bool.isRequired,
  title: PropTypes.string,
  content: PropTypes.string,
  inputPosition: PropTypes.number,
};

const mapStateToProps = (state) => state['basic/message-publish'];
export default connect(mapStateToProps)(Container);
