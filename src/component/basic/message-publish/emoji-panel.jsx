import React, { Component } from 'react';
import Proptypes from 'prop-types';
import { copy } from 'copy-list';
import style from './style.css';

function EmojiItem(props) {
  const { value, onClick } = props;
  return (
    <div
      className={style.emoji}
      onClick={() => {
        if (onClick && typeof onClick === 'function') {
          onClick(value);
        } else {
          copy(value);
        }
      }}
    >
      {value}
    </div>
  );
}

EmojiItem.defaultProps = {
  value: '',
};

EmojiItem.propTypes = {
  value: Proptypes.string,
  onClick: Proptypes.func,
};

class EmojiPanel extends Component {
  constructor(props) {
    super(props);
    // 圣诞节彩蛋icon
    const today = new Date();
    const isChristmas = today.getMonth() === 11 && today.getDate() === 25;
    const christmasIcons = isChristmas ? ['🎅', '🤶', '🧦', '🛷', '⛄', '🎄', '🦌', '🔔'] : [];
    this.state = {
      emojiList: [
        '😩', '🖖', '😃', '😛', '😀', '😃', '😄', '😁', '😆', '😅', '😂', '😇', '😉', '😊', '🙂',
        '😋', '😌', '😍', '😘', '😗', '😙', '😚', '😜', '😝', '😛', '😎', '😏', '😶', '😐', '😑', '😒',
        '😳', '😞', '😟', '😠', '😡', '😔', '😕', '🙁', '😬', '😣', '😖', '😫', '😩', '😤', '😮', '😱',
        '😨', '😰', '😯', '😦', '😧', '😢', '😥', '😪', '😓', '😭', '😵', '😲', '😷', '😴', '💤',
        '😈', '👿', '💩', '👻', '💀', '👽', '🎃', '😺', '😸', '😹', '😻', '😼', '🎉', '🌟', '🐞',
        '😽', '🙀', '😿', '😾', '👐', '🙌', '👏', '🙏', '👍', '👎', '👊', '✊', '✌', '👌', '👈', '👉',
        '👆', '👇', '☝', '✋', '🖐', '🖖', '👋', '💪', '🖕', '✍', '💅', '👀', '👤', '👥', '🗣', '👨',
        '🙅', '🙆', '🙋', '❤', '💛', '💚', '💙', '💜', '💔', '❣',
        '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟',
        ...christmasIcons,
      ],
    };
  }

  render() {
    const { emojiList } = this.state;
    const { onClick } = this.props;
    return (
      <div className={style.emojiPanel}>
        {
          emojiList.map((v) => <EmojiItem onClick={onClick} value={v} />)
        }
      </div>
    );
  }
}

EmojiPanel.propTypes = {
  onClick: Proptypes.func,
};

export default EmojiPanel;
