import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { Message, Modal } from 'shineout';
import { cloudMessagePublishApi } from '../../../server/common/cloud-message';

const defaultState = {
  ready: false,
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  title: '',
  content: '',
  inputPosition: 2,
};

export default {
  defaultState,
  // 初始化
  $init: (draft) => {
    assign(draft);
  },
  // 改state数据
  changeData: (draft, action) => {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({
      data: {
        ready: true,
      },
    });
  },
  * publish(action, ctx) {
    const params = {
      ...action.params,
      module: 1, // 消息推送
      messageType: 'message',
    };
    const res = yield cloudMessagePublishApi(params);
    if (res.code === '0') {
      Message.success(t('发送消息成功'));
      if (!action.params.stay) {
        yield ctx.changeData({
          data: {
            title: '',
            content: '',
          },
        });
      }
    } else {
      Modal.error({ title: res.msg });
    }
  },
};
