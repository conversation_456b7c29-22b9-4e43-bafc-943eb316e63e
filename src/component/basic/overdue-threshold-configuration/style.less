/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.handleExportButton{
    margin-right: 6px;
}

.modalCont {
    padding: 10px 0 20px 50px;
}
.modalCont>div {
    margin-bottom: 20px;
}

/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect{
    width: 140px;
 }
 
 .headerUnmatchedText{
     color: #bbb;
 }

.headerSearchTimeLine{
    display: flex;
}

/* list.jsx
---------------------------------------------------------------- */
.listOperationButton{
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}
.listSection{
    height: 0px; /* 必写为了子domheight - 100%有效 */
    flex: 1;    
}




