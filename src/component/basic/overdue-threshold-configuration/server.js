import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '../../../server/common/fileFetch';
import { camel2Under } from '../../../lib/camal-case-convertor';

/**
 * 获取列表数据
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/monitor/get_monitor_item_config',
  param,
}, process.env.WKB_FRONT);

// 下载模板
export const downloadAPI = () => {
  const uri = '/wms/stat/monitor/download_monitor_item_config';
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under()),
  });
};

// 获取监控项目名称
export const getItemTypesAPI = (param) => sendPostRequest({
  url: '/monitor/getItemTypes',
  param,
}, process.env.WKB_FRONT);

// 新增
export const addConfigAPI = (param) => sendPostRequest({
  url: '/monitor/insert_monitor_item_config',
  param,
}, process.env.WKB_FRONT);

// 编辑
export const updateConfigAPI = (param) => sendPostRequest({
  url: '/monitor/update_monitor_item_config',
  param,
}, process.env.WKB_FRONT);

// 删除
export const delConfigAPI = (param) => sendPostRequest({
  url: '/monitor/delete_monitor_item_config',
  param,
}, process.env.WKB_FRONT);

// 导入接口
export const monitorItemConfigImportURL = `${process.env.WGS_FRONT}/file_import/record/wkb/monitor_item_config_import`;
