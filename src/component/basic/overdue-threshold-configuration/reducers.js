import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { formdataPost } from '@src/server/common/fileFetch';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import { getSubWarehouseSelectList } from '@src/server/basic/dictionary';
import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import {
  getListAPI, getItemTypesAPI, addConfigAPI, updateConfigAPI, delConfigAPI, monitorItemConfigImportURL,
} from './server';

// 处理转换布尔值
const handleBool = (obj, keys, trueVal = '1') => {
  const newObj = { ...obj };
  keys.forEach((key) => {
    if (newObj[key] !== undefined && newObj[key] !== '') {
      newObj[key] = newObj[key] === trueVal;
    }
  });
  return newObj;
};

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  warehouseId: undefined, // 仓库
  subWarehouseId: undefined, // 子仓编号
  itemType: undefined, // 监控项目
  isAllCompare: '', // 是否做全仓对比
  creator: '', // 创建人
  beginTime: '', // 创建时间
  endTime: '',
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  selectedRows: [], // 选中行数据
  // 相关下拉数据
  warehouseList: [], // 仓库下拉数据
  subWarehouseList: [], // 子仓下拉数据
  itemTypesList: [], // 监控项目下拉数据
  isToggleList: [{ id: '1', name: t('是') }, { id: '0', name: t('否') }], // 是否下拉数据
  modalType: 0, // 0 代表不显示弹窗；1 新增、2编辑、3导入
  addObj: {// 新增弹窗数据
  },
  addObjSubWarehouseList: [],
  editObj: {// 编辑弹窗数据
  },
  editObjSubWarehouseList: [],
  editLoading: false, // 编辑loading
  file: '',
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改搜索条件addObj属性值
  changeAddObj: (draft, action) => {
    assign(draft.addObj, action.data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    // 取子仓数据
    const { warehouseIds, limit } = yield '';
    const { warehouseId, warehouseList, subWarehouseList } = yield 'nav';
    // 判断仓库进来是否更改
    if (warehouseIds !== warehouseId) {
      yield this.changeData({
        warehouseList,
        subWarehouseList,
        warehouseIds: warehouseId,
        limit: {
          ...limit,
          subWarehouseId: undefined,
        },
      });
    }
    const [itemTypesData] = yield Promise.all([
      getItemTypesAPI(),
    ]);
    if (itemTypesData.code === '0') {
      yield this.changeData({
        itemTypesList: (itemTypesData.info.itemTypes || [])
          .map((obj) => ({ id: obj.code, name: obj.name })),
      });
    } else {
      Modal.error({
        title: itemTypesData.msg,
      });
    }
  },

  /**
   * 新增编辑弹框，仓库改变获取子仓
   * @param {*} action
   * @param {*} ctx
   */
  * getSubWarehouse(action, ctx) {
    const res = yield getSubWarehouseSelectList({ warehouseId: action.data.warehouseId, enabled: 1 });
    if (res.code === '0' && res.info) {
      if (action.data.flag === 'new') {
        yield ctx.changeData({
          addObjSubWarehouseList: res.info.data,
        });
        yield ctx.changeAddObj({
          data: {
            subWarehouseId: '',
          },
        });
      } else {
        yield ctx.changeData({
          subWarehouseList: res.info.data,
        });
      }
    } else {
      if (action.data.flag === 'new') {
        yield ctx.changeData({
          addObjSubWarehouseList: [],
        });
        yield ctx.changeAddObj({
          data: {
            subWarehouseId: '',
          },
        });
      } else {
        yield ctx.changeData({
          subWarehouseList: [],
        });
      }

      Modal.error({ title: res.msg });
    }
  },
  /**
   * 搜索
   */
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({
        title: t('请先在右上角选择仓库！'),
        autoFocusButton: 'ok',
      });
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    const paramObj = handleBool(param, ['isAllCompare']);
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(clearEmpty(paramTrim(assign({}, paramObj, { warehouseId })), [0, false]));
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        selectedRows: [],
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  // 新增
  * addOperation(action, ctx) {
    markStatus('loading');
    const paramObj = handleBool(action.data, ['isAllCompare', 'isEnabled']);
    const res = yield addConfigAPI(paramObj);
    if (res.code === '0') {
      Message.success(t('新增成功！'));
      yield ctx.search();
      yield ctx.changeData({ modalType: 0 });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 编辑
  * editOperation(action, ctx) {
    // 接口响应慢时，先关掉弹窗，让用户做其它操作
    yield ctx.changeData({ modalType: 0 });
    const paramObj = handleBool(action.data, ['isAllCompare', 'isEnabled']);
    markStatus('editLoading');
    const res = yield updateConfigAPI(paramObj);
    if (res.code === '0') {
      Message.success(t('编辑成功！'));
      yield ctx.search();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 删除
  * delOperation(action, ctx) {
    markStatus('loading');
    const res = yield delConfigAPI({ id: action.id });
    if (res.code === '0') {
      Message.success(t('删除成功！'));
      yield ctx.search();
      yield ctx.changeData({ modalType: 0 });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  /**
   * 导入文件
   * @param {object} formData
   */
  * uploadFile(formData) {
    markStatus('loading');
    formData.append('function_node', 35);
    const res = yield formdataPost(monitorItemConfigImportURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        modalType: 0,
        file: '',
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  /**
   * 右上角仓库事件派发
   * @param {object} data
   */
  * changeSubWarehouseList(data) {
    const { warehouseId, subWarehouseList } = data;
    yield this.changeData({
      subWarehouseList,
      warehouseIds: warehouseId,
    });

    yield this.changeLimitData({
      subWarehouseId: undefined,
    });
  },
};
