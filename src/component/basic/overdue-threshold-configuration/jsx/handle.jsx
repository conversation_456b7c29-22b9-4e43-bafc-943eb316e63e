import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import fileSaver from 'file-saver';
import {
  Button, Modal, Select, Rule,
} from 'shineout';
import FileDelayUpload from '@src/component/public-component/upload/fileDelayUpload';
import RuleInput from '@shein-components/WmsInput';
import styles from '@src/component/style.less';
import { downloadAPI } from '../server';
import store from '../reducers';
import style from '../style.less';

const rules = new Rule();
// 判断新增、编辑确认按钮是否禁用
const validDisabled = (obj, len = 7) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { id, ...checkObj } = obj;
  // 确保新增时，6项都有输入值
  if (Object.keys(checkObj).length !== len || checkObj.subWarehouseId === '') {
    return true;
  }
  // 判断数字框空和输入负数的情况
  if (['monitorVal', 'warningVal'].some((key) => obj[key] === '' || obj[key] === '-' || obj[key] < 0)) {
    return true;
  }
  // 警告值不能超过100
  if (obj.warningVal > 100) {
    return true;
  }
  return false;
};
// 关闭弹窗
const closeModal = () => store.changeData({
  modalType: 0,
});

function handle(props) {
  const {
    loading,
    selectedRows,
    modalType,
    addObj,
    addObjSubWarehouseList,
    editObj,
    warehouseList,
    itemTypesList,
    isToggleList,
    editLoading,
    file,
  } = props;

  return (
    <section className={[styles.handle, 'handleSection'].join(' ')}>
      <Button
        type="primary"
        size="default"
        onClick={() => {
          store.changeData({
            modalType: 1,
            addObj: { isAllCompare: '1', isEnabled: '1' },
          });
        }}
      >
        {t('新增')}
      </Button>
      <Button
        type="primary"
        size="default"
        disabled={selectedRows.length !== 1}
        loading={editLoading === 0}
        onClick={() => {
          // console.log(selectedRows[0]);
          const {
            id,
            isAllCompare,
            warehouseId,
            subWarehouseId,
            itemType,
            monitorVal,
            isEnabled,
            warningVal,
          } = selectedRows[0];
          store.changeData({
            modalType: 2,
            editObj: {
              id,
              isAllCompare: isAllCompare ? '1' : '0',
              warehouseId,
              subWarehouseId,
              itemType,
              monitorVal,
              isEnabled: isEnabled ? '1' : '0',
              warningVal,
              // timeOrDate: '1',
            },
          });
          store.getSubWarehouse({ data: { warehouseId, flag: 'new' } });
        }}
      >
        {t('编辑')}
      </Button>
      <Button
        type="primary"
        size="default"
        disabled={selectedRows.length !== 1}
        onClick={() => {
          Modal.confirm({
            title: t('删除超期阈值配置'),
            content: t('确认要将该超期阈值配置删除吗？'),
            onOk: () => store.delOperation({ id: selectedRows[0].id }),
            text: { ok: t('确认'), cancel: t('取消') },
          });
        }}
      >
        {t('删除')}
      </Button>
      <Button
        type="primary"
        size="default"
        onClick={() => {
          store.changeData({ modalType: 3, file: '' });
        }}
      >
        {t('批量导入配置')}
      </Button>
      <Button
        type="primary"
        loading={loading === 0}
        onClick={() => downloadAPI()
          .then((d) => d.blob())
          .then((b) => {
            const blob = new Blob([b], { type: 'application/octet-stream' });
            fileSaver.saveAs(blob, `${t('导入模板下载')}.xlsx`);
          }).catch((error) => {
            Modal.error({
              title: error?.reason?.message || error?.message || t('下载失败,请检查网络连接或联系管理员'),
            });
          })}
      >
        {t('下载导入模板')}
      </Button>
      {/* 以下是相关弹窗代码 */}
      <div>
        {/* 新增弹窗 */}
        <Modal
          visible={modalType === 1}
          maskCloseAble={null}
          width={600}
          title={t('新增超期阈值配置')}
          onClose={closeModal}
          footer={[
            <Button
              key="cancel"
              onClick={closeModal}
            >
              {t('取消')}
            </Button>,
            <Button
              disabled={validDisabled(addObj)}
              key="confirm"
              type="primary"
              onClick={() => {
                if (validDisabled(addObj)) {
                  return;
                }
                store.addOperation({ data: addObj });
              }}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={style.modalCont}>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('仓库')}
                :
              </span>
              <Select
                data-bind="addObj.warehouseId"
                placeholder={t('请选择')}
                width={300}
                data={warehouseList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.nameZh}
                onFilter={(text) => (v) => v.nameZh.toLowerCase().indexOf(text.toLowerCase()) > -1}
                onChange={
                  (id) => {
                    store.getSubWarehouse({ data: { warehouseId: id, flag: 'new' } });
                  }
}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('子仓')}
                :
              </span>
              <Select
                data-bind="addObj.subWarehouseId"
                placeholder={t('请选择')}
                width={300}
                data={addObjSubWarehouseList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.nameZh}
                onFilter={(text) => (v) => v.nameZh.toLowerCase().indexOf(text.toLowerCase()) > -1}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('监控项目')}
                :
              </span>
              <Select
                data-bind="addObj.itemType"
                placeholder={t('请选择')}
                width={300}
                data={itemTypesList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.name}
                onFilter={(text) => (v) => v.name.toLowerCase().indexOf(text.toLowerCase()) > -1}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('超期阈值')}
                :
              </span>
              <RuleInput.Number
                data-bind="addObj.monitorVal"
                width={140}
                style={{ marginRight: '10px' }}
                delay={0}
                digits={0}
                max={9999}
                min={1}
                placeholder={t('请输入正整数')}
                allowNull
                hideArrow
                type="number"
                rules={[rules.required, rules.min(1), rules.max(9999)]}
              />
              {t('小时')}
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('是否启用')}
                :
              </span>
              <Select
                data-bind="addObj.isEnabled"
                placeholder={t('请选择')}
                width={300}
                data={isToggleList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.name}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('是否做全仓对比')}
                :
              </span>
              <Select
                data-bind="addObj.isAllCompare"
                placeholder={t('请选择')}
                width={300}
                data={isToggleList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.name}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('预警占比值(%)')}
                :
              </span>
              <RuleInput.Number
                data-bind="addObj.warningVal"
                width={300}
                delay={0}
                digits={0}
                placeholder={t('请输入0或不大于100的正整数')}
                allowNull
                hideArrow
                type="number"
                rules={[rules.required, rules.min(0), rules.max(100)]}
              />
            </div>
          </div>
        </Modal>
        {/* 编辑弹窗 */}
        <Modal
          visible={modalType === 2}
          maskCloseAble={null}
          width={600}
          title={t('编辑超期阈值配置')}
          onClose={closeModal}
          footer={[
            <Button
              key="cancel"
              onClick={closeModal}
            >
              {t('取消')}
            </Button>,
            <Button
              disabled={validDisabled(editObj)}
              key="confirm"
              type="primary"
              onClick={() => {
                if (validDisabled(editObj)) {
                  return;
                }
                store.editOperation({ data: editObj });
              }}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={style.modalCont}>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('仓库')}
                :
              </span>
              <Select
                disabled
                data-bind="editObj.warehouseId"
                placeholder={t('请选择')}
                width={300}
                data={warehouseList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.nameZh}
                onFilter={(text) => (v) => v.nameZh.toLowerCase().indexOf(text.toLowerCase()) > -1}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('子仓')}
                :
              </span>
              <Select
                disabled
                data-bind="editObj.subWarehouseId"
                placeholder={t('请选择')}
                width={300}
                data={addObjSubWarehouseList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.nameZh}
                onFilter={(text) => (v) => v.nameZh.toLowerCase().indexOf(text.toLowerCase()) > -1}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('监控项目')}
                :
              </span>
              <Select
                disabled
                data-bind="editObj.itemType"
                placeholder={t('请选择')}
                width={300}
                data={itemTypesList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.name}
                onFilter={(text) => (v) => v.name.toLowerCase().indexOf(text.toLowerCase()) > -1}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('超期阈值')}
                :
              </span>
              <RuleInput.Number
                data-bind="editObj.monitorVal"
                width={140}
                style={{ marginRight: '10px' }}
                delay={0}
                digits={0}
                placeholder={t('请输入0或正整数')}
                allowNull
                hideArrow
                type="number"
                rules={[rules.required, rules.min(0)]}
              />
              {t('小时')}
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('是否启用')}
                :
              </span>
              <Select
                data-bind="editObj.isEnabled"
                placeholder={t('请选择')}
                width={300}
                data={isToggleList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.name}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('是否做全仓对比')}
                :
              </span>
              <Select
                data-bind="editObj.isAllCompare"
                placeholder={t('请选择')}
                width={300}
                data={isToggleList}
                datum={{ format: 'id' }}
                keygen="id"
                renderItem={(w) => w.name}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labLarge}>
                <span className={styles.redStar}>*</span>
                {t('预警占比值(%)')}
                :
              </span>
              <RuleInput.Number
                data-bind="editObj.warningVal"
                width={300}
                delay={0}
                digits={0}
                placeholder={t('请输入0或不大于100的正整数')}
                allowNull
                hideArrow
                type="number"
                rules={[rules.required, rules.min(0), rules.max(100)]}
              />
            </div>
          </div>
        </Modal>
        <Modal
          visible={modalType === 3}
          maskCloseAble={null}
          title={t('批量导入配置')}
          onClose={closeModal}
          footer={[
            <Button
              key="cancel"
              onClick={closeModal}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={!file}
              loading={!loading}
              onClick={() => {
                const formData = new FormData();
                formData.append('file', file);
                store.uploadFile(formData);
              }}
            >
              {t('确认上传')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!loading}
              title={t('导入文件')}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
        </Modal>
      </div>
    </section>
  );
}

handle.propTypes = {
  loading: PropTypes.number.isRequired,
  editLoading: PropTypes.bool.isRequired,
  modalType: PropTypes.number.isRequired,
  addObj: PropTypes.shape().isRequired,
  editObj: PropTypes.shape().isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  warehouseList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  addObjSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  itemTypesList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  isToggleList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  file: PropTypes.oneOfType([PropTypes.string, PropTypes.shape()]),
};

export default handle;
