import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import FilterSearchSelect from '@src/component/public-component/select/filter-search-select';
import store, { defaultLimit } from '../reducers';
import styles from '../style.less';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      itemTypesList,
      subWarehouseList,
      isToggleList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 100 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('子仓')}
            name="subWarehouseId"
            data={subWarehouseList}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            renderUnmatched={(r) => r.nameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('监控项目')}
            name="itemType"
            data={itemTypesList}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="name"
            renderUnmatched={(r) => r.name || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <FilterSearchSelect
            label={t('创建人')}
            name="creator"
            clearable
            placeholder={t('请输入')}
          />
          <Select
            label={t('是否做全仓对比')}
            name="isAllCompare"
            data={[{ id: '', name: t('全部') }, ...isToggleList]}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="name"
            renderUnmatched={(r) => r.name || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['beginTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('创建时间')}
            span={2}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  isToggleList: PropTypes.arrayOf(PropTypes.shape()),
  itemTypesList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
