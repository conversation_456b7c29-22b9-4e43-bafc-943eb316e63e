import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('配置编码'),
        width: 120,
        render: 'itemCode',
      },
      {
        title: t('仓库'),
        width: 160,
        render: 'warehouseName',
      },
      {
        title: t('子仓'),
        width: 160,
        render: 'subWarehouseName',
      },
      {
        title: t('监控项目'),
        width: 180,
        render: 'itemTypeName',
      },
      {
        title: t('超期阈值'),
        width: 120,
        render: (r) => (
          <span>
            {r.monitorVal || 0}
            {t('小时')}
          </span>
        ),
      },
      {
        title: t('是否启用'),
        width: 120,
        render: 'isEnabledName',
      },
      {
        title: t('是否做全仓对比'),
        width: 140,
        render: 'isAllCompareName',
      },
      {
        title: t('预警占比值%'),
        width: 120,
        render: 'warningVal',
      },
      {
        title: t('创建日期'),
        width: 190,
        render: 'createTime',
      },
      {
        title: t('创建人'),
        width: 120,
        render: 'creator',
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 100,
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
            onRowSelect={(rows) => store.changeData({ selectedRows: rows })}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'MONITOR_ITEM_CONFIG_OPERATION',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
};

export default List;
