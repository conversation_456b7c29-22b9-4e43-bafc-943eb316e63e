import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import fileSaver from 'file-saver';
import { formdataPost } from '@src/server/common/fileFetch';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { queryParkList } from '@src/lib/dealFunc';
import {
  transformToPdfUrl, textToBarcode,
} from '@src/lib/print-new';
import { STATISTICAL_DOWNLOAD, STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import {
  queryContainer, offContainer, onContainer, exportFile, printContainer, containerImportURL, batchOperationContainerAPI,
} from './server';
import tpl from './container.ejs';

export const defaultLimit = {
  containerCode: '',
  containerTypes: [],
  department: '',
  enabled: 1,
  usableStatus: '',
  printRule: '',
  printTimes: '',
  warehouseId: undefined,
  isOneTimes: '',
  startTime: '',
  endTime: '',
  updateStartTime: '',
  updateEndTime: '',
  parkType: [], // 所在园区
  subWarehouseId: [], // 所在子仓
  bindRfId: '', // 是否绑定RFID
  tid: '', // TID
  rfUsableStatus: [], // RFID状态
  rfEnabled: '', // RFID可用状态
  physicalAttrList: [], // 物理属性
};
// 其他默认值
const defaultState = {
  warehouseIds: '', // 保留右上角仓库，由于init时判断是否要重置子仓等
  formRef: {},
  loading: 1, // 0 loading, 1 load success, 2 load fail
  dataLoading: 1, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  list: [],
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录id
  enabledList: [],
  usableStatusList: [],
  containerTypeList: [],
  printRuleList: [],
  ids: [],
  itemList: [],
  disabledOff: true,
  disabledOn: true,
  errorVisible: false,
  isOneTimesList: [
    {
      id: 0,
      name: t('否'),
    },
    {
      id: 1,
      name: t('是'),
    },
  ],
  parkList: [], // 园区下拉数据
  subWarehouseList: [], // 子仓下拉数据
  preSubWarehouseList: [],
  isRFIDList: [// 是否绑定RFID
    { dictNameZh: t('是'), dictCode: 1 },
    { dictNameZh: t('否'), dictCode: 0 },
  ],
  RFIDStatusList: [], // RFID状态
  RFIDUsedList: [// RFID可用状态
    { id: '', name: t('全部') },
    { id: 1, name: t('启用') },
    { id: 0, name: t('禁用') },
  ],
  printModal: false,
  uploadFile: '',
  importModalVisible: false, // 导入弹出框是否显示 默认不显示
  file: '', // 导入文件对象
  batchContainerModalType: 1, // 批量容器弹窗 1-新增 2-编辑
  batchContainerModal: false, // 批量容器弹窗（新增/编辑）
  batchContainerInfo: {
    prefix: '', // 容器号前缀
    beginSerial: '', // 容器序号开始
    endSerial: '', // 容器序号结束
    warehouseId: '', // 仓库
    containerType: '', // 容器类型
    enabled: 1, // 可用状态，默认为启用
    isOneTimes: 0, // 是否一次性容器，默认为否
    department: '', // 归属部门
    specification: '', // 规格
    useCutOffTime: '', // 使用截止时间
    physicalAttr: '', // 物理属性
  }, // 批量容器-信息（新增/编辑）
  batchContainerFormRef: {}, // 批量容器-表单校验（新增/编辑）
  warehouseList: [], // 仓库列表
  physicalAttrListSelect: [], // 容器物理属性下拉框
  currentWarehouseList: [], // 权限仓库列表
  maxPageNumber: 1, // 最大页数
  togglePaperVisible: false, // 切换打印纸张尺寸弹框 库位规格默认80*50。规格枚举值：80*50 80*40
  paperSize: 1, // 打印尺寸 默认 80*50
};

export default {
  state: defaultState,
  // 改变state的值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },

  /**
   * 初始化数据
   */
  * init() {
    markStatus('loading');
    const { currentWarehouseList } = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
    });
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['ENABLED', 'CONTAINER_TYPE', 'CONTAINER_USABLE_STATUS', 'CONTAINER_PRINT_RULE', 'FR_USABLE_STATUS', 'CONTAINER_PHYSICAL_PROPERTIES'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        enabledList: selectData.info.data.find((x) => x.catCode === 'ENABLED').dictListRsps,
        containerTypeList: selectData.info.data.find((x) => x.catCode === 'CONTAINER_TYPE').dictListRsps,
        usableStatusList: selectData.info.data.find((x) => x.catCode === 'CONTAINER_USABLE_STATUS').dictListRsps,
        printRuleList: selectData.info.data.find((x) => x.catCode === 'CONTAINER_PRINT_RULE').dictListRsps,
        RFIDStatusList: selectData.info.data.find((x) => x.catCode === 'FR_USABLE_STATUS').dictListRsps,
        physicalAttrListSelect: selectData.info.data.find((x) => x.catCode === 'CONTAINER_PHYSICAL_PROPERTIES').dictListRsps,
      });
    } else {
      Modal.error({
        title: selectData.msg || t('请求失败,请稍后再试'),
        autoFocusButton: 'ok',
      });
    }

    // 获取子仓列表
    const { warehouseIds } = yield '';
    const { subWarehouseList, warehouseId, warehouseList } = yield 'nav';
    // 获取园区列表
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      parkList,
      preSubWarehouseList: subWarehouseList,
      warehouseList,
    });
    if (warehouseIds !== warehouseId) {
      yield this.warehouseChange({ subWarehouseList, warehouseId });
    }
  },

  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { limit } = yield '';
    const { subWarehouseList, warehouseId } = data;
    // 获取园区列表
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      parkList,
      warehouseIds: warehouseId,
      subWarehouseList,
      preSubWarehouseList: subWarehouseList,
      limit: {
        ...limit,
        subWarehouseId: [],
        parkType: [],
      },
    });
  },

  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },

  /**
   * 搜索操作
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
      containerCode: limit.containerCode && limit.containerCode.split(',').length > 0 ? limit.containerCode.split(',') : [],
    };
    markStatus('loading');
    const { code, info, msg } = yield queryContainer(param);
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        ids: [],
        itemList: [],
        disabledOn: true,
        disabledOff: true,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },

  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(data = {}) {
    const { formRef, pageInfo, maxPageNumber } = yield '';
    // 校验
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    if (data.pageNum > 100) {
      Message.warn(t('最多查询到100页'));
      return;
    }

    if (validateFlag) {
      let newMaxPageNumber = maxPageNumber;
      const { isPageClick, ...newPageInfo } = data;
      if (data.pageNum >= maxPageNumber) {
        newMaxPageNumber = data.pageNum;
      } else if (data.pageNum === 1 && !isPageClick) {
        newMaxPageNumber = 1;
      }
      yield this.changeData({
        pageInfo: {
          ...pageInfo,
          ...newPageInfo,
        },
        maxPageNumber: newMaxPageNumber,
      });
      yield this.search();
    }
  },

  /**
   * 按钮操作-导出
   * @returns
   */
  * exportData() {
    const { limit, formRef, pageInfo: { pageNum, pageSize } } = yield '';
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    // 校验
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    if (validateFlag) {
      const param = {
        ...limit,
        pageNum,
        pageSize,
        warehouseId,
        containerCode: limit.containerCode && limit.containerCode.split(',').length > 0 ? limit.containerCode.split(',') : [],
      };
      markStatus('loading');
      const data = yield exportFile(param);
      if (data.code === '0') {
        window.open(STATISTICAL_DOWNLOAD);
      } else {
        Modal.error({
          title: data.msg || t('后台数据出错'),
          autoFocusButton: 'ok',
        });
      }
    }
  },

  /**
   * 注销
   * @param {*} action
   */
  * off(action) {
    markStatus('dataLoading');
    const arr = [];
    action.itemList.forEach((item) => {
      arr.push({
        oldWms: item.oldWms,
        id: item.id,
        containerType: item.containerType,
      });
    });
    const res = yield offContainer({ containerReqs: arr });
    if (res.code === '0') {
      Modal.success({ title: t('注销操作成功') });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },

  /**
   * 启动
   * @param {*} action
   */
  * on(action) {
    markStatus('dataLoading');
    const arr = [];
    action.itemList.forEach((item) => {
      arr.push({
        oldWms: item.oldWms,
        id: item.id,
        containerType: item.containerType,
      });
    });
    const res = yield onContainer({ containerReqs: arr });
    if (res.code === '0') {
      Modal.success({ title: t('启动操作成功') });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },

  /**
   * 打印
   * @param {*} action
   */
  * print() {
    const { itemList, paperSize } = yield '';
    // eslint-disable-next-line no-useless-catch
    try {
      markStatus('dataLoading');
      const arr = [];
      itemList.forEach((item) => {
        arr.push({
          oldWms: item.oldWms,
          id: item.id,
          containerType: item.containerType,
        });
      });
      const res = yield printContainer({ containerReqs: arr });

      const list = itemList.map((li) => ({
        containerCode: li.containerCode,
        barcode: li.containerCode ? textToBarcode(li.containerCode, { marginTop: '8px' }) : null,
      }));

      switch (paperSize) {
        case 1:
          transformToPdfUrl(tpl({
            list,
            tplType: '', // 兼容打包之后报错: tplType is not defined
          }), 50, 80, { landscape: true }, true);
          break;
        case 2:
          transformToPdfUrl(tpl({
            list,
            tplType: 'Small',
          }), 40, 80, { landscape: true }, true);
          break;
        default:
          break;
      }

      if (res.code === '0') {
        // 打印成功不用再触发搜索
      } else {
        Modal.error({ title: res.msg });
      }
    } catch (e) {
      console.error('e', e);
      throw e;
    }
  },
  * printSaga() {
    const status = yield new Promise((resolve) => {
      Modal.confirm({
        title: t('您确定要进行打印?'),
        text: {
          ok: t('确认'),
          cancel: t('取消'),
        },
        onOk: () => resolve(true),
        onCancel: () => resolve(false),
        onClose: () => resolve(false),
      });
    });
    yield this.changeData({
      togglePaperVisible: false,
    });
    if (status) {
      yield this.print();
    }
  },
  * uploadFile(formData) {
    markStatus('dataLoading');
    formData.append('function_node', '19');
    const res = yield formdataPost(containerImportURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 关闭批量导入打印弹框
  * closePrintModal() {
    yield this.changeData({
      printModal: false,
      uploadFile: '',
    });
  },
  // 批量导入打印
  * upload() {
    markStatus('dataLoading');
    const formData = new FormData();
    const { uploadFile } = yield '';
    formData.append('file', uploadFile);
    const res = yield formdataPost('/wmd/front/container/import_print', formData);
    if (res.code) {
      if (res.code === '0') {
        Message.success(t('导入成功'));
        transformToPdfUrl(tpl({
          list: res.info.container_codes.map((item) => ({
            containerCode: item,
            barcode: item ? textToBarcode(item, { marginTop: '8px' }) : null,
          })),
        }), 50, 80, { landscape: true }, true);

        yield this.closePrintModal();
        yield this.handlePaginationChange({ pageNum: 1 });
      } else {
        Modal.error({ title: res.msg });
      }
    } else {
      Message.error(t('数据校验完毕，部分数据有问题，请下载修正后再导入'));
      const blob = new Blob([res], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('导入失败')}.csv`);
    }
  },

  /**
   * 关闭弹窗
   * @param modalType
   * @param formRef
   * @returns {Generator<*, void, *>}
   */
  * closeModal({ modalType, formRef }) {
    // 关闭弹窗，并置为初始值
    yield this.changeData({
      [modalType]: false,
      // 目前仅一处调用该函数，此时直接将batchContainerInfo置为默认，若多处调用此处需做判断
      batchContainerInfo: defaultState.batchContainerInfo,
    });
    if (formRef && formRef.clearValidate) {
      formRef.clearValidate();
    }
  },

  /**
   * 批量新增/编辑容器
   * @returns {Generator<Generator<string|*, void, *>|*|string, void, *>}
   */
  * handleBatchContainer() {
    const { batchContainerModalType, batchContainerInfo, batchContainerFormRef } = yield '';
    const params = {
      operationType: batchContainerModalType,
      ...batchContainerInfo,
    };
    markStatus('dataLoading');
    const res = yield batchOperationContainerAPI(params);
    if (res.code === '0') {
      yield this.closeModal({ modalType: 'batchContainerModal', formRef: batchContainerFormRef });
      // 操作成功后跳转至导出下载页面
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: res.msg });
    }
  },
};
