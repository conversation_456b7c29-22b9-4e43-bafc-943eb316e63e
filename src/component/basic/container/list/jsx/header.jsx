import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Form, Input, Select, Rule,
} from 'shineout';
import InputMore from '@shein-components/inputMore';
import { formatSearchData } from '@public-component/search-queries/utils';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import DateRangePicker from '@shein-components/dateRangePicker2';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import { validateNumber } from '@src/lib/validate';
import store, { defaultLimit } from '../reducers';

const rules = Rule({
  printRule: {
    func: (val, formData, callback) => {
      if (formData.printRule || formData.printTimes) {
        if (!formData.printRule) {
          callback(new Error(t('请选择打印次数关系符')));
        }
        if (!formData.printTimes) {
          callback(new Error(t('请输入打印次数')));
        }
        if (formData.printTimes && !validateNumber().test(formData.printTimes)) {
          store.changeLimitData({ printTimes: '' });
          callback(new Error(t('打印次数必须为小于{}的非负整数', 100000)));
        }
      }
      callback(true);
    },
  },
  // 容器号为空时，容器类型必填
  containerRule: {
    func: (val, formData, callback) => {
      if ((!formData.containerCode) && (!formData.containerTypes?.length)) {
        callback(new Error(t('容器号为空时，容器类型必填！')));
      }

      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      printRuleList,
      usableStatusList,
      enabledList,
      containerTypeList,
      isOneTimesList,
      parkList,
      subWarehouseList,
      preSubWarehouseList,
      isRFIDList,
      RFIDStatusList,
      RFIDUsedList,
      formRef,
      physicalAttrListSelect,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 90 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          onChange={(val) => {
            if (formRef && formRef.validate) formRef.validate().catch(() => {});
            store.changeLimitData(formatSearchData(defaultLimit, val));
          }}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('打印次数'), t('容器号'), t('容器类型')]}
        >
          <InputMore
            label={t('容器号')}
            title={t('添加多个容器号,以回车键隔开')}
            placeholder={t('请输入')}
            modalplaceholder={t('支持输入多个容器号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            name="containerCode"
            max={100}
            maskCloseAble={false}
            overDisabled
            clearable
            required
          />
          <Form.Field
            span={2}
            name={['printRule', 'printTimes']}
            label={t('打印次数')}
            rules={[rules.printRule()]}
            required
          >
            {() => (
              <div style={{ display: 'flex' }}>
                <Select
                  style={{ marginRight: '10px' }}
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  data={printRuleList}
                  name="printRule"
                  clearable
                />
                <Input label={t('容器号')} name="printTimes" clearable />
              </div>
            )}
          </Form.Field>
          <Select
            label={t('使用状态')}
            name="usableStatus"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...usableStatusList]}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('可用状态')}
            name="enabled"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...enabledList]}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Form.Field
            label={t('容器类型')}
            name="containerTypes"
            rules={[rules.containerRule()]}
            required
          >
            <Select
              data={containerTypeList}
              keygen="dictCode"
              format="dictCode"
              renderItem="dictNameZh"
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
              multiple
              compressed
              placeholder={t('全部')}
            />
          </Form.Field>
          <Select
            label={t('一次性容器')}
            name="isOneTimes"
            data={isOneTimesList}
            keygen="id"
            format="id"
            renderItem="name"
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('所在园区')}
            name="parkType"
            keygen="parkType"
            format="parkType"
            renderItem="parkName"
            data={parkList}
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('请选择')}
            onChange={(val) => {
              if (val && val.length > 0) {
                const { parkSubWarehouseList } = fliterSubwarehouse(val);
                store.changeData({
                  subWarehouseList: parkSubWarehouseList,
                });
              } else {
                store.changeData({
                  subWarehouseList: preSubWarehouseList,
                });
              }
              store.changeLimitData({
                parkType: val,
                subWarehouseId: [],
              });
            }}
          />
          <Select
            label={t('所在子仓')}
            name="subWarehouseId"
            keygen="id"
            format="id"
            renderItem="nameZh"
            data={subWarehouseList}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
            placeholder={t('请选择')}
          />
          <Select
            label={t('是否绑定RFID')}
            name="bindRfId"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...isRFIDList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Input label="TID" name="tid" clearable />
          <Select
            label={t('RFID状态')}
            name="rfUsableStatus"
            data={RFIDStatusList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
            placeholder={t('全部')}
          />
          <Select
            label={t('RFID可用状态')}
            name="rfEnabled"
            data={RFIDUsedList}
            keygen="id"
            format="id"
            renderItem="name"
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Input
            label={t('归属部门')}
            name="department"
          />
          <Select
            label={t('容器物理属性')}
            name="physicalAttrList"
            data={physicalAttrListSelect}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            placeholder={t('全部')}
          />
          <DateRangePicker
            type="datetime"
            name={['startTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            inputable
            label={t('生成时间')}
            span={2}
          />
          <DateRangePicker
            type="datetime"
            defaultTime={['00:00:00', '23:59:59']}
            name={['updateStartTime', 'updateEndTime']}
            inputable
            label={t('更新时间')}
            span={2}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  printRuleList: PropTypes.arrayOf(PropTypes.shape()),
  usableStatusList: PropTypes.arrayOf(PropTypes.shape()),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  containerTypeList: PropTypes.arrayOf(PropTypes.shape()),
  isOneTimesList: PropTypes.arrayOf(PropTypes.shape()),
  isRFIDList: PropTypes.arrayOf(PropTypes.shape()),
  parkList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  RFIDStatusList: PropTypes.arrayOf(PropTypes.shape()),
  RFIDUsedList: PropTypes.arrayOf(PropTypes.shape()),
  formRef: PropTypes.shape(),
  physicalAttrListSelect: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
