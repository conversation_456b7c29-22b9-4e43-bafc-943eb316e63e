import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import fileSaver from 'file-saver';
import {
  Button, Form, Input, Modal, Rule, Select, DatePicker, Tooltip,
} from 'shineout';
import Icon from '@shein-components/Icon';
import styles from '@src/component/style.less';
import FileDelayUpload from '@public-component/upload/fileDelayUpload';
import { validateNaturalOneToNine } from '@src/lib/validate';
import { downloadTemplate, downloadTemplateAPI } from '../server';
import store from '../reducers';
import style from '../style.css';

const rules = Rule({
  containerPrefixRule: {
    func: (val, formData, callback) => {
      // 容器前缀禁止输入小写字母
      if (formData.prefix && /[a-z]/.test(formData.prefix)) {
        return callback(new Error(t('禁止输入小写字母')));
      }
      if (formData.prefix.length + formData.beginSerial.length > 15) {
        return callback(new Error(t('容器前缀+容器序号不可超过15位')));
      }
      callback(true);
    },
  },
  containerSerialRule: {
    func: (val, formData, callback) => {
      if (formData.beginSerial && formData.endSerial) {
        if (Number.isNaN(Number(formData.beginSerial)) || Number.isNaN(Number(formData.endSerial)) || !validateNaturalOneToNine().test(Number(formData.beginSerial)) || !validateNaturalOneToNine().test(Number(formData.endSerial))) {
          callback(new Error(t('开始序号与结束序号需输入大于0的正整数')));
        }
        if (formData.beginSerial.length !== formData.endSerial.length) {
          callback(new Error(t('开始序号需与结束序号位数相同')));
        }
        if (Number(formData.beginSerial) > Number(formData.endSerial)) {
          callback(new Error(t('开始序号需小于等于结束序号')));
        }
      }
      callback(true);
    },
  },
  specificationRule: {
    func: (val, formData, callback) => {
      if (formData.containerType === 9 && !formData.specification) {
        callback(new Error());
      }
      // 判断格式是否是数字*数字,其中数字不可为负数、支持浮点数
      if (formData.specification && (formData.specification.split('*').length !== 2 || formData.specification.split('*').some((item) => Number.isNaN(Number(item)) || Math.sign(Number(item)) !== 1))) {
        callback(new Error(t('规格格式有误')));
      }
      callback(true);
    },
  },
});

class Handle extends React.Component {
  render() {
    const {
      disabledOff,
      disabledOn,
      dataLoading,
      exportVisible,
      itemList,
      ids,
      printModal,
      uploadFile,
      importModalVisible, // 导入弹出框是否显示 默认不显示
      file, // 导入文件对象
      batchContainerModal,
      batchContainerInfo,
      warehouseList,
      containerTypeList,
      enabledList,
      isOneTimesList,
      batchContainerFormRef,
      batchContainerModalType,
      physicalAttrListSelect,
      currentWarehouseList,
      togglePaperVisible,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          className={style.buttonMargin}
          loading={!dataLoading}
          disabled={disabledOff}
          onClick={() => {
            Modal.confirm({
              title: t('您确定要把启用容器进行注销?'),
              okText: t('确认'),
              cancelText: t('取消'),
              onOk: () => {
                store.off({ itemList });
              },
            });
          }}
        >
          {t('注销')}
          <Tooltip
            style={{ minWidth: 140, textAlign: 'left' }}
            tip={(
              <div>
                {t('注销触发条件：')}
                <br />
                {t('1、至少勾选一条数据；')}
                <br />
                {t('2、已勾选的数据可用状态为：启用；')}
                <br />
                {t('3、已勾选的数据可用使用状态为：空闲。')}
              </div>
            )}
          >
            <Icon name="pc-info-circle-shineout" style={{ marginLeft: '5px' }} />
          </Tooltip>
        </Button>
        <Button
          type="primary"
          className={style.buttonMargin}
          loading={!dataLoading}
          disabled={disabledOn}
          onClick={() => {
            Modal.confirm({
              title: t('您确定要把注销的容器进行启用?'),
              okText: t('确认'),
              cancelText: t('取消'),
              onOk: () => {
                store.on({ itemList });
              },
            });
          }}
        >
          {t('启用')}
          <Tooltip
            style={{ minWidth: 140, textAlign: 'left' }}
            tip={(
              <div>
                {t('启用触发条件：')}
                <br />
                {t('1、至少勾选一条数据；')}
                <br />
                {t('2、已勾选的数据可用状态为：注销。')}
              </div>
            )}
          >
            <Icon name="pc-info-circle-shineout" style={{ marginLeft: '5px' }} />
          </Tooltip>
        </Button>
        <Button
          type="primary"
          className={style.buttonMargin}
          loading={!dataLoading}
          onClick={() => {
            store.changeData({
              file: '',
              importModalVisible: true,
            });
          }}
        >
          {t('导入')}
        </Button>
        <Modal
          visible={importModalVisible}
          maskCloseAble={null}
          title={t('导入')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={!file}
              loading={!dataLoading}
              onClick={() => {
                const form = new FormData();
                form.append('file', file);
                store.uploadFile(form);
              }}
            >
              {t('确认上传')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!dataLoading}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
        </Modal>
        <Button
          type="primary"
          className={style.buttonMargin}
          disabled={exportVisible}
          loading={!dataLoading}
          onClick={() => {
            store.exportData();
          }}
        >
          {t('导出')}
        </Button>
        <Button
          type="primary"
          className={style.buttonMargin}
          loading={!dataLoading}
          disabled={ids.length === 0}
          onClick={() => {
            const openPaperModal = () => {
              store.changeData({
                paperSize: 1,
                togglePaperVisible: true,
              });
            };

            // 所选容器中是否包含有归属部门的容器
            if (itemList.some((e) => e.department)) {
              const departmentList = [];
              itemList.forEach((e) => {
                if (e.department && !departmentList.includes(e.department)) {
                  departmentList.push(e.department);
                }
              });
              const departmentTip = departmentList.map((e) => e).join('/');

              Modal.confirm({
                title: (
                  <div className={style.confirmModalStyle}>
                    {t('所选的号段中存在为')}
                    {departmentTip}
                    {t('所有的号段，请确认无误后再进行打印')}
                  </div>
                ),
                okText: t('确认'),
                cancelText: t('取消'),
                onOk: () => {
                  openPaperModal();
                },
              });
            } else {
              openPaperModal();
            }
          }}
        >
          {t('打印')}
          <Tooltip
            style={{ minWidth: 140, textAlign: 'left' }}
            tip={(
              <div>
                {t('打印触发条件：')}
                <br />
                {t('1、至少勾选一条数据。')}
              </div>
            )}
          >
            <Icon name="pc-info-circle-shineout" style={{ marginLeft: '5px' }} />
          </Tooltip>
        </Button>
        <Button
          type="primary"
          className={style.buttonMargin}
          loading={!dataLoading}
          onClick={() => downloadTemplate()
            .then((d) => d.blob())
            .then((b) => {
              const blob = new Blob([b], { type: 'application/octet-stream' });
              fileSaver.saveAs(blob, `${t('模板下载')}.xlsx`);
            }).catch((error) => {
              Modal.error({
                title: error?.reason?.message || error?.message || t('下载失败,请检查网络连接或联系管理员'),
              });
            })}
        >
          {t('模版下载')}
        </Button>
        <Button
          type="primary"
          className={style.buttonMargin}
          loading={!dataLoading}
          onClick={() => store.changeData({
            printModal: true,
          })}
        >
          {t('批量导入打印')}
        </Button>

        <Button
          type="primary"
          className={style.buttonMargin}
          loading={!dataLoading}
          onClick={() => store.changeData({
            batchContainerModal: true,
            batchContainerModalType: 1,
          })}
        >
          {t('批量新增容器')}
        </Button>

        <Button
          type="primary"
          className={style.buttonMargin}
          loading={!dataLoading}
          onClick={() => store.changeData({
            batchContainerModal: true,
            batchContainerModalType: 2,
          })}
        >
          {t('批量编辑容器')}
        </Button>

        <Modal
          visible={printModal}
          title={t('批量导入打印')}
          maskCloseAble={null}
          onClose={() => {
            store.closePrintModal();
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => store.closePrintModal()}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              key="comfirm"
              disabled={!uploadFile}
              loading={!dataLoading}
              onClick={() => {
                store.upload();
              }}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div>
            <div className={style.printModalItem}>
              <span className={style.printModalItemSpan}>
                {t('导入文件')}
                :
              </span>
              <FileDelayUpload
                value={uploadFile}
                onChange={(f) => {
                  store.changeData({ uploadFile: f });
                }}
                accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,.xlsx,.xls,"
              />
            </div>
            <div className={style.printModalItem}>
              <span className={style.printModalItemSpan}>
                {t('下载模板')}
                :
              </span>
              <Button
                type="primary"
                size="default"
                loading={dataLoading === 0}
                onClick={() => downloadTemplateAPI()
                  .then((d) => d.blob())
                  .then((b) => {
                    const blob = new Blob([b], { type: 'application/octet-stream' });
                    fileSaver.saveAs(blob, `${t('导入模板')}.xlsx`);
                  }).catch((error) => {
                    Modal.error({
                      title: error?.reason?.message || error?.message || t('下载失败,请检查网络连接或联系管理员'),
                    });
                  })}
              >
                <Icon name="download" style={{ marginRight: 6 }} />
                {t('下载导入模版')}
              </Button>
            </div>
          </div>
        </Modal>
        <Modal
          maskCloseAble={null}
          visible={batchContainerModal}
          width={800}
          title={batchContainerModalType === 1 ? t('批量新增容器') : t('批量编辑容器')}
          inline
          onClose={() => { store.closeModal({ modalType: 'batchContainerModal', formRef: batchContainerFormRef }); }}
          footer={(
            <div>
              <Button onClick={() => { store.closeModal({ modalType: 'batchContainerModal', formRef: batchContainerFormRef }); }}>{t('关闭')}</Button>
              <Modal.Submit>{t('提交')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              Modal.confirm({
                title: t('提示'),
                content: (
                  <div>
                    <div className={style.confirmTip}>{t('请确认是否在{}中{}容器号?', warehouseList.find((item) => item.id === batchContainerInfo.warehouseId).nameZh, batchContainerModalType === 1 ? '新增' : '编辑')}</div>
                    <div className={style.redColor}>{`${batchContainerModalType === 1 ? t('新增') : t('编辑')}${t('容器号')}: ${batchContainerInfo.prefix}${batchContainerInfo.beginSerial} —— ${batchContainerInfo.prefix}${batchContainerInfo.endSerial}`}</div>
                  </div>),
                okText: t('确认'),
                cancelText: t('取消'),
                onOk: () => {
                  store.handleBatchContainer();
                },
              });
            }}
            onChange={(value) => {
              store.changeData({
                batchContainerInfo: value,
              });
            }}
            value={batchContainerInfo}
            inline
            formRef={(f) => store.changeData({ batchContainerFormRef: f })}
          >
            <Form.Item required label={t('容器号前缀')}>
              <Input
                name="prefix"
                placeholder={t('请输入')}
                rules={[rules.containerPrefixRule, rules.required(t('容器号前缀必填'))]}
                autoComplete="off"
              />
            </Form.Item>
            <Form.Item required label={t('容器序号')}>
              <Input
                name="beginSerial"
                placeholder={t('开始序号')}
                style={{ width: 90 }}
                rules={[rules.required(t('容器开始序号必填')), rules.containerSerialRule()]}
                onChange={() => {
                  batchContainerFormRef.validateFields(['endSerial']);
                  batchContainerFormRef.validateFields(['prefix']);
                }}
              />
              &nbsp;~&nbsp;
              <Input
                name="endSerial"
                placeholder={t('结束序号')}
                style={{ width: 90 }}
                rules={[rules.required(t('容器结束序号必填'))]}
                onChange={() => {
                  batchContainerFormRef.validateFields(['beginSerial']);
                }}
              />
            </Form.Item>
            <Form.Item required label={t('仓库')}>
              <Select
                name="warehouseId"
                data={currentWarehouseList}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="nameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                rules={[rules.required(t('仓库必填'))]}
              />
            </Form.Item>
            <Form.Item required label={t('容器类型')}>
              <Select
                name="containerType"
                data={containerTypeList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                rules={[rules.required(t('容器类型必填'))]}
              />
            </Form.Item>
            <Form.Item label={t('可用状态')}>
              <Select
                name="enabled"
                data={enabledList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                dictNameZh
                rules={[rules.required(t('可用状态必填'))]}
              />
            </Form.Item>
            <Form.Item label={t('是否一次性容器')}>
              <Select
                name="isOneTimes"
                data={isOneTimesList}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="name"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.name || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                dictNameZh
                rules={[rules.required(t('是否一次性容器必填'))]}
              />
            </Form.Item>
            <Form.Item label={t('归属部门')}>
              <Input
                name="department" placeholder={t('请输入')}
                autoComplete="off"
              />
            </Form.Item>
            {/* 二分播种墙 - 9 */}
            <Form.Item required={batchContainerInfo.containerType === 9} label={t('规格')}>
              <Input
                name="specification" placeholder={t('请输入')}
                rules={[rules.specificationRule]}
                autoComplete="off"
              />
              <div className={style.labelMargin}>{t('(二分播种墙必填,格式为数字*数字,如5*7)')}</div>
            </Form.Item>
            <Form.Item label={t('使用截止时间')}>
              <DatePicker
                absolute
                style={{ width: 200 }}
                type="date"
                name="useCutOffTime"
                placeholder={t('请选择')}
              />
            </Form.Item>
            <Form.Item label={t('容器物理属性')}>
              <Select
                name="physicalAttr"
                data={physicalAttrListSelect}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
          </Form>
        </Modal>
        {/* 打印按钮 */}
        <Modal
          maskCloseAble={null}
          title={t('请选择纸张规格')}
          visible={togglePaperVisible}
          width={500}
          onClose={() => store.changeData({
            togglePaperVisible: false,
          })}
          footer={[
            <Button
              key="cancel" onClick={() => store.changeData({
                togglePaperVisible: false,
              })}
            >
              {t('取消')}
            </Button>,
            <Button
              key="ok" type="primary" onClick={() => {
                store.printSaga();
              }}
            >
              {t('确定')}
            </Button>,
          ]}
        >
          <div className={styles.inner_list} style={{ display: 'flex' }}>
            <span className={styles.labWidth}>
              {t('请选择纸张规格')}
              :
            </span>
            <Select
              keygen="dictCode"
              format="dictCode"
              renderItem="dictNameZh"
              data-bind="paperSize"
              data={[
                {
                  dictCode: 1,
                  dictNameZh: '80*50',
                },
                {
                  dictCode: 2,
                  dictNameZh: '80*40',
                },
              ]}
              placeholder={t('请选择')}
            />
          </div>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  itemList: PropTypes.arrayOf(PropTypes.shape()),
  ids: PropTypes.arrayOf(PropTypes.string),
  dataLoading: PropTypes.number,
  exportVisible: PropTypes.bool,
  disabledOff: PropTypes.bool,
  disabledOn: PropTypes.bool,
  printModal: PropTypes.bool,
  uploadFile: PropTypes.shape(),
  importModalVisible: PropTypes.bool,
  file: PropTypes.shape(),
  batchContainerModal: PropTypes.bool,
  batchContainerInfo: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  containerTypeList: PropTypes.arrayOf(PropTypes.shape()),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  isOneTimesList: PropTypes.arrayOf(PropTypes.shape()),
  batchContainerFormRef: PropTypes.shape(),
  batchContainerModalType: PropTypes.number,
  physicalAttrListSelect: PropTypes.arrayOf(PropTypes.shape()),
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  togglePaperVisible: PropTypes.bool,
};
export default Handle;
