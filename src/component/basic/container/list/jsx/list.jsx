import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Select } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import Icon from '@shein-components/Icon';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      itemList,
      recordVisible,
      recordId,
      maxPageNumber,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 120,
      },
      {
        title: t('容器号'),
        render: 'containerCode',
        width: 150,
      },
      {
        title: t('使用状态'),
        render: 'usableStatusName',
        width: 90,
      },
      {
        title: t('可用状态'),
        render: 'enabledName',
        key: '3',
        width: 90,
      },
      {
        title: t('容器类型'),
        render: 'containerTypeName',
        key: '4',
        width: 120,
      },
      {
        title: t('规格'),
        render: 'specification',
        key: '41',
        width: 120,
      },
      {
        title: t('打印次数'),
        render: 'printTimes',
        width: 100,
      },
      {
        title: t('是否一次性容器'),
        render: 'isOneTimesName',
        width: 150,
      },
      {
        title: t('容器物理属性'),
        render: 'physicalAttrName',
        width: 150,
      },
      {
        title: t('所在园区'),
        render: 'parkTypeName',
        width: 100,
      },
      {
        title: t('所在子仓'),
        render: 'subWarehouseName',
        width: 100,
      },
      {
        title: 'TID',
        render: 'tid',
        width: 100,
      },
      {
        title: t('RFID状态'),
        render: 'rfUsableStatusName',
        width: 100,
      },
      {
        title: t('RFID可用状态'),
        render: 'rfEnabledName',
        width: 100,
      },
      {
        title: t('连续空交接次数'),
        render: 'emptyHandoversNum',
        width: 100,
      },
      {
        title: t('使用截止日期'),
        width: 190,
        render: (record) => (
          <div>
            {
              record.useCutOffTime && record.useCutOffTime.startsWith('1970') ? '' : record.useCutOffTime
            }
          </div>
        ),
      },
      {
        title: t('生成时间'),
        render: 'createTime',
        width: 190,
      },
      {
        title: t('最后更新时间'),
        render: 'lastUpdateTime',
        width: 190,
      },
      {
        title: t('归属部门'),
        render: 'department',
        width: 150,
      },
      {
        title: t('操作'),
        width: 100,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              size="small"
              text
              type="primary"
              className={styles.tableTextButton}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];

    const rowSelection = (selectedRows) => {
      const selected = selectedRows.map((i) => i.id);
      store.changeData({
        ids: selected,
        itemList: selectedRows,
        isVisible: false,
      });
      const result = selectedRows.every((item) => Number(item.enabled) === 1 && Number(item.usableStatus) === 1);
      const result2 = selectedRows.every((item) => Number(item.enabled) === 2);
      if (result && selected.length > 0) {
        store.changeData({
          disabledOff: false,
        });
      } else {
        store.changeData({
          disabledOff: true,
        });
      }
      if (result2 && selected.length > 0) {
        store.changeData({
          disabledOn: false,
        });
      } else {
        store.changeData({
          disabledOn: true,
        });
      }
    };

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={itemList}
            onRowSelect={rowSelection}
          />
          <div
            className={styles.pagination}
            style={{ textAlign: 'right' }}
          >
            <Button
              type="primary"
              disabled={pageInfo.pageNum === 1}
              onClick={() => {
                store.handlePaginationChange({
                  pageNum: pageInfo.pageNum - 1,
                  pageSize: pageInfo.pageSize,
                  isPageClick: true,
                });
              }}
            >
              <Icon name="pc-arrow-left" />
            </Button>
            {
              (pageInfo.pageNum > 10 || maxPageNumber > 10) ? (
                <>
                  {Array.from({ length: 10 }).map((pi, pIdx) => (
                    <Button
                      type={pageInfo.pageNum === (pIdx + 1) ? 'primary' : 'default'}
                      onClick={() => {
                        store.handlePaginationChange({
                          pageNum: pIdx + 1,
                          pageSize: pageInfo.pageSize,
                          isPageClick: true,
                        });
                      }}
                    >
                      {pIdx + 1}
                    </Button>
                  ))}
                  <Button
                    disabled
                    type="default"
                  >
                    <Icon name="more" />
                  </Button>
                </>
              ) : Array.from({ length: maxPageNumber }).map((pi, pIdx) => (
                <Button
                  type={pageInfo.pageNum === (pIdx + 1) ? 'primary' : 'default'}
                  onClick={() => {
                    store.handlePaginationChange({
                      pageNum: pIdx + 1,
                      pageSize: pageInfo.pageSize,
                      isPageClick: true,
                    });
                  }}
                >
                  {pIdx + 1}
                </Button>
              ))
            }
            <Button
              type="primary"
              disabled={!list.length || list.length < pageInfo.pageSize}
              onClick={() => {
                store.handlePaginationChange({
                  pageNum: pageInfo.pageNum + 1,
                  pageSize: pageInfo.pageSize,
                  isPageClick: true,
                });
              }}
            >
              {pageInfo.pageNum > 10 && pageInfo.pageNum}
              <Icon name="pc-arrow-right" />
            </Button>
            <Select
              keygen
              style={{ marginLeft: '10px', width: 80 }}
              data={pageInfo.pageSizeList}
              value={pageInfo.pageSize}
              onChange={(value) => {
                store.handlePaginationChange({
                  pageNum: 1,
                  pageSize: value,
                });
              }}
            />
          </div>
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'CONTAINER_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  itemList: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  recordId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  maxPageNumber: PropTypes.number,
};

export default List;
