import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';
import { camel2Under } from '@src/lib/camal-case-convertor';
// 搜索
// export const getListApi = (param) => sendPostRequest({
//   url: '/shift_order/list_order',
//   param,
// });

// 导出
// export const exportListApi = (param) => sendPostRequest({
//   url: '/shift_order/export',
//   param,
// });

// 模板下载
export const downloadTemplate = () => {
  const uri = `${process.env.BASE_URI_WMD}/container/download_template`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under()),
  });
};

// 导入模板下载
export const downloadTemplateAPI = () => fileFetch(`${process.env.BASE_URI_WMD}/excel/export_template?type=container_import_print_template.xlsx`, {
  method: 'GET',
  credentials: 'include',
});

// 导出
export const exportFile = (param) => sendPostRequest({
  url: '/container/export',
  param,
}, process.env.BASE_URI_WMD);

// 查询
export const queryContainer = (param) => sendPostRequest({
  url: '/container/list',
  param,
}, process.env.BASE_URI_WMD);

export const offContainer = (param) => sendPostRequest({
  url: '/container/disable',
  param,
}, process.env.BASE_URI_WMD);

export const onContainer = (param) => sendPostRequest({
  url: '/container/enable',
  param,
}, process.env.BASE_URI_WMD);

// 打印
export const printContainer = (param) => sendPostRequest({
  url: '/container/print',
  param,
}, process.env.BASE_URI_WMD);

// 容器管理 - 查询oldWms的操作记录
// export const queryOldWmsConfig = (param) => sendPostRequest({
//   url: '/container/old_record_list',
//   param,
// }, process.env.BASE_URI_WMD);

// 导入接口
export const containerImportURL = `${process.env.WGS_FRONT}/file_import/record/wmd/container_import`;

// 批量新增/编辑容器
export const batchOperationContainerAPI = (param) => sendPostRequest({
  url: '/container/batch_operation_container',
  param,
}, process.env.BASE_URI_WMD);
