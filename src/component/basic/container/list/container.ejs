<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family:serif;
        }
        @page {
            margin: 0.5mm 0mm;
        }
        .box{
            width: 100%;
            height: 100%;
            page-break-before: always;
            page-break-after: always;
            color: #000;
            font-size: 16px;
        }
        .boxWrap{
            width: 80mm;
            height: 49mm;
            overflow: hidden;
            text-align: center;
        }
        .codeWrap{
            width: 60mm;
            height: 35mm;
            margin: 4mm auto 0;
        }
        .codeWrap>img{
            width: 100%;
            height: 100%;
        }
        .code{
            width: 100%;
            height: 10mm;
            line-height: 10mm;
            font-size: 30px;
        }
        .barCode{
            font-family: "Bar-Code 39";
            font-size: 1.3em;
        }
        .boxWrapSmall {
            width: 80mm;
            height: 39mm;
            overflow: hidden;
            text-align: center;
        }

        .boxWrapSmall .codeWrap {
            width: 60mm;
            height: 25mm;
            margin: 4mm auto 0;
        }

        .boxWrapSmall .code {
            width: 100%;
            height: 9mm;
            line-height: 9mm;
            font-size: 30px;
        }
    </style>
</head>
<body>
<div class="box">
    <% const name='boxWrap' + (tplType || '' ); %>
    <% for (var i = 0; i < list.length; i ++) { %>
    <% var item = list[i] %>
    <div class="<%=name%>">
        <div class="codeWrap barCode">
            <img src=<%= item.barcode %>>
        </div>
        <div class="code"><%=item.containerCode%></div>
    </div>
    <% } %>
</div>
</body>
</html>
