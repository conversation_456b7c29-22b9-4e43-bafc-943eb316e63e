import { sendPostRequest } from '@src/server/common/public';

// 日常补货剔除历史销量配置-查询
export const getListAPI = (param) => sendPostRequest({
  url: '/daily_history_sale_config/query',
  param,
}, process.env.WWS_URI);

// 新增
export const addConfigAPI = (param) => sendPostRequest({
  url: '/daily_history_sale_config/addOrEdit',
  param,
}, process.env.WWS_URI);

// 删除
export const deleteConfigAPI = (param) => sendPostRequest({
  url: '/daily_history_sale_config/delete',
  param,
}, process.env.WWS_URI);

export const appointmentQueryAPI = (param) => sendPostRequest({
  url: '/daily_history_sale_config/reserve/query',
  param,
}, process.env.WWS_URI);

export const appointmentAPI = (param) => sendPostRequest({
  url: '/daily_history_sale_config/reserve/save',
  param,
}, process.env.WWS_URI);
