import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Select, Modal, Input, Form, Rule, Textarea, DatePicker, Message,
} from 'shineout';
import { t } from '@shein-bbl/react';
import globalStyles from '@src/component/style.less';
import moment from 'moment';
import { defaultEditObj } from '../reducers';

// 校验日期格式
function isValidDate(dateString) {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateString.match(regex)) {
    return false;
  }
  const date = new Date(dateString);
  const timestamp = date.getTime();
  if (typeof timestamp !== 'number' || Number.isNaN(timestamp)) {
    return false;
  }
  return date.toISOString().startsWith(dateString);
}

const rules = Rule({
  reservationTimeRange: {
    func: (val, formData, callback) => {
      const currentTime = moment();
      const selectTime = moment(val);
      // 选择时间不能小于当前时间
      if (selectTime.isBefore(currentTime)) {
        Message.warn(t('选择时间不能小于当前时间'));
        callback(new Error(t('选择时间不能小于当前时间')));
      }
      callback(true);
    },
  },
  excludeSaleDateRange: {
    func: (val, formData, callback) => {
      const dateArray = val?.split(';').map((date) => date.trim()).filter((e) => e);
      const invalidDates = dateArray.filter((date) => !isValidDate(date));
      if (invalidDates.length > 0) {
        callback(new Error(t('输入格式不正确，请输入格式为"YYYY-MM-DD"的日期，多个日期用英文分号隔开')));
      }
      callback(true);
    },
  },
});

class Handle extends React.Component {
  render() {
    const {
      loading,
      warehouseId,
      editObj,
      editObjVisible,
      warehouseList, // 仓库列表
      nationalLineList,
      store,
    } = this.props;
    const inputStyle = {
      width: '200px',
    };

    //  1 新增，2 编辑，3 预约配置
    let modalTitle = '';
    switch (editObjVisible) {
      case 1:
        modalTitle = t('新增');
        break;
      case 2:
        modalTitle = t('编辑');
        break;
      case 3:
        modalTitle = t('预约配置');
        break;
      default:
        break;
    }
    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            // 获取仓库
            if (!warehouseId) {
              Modal.error({ title: t('请在右上角选择仓库') });
              return;
            }
            store.changeData({
              editObjVisible: 1,
              editObj: { ...defaultEditObj, warehouseId },
            });
          }}
        >
          {t('新增')}
        </Button>
        <Modal
          visible={editObjVisible}
          width={600}
          destroy
          maskCloseAble={null}
          title={modalTitle}
          onClose={() => store.changeData({
            editObjVisible: 0,
          })}
          footer={(
            <div>
              <Button
                onClick={() => store.changeData({ editObjVisible: 0 })}
              >
                {t('取消')}
              </Button>
              <Modal.Submit loading={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={180}
            labelAlign="right"
            onSubmit={() => store.confirmEdit()}
            onChange={(value) => {
              store.changeData({
                editObj: value,
              });
            }}
            value={editObj}
          >
            <div>
              <h4>
                {t('基础信息')}
              </h4>
              <Form.Item required label={`${t('仓库')}:`}>
                <Select
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  name="warehouseId"
                  style={inputStyle}
                  disabled={[2, 3].includes(editObjVisible)}
                  data={warehouseList}
                  placeholder={t('请选择')}
                  rules={[rules.required(t('请选择仓库'))]}
                />
              </Form.Item>
              <Form.Item required label={`${t('国家线')}:`}>
                <Select
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  data={nationalLineList}
                  disabled={[2, 3].includes(editObjVisible)}
                  compressed
                  clearable
                  style={inputStyle}
                  placeholder={t('请选择')}
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  name="nationalLineType"
                  rules={[rules.required(t('请选择国家线'))]}
                />
              </Form.Item>
              <h4>
                {t('参数信息')}
              </h4>
              <Form.Item required label={`${t('获取历史销售库存天数')}:`}>
                <Input.Number
                  allowNull
                  digits={0}
                  hideArrow
                  min={1}
                  max={99}
                  placeholder={t('请输入')}
                  style={inputStyle}
                  name="historySaleStockDay"
                  autocomplete="off"
                  rules={[rules.required(t('请输入获取历史销售库存天数'))]}
                />
              </Form.Item>
              <Form.Item label={t('日常补货踢除指定日期销量')}>
                <Textarea
                  name="excludeSaleDateList"
                  placeholder={t('请输入日期，格式“2024-01-01”英文分号隔开')}
                  maxLength={1000}
                  rows={5}
                  rules={[rules.excludeSaleDateRange()]}
                />
              </Form.Item>
              {
            editObjVisible === 3 && (
              <>
                <h4>
                  {t('预约时间')}
                </h4>
                <Form.Item label={t('预约时间')} required>
                  <DatePicker
                    inputable
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    width={220}
                    name="reserveEffectTime"
                    rules={[rules.required, rules.reservationTimeRange()]}
                    disabled={(value) => {
                      const currentTime = moment().subtract(1, 'days');
                      const selectTime = moment(value, 'yyyy-MM-dd HH:mm:ss');
                      // 选择时间不能小于当前时间
                      return selectTime.isBefore(currentTime);
                    }}
                  />
                </Form.Item>
              </>
            )
          }
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  warehouseId: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  editObj: PropTypes.shape(),
  editObjVisible: PropTypes.number,
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  nationalLineList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
};
export default Handle;
