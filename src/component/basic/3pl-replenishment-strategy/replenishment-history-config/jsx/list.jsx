import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Button, Popover,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import OperationModal from '@public-component/modal/operation-modal';
import globalStyles from '@src/component/style.less';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      store,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('国家线'),
        render: 'nationalLineTypeName',
        width: 120,
      },
      {
        title: t('获取历史销售库存天数'),
        render: 'historySaleStockDay',
        width: 130,
      },
      {
        title: t('日常补货踢除指定日期销量'),
        render: (row) => (row.excludeSaleDateList || []).join(';'),
        width: 150,
      },
      {
        title: t('操作人'),
        render: 'operateUserName',
        width: 150,
      },
      {
        title: t('预约时间'),
        render: 'reserveEffectTime',
        width: 190,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 190,
      },
      {
        title: t('操作'),
        width: 200,
        fixed: 'right',
        render: (record) => (
          <>
            <Button
              type="primary"
              text
              onClick={() => {
                store.changeData({
                  editObjVisible: 2,
                  editObj: {
                    ...record,
                    excludeSaleDateList: (record.excludeSaleDateList || []).join(';'),
                  },
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              type="primary"
              text
              disabled={!loading}
            >
              <Popover.Confirm
                type="warning"
                okType="primary"
                text={{ ok: t('确认'), cancel: t('取消') }}
                onOk={() => {
                  store.deleteData(record.id);
                }}
              >
                {t('请确认是否删除该策略配置?')}
              </Popover.Confirm>
              {t('删除')}
            </Button>
            <Button
              text
              type="primary"
              className={globalStyles.tableTextButton}
              disabled={!loading}
              onClick={() => {
                store.handleAppointment(record);
              }}
            >
              {t('预约配置')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'DAILY_REPLENISH_HISTORY_SALE_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  store: PropTypes.shape(),
};

export default List;
