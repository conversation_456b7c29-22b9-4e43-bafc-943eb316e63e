import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select, Rule } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import DateRangePicker from '@shein-components/dateRangePicker2';
import moment from 'moment';
import { defaultLimit } from '../reducers';

const rule = Rule({
  timeRange: {
    func: (_, formData, callback) => {
      if (!formData.startTime || !formData.endTime) {
        callback(new Error(t('开始时间或结束时间必选')));
      }
      // 开始时间和结束时间不能超过30天
      if (moment(formData.endTime)
        .customDiff(moment(formData.startTime), 'days', true) > 30) {
        callback(new Error(t('开始时间和结束时间不能超过{}天', 30)));
      }
      callback(true);
    },
  },
});

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      nationalLineList,
      store,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('国家线')}
            name="nationalLineTypeList"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={nationalLineList}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['startTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('更新时间')}
            span={2}
            rules={[rule.timeRange()]}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  nationalLineList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
};
export default Header;
