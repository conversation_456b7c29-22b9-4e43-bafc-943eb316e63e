import { sendPostRequest } from '@src/server/common/public';

// 搜索
export const getListAPI = (param) => sendPostRequest({
  url: '/front_warehouse_restock_config/query',
  param,
}, process.env.WWS_URI);

// 新增
export const addConfigAPI = (param) => sendPostRequest({
  url: '/front_warehouse_restock_config/edit',
  param,
}, process.env.WWS_URI);

// 删除
export const deleteConfigAPI = (param) => sendPostRequest({
  url: '/front_warehouse_restock_config/delete',
  param,
}, process.env.WWS_URI);

// 快慢流等级配置-获取快流等级
export const queryFastLevelAPI = (param) => sendPostRequest({
  url: '/fast_slow_flow_level_config/query_fast_level',
  param,
}, process.env.WWS_URI);
