import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import {
  getListAPI, addConfigAPI, deleteConfigAPI, queryFastLevelAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  nationalLineIdList: [],
};

export const defaultEditObj = {
  warehouseId: 1, // 仓库
  nationalLineId: '', // 国家线
  fastLevel: '', // 快流SKU等级
  frontWarehouseDay: '', // 前置仓安全库存天数
  id: '',
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  editObj: defaultEditObj,
  editObjVisible: 0, // 1 新增，2 编辑
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  warehouseId: '', // 右上角控制
  warehouseList: [], // 仓库列表
  list: [],
  selectedRows: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  nationalLineList: [], // 国家线下拉数据
  modalFormRef: {},
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改搜索条件即state.limit属性值
  changeEditObjData(state, data) {
    Object.assign(state.editObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const { warehouseList, warehouseId } = yield 'nav';
    yield this.changeData({ warehouseId, warehouseList });

    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['NATIONAL_LINE_TYPE'] }),
    ]);

    if (selectData.code === '0') {
      yield this.changeData({
        nationalLineList: selectData.info.data.find((item) => item.catCode === 'NATIONAL_LINE_TYPE').dictListRsps,
      });
    } else {
      Modal.error({ title: selectData.msg });
    }
  },
  // 搜索
  * search() {
    const { warehouseId } = yield 'nav';
    // 获取仓库
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = this.state;
    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        selectedRows: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = this.state;
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { warehouseId } = data;
    yield this.changeData({ warehouseId });
    yield this.getSubWarehouse({ warehouseId });
  },
  /**
   * @description 快慢流等级配置-获取快流等级
   * @param warehouseId {number} 右上角的仓库
   * @returns {Generator<void|*, void, *>}
   */
  * queryFastLevel({ nationalLineType }) {
    let fastLevelList = [];
    // 有仓库id才请求
    if (nationalLineType || nationalLineType === 0) {
      const { warehouseId } = yield 'nav';
      const { code, info, msg } = yield queryFastLevelAPI({ nationalLineType, warehouseId });
      if (code === '0') {
        if (info && info.length > 0) {
          fastLevelList = info.map((item) => (
            {
              dictCode: item,
              dictNameZh: item,
            }
          ));
        }
      } else {
        Modal.error({ title: msg });
      }
    }
    yield this.changeEditObjData({ fastLevel: '' });
    // 修改园区下拉数据
    yield this.changeData({ fastLevelList });
  },
  // 1 新增 2 编辑
  * confirmEdit() {
    markStatus('loading');
    const tip = this.state.editObjVisible === 1 ? t('新增成功') : t('编辑成功');
    const { code, msg } = yield addConfigAPI({ ...this.state.editObj });
    if (code === '0') {
      yield this.changeData({ editObjVisible: 0 });
      Message.success(tip);
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 删除
  * deleteData(id) {
    markStatus('loading');
    const { code, msg } = yield deleteConfigAPI({ ids: [id] });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 校验
  * handleModalValidate() {
    const { modalFormRef } = this.state;
    let validateFlag = false;
    yield modalFormRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
};
