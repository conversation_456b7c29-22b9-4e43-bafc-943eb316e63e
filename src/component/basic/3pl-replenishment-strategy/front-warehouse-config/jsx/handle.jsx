import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Select, Modal, Input, Form, Rule,
} from 'shineout';
import { t } from '@shein-bbl/react';
import globalStyles from '@src/component/style.less';
import { defaultEditObj } from '../reducers';

const rules = Rule();

class Handle extends React.Component {
  render() {
    const {
      loading,
      warehouseId,
      editObj,
      editObjVisible,
      warehouseList, // 仓库列表
      fastLevelList,
      nationalLineList,
      store,
    } = this.props;
    const inputStyle = {
      width: '200px',
    };
    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            // 获取仓库
            if (!warehouseId) {
              Modal.error({ title: t('请在右上角选择仓库') });
              return;
            }
            store.changeData({
              editObjVisible: 1,
              editObj: { ...defaultEditObj, warehouseId },
            });
          }}
        >
          {t('新增')}
        </Button>
        <Modal
          type="info"
          visible={editObjVisible}
          width={500}
          destroy
          maskCloseAble={null}
          title={editObjVisible === 1 ? t('新增') : t('编辑')}
          onClose={() => store.changeData({
            editObjVisible: 0,
          })}
          footer={(
            <div>
              <Button
                onClick={() => store.changeData({ editObjVisible: 0 })}
              >
                {t('取消')}
              </Button>
              <Modal.Submit loading={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={160}
            labelAlign="right"
            onSubmit={() => store.confirmEdit()}
            onChange={(value) => {
              store.changeData({
                editObj: value,
              });
            }}
            value={editObj}
          >
            <div>
              <Form.Item required label={`${t('仓库')}:`}>
                <Select
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  name="warehouseId"
                  style={inputStyle}
                  disabled={editObjVisible === 2}
                  data={warehouseList}
                  placeholder={t('请选择')}
                  rules={[rules.required(t('请选择仓库'))]}
                />
              </Form.Item>
              <Form.Item required label={`${t('国家线')}:`}>
                <Select
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  data={nationalLineList}
                  disabled={editObjVisible === 2}
                  compressed
                  clearable
                  style={inputStyle}
                  placeholder={t('请选择')}
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  name="nationalLineId"
                  onChange={(val) => {
                    store.changeEditObjData({ nationalLineId: val });
                    store.queryFastLevel({ nationalLineType: val });
                  }}
                  rules={[rules.required(t('请选择国家线'))]}
                />
              </Form.Item>
              <Form.Item label={`${t('快流SKU等级')}:`}>
                <Select
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  name="fastLevel"
                  disabled={editObjVisible === 2 || !editObj.nationalLineId}
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  style={inputStyle}
                  data={fastLevelList}
                  placeholder={t('请选择')}
                />
              </Form.Item>

              <Form.Item required label={`${t('前置仓安全库存天数')}:`}>
                <Input.Number
                  allowNull
                  digits={1}
                  hideArrow
                  min={0.1}
                  max={99.9}
                  placeholder={t('请输入')}
                  style={inputStyle}
                  name="frontWarehouseDay"
                  autocomplete="off"
                  rules={[rules.required(t('请输入前置仓安全库存天数'))]}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  warehouseId: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  editObj: PropTypes.shape(),
  editObjVisible: PropTypes.number,
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  fastLevelList: PropTypes.arrayOf(PropTypes.shape()),
  nationalLineList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
};
export default Handle;
