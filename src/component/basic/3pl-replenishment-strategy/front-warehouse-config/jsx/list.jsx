import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Button, Popover,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import OperationModal from '@public-component/modal/operation-modal';
import globalStyles from '@src/component/style.less';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      store,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('国家线'),
        render: 'nationalLineName',
        width: 120,
      },
      {
        title: t('快流SKU等级'),
        render: 'fastLevel',
        width: 120,
      },
      {
        title: t('前置仓安全库存天数'),
        render: 'frontWarehouseDay',
        width: 150,
      },
      {
        title: t('创建人'),
        render: 'creator',
        width: 150,
      },
      {
        title: t('创建时间'),
        render: 'createTime',
        width: 190,
      },
      {
        title: t('更新人'),
        render: 'updater',
        width: 120,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 190,
      },
      {
        title: t('操作'),
        width: 200,
        fixed: 'right',
        render: (record) => (
          <>
            <Button
              type="primary"
              text
              onClick={() => {
                const d = record || {};
                store.changeData({
                  editObjVisible: 2,
                  editObj: {
                    ...d,
                  },
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              type="primary"
              text
              disabled={!loading}
            >
              <Popover.Confirm
                type="warning"
                okType="primary"
                text={{ ok: t('确认'), cancel: t('取消') }}
                onOk={() => {
                  store.deleteData(record.id);
                }}
              >
                {t('是否确认删除包裹国家线（{}）配置？', record.nationalLineName)}
              </Popover.Confirm>
              {t('删除')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'FRONT_WAREHOUSE_RESTOCK_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  store: PropTypes.shape(),
};

export default List;
