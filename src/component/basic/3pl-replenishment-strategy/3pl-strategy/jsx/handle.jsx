import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Button } from 'shineout';
import globalStyles from '@src/component/style.less';

class Handle extends React.Component {
  render() {
    const {
      loading,
      store,
      selectedRows,
    } = this.props;

    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              isAppointment: false,
            });
            store.openModal({ type: 'add' });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={selectedRows.length !== 1 || !loading}
          onClick={() => {
            store.handleAppointment();
          }}
        >
          {t('预约配置')}
        </Button>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  store: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default Handle;
