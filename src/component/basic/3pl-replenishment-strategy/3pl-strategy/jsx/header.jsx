import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import { defaultLimit } from '../3pl.reducers';
import Modal from './modal';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      parkTypeList,
      subWarehouseList,
      store,
    } = this.props;

    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('园区')}
            name="parkType"
            data={parkTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            onChange={(val, d) => {
              if (d?.parkType) {
                const { parkSubWarehouseList } = fliterSubwarehouse([d?.parkType]);
                store.changeData({
                  subWarehouseList: parkSubWarehouseList,
                });
              }
              store.changeLimitData({
                parkType: val,
                subWarehouseId: [],
              });
            }}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('子仓')}
            name="subWarehouseId"
            data={subWarehouseList}
            keygen="id"
            format="id"
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
        </SearchAreaContainer>
        <Modal {...this.props} />
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
};

export default Header;
