import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Modal, Button, Select, Radio, Checkbox, Input, Table, Rule, DatePicker, Message,
} from 'shineout';
import Icon from '@shein-components/Icon';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import moment from 'moment';
import {
  MODAL_VISIBLE_CLOSE, MODAL_VISIBLE_ADD, MODAL_VISIBLE_EDIT, defaultEditObj,
} from '../3pl.reducers';
import style from '../../style.less';

const rules = Rule({
  numberRange: {
    func: (val, formData, callback) => {
      if (Number(val) > 100 || Number(val) < 1) {
        callback(new Error(t('输入值范围为{}，且小数点最多{}位', '1-100', 2)));
      }
      callback(true);
    },
  },
  reservationTimeRange: {
    func: (val, formData, callback) => {
      const currentTime = moment();
      const selectTime = moment(val);
      // 选择时间不能小于当前时间
      if (selectTime.isBefore(currentTime)) {
        Message.warn(t('选择时间不能小于当前时间'));
        callback(new Error(t('选择时间不能小于当前时间')));
      }
      callback(true);
    },
  },
});

class ConfigModal extends React.Component {
  render() {
    const {
      loading,
      editObjVisible,
      editObj,
      statusList,
      warehouseList,
      editObjSubWarehouseList,
      editStorageTagList,
      canAdd,
      canDelete,
      parkTypeList,
      upParkTypeList,
      store,
      isAppointment,
    } = this.props;

    const { strategy } = editObj;

    const columns = [
      {
        title: t('策略'),
        render: (record, index) => `${t('策略')}${index + 1}`,
        width: 30,
      },
      {
        title:
          (
            <span>
              <span style={{ color: 'red', marginRight: '5px' }}>*</span>
              {t('合并上架片区')}
            </span>
          ),
        render: (record, i) => (
          <div className={style.mix_group}>
            <div className={style.mix_group_checkbox}>
              {editStorageTagList.map((goodStoreTag) => (
                <Checkbox
                  checked={goodStoreTag.bindGroup !== -1}
                  disabled={
                    !(goodStoreTag.bindGroup === -1 || goodStoreTag.bindGroup === i)
                  }
                  key={goodStoreTag.id}
                  htmlValue={goodStoreTag.id}
                  onChange={(val) => {
                    const newStrategy = JSON.parse(JSON.stringify(strategy));
                    let selectedList = (newStrategy[i]?.region || []).slice();
                    // 判断是否选中
                    if (val !== undefined) {
                      selectedList.push(goodStoreTag.id);
                    } else {
                      selectedList = selectedList.filter((id) => id !== goodStoreTag.id);
                    }
                    newStrategy[i] = newStrategy[i] || {};
                    newStrategy[i].region = selectedList;
                    store.changeEditGoodsStoreTypeList({
                      selectedList: newStrategy[i],
                      index: i,
                      type: 'change',
                    });
                  }}
                >
                  <span style={
                !(goodStoreTag.bindGroup === -1 || goodStoreTag.bindGroup === i)
                  ? { color: 'rgb(173, 177, 188)' } : {}
              }
                  >
                    {goodStoreTag.name}
                  </span>
                </Checkbox>
              ))}
            </div>
          </div>
        ),
        width: 250,
      },
      {
        title:
          (
            <span>
              <span style={{ color: 'red', marginRight: '5px' }}>*</span>
              {t('上架园区')}
            </span>
          ),
        render: (record, i) => (
          <Select
            value={strategy[i]?.parkType}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            data={upParkTypeList}
            onChange={(val) => {
              const newStrategy = JSON.parse(JSON.stringify(strategy));
              newStrategy[i] = newStrategy[i] || {};
              newStrategy[i].parkType = val;
              store.changeConfigObjData({
                ...editObj,
                strategy: newStrategy,
              });
            }}
            placeholder={t('请选择')}
          />
        ),
        width: 100,
      },
      {
        title: t('操作'),
        render: (record, i) => (
          <div>
            <Button
              type="primary"
              text
              loading={!loading}
              disabled={!canAdd}
              onClick={() => {
                store.changeEditGoodsStoreTypeList({
                  selectedList: [],
                  index: i + 1,
                  type: 'add',
                });
              }}
            >
              <Icon name="plus-o" fontSize={20} />
            </Button>
            <Button
              style={{ marginLeft: 0 }}
              type="danger"
              text
              loading={!loading}
              disabled={!canDelete}
              onClick={() => {
                store.changeEditGoodsStoreTypeList({
                  selectedList: [],
                  index: i,
                  key: Object.keys(editObj.strategy)[i],
                  type: 'delete',
                });
              }}
            >
              <Icon name="minus-o" fontSize={20} />
            </Button>
          </div>
        ),
        width: 30,
      },
    ];

    return (
      <Modal
        maskCloseAble={false}
        visible={editObjVisible !== MODAL_VISIBLE_CLOSE}
        title={editObjVisible === MODAL_VISIBLE_ADD ? t('新增3PL仓补货策略配置') : t('编辑3PL仓补货策略配置')}
        width={920}
        bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
        onClose={() => store.changeData({ editObjVisible: MODAL_VISIBLE_CLOSE, editObj: defaultEditObj })}
        footer={[
          <Button
            onClick={() => {
              store.changeData({ editObjVisible: MODAL_VISIBLE_CLOSE, editObj: defaultEditObj });
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            loading={!loading}
            disabled={
              !(
                editObj.status !== undefined
                && editObj.warehouseId !== undefined
                && editObj.subWarehouseId !== undefined
                && editObj.parkType !== ''
                && strategy
                && strategy.every((d) => d.region.length && d.parkType !== undefined && d.parkType !== '')
                && editObj.scale && Number(editObj.scale) < 100 && Number(editObj.scale) >= 1
              )
            }
            type="primary"
            onClick={() => {
              if (isAppointment) {
                if (!editObj.reservationTime) {
                  Message.warn(t('请选择预约时间'));
                  return;
                }
                const currentTime = moment();
                const selectTime = moment(editObj.reservationTime);
                // 选择时间不能小于当前时间
                if (selectTime.isBefore(currentTime)) {
                  Message.warn(t('选择时间不能小于当前时间'));
                  return;
                }
              }
              store.confirmSaveConfig();
            }}
          >
            {t('保存')}
          </Button>,
        ]}
      >
        <div className={style.modalWrapper}>
          {
            isAppointment && (
              <>
                <div className={style.modalWrapperLabel}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  {t('预约时间')}
                </div>
                <div className={style.flex}>
                  <div className={style.inner_list}>
                    <span className={style.labWidth} style={{ width: 70 }}>
                      <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                      {t('预约时间')}
                    </span>
                    <DatePicker
                      inputable
                      type="datetime"
                      format="yyyy-MM-dd HH:mm:ss"
                      width={220}
                      value={editObj.reservationTime}
                      rules={[rules.reservationTimeRange()]}
                      disabled={(value) => {
                        const currentTime = moment().subtract(1, 'days');
                        const selectTime = moment(value, 'yyyy-MM-dd HH:mm:ss');
                        // 选择时间不能小于当前时间
                        return selectTime.isBefore(currentTime);
                      }}
                      onChange={(val) => {
                        store.changeConfigObjData({ reservationTime: val });
                      }}
                    />
                  </div>
                </div>
              </>
            )
          }
          <div className={style.modalWrapperLabel}>
            <span style={{ color: 'red', marginRight: '5px' }}>*</span>
            {t('基础信息')}
          </div>
          <div className={style.flex}>
            <div className={style.inner_list}>
              <span className={style.labWidth}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('仓库')}
              </span>
              <Select
                value={editObj.warehouseId}
                data={warehouseList}
                disabled={editObjVisible === MODAL_VISIBLE_EDIT}
                keygen="id"
                format="id"
                renderItem="nameZh"
                width={220}
                absolute
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.changeWarehouse({
                    warehouseId: value,
                    type: 'modal',
                  });
                }}
              />
            </div>
            <div className={style.inner_list}>
              <span className={style.labWidth}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('园区')}
              </span>
              <Select
                value={editObj.parkType}
                data={parkTypeList}
                disabled={editObjVisible === MODAL_VISIBLE_EDIT}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                width={220}
                absolute
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(val, d) => {
                  if (d?.parkType) {
                    const { parkSubWarehouseList } = fliterSubwarehouse([d?.parkType]);
                    store.changeData({
                      editObjSubWarehouseList: parkSubWarehouseList,
                    });
                  }
                  store.changeConfigObjData({
                    parkType: val,
                    subWarehouseId: '',
                  });
                }}
              />
            </div>
            <div className={style.inner_list}>
              <span className={style.labWidth}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('子仓')}
              </span>
              <Select
                value={editObj.subWarehouseId}
                data={editObjSubWarehouseList}
                disabled={editObjVisible === MODAL_VISIBLE_EDIT}
                keygen="id"
                format="id"
                renderItem="nameZh"
                width={220}
                absolute
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(val) => {
                  store.changeConfigObjData({
                    subWarehouseId: val,
                  });
                }}
              />
            </div>
          </div>
          <div className={style.flex}>
            <div className={style.inner_list}>
              <span className={style.labWidth}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('状态')}
              </span>
              <Radio.Group
                keygen="id"
                value={editObj.status}
                onChange={(val) => store.changeConfigObjData({
                  status: val,
                })}
              >
                {(statusList || []).map((d) => (
                  <Radio key={d.value} htmlValue={d.value}>
                    {d.label}
                  </Radio>
                ))}
              </Radio.Group>
            </div>
            <div className={style.inner_list}>
              <span className={style.labelWidth}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('补货放大倍数')}
              </span>
              <Input.Number
                allowNull
                hideArrow
                digits={2}
                width={200}
                placeholder={t('请输入')}
                value={editObj.scale}
                rules={[rules.numberRange()]}
                onChange={(val) => {
                  store.changeConfigObjData({ scale: val });
                }}
                tip={t('输入值范围为{}，且小数点最多{}位', '1-100', 2)}
                popover="bottom"
              />
            </div>
          </div>
          <div className={style.modalWrapperLabel}>
            <span style={{ color: 'red', marginRight: '5px' }}>*</span>
            {t('补货策略')}
          </div>
          <section>
            <Table
              keygen={(d) => JSON.stringify(d)}
              bordered
              fixed="both"
              style={{ maxHeight: 300 }}
              columns={columns}
              data={strategy || []}
              pagination={false}
            />
          </section>
        </div>
      </Modal>
    );
  }
}

ConfigModal.propTypes = {
  loading: PropTypes.number,
  editObjVisible: PropTypes.number,
  editObj: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  editObjSubWarehouseList: PropTypes.arrayOf(PropTypes.shape),
  editStorageTagList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  upParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  canAdd: PropTypes.bool,
  canDelete: PropTypes.bool,
  store: PropTypes.shape(),
  isAppointment: PropTypes.arrayOf(PropTypes.shape()),
};

export default ConfigModal;
