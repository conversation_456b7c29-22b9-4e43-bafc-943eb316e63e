import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { under2Camel } from '@src/lib/camal-case-convertor';

import {
  Table, Button, Tag, Popover,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyle from '@src/component/style.less';
import styles from '../../style.less';

/**
 * @description 混装组合的列表展示
 * @param {array} origin [[]], 复杂数组
 * @param {number} maxItem 展示最大项目数
 * @param regionList
 * @param parkTypeList
 * @returns {Element}
 */
const detailVoListCabinet = (origin, maxItem, regionList, parkTypeList) => {
  origin = under2Camel(origin);
  // 根据 id 构造数据
  // 判断是否展示 Popover 出现... 才展示。
  let showPopover = false;
  const combination = [];

  origin.forEach((item, index) => {
    // 根据id映射出片区名和园区名
    const regionNameList = regionList.filter((region) => (item.region?.includes(region.id))).map((i) => i.name);
    const parkNameList = parkTypeList.filter((park) => (park.dictCode === (item.parkType))).map((p) => p.dictNameZh);
    item.regionNameList = regionNameList;
    item.parkNameList = parkNameList;
    combination[index] = item;
  });

  combination.forEach((item) => {
    if ((item.regionNameList || []).length > maxItem && !showPopover) {
      showPopover = true;
    }
  });
  if ((combination || []).length > 3 && !showPopover) {
    showPopover = true;
  }

  return (
    <div>
      {/* {showPopover */}
      {/*   && ( */}
      {/*     <Popover */}
      {/*       type="info" */}
      {/*       position="left" */}
      {/*       style={{ padding: 5 }} */}
      {/*       background="#fff" */}
      {/*       border="#fff" */}
      {/*     > */}
      {/*       {combination.map((items, i) => ( */}
      {/*         <div style={{ marginTop: 3 }}> */}
      {/*           <span>{t('策略{}:', i + 1)}</span> */}
      {/*           <span>{(items?.regionNameList || []).map((tag) => (<Tag>{tag}</Tag>))}</span> */}
      {/*         </div> */}
      {/*       ))} */}
      {/*     </Popover> */}
      {/*   )} */}
      {/* 只展示3组 */}
      {(combination || []).map((items, i) => (
        <div className={styles.tagList}>
          <span className={styles.label}>{t('策略{}:', i + 1)}</span>
          <div>
            <span className={styles.flexStyle}>
              <span className={styles.tagLabel}>{t('合并片区')}</span>
              {(items?.regionNameList || []).map((tag) =>
                // eslint-disable-next-line
                <Tag>{tag}</Tag>)}
            </span>
            <span className={styles.flexStyle}>
              <span className={styles.tagLabel}>{t('上架园区')}</span>
              {(items?.parkNameList || []).map((tag) =>
                // eslint-disable-next-line
                <Tag>{tag}</Tag>)}
            </span>
          </div>

        </div>
      ))}
      {/* {combination && combination.length > 3 && ( */}
      {/*   <div style={{ marginTop: 3 }}>...</div> */}
      {/* )} */}
    </div>
  );
};

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      upParkTypeList,
      regionList,
      store,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        width: 140,
        render: 'warehouseName',
      },
      {
        title: t('片区'),
        width: 140,
        render: 'regionName',
      },
      {
        title: t('园区'),
        width: 140,
        render: 'parkTypeName',
      },
      {
        title: t('子仓'),
        width: 140,
        render: 'subWarehouseName',
      },
      {
        title: t('补货放大倍数'),
        width: 140,
        render: 'scale',
      },
      {
        title: t('补货策略'),
        width: 250,
        render: (d) => detailVoListCabinet(JSON.parse(d.strategy || '[]'), 3, regionList, upParkTypeList),
      },
      {
        title: t('合并计算模式'),
        width: 140,
        render: 'calcModeName',
      },
      {
        title: t('状态'),
        width: 100,
        render: (d) => {
          if (d.status) {
            return t('启用');
          }
          return t('禁用');
        },
      },
      {
        title: t('创建人'),
        width: 240,
        render: 'createUserName',
      },
      {
        title: t('创建时间'),
        width: 180,
        render: 'createTime',
      },
      {
        title: t('更新人'),
        width: 140,
        render: 'updateUserName',
      },
      {
        title: t('更新时间'),
        width: 180,
        render: 'lastUpdateTime',
      },
      {
        title: t('操作'),
        width: 240,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              text
              type="primary"
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.id,
              })}
            >
              {t('操作日志')}
            </Button>
            <Button
              text
              type="primary"
              className={globalStyle.tableTextButton}
              disabled={!loading}
              onClick={() => {
                store.changeData({
                  isAppointment: false,
                });
                store.openModal({
                  type: 'edit',
                  data: record,
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              text
              type="danger"
              className={globalStyle.tableTextButton}
              disabled={!loading}
            >
              <Popover.Confirm
                position="top"
                onOk={() => store.deleteId(record.id)}
                type="warning"
                okType="primary"
              >
                {t('请确认是否删除该策略配置?')}
              </Popover.Confirm>
              {t('删除')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={globalStyle.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            empty={t('暂无数据')}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              className: globalStyle.pagination,
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'WWS_THREE_PL_REPLENISH_STRATEGY',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}
List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  upParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  regionList: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  store: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};
export default List;
