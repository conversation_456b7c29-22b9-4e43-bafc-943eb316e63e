import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import { dictSelect } from '@src/server/basic/dictionary';
import { fliterSubwarehouse, queryParkList } from '@src/lib/dealFunc';
import { under2Camel } from '@src/lib/camal-case-convertor';
import {
  getListAPI,
  deleteConfigAPI,
  addOrEditAPI,
  appointmentQueryAPI,
  appointmentEditAPI,
} from '../server';

export const STATUS_DISABLE = 0; // 状态 禁用
export const STATUS_ENABLE = 1; // 状态 启用
export const WAREHOUSE_ID_FOSHAN = 1; // 仓库: 佛山仓 id
export const MODAL_VISIBLE_CLOSE = 0; // 弹窗 关闭
export const MODAL_VISIBLE_ADD = 1; // 弹窗 新增
export const MODAL_VISIBLE_EDIT = 2; // 弹窗 编辑

export const defaultLimit = {
  parkType: '', // 园区
  subWarehouseId: [], // 子仓
};

export const defaultEditObj = {
  status: STATUS_ENABLE, // 是否开启[0禁用 1开启]
  warehouseId: WAREHOUSE_ID_FOSHAN, // 仓库id
  parkType: '', // 园区
  subWarehouseId: '', // 子仓
  scale: '', // 补货放大倍数
  strategy: [{
    region: [],
    parkType: '',
  }],
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  loading: 1, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,

  warehouseList: [], // 仓库下拉
  parkTypeList: [], // 园区下拉
  statusList: [ // 状态下拉
    { label: t('启用'), value: STATUS_ENABLE },
    { label: t('禁用'), value: STATUS_DISABLE },
  ],
  storageTagList: [], // 商品存储属性

  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeOptions: [20, 50, 100], // 表格页显示条数
  },
  list: [],

  // 操作记录
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗

  // 新增/编辑弹窗
  editObjSubWarehouseList: [], // 子仓下拉
  editObjVisible: MODAL_VISIBLE_CLOSE, // 0 关闭，1 新增 2 编辑
  editObj: {
    status: STATUS_ENABLE, // 是否开启[0禁用 1开启]
    warehouseId: WAREHOUSE_ID_FOSHAN, // 仓库id
    parkType: '', // 园区
    subWarehouseId: '', // 子仓
    scale: '', // 补货放大倍数
    reservationTime: '', // 预约时间
    strategy: [{
      region: [],
      parkType: '',
    }],
  },
  editStorageTagList: [], // 新增/编辑用的商品存储属性，多一个标明被哪一组选择了 字段
  canAdd: true, // 控制混装组合
  canDelete: true, // 控制混装组合
  subWarehouseList: [],
  regionList: [], // 片区
  selectedRows: [],
  isAppointment: false, // 是否预约配置
};

export default {
  state: defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 新增/编辑
  changeConfigObjData(state, data) {
    Object.assign(state, {
      editObj: {
        ...state.editObj,
        ...data,
      },
    });
  },
  // 页面初始化
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['STORAGE_TAG', 'THIRD_PARK_ENUM', 'REGION_ENUM', 'PARK_ENUM'] }),
    ]);
    // 商品存储属性
    if (selectData.code === '0') {
      yield this.changeData({
        storageTagList: selectData.info.data.find((item) => item.catCode === 'STORAGE_TAG').dictListRsps.map((x) => ({ id: x.dictCode, name: x.dictNameZh })) || [],
        parkTypeList: selectData.info.data.find((item) => item.catCode === 'THIRD_PARK_ENUM').dictListRsps || [],
        upParkTypeList: selectData.info.data.find((item) => item.catCode === 'PARK_ENUM').dictListRsps || [],
        regionList: selectData.info.data.find((item) => item.catCode === 'REGION_ENUM').dictListRsps.map((x) => ({ id: x.dictCode, name: x.dictNameZh })).filter((item) => item.id > 0 && item.id !== 4) || [],
      });
    } else {
      Modal.error({
        title: selectData.msg,
      });
    }
    // 仓库列表
    const { warehouseList, warehouseId } = yield 'nav';
    yield this.changeData({
      warehouseList,
    });
    // yield this.changeWarehouse({});

    yield this.changeWarehouse({ warehouseId });
  },
  // 查询
  * search() {
    // 获取仓库
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const { pageNum, pageSize } = pageInfo;
    const params = {
      ...limit,
      warehouseId,
      pageNum,
      pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(clearEmpty(paramTrim(params), [0, '0', false]));
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        selectedRows: [],
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 页签改变
  * handlePaginationChange(data = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 删除
  * deleteId(id) {
    markStatus('loading');
    const { code, msg } = yield deleteConfigAPI({
      id,
    });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 预约配置
  * handleAppointment() {
    markStatus('loading');
    const { selectedRows } = yield '';
    const { id } = selectedRows[0];
    const { code, msg, info } = yield appointmentQueryAPI({
      strategyId: id,
    });
    if (code === '0') {
      yield this.changeData({
        isAppointment: true,
      });
      yield this.openModal({
        type: 'edit',
        data: info || {},
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 打开弹窗
  * openModal({ type, data }) {
    const { warehouseId } = yield 'nav';
    if (type === 'add') {
      // 新增
      yield this.createEditGoodsStoreTypeList({
        editObjVisible: MODAL_VISIBLE_ADD, // 新增
        editObj: {
          status: STATUS_ENABLE, // 是否开启[0禁用 1开启], 默认启用
          warehouseId: WAREHOUSE_ID_FOSHAN, // 仓库id, 默认佛山仓
          parkType: '', // 园区'
          subWarehouseId: '',
          scale: '',
          strategy: [{
            region: [],
            parkType: '',
          }], // 混装组合
        },
      });
      yield this.changeWarehouse({
        warehouseId: WAREHOUSE_ID_FOSHAN,
        type: 'modal',
      });
    } else {
      // 编辑
      if (data?.parkTypeName) {
        // 此处需要找到对应的parkType去查询
        const { parkList } = yield queryParkList(warehouseId);
        const parkType = parkList.find((i) => i.parkName === data?.parkTypeName) === -1 ? '' : parkList.find((i) => i.parkName === data?.parkTypeName).parkType;

        if (parkType) {
          const { parkSubWarehouseList } = fliterSubwarehouse([parkType]);
          yield this.changeData({
            editObjSubWarehouseList: parkSubWarehouseList,
          });
        }
      }

      yield this.createEditGoodsStoreTypeList({
        editObjVisible: MODAL_VISIBLE_EDIT, // 编辑
        editObj: {
          ...data,
          id: data.id, // 存储标签混装配置id,编辑时传值
          status: data.status ? 1 : 0, // 是否开启[0禁用 1开启], 默认启用
          warehouseId: data.warehouseId, // 仓库id, 默认佛山仓
          parkType: data.parkType, // 园区
          // strategy: list, // 混装组合
          strategy: under2Camel(JSON.parse(data.strategy || '[]')),
        },
      });
      yield this.changeWarehouse({
        warehouseId: data.warehouseId,
        type: 'modal',
        isInit: true,
      });
    }
  },
  // 1 新增 2 编辑
  * confirmSaveConfig() {
    markStatus('loading');
    const {
      editObjVisible, editObj, pageInfo, isAppointment,
    } = yield '';
    const param = { ...editObj };
    const { pageNum, pageSize } = pageInfo;

    // 新增不传id
    if (editObjVisible === MODAL_VISIBLE_ADD) {
      Reflect.deleteProperty(param, 'id');
    }
    // 非预约不传预约时间
    if (!isAppointment) {
      Reflect.deleteProperty(param, 'reservationTime');
    }
    const api = isAppointment ? appointmentEditAPI : addOrEditAPI;
    const { code, msg } = yield api({
      ...param, pageNum, pageSize,
    });
    if (code === '0') {
      let tips = editObjVisible === MODAL_VISIBLE_ADD ? t('新增成功') : t('编辑成功');
      if (isAppointment) {
        tips = t('预约保存成功');
      }
      Message.success(tips);
      yield this.changeData({
        editObjVisible: MODAL_VISIBLE_CLOSE,
        isAppointment: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 新增/编辑时创建组合数组
  * createEditGoodsStoreTypeList({ editObjVisible, editObj }) {
    const { regionList } = yield '';
    markStatus('loading');
    let canAdd = true;
    let canDelete = true;
    const { strategy } = editObj;
    const markIndex = strategy.map((item) => item.region).reduce((acc, curr, index) => {
      curr.forEach((id) => {
        acc[id] = index;
      });
      return acc;
    }, {});
    const editStorageTagList = regionList.reduce((acc, curr, currentIndex, arr) => {
      let index = -1;
      const { mixs } = acc;
      let { result } = acc;
      // eslint-disable-next-line no-cond-assign
      if ((index = mixs.indexOf(curr.id)) > -1) {
        mixs.splice(index, 1);
        result.push({
          ...curr,
          bindGroup: markIndex[curr.id],
        });
      } else {
        result.push({
          ...curr,
          bindGroup: -1, // -1 表示没有被使用
        });
      }
      // 最后一个，遍历双方组合一起，然后返回数组
      if (currentIndex === arr.length - 1) {
        result = [
          ...result,
          ...mixs.map((id) => ({ id, name: id, bindGroup: markIndex[id] })),
        ];
        return result;
      }
      return {
        mixs,
        result,
      };
    }, {
      mixs: strategy.map((item) => item.region).flat(),
      result: [],
    });
    // 判断是否可以新增/删除组
    if ([strategy.map((item) => item.region)].length === 1) {
      canDelete = false;
    }
    if (editObjVisible === MODAL_VISIBLE_ADD) {
      canAdd = false;
      canDelete = false;
    }
    if (canAdd) {
      canAdd = editStorageTagList.some(({ bindGroup }) => bindGroup === -1);
    }
    yield this.changeData({
      editObjVisible,
      canAdd,
      canDelete,
      editObj: {
        ...this.state.editObj,
        ...editObj,
        strategy,
      },
      editStorageTagList,
    });
  },
  /**
   * @description 混装组合
   * @param {array} selectedList 用户勾选变化
   * @param {number} index 第几组
   * @param {string} type add/delete/change 新增组/删除组/修改组
   * @returns {varType}
   */
  * changeEditGoodsStoreTypeList({
    selectedList, index, type,
  }) {
    markStatus('loading');
    // 控制是否可以新增/删除
    let canAdd = true;
    let canDelete = true;
    const { strategy } = this.state.editObj;
    let { editStorageTagList } = yield '';
    // 旧的数据
    const oldSelectedIds = strategy[index]?.region || [];
    // 替换 strategy 数据
    // 如果是 删除/修改，先将被修改的组数据初始化
    switch (type) {
      case 'add': {
        strategy.splice(index, 0, {
          region: [],
          parkType: '',
        });
        // 所有后面的数组都要加1
        editStorageTagList = editStorageTagList.map((ele) => {
          // 后面的要往前挪一组
          if (ele.bindGroup >= index) {
            return {
              ...ele,
              bindGroup: ele.bindGroup + 1,
            };
          }
          return ele;
        });
        canAdd = false;
        break;
      }
      case 'delete':
        strategy.splice(index, 1);
        editStorageTagList = editStorageTagList.map((ele) => {
          let { bindGroup } = ele;
          if (Array.isArray(oldSelectedIds) && oldSelectedIds.includes(ele.id)) {
            bindGroup = -1;
            return {
              ...ele,
              bindGroup, // -1 表示没有被使用
            };
          }
          // 后面的要往前挪一组
          if (bindGroup > index) {
            bindGroup--;
            return {
              ...ele,
              bindGroup,
            };
          }
          return ele;
        });
        break;
      case 'change':
        // strategy[index] = selectedList;
        strategy.splice(index, 1, selectedList);
        editStorageTagList = editStorageTagList.map((ele) => {
          let { bindGroup } = ele;
          if (selectedList?.region?.includes(ele.id)) {
            bindGroup = index;
          } else if (Array.isArray(oldSelectedIds) && oldSelectedIds.includes(ele.id)) {
            bindGroup = -1;
          }
          return {
            ...ele,
            bindGroup,
          };
        });
        // 只要存在还未被选择的合并上架片区，就可以继续添加
        canAdd = editStorageTagList.some(({ bindGroup }) => bindGroup === -1);
        break;
      default:
        throw new Error(`unknown param type: ${type}`);
    }
    // 控制是否可以新增/删除 组
    if (strategy.length === 1) {
      canDelete = false;
    }
    // 已全选，不可新增
    // if (regionList.length === editStorageTagList.reduce((pre, cur) => pre.concat(cur), []).length) {
    //   canAdd = false;
    // }
    yield this.changeData({
      editStorageTagList,
      canAdd,
      canDelete,
    });
    yield this.changeConfigObjData({
      strategy,
    });
  },
  /**
   * @description 仓库改变
   * @param {number} warehouseId
   * @param {string} type 'list' 列表数据/'modal' 弹窗数据，默认为'list'
   * @param {boolean} isInit 是否为初始化，默认为false
   */
  * changeWarehouse({ warehouseId, type = 'list', isInit = false }) {
    const { editObj, parkTypeList } = yield '';

    if (!warehouseId && type === 'list') {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      subWarehouseId: '',
      parkTypeList: parkTypeList.map((item) => ({
        ...item,
        parkType: parkList.find((i) => i.parkName === item.dictNameZh) === -1 ? '' : parkList.find((i) => i.parkName === item.dictNameZh)?.parkType,
      })),
    });

    if (type === 'modal' && isInit === false) {
      yield this.changeData({
        editObj: {
          ...editObj,
          parkType: '', // reset
        },
      });
    }
  },
};
