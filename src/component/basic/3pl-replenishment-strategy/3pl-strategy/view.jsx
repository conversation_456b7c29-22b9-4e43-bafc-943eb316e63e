import React, { useEffect } from 'react';
import { useStore } from 'rrc-loader-helper';
import ContainerPage from '@public-component/search-queries/container';
import { TopAreaHeight } from '@src/lib/constants';
import statusReducers from './3pl.reducers';
import Header from './jsx/header';
import Handle from './jsx/handle';
import List from './jsx/list';

function PlStrategyContainer(props) {
  const [state, store] = useStore(statusReducers, true);

  useEffect(() => {
    store.init();
  }, []);
  return (
    <ContainerPage
      customStyle={{ height: `calc(100vh - ${TopAreaHeight}px - 92px)` }}
    >
      <Header {...props} {...state} store={store} />
      <Handle {...props} {...state} store={store} />
      <List {...props} {...state} store={store} />
    </ContainerPage>
  );
}

export default PlStrategyContainer;
