import React, { useEffect } from 'react';
import { i18n } from '@shein-bbl/react';
import { useStore } from 'rrc-loader-helper';
import ContainerPage from '@public-component/search-queries/container';
import { IWarehouseChange } from '@src/typing/base';
import { TopAreaHeight } from '@src/lib/constants';
import statusReducers from './reducers';
import Header from './jsx/header';
import List from './jsx/list';
import Handle from './jsx/handle';
import { IPageProps } from './types';

function Container(props: IPageProps) {
  const [state, store] = useStore(statusReducers, true);
  useEffect(() => {
    store.init();
  }, []);

  return (
    <ContainerPage
      warehouseChange={(data: IWarehouseChange) => store.warehouseChange(data)}
      customStyle={{ height: `calc(100vh - ${TopAreaHeight}px - 92px)` }}
    >
      <Header {...props} {...state} store={store} />
      <Handle {...props} {...state} store={store} />
      <List {...props} {...state} store={store} />
    </ContainerPage>
  );
}

export default i18n(Container);
