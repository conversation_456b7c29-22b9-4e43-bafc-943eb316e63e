// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheckimport { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
// import { getWarehouseId } from '@src/lib/dealFunc';
import { t } from '@shein-bbl/react';
import { paramTrim, clearEmpty } from '@src/lib/deal-func';
import { INavStateType, IWarehouseChange } from '@src/typing/base';
import { getWarehouseAPI, IGetWarehouseAPIResponse } from '@src/server/basic-ts/warehous';
import { queryParkAreaAPI } from '@src/server/outbound/basic';
import {
  IDataItem, IGetListAPIResponse, ILimitType, IStateType,
} from './types';
import {
  getListAPI, editAPI, addAPI, deleteAPI,
} from './server';

export const defaultLimit: ILimitType = {
  orderTypeList: [],
  statusList: [],
  startTime: '',
  endTime: '',
};

const defaultState: IStateType = {
  limit: defaultLimit,
  ready: false,
  loading: 1, // 0 loading, 1 load success, 2 load fail
  list: [],
  selectedGroups: [], // 已选择绑定的用户组
  operationModalVisible: false, // 操作记录弹窗
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  subWarehouseList: [], // header 子仓列表
  currentWarehouseList: [],
  formRef: undefined,
  ids: [],
  addModalVisible: false,
  warehoueList: [],
  statusList: [
    { dictCode: 0, dictNameZh: t('禁用') },
    { dictCode: 1, dictNameZh: t('启用') },
  ],
  parkSubwarehouseAreaList: [], // 园区-子仓-库区
};

export default {
  state: defaultState,
  $init: () => defaultState, // 可选使用，在页面初始化的时候会重置state
  /**
   * 改变state的值
   */
  changeData(state: Partial<IStateType>, data?: Partial<IStateType>) {
    Object.assign(state, data);
  },
  /**
   * 改搜索条件limit属性值
   */
  changeLimitData(state: Partial<ILimitType>, data?: Partial<ILimitType>) {
    // !!! LCD对于action方法有改动state实际为IState，为保证程序正常强制as一下
    const stateData = state as IStateType;
    Object.assign(stateData, {
      limit: {
        ...stateData.limit,
        ...data,
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef }: IStateType = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 校验
  * handleFormValidate() {
    const { formRef }: IStateType = this.state;
    let validateFlag = false;
    if (formRef) {
      yield formRef.validate().then(() => {
        validateFlag = true;
      }).catch(() => {
        validateFlag = false;
      });
    }
    return validateFlag;
  },
  /**
   * 右上角更换仓库触发
   * @param {*} action
   */
  * changeSubWarehouseList(action: IWarehouseChange) {
    const { subWarehouseList } = action;
    yield this.changeData({
      subWarehouseList,
    });
  },
  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(arg = {}) {
    const validateFlag: boolean = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  /**
   * 初始化数据
   */
  * init() {
    const {
      warehouseList,
      currentWarehouseList,
      warehouseId,
      permissionSubWarehouseList,
    }: INavStateType = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
      warehouseList,
    });
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    yield this.changeData({
      warehouseId,
      subWarehouseList: permissionSubWarehouseList,
    });

    const warehouse: IGetWarehouseAPIResponse = yield getWarehouseAPI({ enabled: 1 });
    if (warehouse.code === '0') {
      yield this.changeData({
        currentWarehouseList: warehouse.info?.data,
        ready: true,
      });
    } else {
      Modal.error({
        title: warehouse.msg,
      });
    }
  },
  /**
   * 查询表格数据
   */
  * search() {
    // const warehouseId = getWarehouseId();
    // if (!warehouseId) {
    //   Modal.error({ title: t('请选择右上角仓库') });
    //   return;
    // }
    const {
      pageInfo: {
        pageNum,
        pageSize,
      },
      limit,
    }: IStateType = yield '';
    markStatus('loading');

    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }

    const data: IGetListAPIResponse = yield getListAPI(clearEmpty(paramTrim({
      ...limit,
      pageNum,
      pageSize,
      warehouseId,
    }), [0, '0', false]));
    if (data.code === '0') {
      yield this.changeData({
        list: data.info?.data,
        selectedGroups: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: data.info?.meta?.count ?? 0,
        },
      });
    } else {
      Modal.error({
        title: data.msg,
      });
    }
  },
  // 右上角仓库事件派发
  * warehouseChange(data: IWarehouseChange) {
    const { subWarehouseList, warehouseId } = data;
    yield this.changeData({ subWarehouseList, warehouseId });
  },
  * editRecord(data: IDataItem) {
    const res: IGetListAPIResponse = yield editAPI(data);
    if (res.code === '0') {
      yield this.changeData({ addModalVisible: false, detailId: undefined, detailInfo: undefined });
      yield this.search();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * addRecord(data: IDataItem) {
    const res: IGetListAPIResponse = yield addAPI(data);
    if (res.code === '0') {
      yield this.changeData({ addModalVisible: false, detailId: undefined, detailInfo: undefined });
      yield this.search();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * deleteRecord(id: number) {
    const res: IGetListAPIResponse = yield deleteAPI({ id });
    if (res.code === '0') {
      yield this.search();
    } else {
      Modal.error({ title: res.msg });
    }
  },

  // 获取园区-子仓-库区
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  * queryParkArea(action) {
    markStatus('loading');
    const { code, info, msg } = yield queryParkAreaAPI(action);
    if (code === '0') {
      const { parkList } = info;
      const newParkSubwarehouseAreaList = [...parkList];
      // 园区-子仓-库区数组
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const parkSubwarehouseAreaList = [];
      // 园区-子仓数组
      // const parkSubwarehouseList = [];
      // 把树形结构替换成默认形式
      // {
      //    id: '1',
      //    title: '1',
      //    children: []
      // }
      newParkSubwarehouseAreaList.forEach((item1) => {
        const pushObj1 = {};
        // id替换成【园区id前加上'1-' 子仓id前加上2- 库区id前加上3-】
        // 原因：因为园区、子仓、库区的id存在重叠，树形组件选择会选择错误
        pushObj1.id = `${item1.parkType}`;
        pushObj1.name = item1.parkName;
        const pushObj1s = {};
        pushObj1s.id = `${item1.parkType}`;
        pushObj1s.name = item1.parkName;
        const children1 = [];
        const children1s = [];
        if (item1.subWarehouseList && item1.subWarehouseList.length > 0) {
          item1.subWarehouseList.forEach((item2) => {
            const pushObj2 = {};
            pushObj2.id = `${item2.subWarehouseId}`;
            pushObj2.name = item2.subWarehouseName;
            // 园区-子仓
            children1s.push({
              id: `2-${item2.subWarehouseId}`,
              name: item2.subWarehouseName,
              children: [],
            });
            const children2 = [];
            if (item2.areaList && item2.areaList.length > 0) {
              item2.areaList.forEach((item3) => {
                const pushObj3 = {};
                pushObj3.id = `${item3.areaId}`;
                pushObj3.name = item3.areaName;
                pushObj3.children = [];
                children2.push(pushObj3);
              });
              pushObj2.children = children2;
            } else {
              pushObj2.children = [];
            }
            children1.push(pushObj2);
          });
          pushObj1.children = children1;
          pushObj1s.children = children1s;
        } else {
          pushObj1.children = [];
          pushObj1s.children = [];
        }
        parkSubwarehouseAreaList.push(pushObj1);
        // parkSubwarehouseList.push(pushObj1s);
      });

      yield this.changeData({
        parkSubwarehouseAreaList,
        // parkSubwarehouseList,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
};
