import {
  IFetchResponse, IPageInfo, ISubwarehouseItem, IViewBaseProps, IWarehouseItem,
} from '@src/typing/base';
import { FormRef } from 'shineout/lib/Form/Props';

interface IControlVariableListItem {
  /** 控量具体的值 */
  controlVariable?:number;
  /** 控量维度，0不控制1仓库维度2包裹国家线维度 */
  dimension?:string;
  /** 仓库ID或国家线类型 */
  dimensionId?:number;
  /** 仓库ID或国家线类型 */
  dimensionIdStr?:string;
  /** 控量维度 */
  dimensionStr?:string;
}

type IControlVariableList = IControlVariableListItem[];

interface IPreemptiveModeListItem {
  /** 库区类别 */
  areaCategory?:number;
  /** 库区类别 */
  areaCategoryStr?:string;
  /** 预占模式 */
  preemptiveMode?:string;
  /** 预占模式 */
  preemptiveModeStr?:string;
}

type IPreemptiveModeList = IPreemptiveModeListItem[];

export interface IOccupyPrePriorityListItem {
  /** 库区类别 */
  areaCategory?:string;
  /** 库区类别 */
  areaCategoryStr?:string;
  /** 箱规库区 1箱规库存2非箱规库区 */
  boxAreaType?:string;
  /** 箱规库区 */
  boxAreaTypeStr?:string;
  /** 优先级 */
  priorityOrder:number | string | null;
}

type IOccupyPrePriorityList = IOccupyPrePriorityListItem[];

export interface IPreemptivePrePriorityListItem {
  /** 特殊出库单类型 */
  billType?:string;
  /** 特殊出库单类型 */
  billTypeStr?:string;
  /** 预占优先级 */
  occupyPrePriorityList:IOccupyPrePriorityList;
  /** 订单类型 */
  orderType?:string;
  /** 订单类型 */
  orderTypeStr?:string;
}

export type IPreemptivePrePriorityList = IPreemptivePrePriorityListItem[];

interface IRegionListItem {
  /** 子仓或库区id */
  regionId?:number;
  /** 子仓或库区 */
  regionStr?:string;
}

type IRegionList = IRegionListItem[];

interface IUnPreemptiveListItem {
  /** 预占维度 1子仓维度，2库区维度 */
  dimension?:string | number;
  /** 预占维度 */
  dimensionStr?:string;
  /** 子仓或库区集合 */
  regionList?:IRegionList;
}

type IUnPreemptiveList = IUnPreemptiveListItem[];

export interface IDataItem {
  /** 控制具体值 */
  controlVariableList?:IControlVariableList;
  /** ID */
  id?:number;
  /** 补货类型,1紧急补货2日常补货 */
  orderType?:string;
  /** 补货类型 */
  orderTypeStr?:string;
  /** 箱规库区预占模式集合 */
  preemptiveModeList?:IPreemptiveModeList;
  /** 备货区库存预占优先级 */
  preemptivePrePriorityList?:IPreemptivePrePriorityList;
  /** 状态 0:禁用，1:启用 */
  status?:string | number;
  /** 状态 */
  statusStr?:string;
  /** 超补比例，0-100的整数 */
  supplementaryProportion?:number;
  /** 不可预占区域 */
  unPreemptiveList?:IUnPreemptiveList;
  /** 仓库ID */
  warehouseId?:number;
  /** 仓库 */
  warehouseStr?:string;
  updateUserName?:string;
  lastUpdateTime?:string;
}

type IData = IDataItem[];

interface IMeta {
  /** 数据记录数 */
  count?:number;
  /** 用户自定义扩展 */
  customObj?:unknown;
}

interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export interface ILimitType {
  /** 更新时间结束时间 */
  endTime?:string;
  /** 补货类型集合 */
  orderTypeList?:number[];
  /** 当前页码 */
  pageNum?:number;
  /** 页宽度 */
  pageSize?:number;
  /** 更新时间开始时间 */
  startTime?:string;
  /** 启用状态集合 */
  statusList?:number[];
}

export interface DictionaryItem {
  id?: number;
  dictCode: string | number;
  dictName?: string;
  dictNameZh: string;
}

export interface IStateType {
  loading: number; // 0 loading, 1 load success, 2 load fail
  limit: ILimitType;
  ready: boolean;
  subWarehouseList: ISubwarehouseItem[];
  currentWarehouseList: IWarehouseItem[];
  list: IDataItem[];
  operationModalVisible: boolean;
  selectedGroups: IDataItem[];
  pageInfo: IPageInfo;
  formRef?: FormRef<ILimitType>
  recordId?: number;
  ids: number[];
  addModalVisible?: boolean;
  detailId?: number;
  detailInfo?: IDataItem;
  warehoueList: IWarehouseItem[];
  statusList: DictionaryItem[];
  parkSubwarehouseAreaList: ISubwarehouseItem[];
  warehouseId?: number;
}

export interface Store {
  handlePaginationChange: (params: { pageNum: number; pageSize?: number }) => void;
  changeLimitData: (val: ILimitType) => void;
  clearLimitData: () => void;
  // eslint-disable-next-line no-use-before-define
  changeData: (val: Partial<IStateType>) => void;
  addRecord: (val: IDataItem) => void;
  editRecord: (val: IDataItem) => void;
  deleteRecord: (val: number) => void;
}

export type IPageProps = IViewBaseProps<IStateType>;
