import { sendPostRequest } from '@src/server/common/public';
import { ILimitType, IGetListAPIResponse, IDataItem } from './types';

/**
 * 搜索接口
 */
// eslint-disable-next-line import/prefer-default-export
export const getListAPI = (param: ILimitType): Promise<IGetListAPIResponse> => sendPostRequest({
  url: '/replenish_config_box_spec/query',
  param,
}, process.env.WWS_URI);

/**
 * 新增接口
 */
// eslint-disable-next-line import/prefer-default-export
export const addAPI = (param: IDataItem): Promise<IGetListAPIResponse> => sendPostRequest({
  url: '/replenish_config_box_spec/add',
  param,
}, process.env.WWS_URI);

/**
 * 新增接口
 */
// eslint-disable-next-line import/prefer-default-export
export const editAPI = (param: IDataItem): Promise<IGetListAPIResponse> => sendPostRequest({
  url: '/replenish_config_box_spec/edit',
  param,
}, process.env.WWS_URI);

/**
 * 新增接口
 */
// eslint-disable-next-line import/prefer-default-export
export const deleteAPI = (param: { id: number }): Promise<IGetListAPIResponse> => sendPostRequest({
  url: '/replenish_config_box_spec/del',
  param,
}, process.env.WWS_URI);
