.check {
    color: green;
    font-size: 18px;
    cursor: pointer;
    width: 80%;
    margin: 0 auto;
}
.itemTitle {
    font-weight: bold;
}
.itemTitle:after {
    content: '';
    display: block;
    clear: both;
    width: 100%;
    border-bottom: 1px dashed #aaa;
    margin: 10px 0 20px 0;
}
.rowWrap, .rowChildWrap {
    display: flex;
    flex-direction: row;
    align-items: center; 
    .iconList {
        .icon {
          margin-left: 12px;
          font-size: 20px;
          color: #1890ff;
          cursor: pointer;
        }
    }
}
.rowChildWrap {
    margin-left: 40px;
}
