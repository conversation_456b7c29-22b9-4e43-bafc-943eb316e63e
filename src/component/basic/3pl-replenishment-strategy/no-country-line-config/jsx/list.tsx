import React from 'react';
import { t } from '@shein-bbl/react';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import style from '@src/component/style.less';
import {
  Table, Button, Modal,
} from 'shineout';
import type { ColumnItem } from 'shineout/lib/Table/Props';
import { IDataItem, IPageProps, Store } from '../types';

function List(props: IPageProps & { store: Store}) {
  const {
    loading,
    list,
    pageInfo,
    selectedGroups,
    operationModalVisible,
    recordId,
    store,
  } = props;

  const columns: ColumnItem<IDataItem>[] = [
    {
      title: t('仓库'),
      render: 'warehouseStr',
      width: 130,
    },
    {
      title: t('状态'),
      render: 'statusStr',
      width: 130,
    },
    {
      title: t('补货类型'),
      render: 'orderTypeStr',
      width: 130,
    },
    {
      title: t('备货区预占优先级'),
      render: (r) => (
        <div>
          { r.preemptivePrePriorityList?.map((i, index) => (
            <div>
              <div>
                {t('订单类型{}', index + 1)}
                ：
                {i.orderTypeStr}
                {i.billTypeStr ? ` ${i.billTypeStr}` : ''}
              </div>
              {i.occupyPrePriorityList?.sort((a, b) => a.priorityOrder - b.priorityOrder).map((j, jIndex) => (
                <div>
                  {`${jIndex + 1}、`}
                  {j.boxAreaTypeStr}
                  ,
                  {j.areaCategoryStr}
                </div>
              ))}
            </div>
          ))}
        </div>
      ),
      width: 160,
    },
    {
      title: t('备货区不可预占区域'),
      render: (r) => (
        <div>
          {/* { r.unPreemptiveList?.map((i) => ( */}
          <div>
            <div>
              {t('子仓')}
              ：
              {r?.unPreemptiveList && (r?.unPreemptiveList || []).find((v) => v.dimension === 1)?.regionList?.map((j) => j.regionStr).join(',')}
            </div>
            <div>
              {t('库区')}
              ：
              {r?.unPreemptiveList && (r?.unPreemptiveList || []).find((v) => v.dimension === 2)?.regionList?.map((j) => j.regionStr).join(',')}
            </div>
          </div>

          {/* ))} */}
        </div>
      ),
      width: 160,
    },
    {
      title: t('箱规预占模式'),
      render: (r) => (
        <div>
          { r.preemptiveModeList?.map((i, index) => (
            <div>
              {index + 1}
              、
              {i.preemptiveModeStr}
              ：
              {i.areaCategoryStr}
            </div>
          ))}
        </div>
      ),
      width: 130,
    },
    {
      title: t('箱规库区超补比例'),
      render: (r) => `${r.supplementaryProportion}%`,
      width: 130,
    },
    {
      title: t('每小时生成紧急补货量上限（件）'),
      render: (r) => (
        <div>
          { r.controlVariableList?.map((i) => (i.controlVariable !== -1 ? (
            <div>
              {i.dimensionIdStr}
              ：
              {i.controlVariable}
              {t('件')}
            </div>
          ) : null))}
        </div>
      ),
      width: 130,
    },
    {
      title: t('操作人'),
      render: 'updateUserName',
      width: 130,
    },
    {
      title: t('更新时间'),
      render: 'lastUpdateTime',
      width: 180,
    },
    {
      title: t('操作'),
      width: 160,
      render: (d) => (
        <div>
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ operationModalVisible: true, recordId: d.id })}
          >
            {t('操作日志')}
          </Button>
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ addModalVisible: true, detailId: d.id, detailInfo: d })}
          >
            {t('编辑')}
          </Button>
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => {
              Modal.confirm({
                title: t('提示'),
                content: t('确定删除吗？'),
                onOk: () => store.deleteRecord(d.id as number),
              });
            }}
          >
            {t('删除')}
          </Button>
        </div>
      ),
    },
  ];

  const handelRowSelect = (val: IDataItem[]) => {
    store.changeData({
      selectedGroups: val,
    });
  };

  return (
    <section className={style.tableSection}>
      <SearchAreaTable>
        <Table<IDataItem, IDataItem[]>
          style={{ height: '100%' }}
          rowClassName={() => style.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={list}
          columns={columns}
          value={selectedGroups}
          onRowSelect={handelRowSelect}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            className: style.pagination,
            layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
      </SearchAreaTable>
      <OperationModal
        visible={operationModalVisible}
        param={{
          operateId: recordId,
          operateCode: 'REPLENISH_CONFIG_BOX_SPEC',
        }}
        onCancel={() => store.changeData({ operationModalVisible: false })}
      />
    </section>
  );
}

export default List;
