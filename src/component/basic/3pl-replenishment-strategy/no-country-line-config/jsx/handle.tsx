import React from 'react';
import style from '@src/component/style.less';
import { IWarehouseItem } from '@src/typing/base';
import {
  Button, Tooltip,
} from 'shineout';
import { t } from '@shein-bbl/react';
import { IPageProps, Store } from '../types';
import AddModal from './add-modal';

function Handle(props: IPageProps & { store: Store; warehouseList: IWarehouseItem[] }) {
  const {
    loading,
    addModalVisible,
    store,
    warehouseList,
    detailId,
    detailInfo,
    parkSubwarehouseAreaList,
    warehouseId,
  } = props;
  const isAddBtnDisabled = !loading;
  return (
    <section className={[style.handle, 'handleSection'].join(' ')}>
      <Tooltip tip={isAddBtnDisabled ? t('按钮置灰文字提示') : ''} trigger="hover" disabledChild={isAddBtnDisabled}>
        <Button disabled={isAddBtnDisabled} type="primary" onClick={() => store.changeData({ addModalVisible: true })}>{t('新增')}</Button>
      </Tooltip>
      <AddModal
        visible={addModalVisible}
        detail={detailInfo}
        id={detailId}
        warehouseId={warehouseId}
        warehouseList={warehouseList}
        onClose={() => store.changeData({ addModalVisible: false, detailId: undefined, detailInfo: undefined })}
        onOk={async (data) => {
          if (detailId) {
            await store.editRecord(data);
          } else {
            await store.addRecord(data);
          }
        }}
        parkSubwarehouseAreaList={parkSubwarehouseAreaList}
        store={store}
      />
    </section>
  );
}

export default Handle;
