// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import React, { useCallback, useEffect, useState } from 'react';
import { t } from '@shein-bbl/react';
import {
  Modal, Form, Button, Input,
  Select, Radio, Rule, TreeSelect,
} from 'shineout';
import Icon from '@shein-components/Icon';
import { IWarehouseItem } from '@src/typing/base';
import { selectSubWarehouse } from '@src/server/basic/sub-warehouse';
// import { selectArea } from '@src/server/basic/area';
import useDictionary from '../../../../../lib/hooks/useDictionary';
import style from '../style.less';
import {
  IDataItem,
  DictionaryItem,
  IOccupyPrePriorityListItem,
  IPreemptivePrePriorityListItem,
  Store,
} from '../types';

// interface IAreaItem {
//   id: number;
//   area: string;
// }

const statusList = [
  { dictCode: 1, dictNameZh: t('启用') },
  { dictCode: 0, dictNameZh: t('禁用') },
];

const defaultFormData: IDataItem = {
  warehouseId: '',
  status: 1,
  orderType: '',
  supplementaryProportion: '',
  preemptivePrePriorityList: [
    {
      orderType: '',
      occupyPrePriorityList: [
        {
          priorityOrder: null,
          boxAreaType: '',
          areaCategory: '',
        },
      ],
      // lastPriority: {
      //   boxAreaType: '',
      //   boxAreaTypeName: '',
      //   areaCategory: '',
      //   areaCategoryName: '',
      // },
    },
  ],
  preemptiveModeList: [
    {
      /** 库区类别 */
      areaCategory: '',
      /** 预占模式 */
      preemptiveMode: '',
    },
  ],
  controlVariableList: [
    {
      /** 仓库ID或国家线类型 */
      dimensionId: '',
      /** 控量维度，0不控制1仓库维度2包裹国家线维度 */
      dimension: '',
      /** 控量具体的值 */
      controlVariable: '',
    },
  ],
  unPreemptiveList: [
    {
      dimension: 1,
      regionList: [],
    },
    {
      dimension: 2,
      regionList: [],
    },
  ],
};

const rule = Rule({
  ratioRange: {
    func: (val, formData, callback) => {
      if (Number(val) > 100 || Number(val) < 0) {
        callback(new Error(t('整数 0 - 100')));
      }
      callback(true);
    },
  },
  hourNumberRange: {
    func: (val, formData, callback) => {
      if (Number(val) > 999999999 || Number(val) < 0) {
        callback(new Error(t('整数 0 - 999999999')));
      }
      callback(true);
    },
  },
  onlyOneValue: {
    func: (_, formData, callback, props) => {
      const { title } = props;
      for (let i = 0; i < formData.preemptivePrePriorityList.length; i++) {
        const duplicateList = [];
        const list = [];
        for (let j = 0; j < formData.preemptivePrePriorityList[i].occupyPrePriorityList.length; j++) {
          if (list.indexOf(`${formData.preemptivePrePriorityList[i].occupyPrePriorityList[j].boxAreaType}-${formData.preemptivePrePriorityList[i].occupyPrePriorityList[j].areaCategory}`) === -1) {
            list.push(`${formData.preemptivePrePriorityList[i].occupyPrePriorityList[j].boxAreaType}-${formData.preemptivePrePriorityList[i].occupyPrePriorityList[j].areaCategory}`);
            // eslint-disable-next-line no-continue
            continue;
          }
          duplicateList.push(`${i}-${formData.preemptivePrePriorityList[i].occupyPrePriorityList[j].boxAreaType}-${formData.preemptivePrePriorityList[i].occupyPrePriorityList[j].areaCategory}`);
        }
        if (duplicateList.length > 0 && duplicateList.includes(title)) {
          callback(new Error(t('箱规库区+库位类别只能选择一次')));
          return;
        }
      }
      callback(true);
    },
  },
});
interface Props {
    visible?: boolean;
    id?: number;
    detail?: IDetailInfo;
    onClose: () => void;
    onOk?: (params: IDetailInfo) => void;
    warehouseList: IWarehouseItem[];
    parkSubwarehouseAreaList: [];
    store: Store;
    warehouseId: number;
}

function DetailModal(props: Props & { store: Store}) {
  const {
    visible, detail, onClose, onOk, id, warehouseList,
    parkSubwarehouseAreaList, store, warehouseId,
  } = props;
  const [formData, setFormData] = useState<IDetailInfo>({ ...defaultFormData });
  const [subWarehouseList, setSubWarehouseList] = useState<IWarehouseItem[]>([]);
  // const [areaList, setAreaList] = useState<IAreaItem[]>([]);
  const [selectedSpecialOutOrderTypeList, setSelectedSpecialOutOrderTypeList] = useState<(number | string)[]>([]);
  const [loading, setLoading] = useState(false);
  const [formRef, setFormRef] = useState<FormRef>();

  const dictionary = useDictionary(['BILL_TYPE', 'REPLENISH_ORDER_TYPE', 'AREA_CATEGORY', 'NO_COUNTRY_LINE_PREEMPTIVE_MODE',
    'NO_COUNTRY_LINE_CONTROL_VARIABLE', 'NO_COUNTRY_LINE_ORDER_TYPE', 'NATIONAL_LINE_TYPE', 'NO_COUNTRY_LINE_BOX_AREA_TYPE',
  ], 'wmd');

  const addItem = useCallback((index: number) => {
    const preemptivePrePriorityList = formData.preemptivePrePriorityList || [];
    preemptivePrePriorityList.splice(index + 1, 0, {
      occupyPrePriorityList: [{}],
    });
    setFormData({
      ...formData,
      preemptivePrePriorityList,
    });
  }, [formData]);

  const deleteItem = useCallback((index: number) => {
    const preemptivePrePriorityList = formData.preemptivePrePriorityList || [];
    preemptivePrePriorityList.splice(index, 1);
    setFormData({
      ...formData,
      preemptivePrePriorityList,
    });
  }, [formData]);

  const addChildItem = useCallback((parentIndex: number, index: number) => {
    const preemptivePrePriorityList = formData.preemptivePrePriorityList || [];
    const occupyPrePriorityList = preemptivePrePriorityList[parentIndex].occupyPrePriorityList || [];
    occupyPrePriorityList.splice(index + 1, 0, {});
    setFormData({
      ...formData,
      preemptivePrePriorityList,
    });
  }, [formData]);

  const deleteChildItem = useCallback((parentIndex: number, index: number) => {
    const preemptivePrePriorityList = formData.preemptivePrePriorityList || [];
    const occupyPrePriorityList = preemptivePrePriorityList[parentIndex].occupyPrePriorityList || [];
    occupyPrePriorityList.splice(index, 1);
    setFormData({
      ...formData,
      preemptivePrePriorityList,
    });
  }, [formData]);

  const addModeListItem = useCallback((index: number) => {
    const preemptiveModeList = formData.preemptiveModeList || [];
    preemptiveModeList.splice(index + 1, 0, {});
    setFormData({
      ...formData,
      preemptiveModeList,
    });
  }, [formData]);

  const deleteModeListItem = useCallback((index: number) => {
    const preemptiveModeList = formData.preemptiveModeList || [];
    preemptiveModeList.splice(index, 1);
    setFormData({
      ...formData,
      preemptiveModeList,
    });
  }, [formData]);

  const addNumberConfigListItem = useCallback((index: number) => {
    const controlVariableList = formData.controlVariableList || [];
    controlVariableList.splice(index + 1, 0, {
      dimensionId: '',
      dimension: 2,
      controlVariable: '',
    });
    setFormData({
      ...formData,
      controlVariableList,
    });
  }, [formData]);

  const deleteNumberConfigListItem = useCallback((index: number) => {
    const controlVariableList = formData.controlVariableList || [];
    controlVariableList.splice(index, 1);
    setFormData({
      ...formData,
      controlVariableList,
    });
  }, [formData]);

  const saveData = useCallback(async () => {
    if (onOk) {
      setLoading(true);

      await onOk({
        ...formData,
        unPreemptiveList: [
          {
            dimension: 1,
            regionList: formData.unPreemptiveList[0].regionList.map((regionId: number) => ({ regionId })),
          },
          {
            dimension: 2,
            regionList: formData.unPreemptiveList[1].regionList.map((regionId: number) => ({ regionId })),
          },
        ],
        preemptivePrePriorityList: formData.preemptivePrePriorityList.map((item: IPreemptivePrePriorityListItem) => ({
          ...item,
          occupyPrePriorityList: item.occupyPrePriorityList.map((j: IOccupyPrePriorityListItem, index: number) => ({
            ...j,
            priorityOrder: index,
          })),
        })),
      });
      setLoading(false);
    }
  }, [formData, onOk]);
  const getSubWarehouseList = useCallback(async (wid: number) => {
    if (!wid) {
      setSubWarehouseList([]);
      return;
    }
    try {
      const res = await selectSubWarehouse({ warehouseId: wid, enabled: 1 });
      if (res.code === '0') {
        setSubWarehouseList(res.info?.data);
      } else {
        Modal.error({ title: res.msg });
      }
    } catch (e: { message?: string; reason?: { message: string; }; }) {
      Modal.error({ title: e.message || e.reason?.message });
    }
  }, []);

  useEffect(() => {
    if (!visible) {
      formRef?.clearValidate();
      return;
    }
    setLoading(false);
    setSelectedSpecialOutOrderTypeList([]);
    if (!detail) {
      getSubWarehouseList(warehouseId);
      store.queryParkArea({ id: warehouseId, isQueryAreaType: false });
      setFormData({ ...defaultFormData, warehouseId });
      return;
    }
    // console.log('getSubWarehouseList detail', detail);
    const subWarehouseRegionList = detail?.unPreemptiveList ? detail?.unPreemptiveList.find((v) => v.dimension === 1)?.regionList.map((i: { regionId: number }) => i.regionId) : [];
    const newDetail = {
      ...detail,
      unPreemptiveList: [
        {
          dimension: 1,
          regionList: subWarehouseRegionList,
        },
        {
          dimension: 2,
          regionList: detail?.unPreemptiveList ? detail.unPreemptiveList.find((v) => v.dimension === 2)?.regionList.map((i: { regionId: number }) => `${i.regionId}`) : [],
        },
      ],
      // 每小时可生成补货量
      controlVariableList: detail?.controlVariableList?.map((i) => ({
        ...i,
        controlVariable: i.controlVariable === -1 ? '' : i.controlVariable,
      })),
    };
    console.log('🚀🚗', newDetail, detail);

    setFormData({ ...newDetail });
    getSubWarehouseList(detail.warehouseId);
    // getAreaList(subWarehouseRegionList);
    store.queryParkArea({ id: detail.warehouseId, isQueryAreaType: false });
  }, [visible, detail, formRef, warehouseId]);

  return (
    <Modal
      title={!id ? t('新增') : t('编辑')}
      visible={visible}
      hideClose
      maskCloseAble={false}
      style={{ width: 900, maxHeight: 800, overflowY: 'auto' }}
      onClose={() => {
        if (onClose) {
          onClose();
        }
      }}
      footer={[
        <Modal.Submit loading={loading}>{t('保存')}</Modal.Submit>,
        <Button
          type="default"
          onClick={() => {
            if (onClose) {
              onClose();
            }
          }}
        >
          {t('返回')}
        </Button>,
      ]}
    >
      <Form
        inline
        labelWidth={140}
        labelAlign="right"
        value={formData}
        formRef={(f: FormRef) => {
          setFormRef(f);
        }}
        onChange={(val) => {
          setFormData(val);
        }}
        onSubmit={async () => {
          saveData();
        }}
      >

        <div className={style.itemTitle}>
          {t('基础信息')}
        </div>
        <div className={style.selectWrap}>
          <Form.Item required label={t('仓库: ')}>
            <Form.Field name="warehouseId" rules={[rule.required(t('请选择仓库'))]}>
              {({ value }) => (
                <Select<IWarehouseItem>
                  data={warehouseList}
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  width={220}
                  absolute
                  // disabled={!!id}
                  disabled
                  placeholder={t('请选择仓库')}
                  renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                  onFilter={(text) => (d) => (d?.nameZh ?? '').toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  value={value}
                  onChange={(val) => {
                    // onChange(val);
                    setFormData({
                      ...formData,
                      warehouseId: val,
                      unPreemptiveList: [{
                        dimension: 1,
                        regionList: [],
                      },
                      {
                        dimension: 2,
                        regionList: [],
                      },
                      ],
                    });
                    getSubWarehouseList(val);
                    store.queryParkArea({ id: val, isQueryAreaType: false });
                  }}
                />
              )}
            </Form.Field>

          </Form.Item>
          <Form.Item label={t('状态: ')}>
            <Radio.Group<DictionaryItem, string>
              name="status"
              data={statusList}
              keygen="dictCode"
              format="dictCode"
              renderItem="dictNameZh"
            />
          </Form.Item>
          <Form.Item label={t('补货类型: ')}>
            <Select<DictionaryItem>
              name="orderType"
              data={dictionary.data.REPLENISH_ORDER_TYPE}
              disabled={!!id}
              keygen="dictCode"
              format="dictCode"
              renderItem="dictNameZh"
              width={220}
              absolute
              placeholder={t('请选择补货类型')}
              renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择补货类型')}</span>}
              onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            />
          </Form.Item>
        </div>

        <div className={style.itemTitle}>
          {t('备货区库存预占优先级')}
        </div>
        <div className={style.selectWrap}>
          { formData.preemptivePrePriorityList && formData.preemptivePrePriorityList.map((item, index: number) => (
            <div>
              <div className={style.rowWrap}>
                <Form.Item label={`${t('订单类型') + (index + 1)}: `}>
                  <Select<DictionaryItem>
                    name={`preemptivePrePriorityList[${index}].orderType`}
                    data={dictionary.data.NO_COUNTRY_LINE_ORDER_TYPE}
                    keygen="dictCode"
                    format="dictCode"
                    renderItem="dictNameZh"
                    width={140}
                    absolute
                    placeholder={t('请选择订单类型')}
                    renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择订单类型')}</span>}
                    onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  />
                </Form.Item>
                {formData.preemptivePrePriorityList[index].orderType === 2 && (
                  <Form.Item label={null} labelWidth={0}>
                    <Form.Field name={`preemptivePrePriorityList[${index}].billType`}>
                      {({ value, onChange }) => (
                        <Select<DictionaryItem>
                          data={dictionary.data.BILL_TYPE}
                          keygen="dictCode"
                          format="dictCode"
                          renderItem="dictNameZh"
                          width={180}
                          absolute
                          multiple
                          compressed
                          disabled={(i) => {
                            if (selectedSpecialOutOrderTypeList[index] && selectedSpecialOutOrderTypeList[index].indexOf(i.dictCode) > -1) {
                              return false;
                            }
                            return selectedSpecialOutOrderTypeList.reduce((a, b) => a.concat(b), []).indexOf(i.dictCode) > -1;
                          }}
                          placeholder={t('请选择特殊出库单据类型')}
                          renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择特殊出库单据类型')}</span>}
                          onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                          value={value}
                          onChange={(val) => {
                            onChange(val);
                          }}
                          clearable
                          onCollapse={(collapse) => {
                            if (!collapse) {
                              const list = [...selectedSpecialOutOrderTypeList];
                              list.splice(index, 1, value);
                              setSelectedSpecialOutOrderTypeList(list);
                            }
                          }}
                        />
                      )}
                    </Form.Field>

                  </Form.Item>
                )}
                <div className={style.iconList} style={{ marginBottom: 10 }}>
                  <Icon className={style.icon} name="plus-o" onClick={() => addItem(index)} />
                  {
                    formData.preemptivePrePriorityList.length > 1 && (
                      <Icon className={style.icon} name="minus-o" onClick={() => deleteItem(index)} />
                    )
                  }
                </div>
              </div>
              {item.occupyPrePriorityList && item.occupyPrePriorityList.map((priorityItem, priorityIndex: number) => (
                <div className={style.rowChildWrap}>
                  <Form.Item label={`${t('优先级') + (priorityIndex + 1)}: `}>
                    <Form.Field
                      name={[`preemptivePrePriorityList[${index}].occupyPrePriorityList[${priorityIndex}].boxAreaType`, `preemptivePrePriorityList[${index}].occupyPrePriorityList[${priorityIndex}].areaCategory`]}
                      title={`${index}-${formData.preemptivePrePriorityList[index].occupyPrePriorityList[priorityIndex].boxAreaType}-${formData.preemptivePrePriorityList[index].occupyPrePriorityList[priorityIndex].areaCategory}`}
                      rules={[rule.onlyOneValue()]}
                    >
                      {({ value, onChange }) => (
                        <div className={style.rowWrap}>
                          <Select<DictionaryItem>
                            data={dictionary.data.NO_COUNTRY_LINE_BOX_AREA_TYPE}
                            keygen="dictCode"
                            format="dictCode"
                            renderItem="dictNameZh"
                            width={220}
                            absolute
                            name={`preemptivePrePriorityList[${index}].occupyPrePriorityList[${priorityIndex}].boxAreaType`}
                            placeholder={t('请选择箱规库区类型')}
                            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择箱规库区类型')}</span>}
                            onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                            value={value[0]}
                            onChange={(val) => {
                              onChange([val, value[1]]);
                            }}
                            onCollapse={(collapse) => {
                              if (!collapse && value[0] && value[1]) {
                                formRef?.validate();
                              }
                            }}
                          />
                          <Select<DictionaryItem>
                            data={dictionary.data.AREA_CATEGORY}
                            keygen="dictCode"
                            format="dictCode"
                            renderItem="dictNameZh"
                            width={220}
                            style={{ marginLeft: 10 }}
                            absolute
                            name={`preemptivePrePriorityList[${index}].occupyPrePriorityList[${priorityIndex}].areaCategory`}
                            placeholder={t('请选择库区类别')}
                            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择库区类别')}</span>}
                            onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                            value={value[1]}
                            onChange={(val) => {
                              onChange([value[0], val]);
                            }}
                            onCollapse={(collapse) => {
                              if (!collapse && value[0] && value[1]) {
                                formRef?.validate();
                              }
                            }}
                          />
                          <div className={style.iconList}>
                            <Icon className={style.icon} name="plus-o" onClick={() => addChildItem(index, priorityIndex)} />
                            {
                            formData.preemptivePrePriorityList[index].occupyPrePriorityList.length > 1 && (
                              <Icon className={style.icon} name="minus-o" onClick={() => deleteChildItem(index, priorityIndex)} />
                            )
                          }
                          </div>
                        </div>
                      )}
                    </Form.Field>
                  </Form.Item>

                </div>
              ))}
              <div className={style.rowChildWrap}>
                <Form.Item label={t('兜底优先级: ')}>
                  <div style={{ marginTop: 10 }}>{t('其他备货区')}</div>
                </Form.Item>
              </div>
            </div>
          ))}
        </div>

        <div className={style.itemTitle}>
          {t('不可预占区域：区域维度+具体内容')}
        </div>
        <div className={style.selectWrap}>
          <div>
            <Form.Item label={t('1、子仓: ')}>
              <Form.Field name="unPreemptiveList[0].regionList">
                {({ value }) => (
                  <Select<IWarehouseItem>
                    data={subWarehouseList}
                    keygen="id"
                    format="id"
                    renderItem="nameZh"
                    width={220}
                    absolute
                    multiple
                    clearable
                    compressed
                    placeholder={t('请选择子仓')}
                    renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                    onFilter={(text) => (d) => (d?.nameZh ?? '').toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    value={value}
                    onChange={(val) => {
                      // onChange(val);
                      const newUnPreemptiveList = [...formData.unPreemptiveList || []];
                      newUnPreemptiveList[0] = {
                        dimension: 1,
                        regionList: val,
                      };
                      setFormData({
                        ...formData,
                        unPreemptiveList: [...newUnPreemptiveList],
                      });
                      // getAreaList(val);
                    }}
                  />
                )}
              </Form.Field>
            </Form.Item>
          </div>
          <div>
            <Form.Item label={t('2、库区: ')}>
              <TreeSelect
                keygen="id"
                format="id"
                renderItem="name"
                childrenKey="children"
                name="unPreemptiveList[1].regionList"
                value={formData.unPreemptiveList[1]?.regionList}
                data={parkSubwarehouseAreaList}
                width={220}
                mode={2}
                onChange={(val) => {
                  const newUnPreemptiveList = [...formData.unPreemptiveList || []];
                  newUnPreemptiveList[1] = {
                    dimension: 2,
                    regionList: val,
                  };
                  setFormData({
                    ...formData,
                    unPreemptiveList: [...newUnPreemptiveList],
                  });
                }}
                onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                // style={inputStyle}
                placeholder={t('请选择')}
                multiple
                compressed
                clearable
              />
              {/* <Select<IAreaItem>
                name="unPreemptiveList[1].regionList"
                data={areaList}
                keygen="id"
                format="id"
                renderItem="area"
                width={220}
                absolute
                multiple
                clearable
                placeholder={t('请选择库区')}
                compressed
                renderUnmatched={(r) => r.area || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d?.area?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(val) => {
                  // onChange(val);
                  const newUnPreemptiveList = [...formData.unPreemptiveList || []];
                  newUnPreemptiveList[1] = {
                    dimension: 2,
                    regionList: val,
                  };
                  setFormData({
                    ...formData,
                    unPreemptiveList: [...newUnPreemptiveList],
                  });
                }}
              /> */}
            </Form.Item>
          </div>
        </div>

        <div className={style.itemTitle}>
          {t('箱规库区预占配置')}
        </div>
        <div className={style.selectWrap}>
          { formData.preemptiveModeList && formData.preemptiveModeList.map((item, index: number) => (
            <div>
              <div className={style.rowWrap}>
                <Form.Item required label={`${t('箱规库区预占模式') + (index + 1)}: `}>
                  <Form.Field name={[`preemptiveModeList[${index}].areaCategory`, `preemptiveModeList[${index}].preemptiveMode`]} rules={[rule.required(t('库区类别+预占模式不能为空'))]}>
                    {({ value, onChange }) => (
                      <div className={style.rowWrap}>
                        <Select<DictionaryItem>
                          data={dictionary.data.AREA_CATEGORY}
                          keygen="dictCode"
                          format="dictCode"
                          renderItem="dictNameZh"
                          width={220}
                          multiple
                          compressed
                          absolute
                          placeholder={t('请选择库区类别')}
                          renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择库区类别')}</span>}
                          onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                          value={value[0]}
                          onChange={(val) => {
                            onChange([val, value[1]]);
                          }}
                        />
                        <Select<DictionaryItem>
                          data={dictionary.data.NO_COUNTRY_LINE_PREEMPTIVE_MODE}
                          keygen="dictCode"
                          format="dictCode"
                          renderItem="dictNameZh"
                          width={220}
                          style={{ marginLeft: 10 }}
                          absolute
                          placeholder={t('请选择预占模式')}
                          renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择预占模式')}</span>}
                          onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                          value={value[1]}
                          onChange={(val) => {
                            onChange([value[0], val]);
                          }}
                        />
                        <div className={style.iconList}>
                          <Icon className={style.icon} name="plus-o" onClick={() => addModeListItem(index)} />
                          {
                            formData.preemptiveModeList.length > 1 && (
                              <Icon className={style.icon} name="minus-o" onClick={() => deleteModeListItem(index)} />
                            )
                          }
                        </div>
                      </div>
                    )}
                  </Form.Field>
                </Form.Item>

              </div>

            </div>
          ))}
          <div className={style.rowWrap}>
            <Form.Item required label={t('箱规库区超补比例: ')}>
              <Form.Field name="supplementaryProportion" rules={[rule.required(), rule.ratioRange()]}>
                {({ value, onChange }) => (
                  <Input.Group>
                    <Input.Number allowNull clearable digits={0} hideArrow placeholder={t('请输入数字')} value={value} onChange={onChange} />
                    <span>%</span>
                  </Input.Group>
                )}
              </Form.Field>
            </Form.Item>
          </div>
        </div>

        <div className={style.itemTitle}>
          {t('控量配置')}
        </div>
        <div className={style.selectWrap}>
          { formData.controlVariableList && formData.controlVariableList.map((item, index: number) => (
            <div>
              <div className={style.rowWrap}>
                <Form.Item labelWidth={0} label={null}>
                  <Form.Field name={[`controlVariableList[${index}].dimension`, `controlVariableList[${index}].dimensionId`]}>
                    {({ value, onChange }) => (
                      <div className={style.rowWrap}>
                        <Select<DictionaryItem>
                          data={dictionary.data.NO_COUNTRY_LINE_CONTROL_VARIABLE}
                          keygen="dictCode"
                          format="dictCode"
                          renderItem="dictNameZh"
                          width={140}
                          absolute
                          placeholder={t('请选择维度')}
                          renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('选择维度')}</span>}
                          onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                          value={value[0]}
                          clearable
                          onChange={(val) => {
                            onChange([val, value[1]]);
                            if (val === 1) {
                              setFormData({
                                ...formData,
                                controlVariableList: [{
                                  dimensionId: '',
                                  dimension: 1,
                                  controlVariable: '',
                                }],
                              });
                            }
                          }}
                        />
                        {formData.controlVariableList[index].dimension === 2 && (
                        <Select<DictionaryItem>
                          data={dictionary.data.NATIONAL_LINE_TYPE}
                          keygen="dictCode"
                          format="dictCode"
                          renderItem="dictNameZh"
                          width={180}
                          style={{ marginLeft: 10 }}
                          absolute
                          placeholder={t('请选择包裹国家线')}
                          renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('选择包裹国家线')}</span>}
                          onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                          value={value[1]}
                          onChange={(val) => {
                            onChange([value[0], val]);
                          }}
                        />
                        )}
                      </div>
                    )}
                  </Form.Field>
                </Form.Item>
                {/* {formData.controlVariableList[index].dimensionId === 2 && ( */}
                <Form.Item label={t('每小时可生成补货量')}>
                  <Input.Number
                    name={`controlVariableList[${index}].controlVariable`}
                    rules={[rule.hourNumberRange()]}
                    allowNull
                    clearable
                    hideArrow
                    placeholder={t('请输入数字')}
                    digits={0}
                    width={180}
                  />
                </Form.Item>
                {/* )} */}
                {formData.controlVariableList[index].dimension === 2 && (
                  <div className={style.iconList} style={{ marginBottom: 10 }}>
                    <Icon className={style.icon} name="plus-o" onClick={() => addNumberConfigListItem(index)} />
                    {
                      formData.controlVariableList.length > 1 && (
                        <Icon className={style.icon} name="minus-o" onClick={() => deleteNumberConfigListItem(index)} />
                      )
                    }
                  </div>
                )}
              </div>

            </div>
          ))}
        </div>
      </Form>
    </Modal>
  );
}

export default DetailModal;
