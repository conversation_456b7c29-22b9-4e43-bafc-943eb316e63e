import React from 'react';
import { t } from '@shein-bbl/react';
import { Rule, Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import DictionarySelect from '@src/component/public-component/dictionary-select';
import DateRangePicker from '@shein-components/dateRangePicker2';
import type { FormRef } from 'shineout/lib/Form/Props';
import moment from 'moment';
import { defaultLimit } from '../reducers';
import { ILimitType, IPageProps, Store } from '../types';

const rules = Rule({
  timeRange: {
    func: (_, formData, callback) => {
      // 开始时间和结束时间不能超过30天
      if (moment(formData.endTime).customDiff(moment(formData.startTime), 'months', true) > 3) {
        callback(new Error(t('开始时间和结束时间不能超过{}个月', 3)));
      }
      callback(true);
    },
  },
});

function Header(props: IPageProps & { store: Store}) {
  const {
    loading,
    limit,
    store,
    statusList,
  } = props;

  return (
    <section>
      {/* 高级搜索 */}
      <SearchAreaContainer
        value={limit}
        labelStyle={{ width: 90 }}
        searching={!loading}
        collapseOnSearch={false}
        clearUndefined={false}
        onSearch={() => {
          store.handlePaginationChange({ pageNum: 1 });
        }}
        onChange={(val: ILimitType) => {
          // 业务需求隐藏表单就设置默认值
          store.changeLimitData(formatSearchData(defaultLimit, val));
        }}
        onClear={() => store.clearLimitData()}
        formRef={(f: FormRef<ILimitType>) => {
          store.changeData({
            formRef: f,
          });
        }}
      >
        <DictionarySelect name="orderTypeList" multiple compressed label={t('补货类型')} code="REPLENISH_ORDER_TYPE" type="wmd" width={200} />

        <Select
          label={t('启用状态')}
          name="statusList"
          data={statusList}
          absolute
          format="dictCode"
          keygen="dictCode"
          renderItem="dictNameZh"
          placeholder={t('请选择')}
          onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          clearable
          compressed
          multiple
        />

        <DateRangePicker
          placeholder={[t('开始时间'), t('结束时间')]}
          type="datetime"
          inputable
          format="yyyy-MM-dd HH:mm:ss"
          name={['startTime', 'endTime']}
          defaultTime={['00:00:00', '23:59:59']}
          label={t('更新时间')}
          span={2}
          rules={[rules.timeRange()]}
        />
      </SearchAreaContainer>
    </section>
  );
}

export default Header;
