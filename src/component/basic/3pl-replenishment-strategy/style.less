.modalWrapper {
  display: flex;
  flex-direction: column;
}

.modalWrapperLabel {
  border-bottom: #d0d0d0 1px dotted;
  margin-bottom: 10px;
}

.inner_list {
  display: flex;
  align-items: center;
  margin-right: 20px;
  //flex: 1;
}

.labWidth {
  width: 40px;
  margin-right: 10px;
}

.labelWidth {
  width: 100px;
  margin-right: 10px;
}

.flex {
  display: flex;
  margin-left: 30px;
  margin-bottom: 30px;
}

.dotted_header {
  border-bottom: #d0d0d0 1px dashed;
  padding-bottom: 5px;
  margin-bottom: 10px;
}

.mix_group {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.mix_group_checkbox {
  margin-left: 10px;
  padding: 4px 6px;
  border: 1px solid rgb(212, 210, 210);
}

.block_label {
  padding: 0 10px 10px 0;
}

.thead {
  display: flex;
  background: #e9ecef;
  padding: 10px 5px 10px 10px;
  justify-content: space-between;

  .required {
    &:before {
      content: '*';
      color: red;
      margin-right: 5px;
    }
  }

  div {
    flex: 1;
  }
}

.disFlex {
  display: flex;
  align-items: flex-start;
  margin-top: 10px;
  padding: 0 5px 10px 10px;
}

.delIcon {
  margin-top: 6px;
  margin-right: 2px;
}

.flexStyle {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .tagLabel {
    margin-right: 10px;
  }
}

.flexStyle>div {
  margin-bottom: 5px;
}

.tagList {
  display: flex;
  margin-top: 3px;

  .label {
    min-width: 50px;
  }
}

.mergeModalTitle {
  display: flex;
  flex-direction: row;
  margin-bottom: 5px;

  .question {
    font-size: 18px;
    cursor: pointer;
  }
}

.mergeModal {
  :global {
    .so-checkinput {
      margin-right: 10px;
    }
  }
}

.red {
  display: inline-block;
  margin-left: 12px;
  font-size: 16px;
  font-weight: bold;
  color: red;
}