import { sendPostRequest } from '@src/server/common/public';

import fileFetch from '@src/server/common/fileFetch';
import { camel2Under } from '@src/lib/camal-case-convertor';
/**  3pl */
// 获取列表数据
export const getListAPI = (param) => sendPostRequest({
  url: '/three_pl_replenish_strategy/query',
  param,
}, process.env.WWS_URI);

export const addOrEditAPI = (param) => sendPostRequest({
  url: '/three_pl_replenish_strategy/edit',
  param,
}, process.env.WWS_URI);

// 存储标签混装配置-删除
export const deleteConfigAPI = (param) => sendPostRequest({
  url: '/three_pl_replenish_strategy/del',
  param,
}, process.env.WWS_URI);

/** 合并规则 */
// 列表查询
export const queryListAPI = (param) => sendPostRequest({
  url: '/replenish_change_upper_config/query',
  param,
}, process.env.WWS_URI);

// 下载导入模板
export const downloadTemplateAPI = () => {
  const uri = `${process.env.WWS_URI}/replenish_change_upper_config/download_template`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under()),
  });
};

export const UPLOAD_URL = `${process.env.WGS_FRONT}/file_import/record/wws/replenish_change_upper_config`;

// 新增
export const addMergeRecordAPI = (param) => sendPostRequest({
  url: '/replenish_change_upper_config/add',
  param,
}, process.env.WWS_URI);

// 更新
export const updateMergeRecordAPI = (param) => sendPostRequest({
  url: '/replenish_change_upper_config/edit',
  param,
}, process.env.WWS_URI);

// 删除
export const deleteMergeRecordAPI = (param) => sendPostRequest({
  url: '/replenish_change_upper_config/del',
  param,
}, process.env.WWS_URI);

// 3PL补货策略预约配置查询
export const appointmentQueryAPI = (param) => sendPostRequest({
  url: '/three_pl_replenish_strategy_reserve/query',
  param,
}, process.env.WWS_URI);

// 3PL补货策略预约配置编辑保存
export const appointmentEditAPI = (param) => sendPostRequest({
  url: '/three_pl_replenish_strategy_reserve/save',
  param,
}, process.env.WWS_URI);

// 补货任务上架园区转换配置预约查询
export const appointmentReserveQueryAPI = (param) => sendPostRequest({
  url: '/replenish_change_upper_config_reserve/query',
  param,
}, process.env.WWS_URI);

// 补货任务上架园区转换配置预约保存
export const appointmentReserveEditAPI = (param) => sendPostRequest({
  url: '/replenish_change_upper_config_reserve/save',
  param,
}, process.env.WWS_URI);

// 补货上架合并配置导出
export const exportApi = (param) => sendPostRequest({
  url: '/replenish_change_upper_config/export',
  param,
}, process.env.WWS_URI);
