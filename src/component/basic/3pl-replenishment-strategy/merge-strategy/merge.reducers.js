import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import { dictSelect } from '@src/server/basic/dictionary';
import fileSaver from 'file-saver';
import { formdataPost } from '@src/server/common/fileFetch';
import { STATISTICAL_IMPORT_CENTER, STATISTICAL_DOWNLOAD } from '@src/lib/jump-url';
import { queryRegionAPI, getRegionAPI, getConfigByCodeApi } from '@src/server/common/common';
import {
  queryListAPI,
  deleteMergeRecordAPI,
  addMergeRecordAPI,
  updateMergeRecordAPI,
  downloadTemplateAPI,
  UPLOAD_URL,
  appointmentReserveQueryAPI,
  appointmentReserveEditAPI,
  exportApi,
} from '../server';

export const MODAL_VISIBLE_CLOSE = 0; // 弹窗 关闭
export const MODAL_VISIBLE_ADD = 1; // 弹窗 新增
export const MODAL_VISIBLE_EDIT = 2; // 弹窗 编辑
export const MODAL_APPOINTMENT_EDIT = 3; // 弹窗 预约

export const defaultLimit = {
  underParkTypes: [], // 下架园区
  orderTypes: [], // 补货类型
  mergeStrategy: [], // 合并策略
  distinctParkTypes: [], // 目的园区
  status: '', // 状态
  underRegions: [],
};

export const defaultModalInfo = {
  warehouseId: '', // 仓库id
  downRegion: '', // 下架片区
  underParkType: '', // 下架园区
  orderType: '', // 补货类型
  mergeStrategy: '', // 合并策略
  distinctRegion: '', // 指向目的片区
  distinctParkType: '', // 指向目的园区
  status: true, // 是否开启[false禁用 true开启]
  mergeRegions: [], // 合并片区
  mergeParkTypes: [], // 合并园区
  effectTimes: [], // 有效时间
  reserveEffectTime: '', // 预约时间
};
// 时间列表
const effectTimeList = Array.from({ length: 24 }, (_, index) => (index < 10 ? {
  label: `0${index}`,
  value: index,
} : {
  label: `${index}`,
  value: index,
}));

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  loading: 1, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  warehouseList: [], // 仓库列表
  parkTypeList: [], // 查询条件 - 园区下拉
  regionTypeList: [], // 片区下拉
  replenishTypeList: [], // 补货类型
  statusList: [ // 状态下拉
    { dictNameZh: t('启用'), dictCode: true },
    { dictNameZh: t('禁用'), dictCode: false },
  ],
  mergeStrategyList: [
    { dictNameZh: t('合并园区'), dictCode: 0 },
    { dictNameZh: t('合并片区'), dictCode: 1 },
  ],
  effectTimeList,

  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeOptions: [20, 50, 100], // 表格页显示条数
  },
  list: [],

  // 操作记录
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗

  // 新增/编辑弹窗
  modalInfoSubWarehouseList: [], // 子仓下拉
  modalInfoVisible: MODAL_VISIBLE_CLOSE, // 0 关闭，1 新增 2 编辑
  modalInfo: defaultModalInfo,
  filteredRegionList: [], // 片区下拉 - 仓库变化
  underParkTypeList: [], // 下架园区下拉数据
  distinctParkTypeList: [], // 指向目的园区下拉数据
  effectTimeConfigVals: [], // 有效时间配置值
  underRegionList: [],
  filterParkTypeList: [],
  importModalVisible: false, // 导入弹窗
  file: '', // 上传文件
};

export default {
  state: defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 新增/编辑
  changeModalInfo(state, data) {
    Object.assign(state, {
      modalInfo: {
        ...state.modalInfo,
        ...data,
      },
    });
  },
  // 页面初始化
  * init() {
    const selectData = yield dictSelect({ catCode: ['REPLENISH_ORDER_TYPE', 'PARK_ENUM', 'REGION_ENUM'] });
    // 商品存储属性
    if (selectData.code === '0') {
      yield this.changeData({
        replenishTypeList: selectData.info.data.find((item) => item.catCode === 'REPLENISH_ORDER_TYPE').dictListRsps || [],
        parkTypeList: selectData.info.data.find((item) => item.catCode === 'PARK_ENUM').dictListRsps || [],
        regionTypeList: selectData.info.data.find((item) => item.catCode === 'REGION_ENUM').dictListRsps || [],
      });
    } else {
      Modal.error({
        title: selectData.msg,
      });
    }
    // 仓库列表
    const { warehouseList, warehouseId } = yield 'nav';
    yield this.changeData({
      warehouseList,
    });

    yield this.warehouseChange({ warehouseId });
    yield this.getEffectTimeVals();
    yield this.limitRegionQuery();
  },
  // 下架片区-搜索下拉
  * limitRegionQuery() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      return;
    }
    const { code, msg, info } = yield queryRegionAPI({ warehouseId }); // 获取片区列表
    if (code === '0') {
      yield this.changeData({
        underRegionList: info || [],
      });
      yield this.changeLimitData({
        underParkTypes: [],
        underRegions: [],
      });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  * getFilterParkList(regionList) {
    if (regionList.length === 0) {
      yield this.changeLimitData({
        underParkTypes: [],
      });
      return;
    }
    // 获取园区列表
    const { code, info, msg } = yield getRegionAPI({ regionList });
    if (code === '0') {
      yield this.changeData({
        filterParkTypeList: (info.parkInfos || []).map((e) => ({
          dictCode: e.parkType,
          dictNameZh: e.parkName,
        })),
      });
      yield this.changeLimitData({
        underParkTypes: [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 读取有效时间配置值
  * getEffectTimeVals() {
    const res = yield getConfigByCodeApi({ param: 'DAILY_REPLENISH_MERGE_CONFIG_EFFECTIVE_DATE' });
    if (res.code === '0' && res.info) {
      const vals = (res.info.configValue || '').split(',');
      yield this.changeData({
        effectTimeConfigVals: vals.map((e) => Number(e)),
      });
    }
  },
  // 查询
  * search() {
    // 获取仓库
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const { pageNum, pageSize } = pageInfo;
    const params = {
      ...limit,
      warehouseId,
      pageNum,
      pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield queryListAPI(clearEmpty(paramTrim(params), [0, '0', false]));
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 页签改变
  * handlePaginationChange(data = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  /**
   * 下载导入模板
   * @returns {Generator<*, void, *>}
   */
  * downloadTemplate() {
    markStatus('loading');
    const res = yield downloadTemplateAPI();
    const b = yield res.blob();
    const blob = new Blob([b], { type: 'application/octet-stream' });
    fileSaver.saveAs(blob, `${t('补货上架合并配置')}.xls`);
  },
  //
  * uploadFile() {
    markStatus('loading');
    const { file } = yield '';
    const formData = new FormData();
    formData.append('file', file);
    formData.append('function_node', 139);
    const res = yield formdataPost(UPLOAD_URL, formData);
    if (res.code === '0') {
      yield this.changeData({
        file: '',
        importModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
      window.open(STATISTICAL_IMPORT_CENTER);
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 删除
  * handleRecordDelete(id) {
    markStatus('loading');
    const { code, msg } = yield deleteMergeRecordAPI({ id });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  // 打开弹窗
  * openOrCloseModal({ type, record = defaultModalInfo }, ctx) {
    let info = record;
    const isAppointment = type === MODAL_APPOINTMENT_EDIT;
    if (isAppointment) {
      const appointmentInfo = yield this.appointmentQuery(record.id);
      info = appointmentInfo?.id ? {
        ...appointmentInfo,
        reserveId: appointmentInfo?.id,
        id: record.id,
      } : record;
    }
    yield this.changeData({
      modalInfoVisible: type,
      modalInfo: info,
    });

    // 编辑 - 额外获取园区下拉列表
    if ([MODAL_VISIBLE_EDIT, MODAL_APPOINTMENT_EDIT].includes(type)) {
      const {
        warehouseId, downRegion, underParkType, distinctRegion, distinctParkType,
      } = info;
      if (info.warehouseId) {
        yield ctx.handleRegionQuery({ warehouseId, downRegion, distinctRegion });
      }

      // 存在下架片区
      if (![null, undefined, ''].includes(downRegion)) {
        yield this.handleParkListQuery({ region: downRegion, parkType: underParkType });
      }

      // 存在目的片区
      if (![null, undefined, ''].includes(distinctRegion)) {
        yield ctx.handleParkListQuery({
          region: distinctRegion,
          parkType: distinctParkType,
          regionKey: 'distinctRegion',
          parkKey: 'distinctParkType',
        });
      }
    }
  },
  // 1 新增 2 编辑
  * confirmSaveConfig() {
    markStatus('loading');
    const { modalInfoVisible, modalInfo } = yield '';
    const isAppointment = modalInfoVisible === MODAL_APPOINTMENT_EDIT;

    const param = {
      ...modalInfo,
      isNewPage: true,
    };
    let api;
    let tips;
    if (isAppointment) {
      api = appointmentReserveEditAPI;
      tips = t('预约配置成功');
    } else {
      api = modalInfoVisible === MODAL_VISIBLE_ADD ? addMergeRecordAPI : updateMergeRecordAPI;
      tips = modalInfoVisible === MODAL_VISIBLE_ADD ? t('新增成功') : t('编辑成功');
    }
    const { code, msg } = yield api(param);
    if (code === '0') {
      Message.success(tips);
      // 关闭弹框
      yield this.openOrCloseModal({ type: MODAL_VISIBLE_CLOSE });
      // 重新搜索
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 仓库变化 - 片区重新处理
  * handleRegionQuery({ warehouseId, downRegion = null, distinctRegion = null }, ctx) {
    // 将园区和片区全部置为null
    yield ctx.changeModalInfo({
      downRegion, // 下架片区 - 编辑时默认
      underParkType: null, // 下架园区
      distinctRegion, // 指向目的片区 - 编辑时默认
      distinctParkType: null, // 指向目的园区
      warehouseId,
    });
    const { code, msg, info } = yield queryRegionAPI({ warehouseId }); // 获取片区列表

    if (code === '0') {
      yield this.changeData({
        underParkTypeList: [], // 下架园区置空
        distinctParkList: [], // 指向目的园区置空
        filteredRegionList: info?.filter((item) => item.region !== 0) || [],
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
      yield this.changeData({
        filteredRegionList: [],
        underParkTypeList: [], // 下架园区置空
        distinctParkList: [], // 指向目的园区置空
      });
    }
  },
  // 获取园区列表, 根据 regionKey与parkKey 区分是下架还是指向的园区和片区
  * handleParkListQuery({
    region, parkType = null, regionKey = 'downRegion', parkKey = 'underParkType',
  }, ctx) {
    if (['undefined', null, ''].includes(region)) return;

    yield ctx.changeModalInfo({
      [regionKey]: region,
      [parkKey]: parkType,
    });
    // 获取园区列表
    const { code, info, msg } = yield getRegionAPI({ region });
    if (code === '0') {
      yield this.changeData({
        [`${parkKey}List`]: info.parkInfos,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * @description 仓库改变
   * @param {number} warehouseId
   * @param {string} type 'list' 列表数据/'modal' 弹窗数据，默认为'list'
   * @param {boolean} isInit 是否为初始化，默认为false
   */
  * warehouseChange({ warehouseId }) {
    yield this.changeData({
      warehouseId,
    });
    yield this.limitRegionQuery();
  },
  // 预约配置查询
  * appointmentQuery(id) {
    markStatus('loading');
    const { code, msg, info } = yield appointmentReserveQueryAPI({
      id,
    });
    if (code === '0') {
      return info || {};
    }
    Modal.error({ title: msg });
  },

  /**
   * 导出
   */
  * exportExcel() {
    markStatus('loading');
    const { limit } = yield '';
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const res = yield exportApi(clearEmpty(paramTrim({ ...limit, warehouseId }), [0, '0', false]));
    if (res.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: res.msg });
    }
  },
};
