import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import { defaultLimit } from '../merge.reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      parkTypeList,
      replenishTypeList,
      mergeStrategyList,
      statusList,
      store,
      underRegionList,
      filterParkTypeList,
    } = this.props;

    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            name="underRegions"
            data={underRegionList}
            keygen="region"
            format="region"
            renderItem="regionName"
            label={t('下架片区')}
            placeholder={t('全部')}
            onFilter={(text) => (d) => d.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(val) => store.getFilterParkList(val)}
            clearable
            multiple
            compressed
          />
          <Select
            label={t('下架园区')}
            name="underParkTypes"
            data={limit.underRegions?.length > 0 ? filterParkTypeList : parkTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
          />
          <Select
            label={t('补货类型')}
            name="orderTypes"
            data={replenishTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
            placeholder={t('全部')}
          />
          <Select
            label={t('合并策略')}
            name="mergeStrategy"
            data={mergeStrategyList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
            placeholder={t('全部')}
          />
          <Select
            label={t('指向目的园区')}
            name="distinctParkTypes"
            data={parkTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
          />
          <Select
            label={t('状态')}
            name="status"
            data={statusList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  store: PropTypes.shape(),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape()), // 园区
  replenishTypeList: PropTypes.arrayOf(PropTypes.shape()), // 补货类型
  mergeStrategyList: PropTypes.arrayOf(PropTypes.shape()), // 合并策略
  statusList: PropTypes.arrayOf(PropTypes.shape()), // 状态
  underRegionList: PropTypes.arrayOf(PropTypes.shape()),
  filterParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
};

export default Header;
