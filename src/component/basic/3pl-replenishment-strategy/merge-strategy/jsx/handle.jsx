import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Button, Modal } from 'shineout';
import globalStyles from '@src/component/style.less';
import FileDelayUpload from '@public-component/upload/fileDelayUpload';
import { MODAL_VISIBLE_ADD } from '../merge.reducers';
import AddEditModal from './modal';
import style from '../../style.less';

class Handle extends React.Component {
  render() {
    const {
      loading,
      store,
      importModalVisible,
      file,
    } = this.props;

    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => store.openOrCloseModal({ type: MODAL_VISIBLE_ADD })}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              importModalVisible: true,
              file: '',
            });
          }}
        >
          {t('导入')}
        </Button>
        <Button
          type="primary"
          size="default"
          loading={!loading}
          onClick={() => store.downloadTemplate()}
        >
          {t('下载导入模版')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.exportExcel();
          }}
        >
          {t('导出')}
        </Button>
        <div className={style.red}>{t('请先确认“库存预占区域优先级”，再进行配置')}</div>
        <AddEditModal {...this.props} />
        <Modal
          maskCloseAble={null}
          visible={importModalVisible}
          title={t('导入')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={!file}
              loading={!loading}
              onClick={() => {
                store.uploadFile();
                return false;
              }}
            >
              {t('确认上传')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!loading}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  store: PropTypes.shape(),
  importModalVisible: PropTypes.bool,
  file: PropTypes.string,
};

export default Handle;
