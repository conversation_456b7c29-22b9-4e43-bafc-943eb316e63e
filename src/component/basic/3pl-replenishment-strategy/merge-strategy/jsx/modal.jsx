import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Modal, Button, Select, Radio, Form, Popover, Checkbox, DatePicker, Rule, Message,
} from 'shineout';
import moment from 'moment';
import Icon from '@shein-components/Icon';
import { formatSearchData } from '@public-component/search-queries/utils';
import {
  MODAL_VISIBLE_CLOSE, MODAL_VISIBLE_ADD, MODAL_VISIBLE_EDIT, defaultModalInfo, MODAL_APPOINTMENT_EDIT,
} from '../merge.reducers';
import style from '../../style.less';

const rules = Rule({
  reservationTimeRange: {
    func: (val, formData, callback) => {
      const currentTime = moment();
      const selectTime = moment(val);
      // 选择时间不能小于当前时间
      if (selectTime.isBefore(currentTime)) {
        Message.warn(t('选择时间不能小于当前时间'));
        callback(new Error(t('选择时间不能小于当前时间')));
      }
      callback(true);
    },
  },
});
class ConfigModal extends React.Component {
  render() {
    const {
      loading,
      modalInfoVisible,
      modalInfo,
      statusList,
      filteredRegionList, // 下架片区列表
      underParkTypeList, // 下架园区列表
      distinctParkTypeList, // 指向目的园区列表
      replenishTypeList,
      mergeStrategyList, // 合并策略列表
      warehouseList,
      parkTypeList, // 园区列表
      regionTypeList, // 片区列表
      effectTimeList,
      store,
      effectTimeConfigVals,
    } = this.props;

    // 需要校验的参数
    const validKeys = [
      'warehouseId', 'downRegion', 'underParkType', 'orderType',
      'mergeStrategy', 'distinctRegion', 'distinctParkType', 'status',
    ];

    // 校验是否已选合并园区
    const isValid = validKeys.every((key) => ![null, undefined, ''].includes(modalInfo[key]))
        && (
          // 0 - 合并园区，则判断是否已选园区
          modalInfo.mergeStrategy === 0
            ? modalInfo.mergeParks?.length !== 0
            : modalInfo.mergeRegions?.length !== 0
        )
        && modalInfo.effectTimes?.length !== 0;

    let title = '';
    const isAppointment = MODAL_APPOINTMENT_EDIT === modalInfoVisible;
    if (isAppointment) {
      title = (
        <span>
          {t('预约配置')}
          <span style={{ color: 'red', marginLeft: 20 }}>{t('该页面所有参数仅在【预约配置时间】到达后生效')}</span>
        </span>
      );
    } else if (modalInfoVisible === MODAL_VISIBLE_ADD) {
      title = t('新增');
    } else {
      title = t('编辑');
    }

    return (
      <Modal
        destroy
        maskCloseAble={false}
        visible={modalInfoVisible !== MODAL_VISIBLE_CLOSE}
        title={title}
        width={800}
        bodyStyle={{ maxHeight: '800px', minHeight: '500px', overflow: 'auto' }}
        onClose={() => store.changeData({ modalInfoVisible: MODAL_VISIBLE_CLOSE, modalInfo: defaultModalInfo })}
        footer={[
          <Button
            onClick={() => {
              store.changeData({ modalInfoVisible: MODAL_VISIBLE_CLOSE, modalInfo: defaultModalInfo });
            }}
          >
            {t('取消')}
          </Button>,
          <Modal.Submit
            disabled={!isValid}
            loading={!loading}
          >
            {t('保存')}
          </Modal.Submit>,
        ]}
      >
        <Form
          labelWidth={100}
          labelAlign="right"
          style={{ maxWidth: 800 }}
          className={style.mergeModal}
          onSubmit={() => store.confirmSaveConfig()}
          onChange={(value) => store.changeModalInfo(formatSearchData(defaultModalInfo, value))}
          value={modalInfo}
          inline
          formRef={(f) => store.changeData({ modalFormRef: f })}
        >
          <Form.Item required label={t('仓库')}>
            <Select
              name="warehouseId"
              data={warehouseList}
              disabled={[MODAL_VISIBLE_EDIT, MODAL_APPOINTMENT_EDIT].includes(modalInfoVisible)}
              keygen="id"
              format="id"
              renderItem="nameZh"
              width={220}
              placeholder={t('请选择')}
              onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              onChange={(val) => store.handleRegionQuery({ warehouseId: val })}
            />
          </Form.Item>
          <Form.Item required label={t('下架片区')}>
            <Select
              name="downRegion"
              disabled={[MODAL_VISIBLE_EDIT, MODAL_APPOINTMENT_EDIT].includes(modalInfoVisible)}
              data={filteredRegionList}
              keygen="region"
              format="region"
              renderItem="regionName"
              placeholder={t('请选择')}
              width={220}
              onFilter={(text) => (d) => d.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              onChange={(val) => store.handleParkListQuery({ region: val })}
              clearable
            />
          </Form.Item>
          <Form.Item required label={t('下架园区')}>
            <Select
              name="underParkType"
              disabled={[MODAL_VISIBLE_EDIT, MODAL_APPOINTMENT_EDIT].includes(modalInfoVisible)}
              data={underParkTypeList}
              keygen="parkType"
              format="parkType"
              renderItem="parkName"
              placeholder={t('请选择')}
              width={220}
              onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
            />
          </Form.Item>
          <Form.Item required label={t('补货类型')}>
            <Select
              label={t('补货类型')}
              disabled={[MODAL_VISIBLE_EDIT, MODAL_APPOINTMENT_EDIT].includes(modalInfoVisible)}
              name="orderType"
              data={replenishTypeList}
              keygen="dictCode"
              format="dictCode"
              renderItem="dictNameZh"
              width={220}
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
              placeholder={t('请选择')}
            />
          </Form.Item>
          <Form.Item required label={t('合并策略')}>
            <Select
              label={t('合并策略')}
              disabled={[MODAL_VISIBLE_EDIT, MODAL_APPOINTMENT_EDIT].includes(modalInfoVisible)}
              name="mergeStrategy"
              data={mergeStrategyList}
              keygen={(d) => `${d.dictCode}`}
              format="dictCode"
              renderItem="dictNameZh"
              width={220}
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              onChange={(val) => {
                store.changeModalInfo({
                  mergeStrategy: val,
                  mergeParkTypes: [],
                  mergeRegions: [],
                });
              }}
              clearable
              placeholder={t('请选择')}
            />
          </Form.Item>
          <Form.Item required label={t('指向目的片区')}>
            <Select
              name="distinctRegion"
              data={filteredRegionList}
              keygen="region"
              format="region"
              renderItem="regionName"
              placeholder={t('请选择')}
              width={220}
              onFilter={(text) => (d) => d.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              onChange={(val) => store.handleParkListQuery({ region: val, regionKey: 'distinctRegion', parkKey: 'distinctParkType' })}
              clearable
            />
          </Form.Item>
          <Form.Item required label={t('指向目的园区')}>
            <Select
              label={t('指向目的园区')}
              name="distinctParkType"
              data={distinctParkTypeList}
              keygen="parkType"
              format="parkType"
              renderItem="parkName"
              placeholder={t('请选择')}
              width={220}
              onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
            />
          </Form.Item>
          <Form.Item required label={t('状态')}>
            <Radio.Group
              name="status"
              data={statusList}
              renderItem="dictNameZh"
              format="dictCode"
              keygen="dictNameZh"
            />
          </Form.Item>
          {
            // 合并园区
            modalInfo.mergeStrategy === 0 && (
              <>
                <div className={style.mergeModalTitle}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  {t('合并园区')}
                </div>
                <Form.Item label="" labelWidth="0">
                  <Checkbox.Group
                    name="mergeParkTypes"
                    data={parkTypeList}
                    renderItem="dictNameZh"
                    format="dictCode"
                    keygen="dictCode"
                  />
                </Form.Item>
              </>
            )
          }
          {
            // 合并片区
            modalInfo.mergeStrategy === 1 && (
              <>
                <div className={style.mergeModalTitle}>
                  <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                  {t('合并片区')}
                </div>
                <Form.Item label="" labelWidth="0">
                  <Checkbox.Group
                    name="mergeRegions"
                    data={regionTypeList}
                    renderItem="dictNameZh"
                    format="dictCode"
                    keygen="dictCode"
                  />
                </Form.Item>
              </>
            )
          }
          <div className={style.mergeModalTitle}>
            <span style={{ color: 'red', marginRight: '5px' }}>*</span>
            {t('生效时间')}
            <div>
              <Popover trigger="hover" position="top-left">
                {t('选择“00”，表示0点-1点的补货明细会合并园区（1点生成补货任务）')}
              </Popover>
              <Icon className={style.question} name="question" />
            </div>

          </div>
          <Form.Item label="" labelWidth="0">
            <Checkbox.Group
              name="effectTimes"
              // 补货类型=日常补货，可选择时间：根据参数配置情况进行展示对应的时间范围
              data={modalInfo.orderType === 2 ? effectTimeList.filter((e) => effectTimeConfigVals.includes(e.value)) : effectTimeList}
              renderItem="label"
              format="value"
              keygen="label"
            />
          </Form.Item>
          {
            isAppointment && (
              <Form.Item label={t('预约时间')} required labelWidth="100">
                <DatePicker
                  inputable
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  width={220}
                  name="reserveEffectTime"
                  rules={[rules.required, rules.reservationTimeRange()]}
                  disabled={(value) => {
                    const currentTime = moment().subtract(1, 'days');
                    const selectTime = moment(value, 'yyyy-MM-dd HH:mm');
                    // 选择时间不能小于当前时间
                    return selectTime.isBefore(currentTime);
                  }}
                />
              </Form.Item>
            )
          }
        </Form>
      </Modal>
    );
  }
}

ConfigModal.propTypes = {
  loading: PropTypes.number,
  modalInfoVisible: PropTypes.number,
  modalInfo: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  filteredRegionList: PropTypes.arrayOf(PropTypes.shape()),
  underParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  replenishTypeList: PropTypes.arrayOf(PropTypes.shape()),
  mergeStrategyList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  regionTypeList: PropTypes.arrayOf(PropTypes.shape()),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  distinctParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  effectTimeList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
  effectTimeConfigVals: PropTypes.arrayOf(PropTypes.number),
};

export default ConfigModal;
