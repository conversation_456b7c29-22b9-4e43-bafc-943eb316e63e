import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Button, Modal, Tag,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyle from '@src/component/style.less';
import { MODAL_VISIBLE_EDIT, MODAL_APPOINTMENT_EDIT } from '../merge.reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      store,
    } = this.props;

    const columns = [
      {
        title: t('ID'),
        width: 130,
        render: 'id',
      },
      {
        title: t('仓库'),
        width: 100,
        render: 'warehouseName',
      },
      {
        title: t('下架片区'),
        width: 120,
        render: 'downRegionName',
      },
      {
        title: t('下架园区'),
        width: 120,
        render: 'underParkTypeName',
      },
      {
        title: t('补货类型'),
        width: 120,
        render: 'orderTypeName',
      },
      {
        title: t('合并策略'),
        width: 120,
        render: 'mergeStrategyName',
      },
      {
        title: t('合并园区'),
        width: 260,
        render: (r) => r.mergeParkTypeNames?.map((name) => <Tag>{name}</Tag>),
      },
      {
        title: t('合并片区'),
        width: 260,
        render: (r) => r.mergeRegionNames?.map((name) => <Tag>{name}</Tag>),
      },
      {
        title: t('指向目的片区'),
        width: 120,
        render: 'distinctRegionName',
      },
      {
        title: t('指向目的园区'),
        width: 120,
        render: 'distinctParkTypeName',
      },
      {
        title: t('生效时间'),
        width: 180,
        render: (r) => (r.effectTimes?.sort((a, b) => a - b)?.map((time) => time).join(',')),
      },
      {
        title: t('状态'),
        width: 100,
        render: (d) => (d.status ? t('启用') : t('禁用')),
      },
      {
        title: t('创建人'),
        width: 180,
        render: 'createUserName',
      },
      {
        title: t('创建时间'),
        width: 180,
        render: 'createTime',
      },
      {
        title: t('更新人'),
        width: 180,
        render: 'updateUserName',
      },
      {
        title: t('更新时间'),
        width: 180,
        render: 'lastUpdateTime',
      },
      {
        title: t('操作'),
        width: 240,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              text
              type="primary"
              className={globalStyle.tableTextButton}
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.id,
              })}
            >
              {t('操作日志')}
            </Button>
            <Button
              text
              type="primary"
              className={globalStyle.tableTextButton}
              disabled={!loading}
              onClick={() => {
                store.openOrCloseModal({
                  type: MODAL_VISIBLE_EDIT,
                  record,
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              text
              type="primary"
              className={globalStyle.tableTextButton}
              disabled={!loading}
              onClick={() => {
                store.openOrCloseModal({
                  type: MODAL_APPOINTMENT_EDIT,
                  record,
                });
                store.changeData({ isAppointment: true });
              }}
            >
              {t('预约配置')}
            </Button>
            <Button
              text
              type="danger"
              className={globalStyle.tableTextButton}
              disabled={!loading}
              onClick={() => {
                Modal.confirm({
                  title: t('请确认是否删除该策略配置?'),
                  onOk: () => {
                    store.handleRecordDelete(record.id);
                  },
                  text: { ok: t('确认'), cancel: t('取消') },
                });
              }}
            >
              {t('删除')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={globalStyle.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            empty={t('暂无数据')}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              className: globalStyle.pagination,
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'REPLENISH_CHANGE_UPPER_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}
List.propTypes = {
  loading: PropTypes.number,
  store: PropTypes.shape(),
  list: PropTypes.arrayOf(PropTypes.shape()),
  upParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  regionList: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};
export default List;
