import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import { Spin, Tabs } from 'shineout';
import globalStyles from '@src/component/style.less';
import store from './reducers';
// import PlStrategyView from './3pl-strategy/view';
import MergeStrategyView from './merge-strategy/view';
import FrontWarehouseConfigView from './front-warehouse-config/view';
import ReplenishmentHistoryConfig from './replenishment-history-config/view';
import NoCountryLineConfig from './no-country-line-config/view';

class Container extends React.Component {
  render() {
    const {
      ready,
      tabKey,
    } = this.props;
    if (ready) {
      return (
        <Tabs
          shape="line"
          defaultActive={tabKey}
          onChange={(key) => {
            store.changeData({ tabKey: key });
          }}
          className={`${globalStyles.tabsSection} ${globalStyles.noBorder}`}
        >
          {/* <Tabs.Panel className={globalStyles.tabsPanel} tab={t('3PL补货配置')}>
            <PlStrategyView {...this.props} />
          </Tabs.Panel> */}
          <Tabs.Panel className={globalStyles.tabsPanel} tab={t('补货上架合并配置')}>
            <MergeStrategyView {...this.props} />
          </Tabs.Panel>
          <Tabs.Panel className={globalStyles.tabsPanel} tab={t('前置仓补货配置')}>
            <FrontWarehouseConfigView {...this.props} />
          </Tabs.Panel>
          <Tabs.Panel className={globalStyles.tabsPanel} tab={t('日常补货剔除历史销量配置')}>
            <ReplenishmentHistoryConfig {...this.props} />
          </Tabs.Panel>
          <Tabs.Panel className={globalStyles.tabsPanel} tab={t('无国家线补货配置')}>
            <NoCountryLineConfig {...this.props} />
          </Tabs.Panel>
        </Tabs>
      );
    }
    return (
      <div style={{ textAlign: 'center' }}>
        <Spin size={50} name="default" />
      </div>
    );
  }
}

Container.propTypes = {
  ready: PropTypes.bool.isRequired,
  tabKey: PropTypes.number.isRequired,
};

export default Container;
