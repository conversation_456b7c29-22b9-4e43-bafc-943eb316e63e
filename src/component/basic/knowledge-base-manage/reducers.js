import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { t } from '@shein-bbl/react';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { formdataPost } from '@src/server/common/fileFetch';
import {
  getListAPI, deleteItemAPI, addUrl, editUrl,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  startTime: '',
  endTime: '',
};

export const defaultAddEditObj = {
  businessType: '', // 业务域
  configNo: '', // 规则编码
  imageUrl: '',
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  statusList: [],
  deductionCostTypeList: [],
  serviceProviderList: [],
  subWarehouseList: [],
  secondCostList: [],
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  selectedRows: [],
  addEditObj: defaultAddEditObj,
  businessTypeList: [], // 业务域
  // 操作区域
  addEditModalVisible: 0,
  file: '',
  fileList: [],
  isEditPdf: false,
  localUrl: '',
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const selectData = yield dictSelect({ catCode: ['BUSINESS_BIG_TYPE'] });
    if (selectData.code === '0') {
      yield this.changeData({
        businessTypeList: selectData.info.data.find((item) => item.catCode === 'BUSINESS_BIG_TYPE').dictListRsps,
      });
    } else {
      Modal.error({ title: selectData.msg });
    }
  },

  /**
   * 搜索
   */
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Message.error(t('请先选择右上角仓库'));
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        selectedRows: [],
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  // 删除
  * handleDelete() {
    const { selectedRows } = yield '';
    markStatus('loading');
    const { code, msg } = yield deleteItemAPI({
      ids: selectedRows.map((e) => e.id),
    });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 新增编辑
  * addOrEdit() {
    const { warehouseId } = yield 'nav';
    const {
      addEditObj, addEditModalVisible, isEditPdf, file,
    } = yield '';
    if (!warehouseId) {
      Message.error(t('请先选择右上角仓库'));
      return;
    }
    if (!file) {
      Message.error(t('请先上传知识库'));
      return;
    }
    markStatus('loading');
    const {
      businessType,
      configNo,
      id,
    } = addEditObj;
    const isAdd = addEditModalVisible === 1;
    const formData = new FormData();
    // 如果是新增，或者编辑状态下修改pdf就传file了,否则不传file
    const fileParam = isAdd || (!isAdd && isEditPdf) ? file : null;
    formData.append('file', fileParam);
    formData.append('business_type', businessType);
    if (id) formData.append('id', id);
    if (isAdd) formData.append('config_no', configNo);
    if (isAdd) formData.append('warehouse_id', warehouseId);

    const api = isAdd ? addUrl : editUrl;
    const { code, msg } = yield formdataPost(api, formData);
    if (code === '0') {
      Message.success(isAdd ? t('新增成功') : t('编辑成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
      yield this.changeData({
        addEditModalVisible: 0,
        file: '',
      });
    } else {
      Modal.error({ title: msg });
    }
  },
};
