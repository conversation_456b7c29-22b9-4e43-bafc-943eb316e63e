import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('规则编码'),
        width: 150,
        render: 'configNo',
      },
      {
        title: t('业务域'),
        render: 'businessTypeName',
        width: 120,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 200,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 100,
        render: (record) => (
          <>
            <Button
              size="small"
              text
              type="primary"
              className={globalStyles.tableTextButton}
              onClick={() => {
                const imageUrl = record.fileUrl || '';
                store.changeData({
                  addEditModalVisible: 2,
                  addEditObj: {
                    ...record,
                  },
                  file: {
                    imageUrl,
                    name: record.fileName,
                  },
                  localUrl: '',
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              className={globalStyles.tableTextButton}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              className: globalStyles.pagination,
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'WGS_KNOWLEDGE_BASE_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
