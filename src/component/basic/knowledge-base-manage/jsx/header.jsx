import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <DateRangePicker
            required
            alwaysVisible
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['startTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('更新时间')}
            span={2}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
};
export default Header;
