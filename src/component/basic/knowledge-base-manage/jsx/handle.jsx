import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Input, Select, Rule, Popover,
} from 'shineout';
import UploadPlus from '@shein-components/upload_plus';
import { t } from '@shein-bbl/react';
import globalStyles from '@src/component/style.less';
import store, { defaultAddEditObj } from '../reducers';

export const uploadFileURL = `${process.env.WGS_FRONT}/upload/file_upload`;

function validateInput(value) {
  // 正则表达式，匹配英文字符、数字、“-”和“_”
  const regex = /^[a-zA-Z0-9-_]+$/;
  return regex.test(value);
}

const rules = new Rule({
  taxRange: {
    func: (val, _, callback) => {
      if (!validateInput(val)) {
        callback(new Error(t('不可输入特殊字符、中文字符')));
      }
      callback(true);
    },
  },
});

class Handle extends React.Component {
  render() {
    const {
      loading,
      addEditModalVisible,
      addEditObj,
      businessTypeList,
      selectedRows,
      file,
      localUrl,
    } = this.props;
    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          onClick={() => {
            store.changeData({
              addEditModalVisible: 1,
              addEditObj: {
                ...defaultAddEditObj,
              },
              file: '',
              localUrl: '',
            });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length === 0}
        >
          <Popover.Confirm
            onOk={() => {
              store.handleDelete();
            }}
            type="warning"
            okType="primary"
          >
            {t('是否确认删除？')}
          </Popover.Confirm>
          {t('删除')}
        </Button>
        <Modal
          visible={addEditModalVisible}
          width={500}
          destroy
          maskCloseAble={false}
          title={addEditModalVisible === 1 ? t('新增知识库配置') : t('编辑知识库配置')}
          onClose={() => store.changeData({ addEditModalVisible: 0 })}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ addEditModalVisible: 0 })}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading}>{t('保存')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={130}
            labelAlign="right"
            style={{ maxWidth: 500 }}
            onSubmit={() => {
              store.addOrEdit();
            }}
            onChange={(value) => {
              store.changeData({
                addEditObj: value,
              });
            }}
            value={addEditObj}
          >
            <div>
              <Form.Item required label={t('规则编码')}>
                <Input
                  name="configNo"
                  placeholder={t('请输入')}
                  style={{ width: 200 }}
                  maxLength={10}
                  disabled={addEditModalVisible === 2}
                  rules={[rules.required(t('请输入规则编码')), rules.taxRange()]}
                />
              </Form.Item>
              <Form.Item required label={t('业务域')}>
                <Select
                  name="businessType"
                  data={businessTypeList}
                  keygen="dictCode"
                  format="dictCode"
                  placeholder={t('请选择')}
                  renderItem="dictNameZh"
                  style={{ width: 200 }}
                  rules={[rules.required(t('请选择业务域'))]}
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase()
                    .indexOf(text.toLowerCase()) >= 0}
                  clearable
                />
              </Form.Item>
              <Form.Item required label={t('上传知识库')}>
                <UploadPlus
                  accept=".pdf"
                  autoUploadKeyName="file"
                  filePathKeyName="imageUrl"
                  fileList={file ? [file] : []}
                  onChange={(f) => {
                    const isEdit = addEditModalVisible === 2;
                    store.changeData({
                      file: f[0],
                      isEditPdf: isEdit,
                      localUrl: f[0] ? URL.createObjectURL(f[0]) : '',
                    });
                  }}
                  renderResult={(d) => (
                    <Button
                      size="small"
                      type="link"
                      onClick={() => file && window.open(file?.imageUrl || localUrl)}
                    >
                      {d.name}
                    </Button>
                  )}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  addEditObj: PropTypes.shape(),
  addEditModalVisible: PropTypes.bool,
  businessTypeList: PropTypes.arrayOf(PropTypes.shape()),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  file: PropTypes.shape(),
  localUrl: PropTypes.string,
};
export default Handle;
