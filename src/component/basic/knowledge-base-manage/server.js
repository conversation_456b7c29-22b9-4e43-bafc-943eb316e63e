import { sendPostRequest } from '@src/server/common/public';
//  查询列表
export const getListAPI = (param) => sendPostRequest({
  url: '/knowledge_base_config/query',
  param,
}, process.env.WGS_FRONT);

// 删除
export const deleteItemAPI = (param) => sendPostRequest({
  url: '/knowledge_base_config/delete',
  param,
}, process.env.WGS_FRONT);

// 新增
export const addUrl = `${process.env.WGS_FRONT}/knowledge_base_config/insert`;
// 编辑
export const editUrl = `${process.env.WGS_FRONT}/knowledge_base_config/update`;
