import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { t } from '@shein-bbl/react';
import { getSubWarehouseApi } from '@src/server/basic/sub-warehouse';
import { cloudMessagePublishSystemAPI, queryAlarmAPI } from './server';

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  warehouseList: [], // 仓库
  warehouseCheckedList: [],
  warehouseAllChecked: false,
  parkList: [], // 库区
  parkCheckedList: [],
  subWarehouseList: [], // 子仓
  subWarehouseCheckedList: [],
  subWarehouseAllChecked: false,
  recordModalVisiable: false,
  isEdit: false, // 编辑报警通知
  parkAllChecked: false,
  parkMapSubwarehouseObj: [],
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 页面初始化
  * init() {
    const { warehouseList } = yield 'nav';
    yield this.changeData({
      warehouseList,
    });
    yield this.getInitData();
  },
  * getInitData() {
    markStatus('loading');
    const { warehouseList } = yield 'nav';
    const { code, msg, info } = yield queryAlarmAPI();
    if (code === '0') {
      const { warehouseIdList, parkList, subWarehouseIdList } = info;
      // 回写
      yield this.changeData({
        warehouseCheckedList: warehouseIdList || [],
        warehouseAllChecked: (warehouseList || []).length === (warehouseIdList || []).length,
        parkCheckedList: parkList || [],
        subWarehouseCheckedList: subWarehouseIdList || [],
      });
      yield this.getParkList({
        warehouseIds: warehouseIdList || [],
      });
      yield this.getSubwarehouseList({
        parks: parkList || [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  * queryParkList(warehouseIdList) {
    let parkList = [];
    let parkMapSubwarehouse = {};
    if (warehouseIdList) {
      const { code, info, msg } = yield getSubWarehouseApi({ warehouseIdList });
      if (code === '0') {
        parkList = Object.values((info.data || []).reduce((result, curr) => {
          const { parkName, parkType } = curr;
          if (parkType == null) {
            return result;
          }
          if (result[parkType]) {
            result[parkType].subWarehouseList.push(curr);
          } else {
            result[parkType] = {
              parkType,
              parkName,
              subWarehouseList: [{ ...curr }],
            };
          }
          return result;
        }, {}));
        parkMapSubwarehouse = parkList.reduce((result, curr) => ({
          ...result,
          [curr.parkType]: curr.subWarehouseList,
        }), {});
      } else {
        Modal.error({ title: msg });
      }
    }
    return { parkList, parkMapSubwarehouse };
  },

  /**
   * 搜索
   */
  * submit() {
    const { subWarehouseCheckedList } = yield '';
    const params = {
      content: JSON.stringify({
        subWarehouseCheckedList: subWarehouseCheckedList || [],
      }),
      title: 'alarm-notice',
      subWarehouseIdList: subWarehouseCheckedList,
      module: 6,
      publishType: 'broadcast',
      messageType: 'message',
      store: false,
      businessType: 1,
    };
    markStatus('loading');
    const { code, msg } = yield cloudMessagePublishSystemAPI(params);
    if (code === '0') {
      Message.success(t('提交成功'));
      yield this.changeData({
        isEdit: false,
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  // 点击仓库获取园区 warehouseIds: 选中的仓库
  * getParkList({ warehouseIds }) {
    if (warehouseIds.length === 0) {
      yield this.changeData({
        parkList: [],
        parkCheckedList: [],
        parkAllChecked: false,
        subWarehouseCheckedList: [],
        subWarehouseAllChecked: false,
        subWarehouseList: [],
      });
      return;
    }
    markStatus('loading');
    const { parkCheckedList } = yield '';
    // 获取园区列表
    const { parkList, parkMapSubwarehouse } = yield this.queryParkList(warehouseIds);
    // 更新选中的园区
    const newParkCheckedList = parkCheckedList.filter((e) => parkList.some((listItem) => listItem.parkType === e));
    yield this.changeData({
      parkList,
      parkCheckedList: newParkCheckedList,
      parkMapSubwarehouseObj: parkMapSubwarehouse,
      parkAllChecked: newParkCheckedList.length === parkList.length,
    });
  },
  // 点击园区获取子仓 parks: 选中的园区
  * getSubwarehouseList({ parks }) {
    if (parks.length === 0) {
      yield this.changeData({
        subWarehouseCheckedList: [],
        subWarehouseAllChecked: false,
      });
    }
    markStatus('loading');
    const { parkMapSubwarehouseObj, subWarehouseCheckedList } = yield '';
    let newSubWarehouseList = [];
    parks.forEach((parkId) => {
      newSubWarehouseList = [
        ...(parkMapSubwarehouseObj[parkId] || []),
        ...newSubWarehouseList,
      ];
    });

    // 更新选中的子仓
    const newSubWarehouseCheckedList = subWarehouseCheckedList.filter((e) => newSubWarehouseList.some((listItem) => listItem.id === e));
    yield this.changeData({
      subWarehouseList: newSubWarehouseList,
      subWarehouseCheckedList: newSubWarehouseCheckedList,
      subWarehouseAllChecked: newSubWarehouseCheckedList.length === newSubWarehouseList.length,
    });
  },
};
