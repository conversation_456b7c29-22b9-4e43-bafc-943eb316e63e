import { i18n } from '@shein-bbl/react';
import React, { Component } from 'react';
import ContainerPage from '@public-component/search-queries/container';
import { TopAreaHeight } from '@src/lib/constants';
import store from './reducers';
import List from './jsx/list';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    return (
      <ContainerPage
        // 减去顶部
        customStyle={{
          height: `calc(100vh - ${TopAreaHeight}px)`,
          backgroundColor: '#fff',
        }}
      >
        <List {...this.props} />
      </ContainerPage>
    );
  }
}

export default i18n(Container);
