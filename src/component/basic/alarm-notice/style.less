/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.handleExportButton{
    margin-right: 6px;
}


/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect{
    width: 140px;
 }

 .headerUnmatchedText{
     color: #bbb;
 }

.headerSearchTimeLine{
    display: flex;
}

/* list.jsx
---------------------------------------------------------------- */
.listOperationButton{
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}
.listSection{
    height: 0px; /* 必写为了子domheight - 100%有效 */
    flex: 1;
}

  .inPermissionsBox{
    display: flex;
    width: 100%;;
  }

  .inPermissionsLeft{
    display: flex;
    width: 100%;
    justify-content: space-between;
    font-size: 14px;
  }

  .inPermissionsLeftBox{
    padding: 5px;
    max-height: 70vh;
    overflow: auto;
    overflow-x: hidden;
  }

  .inPermissionsLeftList{
    height: 70vh;
    border: 1px solid #eaeaea;
    width: 100%;
    margin-top: 9px;
  }

  .inPermissionsLeftTree{
    display: flex;
    justify-content: space-between;
  }

  .inPermissionsMiddle{
    flex: 4;
    position: relative;
  }

  .inPermissionsMiddleBox{
    position: absolute;
    top: 40vh;
    font-size: 14px;
    color: #666c7c;
    background-color: rgb(244, 245, 248);
    width: 80%; display: flex;
    justify-content: space-evenly;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .inPermissionsRight{
    display: flex;
    width: 100%;
    justify-content: space-between;
    font-size: 14px;
  }

  .inPermissionsRightBox{
    border: 1px solid #eaeaea;
    width: 100%;
    margin-top: 9px;
    display: flex;
  }

  .inPermissionsRightCheckbox{
    flex: 1;
    line-height: 24px;
    :global {
      .treeBox .so-checkinput-group.so-checkinput-block {
        max-height: calc(100vh - 200px);
      }
    }
  }

  .WarehouseTypesBox{
    height: 28px;
    background-color: #f4f5f8;
    padding-left: 10px;
  }

  .WarehouseTypesCheckBox{
    display: flex;
    justify-content: space-between;
    width: 100%;
    line-height: 16px;
  }

  .WarehouseTypesBtn{
    display: flex;
    justify-content: right;
    margin-top: 10px;
  }

  .rulesTable{
    height: 70vh !important;
  }

  .marginLeftSelect{
    margin-left: 10px;
  }

  .marginRightSelect {
    margin-right: 10px;
  }

  .primaryText {
    color: #197afa;
    cursor: pointer;
  }

  .hintText{
    font-size: 18px;
    margin-left: 5px;
    line-height: 32px;
    color: red;
  }

  .scrollBth{
    margin-top: 10px;
    color: blue;
    cursor: pointer
  }

  .listArea {
    padding: 12px;
  }
