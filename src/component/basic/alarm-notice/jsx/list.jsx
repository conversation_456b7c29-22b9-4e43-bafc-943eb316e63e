import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import React from 'react';
import {
  Checkbox, Button, Spin,
} from 'shineout';
import Icon from '@shein-components/Icon';
import OperationModal from '@public-component/modal/operation-modal';
import styles from '@src/component/style.less';
import store from '../reducers';
import style from '../style.less';

function List(props) {
  const {
    recordModalVisiable,
    warehouseAllChecked,
    warehouseList,
    warehouseCheckedList,
    parkAllChecked,
    parkList,
    parkCheckedList,
    subWarehouseAllChecked,
    subWarehouseList,
    subWarehouseCheckedList,
    isEdit,
    loading,
  } = props;
  return (
    <div className={[styles.commonBgColor, style.listArea].join(' ')}>
      <div className={style.inPermissionsBox}>
        <div style={{ flex: '2', marginRight: 8 }}>
          <div className={style.inPermissionsLeft}>
            <Button
              type="primary"
              disabled={isEdit}
              onClick={() => {
                store.changeData({
                  isEdit: true,
                });
              }}
            >
              {t('编辑报警通知')}
            </Button>
          </div>
        </div>
        <div style={{ flex: '25' }}>
          <div className={style.inPermissionsRight}>
            <span>
              {t('选择报警通知子仓')}
            </span>
            <span
              style={{ color: '#197AFA', cursor: 'pointer' }}
              onClick={() => {
                store.changeData({ recordModalVisiable: true });
              }}
            >
              {t('操作记录')}
            </span>
          </div>
          <Spin loading={!loading} size={20}>
            <div className={style.inPermissionsRightBox}>
              <div
                style={{
                  borderLeft: '1px solid #EAEAEA',
                }}
                className={style.inPermissionsRightCheckbox}
              >
                <div className={style.WarehouseTypesBox}>
                  {t('仓库名称')}
                </div>
                <div style={{ padding: '5px 10px 0 10px', fontSize: '14px' }} className="treeBox">
                  {warehouseList.length !== 0
                    && (
                    <Checkbox
                      disabled={!isEdit}
                      style={{ lineHeight: '16px' }}
                      value={warehouseAllChecked}
                      onChange={(e, checked) => {
                        store.changeData({
                          warehouseAllChecked: checked,
                          warehouseCheckedList: checked ? warehouseList.map((item) => (item.id)) : [],
                        }).then(() => {
                          store.getParkList({
                            warehouseIds: checked ? warehouseList.map((item) => (item.id)) : [],
                          });
                        });
                      }}
                    >
                      {t('全选')}
                    </Checkbox>
                    )}
                  <Checkbox.Group
                    disabled={!isEdit}
                    data={warehouseList}
                    keygen="id"
                    format="id"
                    renderItem={(item) => (
                      <div
                        style={{
                          color: warehouseCheckedList.some((v) => (v === item.id) && v !== -1) ? '#197AFA' : '',
                        }}
                        className={style.WarehouseTypesCheckBox}
                      >
                        <span>
                          {item.nameZh}
                        </span>
                        <span>
                          <Icon name="triangle-right" />
                        </span>
                      </div>
                    )}
                    onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    block
                    value={warehouseCheckedList}
                    onChange={(checkedList) => {
                      store.changeData({
                        warehouseCheckedList: checkedList,
                        warehouseAllChecked: checkedList.length === warehouseList.length,
                      }).then(() => {
                        store.getParkList({
                          warehouseIds: checkedList, checkAll: false,
                        });
                      });
                    }}
                  />
                </div>
              </div>
              <div
                style={{
                  borderLeft: '1px  solid #EAEAEA',
                }}
                className={style.inPermissionsRightCheckbox}
              >
                <div
                  className={style.WarehouseTypesBox}
                >
                  {t('园区名称')}
                </div>
                <div style={{ padding: '5px 10px 0 10px', fontSize: '14px' }} className="treeBox">
                  {parkList.length !== 0
                    && (
                    <Checkbox
                      disabled={!isEdit}
                      style={{ lineHeight: '16px' }}
                      value={parkAllChecked}
                      onChange={(e, checked) => {
                        store.changeData({
                          parkAllChecked: checked,
                          parkCheckedList: checked ? parkList.map((item) => (item.parkType)) : [],
                        }).then(() => {
                          store.getSubwarehouseList({
                            parks: checked ? parkList.map((item) => (item.parkType)) : [],
                          });
                        });
                      }}
                    >
                      {t('全选')}
                    </Checkbox>
                    )}
                  <Checkbox.Group
                    disabled={!isEdit}
                    data={parkList}
                    keygen="parkType"
                    format="parkType"
                    renderItem={(item) => (
                      <div
                        style={{
                          color: parkCheckedList.some((v) => (v === item.parkType) && v !== -1) ? '#197AFA' : '',
                        }}
                        className={style.WarehouseTypesCheckBox}
                      >
                        <span>
                          {item.parkName}
                        </span>
                        <span>
                          <Icon name="triangle-right" />
                        </span>
                      </div>
                    )}
                    onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    block
                    value={parkCheckedList}
                    onChange={(checkedList) => {
                      store.changeData({
                        parkCheckedList: checkedList,
                        parkAllChecked: checkedList.length === parkList.length,
                      }).then(() => {
                        store.getSubwarehouseList({
                          parks: checkedList,
                        });
                      });
                    }}
                  />
                </div>
              </div>
              <div
                style={{
                  borderLeft: '1px solid #EAEAEA',
                }}
                className={style.inPermissionsRightCheckbox}
              >
                <div
                  className={style.WarehouseTypesBox}
                >
                  {t('子仓名称')}
                </div>
                <div style={{ padding: '5px 10px 0 10px', fontSize: '14px' }} className="treeBox">
                  {subWarehouseList.length !== 0 && (
                  <Checkbox
                    disabled={!isEdit}
                    style={{ lineHeight: '16px' }}
                    value={subWarehouseAllChecked}
                    onChange={(e, checked) => {
                      store.changeData({
                        subWarehouseAllChecked: checked,
                        subWarehouseCheckedList: checked ? subWarehouseList.map((item) => item.id) : [],
                      });
                    }}
                  >
                    {t('全选')}
                  </Checkbox>
                  )}
                  <Checkbox.Group
                    disabled={!isEdit}
                    data={subWarehouseList}
                    keygen="id"
                    format="id"
                    renderItem={(item) => (
                      <div
                        style={{
                          color: subWarehouseCheckedList.some((v) => (v === item.id) && v !== -1) ? '#197AFA' : '',
                        }}
                        className={style.WarehouseTypesCheckBox}
                      >
                        <span>
                          {item.nameZh}
                        </span>
                      </div>
                    )}
                    onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    block
                    value={subWarehouseCheckedList}
                    onChange={(checkedList) => {
                      store.changeData({
                        subWarehouseCheckedList: checkedList,
                        subWarehouseAllChecked: checkedList.length === subWarehouseList.length,
                      });
                    }}
                  />
                </div>
              </div>
            </div>
          </Spin>
        </div>
      </div>
      {
        isEdit && (
          <div className={styles.commonBgColor}>
            <div className={style.WarehouseTypesBtn}>
              <Button
                onClick={() => {
                  store.changeData({
                    isEdit: false,
                  });
                }}
                disabled={!loading}
              >
                {t('取消')}
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  store.submit();
                }}
                disabled={!loading}
              >
                {t('提交')}
              </Button>
            </div>
          </div>
        )
      }
      {/* 操作记录 */}
      <OperationModal
        visible={recordModalVisiable}
        param={{
          operateId: 1,
          operateCode: 'WMS_PDA_ALARM_RECORD',
        }}
        onCancel={() => store.changeData({ recordModalVisiable: false })}
      />
    </div>
  );
}

List.propTypes = {
  recordModalVisiable: PropTypes.bool,
  warehouseAllChecked: PropTypes.oneOfType(PropTypes.number, PropTypes.bool),
  warehouseCheckedList: PropTypes.arrayOf(PropTypes.number),
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  parkAllChecked: PropTypes.oneOfType(PropTypes.number, PropTypes.bool),
  parkCheckedList: PropTypes.arrayOf(PropTypes.number),
  parkList: PropTypes.arrayOf(PropTypes.shape),
  subWarehouseAllChecked: PropTypes.oneOfType(PropTypes.number, PropTypes.bool),
  subWarehouseCheckedList: PropTypes.arrayOf(PropTypes.number),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape),
  isEdit: PropTypes.bool,
  loading: PropTypes.number,
};
export default List;
