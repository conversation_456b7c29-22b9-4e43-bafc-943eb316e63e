import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import moment from 'moment';
import fileSaver from 'file-saver';
import { Message, Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { formdataPost } from '@src/server/common/fileFetch';
import { STATISTICAL_DOWNLOAD, STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import {
  getListAPI, exportListAPI, downloadTemplateAPI, updateListAPI, goodsWhiteImportURL,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  skc: '',
  whiteStatus: 0, // 默认启用状态
  startTime: '', // 开始时间
  endTime: moment().format('YYYY-MM-DD'),
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [10, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  whiteStatusList: [
    {
      dictCode: 0,
      dictNameZh: t('启用'),
    },
    {
      dictCode: 1,
      dictNameZh: t('禁用'),
    },
  ],
  addOrEditModalVisible: false,
  modalType: 1, // 1 新增 2编辑
  addOrEditInfo: {},
  selectedRows: [],
  importModalVisible: false, // 导入弹出框是否显示 默认不显示
  file: '', // 导入文件对象
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改Modal里即addOrEditInfo属性值
  changeAddOrEditInfoData(state, data) {
    Object.assign(state.addOrEditInfo, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 搜索
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        selectedRows: [],
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 导出
  * exportData() {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { limit } = yield '';
    // 一般导出接口不用传页数页码，因为是要导出全部数据：部分旧页面有传就与旧页面一致；新页面跟后端统一按规范不用传页数页码
    // 实际开发时，根据情况决定是否加仓库id，页数页码等字段
    const param = { ...limit };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(param);
    if (code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: msg });
    }
  },
  // 上传文件
  * uploadFile(formData) {
    markStatus('loading');
    formData.append('function_node', '24');
    const res = yield formdataPost(goodsWhiteImportURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 下载模板
  * downloadTemplate() {
    try {
      const res = yield downloadTemplateAPI();
      const b = yield res.blob();
      const blob = new Blob([b], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('导入模板')}.xlsx`);
    } catch (e) {
      Modal.error({ title: e.reason.message });
    }
  },
  // 1 新增 2 编辑
  * updateList() {
    markStatus('loading');
    const { modalType, addOrEditInfo } = yield '';
    const tip = modalType === 1 ? t('新增成功') : t('更新成功');
    const { code, msg } = yield updateListAPI({ ...addOrEditInfo, optionType: modalType });
    if (code === '0') {
      Message.success(tip);
      yield this.changeData({
        addOrEditModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
};
