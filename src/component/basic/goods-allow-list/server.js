import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';

// 搜索
export const getListAPI = (param) => sendPostRequest({
  url: '/goods/white_query',
  param,
}, process.env.BASE_URI_WMD);

// 导出
export const exportListAPI = (param) => sendPostRequest({
  url: '/goods/white_export',
  param,
}, process.env.BASE_URI_WMD);

// 更新
export const updateListAPI = (param) => sendPostRequest({
  url: '/goods/white_update',
  param,
}, process.env.BASE_URI_WMD);

// 下载模板
export const downloadTemplateAPI = () => fileFetch(`${process.env.BASE_URI_WMD}/excel/export_template?type=goods_white_import.xls`, {
  method: 'GET',
  credentials: 'include',
});

// 导入接口
export const goodsWhiteImportURL = `${process.env.WGS_FRONT}/file_import/record/wmd/goods_white_import`;
