import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import Ellipsis from '@src/component/public-component/ellipsis';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('SKC'),
        render: 'skc',
        width: 100,
      },
      {
        title: t('SKU'),
        render: 'skuCode',
        width: 120,
      },
      {
        title: t('商品名称（中文）'),
        width: 160,
        render: (d) => <Ellipsis str={d.zhName} splitLen={120} />,
      },
      {
        title: t('商品名称（英文）'),
        width: 160,
        render: (d) => <Ellipsis str={d.enName} splitLen={120} />,
      },
      {
        title: t('状态'),
        render: 'whiteStatusName',
        width: 80,
      },
      {
        title: t('开始时间'),
        render: 'startTime',
        width: 120,
      },
      {
        title: t('结束时间'),
        render: 'endTime',
        width: 120,
      },
      {
        title: t('更新人'),
        render: 'updater',
        width: 100,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 100,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 80,
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'GOODS_WHITE_OPERATE',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
