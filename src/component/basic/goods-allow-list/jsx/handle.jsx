import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import {
  Button, DatePicker, Form, Input, Modal, Rule, Select,
} from 'shineout';
import FileDelayUpload from '@src/component/public-component/upload/fileDelayUpload';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store from '../reducers';

const rule = Rule({
  compare: {
    func: (val, formData, callback) => {
      if (formData.startTime && formData.endTime && moment(formData.endTime)
        .diff(moment(formData.startTime), 'days', true) < 0) {
        callback(new Error(t('结束时间不能小于开始时间')));
      }
      callback(true);
    },
  },
});

class Handle extends React.Component {
  render() {
    const {
      loading,
      modalType,
      addOrEditModalVisible,
      addOrEditInfo,
      selectedRows,
      whiteStatusList,
      importModalVisible, // 导入弹出框是否显示 默认不显示
      file, // 导入文件对象
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({ addOrEditModalVisible: true, modalType: 1, addOrEditInfo: { whiteStatus: 0 } });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            const curRowData = (selectedRows[0] || {});
            store.changeData({
              addOrEditModalVisible: true,
              modalType: 2,
              addOrEditInfo: {
                id: curRowData.id,
                skc: curRowData.skc,
                startTime: curRowData.startTime,
                endTime: curRowData.endTime,
                whiteStatus: curRowData.whiteStatus,
              },
            });
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            store.changeData({
              importModalVisible: true,
              file: '',
            });
          }}
        >
          {t('导入')}
        </Button>
        <Modal
          maskCloseAble={null}
          visible={importModalVisible}
          title={t('导入')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={!file}
              loading={!loading}
              onClick={() => {
                const formData = new FormData();
                formData.append('file', file);
                store.uploadFile(formData);
                return false;
              }}
            >
              {t('确认上传')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!loading}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
        </Modal>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => store.downloadTemplate()}
        >
          {t('下载导入模版')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.exportData();
          }}
        >
          {t('导出')}
        </Button>
        <Modal
          destroy
          maskCloseAble={null}
          visible={addOrEditModalVisible}
          width={400}
          title={modalType === 1 ? t('新增') : t('编辑')}
          onClose={() => {
            store.changeData({ addOrEditModalVisible: false });
          }}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ addOrEditModalVisible: false })}>{t('取消')}</Button>
              <Modal.Submit
                disabled={!addOrEditInfo.skc || !addOrEditInfo.startTime || !addOrEditInfo.endTime || addOrEditInfo.whiteStatus === '' || addOrEditInfo.whiteStatus === undefined}
              >
                {t('确认')}
              </Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 400 }}
            onSubmit={() => {
              store.updateList();
            }}
            onChange={(value) => {
              store.changeData({
                addOrEditInfo: value,
              });
            }}
            value={addOrEditInfo}
            inline
          >
            <Form.Item required label={t('SKC')}>
              <Input
                label={t('SKC')}
                name="skc"
                disabled={modalType === 2}
                style={{ width: '160px' }}
                placeholder={t('请输入')}
                rules={[rule.required]}
                onChange={(val) => store.changeAddOrEditInfoData({
                  skc: val,
                })}
              />
            </Form.Item>
            <Form.Item required label={t('开始时间')}>
              <DatePicker
                disabled={modalType === 2}
                style={{ width: '160px' }}
                label={t('开始时间')}
                type="date"
                name="startTime"
                placeholder={t('开始时间')}
                rules={[rule.required, rule.compare()]}
                onChange={(val) => store.changeAddOrEditInfoData({
                  startTime: val,
                })}
              />
            </Form.Item>
            <Form.Item required label={t('结束时间')}>
              <DatePicker
                disabled={modalType === 2}
                style={{ width: '160px' }}
                label={t('结束时间')}
                type="date"
                name="endTime"
                placeholder={t('结束时间')}
                rules={[rule.required, rule.compare()]}
                onChange={(val) => store.changeAddOrEditInfoData({
                  endTime: val,
                })}
              />
            </Form.Item>
            <Form.Item required label={t('状态')}>
              <Select
                style={{ width: '160px' }}
                label={t('状态')}
                name="whiteStatus"
                data={whiteStatusList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                rules={[rule.required]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                onChange={(val) => store.changeAddOrEditInfoData({
                  whiteStatus: val,
                })}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalType: PropTypes.number,
  addOrEditModalVisible: PropTypes.bool,
  addOrEditInfo: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  whiteStatusList: PropTypes.arrayOf(PropTypes.shape()),
  importModalVisible: PropTypes.bool,
  file: PropTypes.shape(),
};
export default Handle;
