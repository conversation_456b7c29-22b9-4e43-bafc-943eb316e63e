import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import {
  Input, Select, Rule, DatePicker,
} from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

const rule = Rule({
  validEndTime: {
    func: (val, formData, callback) => {
      // 如果没有选择skc则结束时间必填
      if (!formData.skc && !formData.endTime) {
        callback(new Error(t('如果没有输入skc则结束时间必填')));
      }
      callback(true);
    },
  },
  timeRange: {
    func: (val, formData, callback) => {
      // 限制可选时间段最大不超过1个月
      // if (formData.startTime && formData.endTime && moment(formData.endTime)
      //   .diff(moment(formData.startTime), 'month', true) > 1) {
      //   callback(new Error(t('开始时间到结束时间不能大于{}个月', 1)));
      // }
      // 结束时间不能小于开始时间
      if (formData.startTime && formData.endTime && moment(formData.endTime)
        .diff(moment(formData.startTime), 'days', true) < 0) {
        callback(new Error(t('结束时间不能小于开始时间')));
      }
      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      whiteStatusList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('结束时间')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Input label={t('SKC')} name="skc" placeholder={t('请输入')} />
          <Select
            label={t('状态')}
            name="whiteStatus"
            data={whiteStatusList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <DatePicker
            label={t('开始时间')}
            type="date"
            name="startTime"
            placeholder={t('开始时间')}
            rules={[rule.timeRange()]}
          />
          <DatePicker
            label={t('结束时间')}
            type="date"
            name="endTime"
            placeholder={t('结束时间')}
            rules={[rule.timeRange(), rule.validEndTime()]}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  whiteStatusList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
