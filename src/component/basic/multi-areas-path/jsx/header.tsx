// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import React from 'react';
import { t } from '@shein-bbl/react';
import {
  Input, Select, Rule, Form,
} from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import DictionarySelect from '@src/component/public-component/dictionary-select';
import { ISubwarehouseItem } from '@src/typing/base';
import type { FormRef } from 'shineout/lib/Form/Props';
import moment from 'moment';
import InputMore from '@shein-components/inputMore';
import DateRangePicker from '@shein-components/dateRangePicker2';
import store, { defaultLimit } from '../reducers';
import { ILimitType, IPageProps } from '../types';
import style from '../style.less';

const rules = Rule({
  timeRange: {
    func: (_, formData, callback) => {
      // 时间校验
      // 缺少子仓
      if ((!formData.areaPath && !formData.areaIdList?.length && !formData.startRoadway && !formData.endRoadway && !formData.locationListStr && !formData.pathStatus)
        && (!formData.endLastUpdateTime && !formData.startLastUpdateTime)) {
        callback(new Error(t('更新时间必填')));
      }
      if (moment(formData.endLastUpdateTime)
        .diff(moment(formData.startLastUpdateTime), 'days', true) > 7) {
        callback(new Error(t('开始时间和结束时间不能超过{}天', 7)));
      }
      callback(true);
    },
  },
  numberFormat: {
    func: (val, _, callback) => {
      if (val && !/^(\d|\w)+,(\d|\w)+$/.test(val)) {
        callback(new Error(t('输入格式为库区,库区，用英文逗号","隔开，例如：31,32 或 11A,12A 或 A,B')));
      }
      callback(true);
    },
  },
  // 巷道校验
  roadWay: {
    func: (val, formData, callback) => {
      if (formData.startRoadway && (Number.isNaN(Number(formData.startRoadway)) || (!Number.isNaN(Number(formData.startRoadway)) && (Number(formData.startRoadway) < 1)))) {
        store.changeLimitData({
          startRoadway: '',
        });
        callback(new Error(t('请输入01～999的数字')));
      }
      if (formData.endRoadway && (Number.isNaN(Number(formData.endRoadway)) || (!Number.isNaN(Number(formData.endRoadway)) && (Number(formData.endRoadway) < 1)))) {
        store.changeLimitData({
          endRoadway: '',
        });
        callback(new Error(t('请输入01～999的数字')));
      }

      // 巷道校验，开始巷道和结束巷道必须同时存在
      if ((formData.startRoadway && !formData.endRoadway)) {
        callback(new Error(t('结束巷道必填')));
      }
      if ((!formData.startRoadway && formData.endRoadway)) {
        callback(new Error(t('开始巷道必填')));
      }

      // 巷道校验，开始巷道和结束巷道可以单独存在，当二者均有值时，开始巷道不能大于结束巷道
      if (formData.startRoadway && formData.endRoadway && Number(formData.startRoadway) > Number(formData.endRoadway)) {
        callback(new Error(t('开始巷道不能大于结束巷道')));
      }

      callback(true);
    },
  },
});

function Header(props: IPageProps) {
  const {
    loading,
    limit,
    subWarehouseList,
    headerFormAreaList,
  } = props;

  return (
    <section>
      {/* 高级搜索 */}
      <SearchAreaContainer
        value={limit}
        labelStyle={{ width: 90 }}
        searching={!loading}
        collapseOnSearch={false}
        clearUndefined={false}
        onSearch={() => {
          store.handlePaginationChange({ pageNum: 1 });
        }}
        onChange={(val: ILimitType) => {
          // 业务需求隐藏表单就设置默认值
          store.changeLimitData(formatSearchData(defaultLimit, val));
        }}
        onClear={() => store.clearLimitData()}
        formRef={(f: FormRef<ILimitType>) => {
          store.changeData({
            formRef: f,
          });
        }}
      >
        {/* <DictionarySelect name="dictcodeTest" label={t('字典值测试')} code="NORM_IN_STORAGE_TYPE" type="wis" width={200} rules={[rules.required]} /> */}
        <Select<ISubwarehouseItem, number[]>
          label={t('子仓')}
          name="subWarehouseIdList"
          data={subWarehouseList}
          keygen="id"
          format="id"
          renderItem="nameZh"
          onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          multiple
          compressed
          clearable
          placeholder={t('全部')}
          onChange={(val) => {
            store.changeData({
              headerFormAreaList: [],
              limit: {
                ...limit,
                subWarehouseIdList: val,
              },
            });
            store.getHeaderFormAreaList(val);
          }}
          required
          rules={[rules.required(t('子仓必填'))]}
        />
        <Input label={t('合并库区方向')} name="areaPath" placeholder={t('全部')} rules={[rules.numberFormat()]} bind={['endLastUpdateTime']} />
        <Select
          label={t('库区')}
          name="areaIdList"
          data={headerFormAreaList}
          keygen="id"
          format="id"
          renderItem="area"
          onFilter={(text) => (d) => d.area?.toLowerCase().indexOf(text?.toLowerCase()) >= 0}
          multiple
          compressed
          clearable
          placeholder={t('全部')}
          bind={['endLastUpdateTime']}
        />
        <Form.Field
          label={t('巷道')}
          name={['startRoadway', 'endRoadway']}
          rules={[rules.roadWay()]}
          required
          bind={['endLastUpdateTime']}
        >
          {({ value, onChange }) => (
            <div style={{ display: 'flex' }}>
              <Input.Group>
                <Input
                  allowNull
                  hideArrow
                  maxLength={3}
                  value={value[0]}
                  onChange={(val) => onChange([val, value[1]])}
                />
                <span className={style.roadwWay}>~</span>
                <Input
                  allowNull
                  hideArrow
                  maxLength={3}
                  value={value[1]}
                  onChange={(val) => onChange([value[0], val])}
                />
              </Input.Group>
            </div>
          )}
        </Form.Field>
        <InputMore
          label={t('库位')}
          title={t('添加多个库位,以回车键隔开')}
          placeholder={t('请输入')}
          modalplaceholder={t('支持输入多个合并库区方向')}
          text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
          name="locationListStr"
          max={200}
          maskCloseAble={false}
          overDisabled
          clearable
          strTransFunc={[(str: string) => str.split(/[\r\n]|(\s+)/g).filter(Boolean).map((v) => v.trim()).filter((v) => Boolean(v))
            .join(','), (str: string) => str.replace(/,/g, '\r\n')]}
          renderNum={(str) => str.split(/[\r\n]|(\s+)/g).filter(Boolean).map((v) => v.trim()).filter((v) => Boolean(v)).length}
          bind={['endLastUpdateTime']}
        />
        <DictionarySelect
          name="pathStatus"
          label={t('状态')}
          code="MULTI_AREA_PATH_STATUS"
          type="wmd"
          width={200}
          bind={['endLastUpdateTime']}
        />

        <DateRangePicker
          placeholder={[t('开始时间'), t('结束时间')]}
          type="datetime"
          inputable
          format="yyyy-MM-dd HH:mm:ss"
          name={['startLastUpdateTime', 'endLastUpdateTime']}
          defaultTime={['00:00:00', '23:59:59']}
          label={t('更新时间')}
          span={2}
          clearable
          required={false}
          rules={[rules.timeRange()]}
          bind={['endLastUpdateTime']}
        />
      </SearchAreaContainer>
    </section>
  );
}

export default Header;
