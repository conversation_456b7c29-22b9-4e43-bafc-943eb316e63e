import React from 'react';
import {
  Button, Modal,
} from 'shineout';
import { t } from '@shein-bbl/react';
import style from '@src/component/style.less';
import fileSaver from 'file-saver';
import FileDelayUpload from '@src/component/public-component/upload/fileDelayUpload';
import { downloadTemplate } from '../server';
import { IPageProps } from '../types';
import store from '../reducers';

function Handle(props: IPageProps) {
  const {
    loading,
    importModalVisible, // 导入弹出框是否显示 默认不显示
    file, // 导入文件对象
  } = props;

  return (
    <section className={[style.handle, 'handleSection'].join(' ')}>
      <Button
        type="primary"
        onClick={() => {
          store.changeData({
            importModalVisible: true,
            file: '',
          });
        }}
      >
        {t('导入')}
      </Button>
      <Modal
        maskCloseAble={null}
        visible={importModalVisible}
        title={t('导入')}
        onClose={() => {
          store.changeData({
            importModalVisible: false,
          });
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              store.changeData({
                importModalVisible: false,
              });
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            type="primary"
            disabled={!file}
            loading={!loading}
            onClick={() => {
              const formData = new FormData();
              formData.append('file', file);
              store.uploadFile(formData);
              return false;
            }}
          >
            {t('确认上传')}
          </Button>,
        ]}
      >
        <div style={{ padding: '20px 0', display: 'flex' }}>
          <span style={{ display: 'inline-block', marginRight: 20 }}>
            {t('导入文件')}
            :
          </span>
          <FileDelayUpload
            value={file}
            loading={!loading}
            onChange={(f) => {
              store.changeData({
                file: f,
              });
            }}
            accept=".xls,.xlsx"
          />
        </div>
        <div style={{ padding: '20px 0', display: 'flex' }}>
          <span style={{ display: 'inline-block', marginRight: 20 }}>
            {t('下载模版')}
            :
          </span>
          <Button
            text
            type="primary"
            loading={!loading}
            onClick={() => downloadTemplate().then((d) => d?.blob()).then((b) => {
              const blob = new Blob([b], { type: 'application/octet-stream' });
              fileSaver.saveAs(blob, `${t('模版下载')}.xlsx`);
            })}
          >
            {t('下载导入模版')}
          </Button>
        </div>
      </Modal>
      <Button
        type="primary"
        onClick={() => {
          store.exportData();
        }}
      >
        {t('导出')}
      </Button>
    </section>
  );
}

export default Handle;
