import React from 'react';
import { t } from '@shein-bbl/react';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import style from '@src/component/style.less';
import {
  Table, Button,
} from 'shineout';
import type { ColumnItem } from 'shineout/lib/Table/Props';
import store from '../reducers';
import { IDataItem, IPageProps } from '../types';

function List(props: IPageProps) {
  const {
    loading,
    list,
    pageInfo,
    selectedGroups,
    operationModalVisible,
    recordId,
  } = props;

  const columns: ColumnItem<IDataItem>[] = [
    {
      title: t('子仓编码'),
      render: 'subWarehouseCode',
      width: 160,
    },
    {
      title: t('子仓'),
      render: 'subWarehouseName',
      width: 160,
    },
    {
      title: t('合并库区方向'),
      render: 'areaPath',
      width: 160,
    },
    {
      title: t('物理库区'),
      render: 'physicalAreaId',
      width: 120,
    },
    {
      title: t('巷道'),
      render: 'roadway',
      width: 120,
    },
    {
      title: t('库位'),
      render: 'location',
      width: 160,
    },
    {
      title: t('状态'),
      render: 'pathStatusName',
      width: 100,
    },
    {
      title: t('拣货顺序号'),
      render: 'pickOrder',
      width: 120,
    },
    {
      title: t('更新时间'),
      render: 'lastUpdateTime',
      width: 180,
    },
    {
      title: t('操作'),
      width: 100,
      render: (record) => (
        <div>
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ operationModalVisible: true, recordId: record.id })}
          >
            {t('操作日志')}
          </Button>
        </div>
      ),
    },
  ];

  const handelRowSelect = (val: IDataItem[]) => {
    store.changeData({
      // ids: val.map((i) => i.id),
      selectedGroups: val,
    });
  };

  return (
    <section className={style.tableSection}>
      <SearchAreaTable>
        <Table<IDataItem, IDataItem[]>
          style={{ height: '100%' }}
          rowClassName={() => style.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={list}
          columns={columns}
          value={selectedGroups}
          onRowSelect={handelRowSelect}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            className: style.pagination,
            layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
      </SearchAreaTable>
      <OperationModal
        visible={operationModalVisible}
        param={{
          operateId: recordId,
          operateCode: 'MULTI_AREA_PATH',
        }}
        onCancel={() => store.changeData({ operationModalVisible: false })}
      />
    </section>
  );
}

export default List;
