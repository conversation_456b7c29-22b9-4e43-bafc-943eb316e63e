import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { formdataPost } from '@src/server/common/fileFetch';
import { getSize } from '@src/middlewares/pagesize';
import { getWarehouseId } from '@src/lib/dealFunc';
import { paramTrim, clearEmpty } from '@src/lib/deal-func';
import { INavStateType, IWarehouseChange } from '@src/typing/base';
import moment from 'moment';
import { selectArea } from '@src/server/basic/area';
import { STATISTICAL_DOWNLOAD, STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import { IGetListAPIResponse, ILimitType, IStateType } from './types';
import { getListAPI, importURL, exportAPI } from './server';

export const defaultLimit: ILimitType = {
  /** 库区 */
  areaIdList: [],
  /** 货位 */
  locationList: [],
  locationListStr: '',
  /** 合并库区方向 */
  areaPath: '',
  subWarehouseIdList: [],
  /** 所属仓库 */
  warehouseId: '',
  /** 更新时间开始 */
  startLastUpdateTime: moment().subtract(6, 'days').format('YYYY-MM-DD 00:00:00'),
  /** 更新时间结束 */
  endLastUpdateTime: moment().format('YYYY-MM-DD 23:59:59'),
  startRoadway: '',
  endRoadway: '',
  pathStatus: '',
};

const defaultState: IStateType = {
  subWarehouseList: [], // list界面子仓数据
  warehouseIds: '', // 保留右上角仓库，由于init时判断是否要重置子仓等
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  formRef: { current: '' }, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(1),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100, 1000], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordList: [],
  headerFormAreaList: [], // 库区
  selectedGroups: [], // 选中的表格列
  exportVisible: true,
  parkList: [],
  preSubWarehouseList: [],
  importModalVisible: false, // 导入弹出框是否显示 默认不显示
  file: '', // 导入文件对象
};

export default {
  state: defaultState,
  $init: () => defaultState, // 可选使用，在页面初始化的时候会重置state
  /**
   * 改变state的值
   */
  changeData(state: Partial<IStateType>, data?: Partial<IStateType>) {
    Object.assign(state, data);
  },
  /**
   * 改搜索条件limit属性值
   */
  changeLimitData(state: Partial<ILimitType>, data?: Partial<ILimitType>) {
    // !!! LCD对于action方法有改动state实际为IState，为保证程序正常强制as一下
    const stateData = state as IStateType;
    Object.assign(stateData, {
      limit: {
        ...stateData.limit,
        ...data,
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef }: IStateType = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 校验
  * handleFormValidate() {
    const { formRef }: IStateType = this.state;
    let validateFlag = false;
    if (formRef) {
      yield formRef.validate().then(() => {
        validateFlag = true;
      }).catch(() => {
        validateFlag = false;
      });
    }
    return validateFlag;
  },
  /**
   * 右上角更换仓库触发
   * @param {*} action
   */
  * changeSubWarehouseList(action: IWarehouseChange) {
    const { subWarehouseList } = action;
    yield this.changeData({
      subWarehouseList,
    });
  },
  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(arg = {}) {
    const validateFlag: boolean = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  /**
   * 初始化数据
   */
  * init() {
    const { currentWarehouseList, warehouseId, permissionSubWarehouseList }: INavStateType = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
    });
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    yield this.changeData({
      subWarehouseList: permissionSubWarehouseList,
      warehouseIds: warehouseId,
    });
    yield this.getHeaderFormAreaList(undefined);
  },
  // 获取库区
  * getHeaderFormAreaList(subWarehouseIds?: number[]) {
    // if (!subWarehouseIds || subWarehouseIds.length === 0) {
    //   yield this.changeData({
    //     headerFormAreaList: [],
    //   });
    //   return;
    // }
    const { limit } = this.state;
    const { code, info, msg } = yield selectArea({
      subWarehouseIdList: !subWarehouseIds || subWarehouseIds.length === 0 ? undefined : subWarehouseIds,
      enabled: 1,
    });
    if (code === '0') {
      yield this.changeData({
        headerFormAreaList: info.data || [],
      });
      yield this.changeLimitData({
        areaIdList: limit.areaIdList?.filter((id) => info.data?.find((i: { id: string }) => i.id === id)),
      });
      return;
    }
    Modal.error({ title: msg });
    yield this.changeData({
      headerFormAreaList: [],
    });
  },
  /**
   * 查询表格数据
   */
  * search() {
    const warehouseId = getWarehouseId();
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    const {
      pageInfo: {
        pageNum,
        pageSize,
      },
      limit,
      warehouseIds,
    }: IStateType = yield '';
    markStatus('loading');

    const res: IGetListAPIResponse = yield getListAPI(clearEmpty(paramTrim({
      ...limit,
      pageNum,
      pageSize,
      locationList: limit.locationListStr ? limit.locationListStr.split(',') : [],
      warehouseId: warehouseIds,
      locationListStr: undefined,
    })));
    if (res.code === '0') {
      yield this.changeData({
        list: res.info?.data,
        selectedGroups: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: res.info?.meta?.count ?? 0,
        },
      });
    } else {
      Modal.error({
        title: res.msg,
      });
    }
  },
  // 导出
  * exportData() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    const { limit, pageInfo: { pageNum, pageSize }, warehouseIds } = yield '';
    markStatus('loading');
    const res: IGetListAPIResponse = yield exportAPI(clearEmpty(paramTrim({
      ...limit,
      pageNum,
      pageSize,
      locationList: limit.locationListStr ? limit.locationListStr.split(',') : [],
      warehouseId: warehouseIds,
      locationListStr: undefined,
    })));
    if (res.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * uploadFile(formData: FormData) {
    markStatus('loading');
    formData.append('function_node', '181');
    const res: IGetListAPIResponse = yield formdataPost(importURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 右上角仓库事件派发
  * warehouseChange(data: IWarehouseChange) {
    const { subWarehouseList, warehouseId } = data;
    yield this.changeData({
      subWarehouseList,
      warehouseIds: warehouseId,
    });
  },
};
