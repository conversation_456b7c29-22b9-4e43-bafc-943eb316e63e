import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';
import { camel2Under } from '@src/lib/camal-case-convertor';
import { IGetListAPIRequest, IGetListAPIResponse } from './types';

/**
 * 搜索接口
 */
export const getListAPI = (param: IGetListAPIRequest): Promise<IGetListAPIResponse> => sendPostRequest({
  url: '/multi_area_path/query_list',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 导出接口
 * soapi链接: https://soapi.sheincorp.cn/application/1552/routes/post_wmd_front_multi_area_path_export/doc
 */
export const exportAPI = (param: IGetListAPIRequest): Promise<IGetListAPIResponse> => sendPostRequest({
  url: '/multi_area_path/export',
  param,
}, process.env.BASE_URI_WMD);

// 模板下载
export const downloadTemplate = () => {
  const uri = `${process.env.BASE_URI_WMD}/multi_area_path/download_template`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under({})),
  });
};

// 导入接口
export const importURL = `${process.env.WGS_FRONT}/file_import/record/wmd/multi_area_path_import`;
