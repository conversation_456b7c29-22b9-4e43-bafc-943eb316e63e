import {
  IFetchResponse, IPageInfo, ISubwarehouseItem, IViewBaseProps, IWarehouseItem,
} from '@src/typing/base';
import { FormRef } from 'shineout/lib/Form/Props';

export interface IGetListAPIRequest {
  /** 库区 */
  areaIdList?:string;
  /** 更新时间结束 */
  endLastUpdateTime?:string;
  /** 货位 */
  locationList?:string[];
  /** 当前页码 */
  pageNum?:number;
  /** 当前页码 */
  pageSize?:number;
  /** 合并库区方向 */
  areaPath?:string;
  /** 更新时间开始 */
  startLastUpdateTime?:string;
  subWarehouseIdList?:number[];
  /** 所属仓库 */
  warehouseId?:number;
  locationListStr?: string;
}

interface IGroupTaskPriorityListItem {
  /** 优先级 */
  priority:string;
  /** 任务子类ID */
  taskId:number;
  /** 子类任务名称 */
  taskName:string;
}

export interface IDataItem {
  /** 所属库区ID */
  area?:string;
  /** 所属库区ID */
  areaId?:number;
  /** 创建时间 */
  createTime?:string;
  /** id */
  id?:number;
  /** 更新时间 */
  lastUpdateTime?:string;
  /** 货位 */
  location?:string;
  /** 合并库区方向 */
  areaPath?:string;
  /** 拣货顺序编号 */
  pickOrder?:string;
  /** 所属子仓ID */
  subWarehouseId?:number;
  /** 所属子仓 */
  subWarehouseName?:string;
  /** 所属仓库 */
  warehouseId?:number;
  /** 所属仓库 */
  warehouseName?:string;
  subWarehouseCode?: string;
  /** 巷道 */
  roadway?: string;
  /** 状态 */
  pathStatusName?: string;
  /** 物理库区 */
  physicalAreaId?: string;
}

type IData = IDataItem[];

interface IMeta {
  /** 数据记录数 */
  count?:number;
  /** 用户自定义扩展 */
  customObj?:unknown;
}

interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export interface ILimitType {
  /** 库区 */
  areaIdList?: string[];
  /** 更新时间结束 */
  endLastUpdateTime?:string;
  /** 货位 */
  locationList?:string[];
  /** 合并库区方向 */
  areaPath?:string;
  /** 更新时间开始 */
  startLastUpdateTime?:string;
  subWarehouseIdList?:number[];
  /** 所属仓库 */
  warehouseId?:string;
  locationListStr?: string;
  /** 巷道 */
  startRoadway?:number | string | null;
  endRoadway?:number | string | null;
  pathStatus?:string;
}

export interface IStateType {
  loading: number; // 0 loading, 1 load success, 2 load fail
  limit: ILimitType;
  ready: boolean;
  subWarehouseList: ISubwarehouseItem[];
  currentWarehouseList: IWarehouseItem[];
  list: IDataItem[];
  showPriorityList: IGroupTaskPriorityListItem[];
  importModalVisible: boolean;
  operationModalVisible: boolean;
  selectedGroups: IDataItem[];
  pageInfo: IPageInfo;
  formRef?: FormRef<ILimitType & { current: string }>;
  recordId?: number;
  recordVisible?: boolean;
  file?: string;
  warehouseIds?: string | number;
  headerFormAreaList?: {id: number; area: string}[];
}

export type IPageProps = IViewBaseProps<IStateType>;
