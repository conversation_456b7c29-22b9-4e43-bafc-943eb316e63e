import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Popover } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      selectedRows,
      pageInfo,
      operationModalVisible,
      recordId,
    } = this.props;

    const columns = [
      {
        title: t('优先级编码'),
        width: 140,
        render: (d) => (
          <Button
            type="primary"
            text
            onClick={() => store.changeData({
              editObjVisible: 2,
              editObj: {
                id: d.id,
                priorityCode: d.priorityCode,
                priority: d.priority,
                enabled: d.enabled,
                remark: d.remark,
                lackMin: d.lackMin,
                lackMax: d.lackMax,
                assigned: d.assigned,
                taskScenes: d.taskScenes,
                nationalLineType: d.nationalLineType,
                replenished: d.replenished,
              },
            })}
          >
            {d.priorityCode}
          </Button>
        ),
      },
      {
        title: t('任务场景'),
        width: 140,
        render: 'taskScenesName',
      },
      {
        title: t('缺货件数区间'),
        width: 140,
        render: (d) => (`${d.lackMin} ~ ${d.lackMax}`),
      },
      {
        title: t('自动下发'),
        width: 140,
        render: 'assignedName',
      },
      {
        title: t('取消补货'),
        width: 140,
        render: 'replenishedName',
      },
      {
        title: t('优先级顺序'),
        width: 140,
        render: 'priority',
      },
      {
        title: t('状态'),
        width: 80,
        render: 'enabledName',
      },
      {
        title: t('备注'),
        width: 140,
        render: (d) => (
          <Button
            style={{ padding: 0 }}
            text
          >
            <Popover style={{ padding: '4px 8px' }}>{d.remark}</Popover>
            {d.remark.length > 13 ? `${d.remark.substring(0, 13)}...` : d.remark}
          </Button>
        ),
      },
      {
        title: t('操作'),
        width: 100,
        fixed: 'right',
        render: (record) => (
          <Button
            text
            type="primary"
            className={globalStyles.tableTextButton}
            onClick={() => {
              store.changeData({
                operationModalVisible: true,
                recordId: record.id,
              });
            }}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(value) => {
              store.changeData({ selectedRows: value });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              className: globalStyles.pagination,
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        <OperationModal
          visible={operationModalVisible}
          title={t('操作日志')}
          param={{
            operateId: recordId,
            operateCode: 'REPLENISH_GOODS_TYPE',
          }}
          onCancel={() => store.changeData({
            operationModalVisible: false,
          })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape),
  selectedRows: PropTypes.arrayOf(),
  pageInfo: PropTypes.shape(),
  operationModalVisible: PropTypes.bool,
  recordId: PropTypes.number,
};

export default List;
