import { sendPostRequest } from '../../../server/common/public';

// 获取列表数据
export const getListAPI = (param) => sendPostRequest({
  url: '/replenishment_priority_config/query',
  param,
}, process.env.WWS_URI);

// 启用/禁用补货优先级配置
export const editPriorityAPI = (param) => sendPostRequest({
  url: '/replenishment_priority_config/edit',
  param,
}, process.env.WWS_URI);

// 添加/编辑补货优先级配置
export const editConfigAPI = (param) => sendPostRequest({
  url: '/replenishment_priority_config/add',
  param,
}, process.env.WWS_URI);
