import { t } from '@shein-bbl/react';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import {
  Table, Select, Input, Button, Modal,
} from 'shineout';
import styles from '../../../../style.less';
import store from '../reducers';
import headerStyles from '../index.less';

class Header extends Component {
  render() {
    const {
      catCode,
      catName,
      addCatVisible,
      checkCatVisible,
      catCheckData,
      dataLoading,
      catAddData,
      loading,
      catList,
      pageInfo,
      pageInfoDict,
      catId,
      hasSearched,
    } = this.props;
    const columns = [
      {
        title: t('序号'),
        width: '20%',
        render: 'id',
      },
      {
        title: t('是否翻译'),
        render: (row) => (
          row.isTranslate ? t('是') : t('否')
        ),
        width: '15%',
      },
      {
        title: t('类型编码'),
        render: 'catCode',
        width: '40%',
      },
      {
        title: t('类型名称'),
        width: '40%',
        render: (record) => (
          <Button
            text
            type="primary"
            onClick={(e) => {
              e.stopPropagation();
              store.changeData({
                checkCatVisible: true,
                catCheckData: {
                  id: record.id,
                  catName: record.catName,
                  isTranslate: record.isTranslate ? 1 : 0,
                },
              });
            }}
          >
            {record.catName}
          </Button>
        ),
      }];
    return (
      <div style={{
        display: 'flex', flexDirection: 'column', height: '100%',
      }}
      >
        <form
          onSubmit={(e) => {
            store.handlePaginationChange({ pageNum: 1 });
            e.preventDefault();
          }}
        >
          <div className={styles.header_wrap} style={{ backgroundColor: '#fff' }}>
            <div className={styles.inner}>
              <span className={styles.lab}>
                {t('类型编码')}
                :
              </span>
              <Input
                className={styles.headerSelect}
                type="text"
                value={catCode}
                delay={0}
                onChange={(val) => {
                  store.changeData({
                    catCode: val,
                  });
                }}
              />
            </div>
            <div className={styles.inner}>
              <span className={styles.lab}>
                {t('类型名称')}
                :
              </span>
              <Input
                className={styles.headerSelect}
                type="text"
                value={catName}
                delay={0}
                onChange={(val) => {
                  store.changeData({
                    catName: val,
                  });
                }}
              />
            </div>
            <div className={styles.inner}>
              <span className={styles.lab}>
                {t('是否翻译')}
                :
              </span>
              <Select
                keygen="id"
                format="id"
                renderItem="name"
                data-bind="isTranslate"
                style={{ width: 100 }}
                data={[{
                  id: 1,
                  name: t('是'),
                }, {
                  id: 0,
                  name: t('否'),
                }]}
              />
            </div>
            <div className={styles.inner}>
              <div className={styles.buttonSearchItem}>
                <Button
                  type="primary"
                  icon="search"
                  loading={dataLoading}
                  htmlType="submit"
                >
                  {t('搜索')}
                </Button>
              </div>
              <div className={styles.buttonNormalItem}>
                <Button
                  type="primary"
                  icon="reload"
                  onClick={() => {
                    store.changeData({
                      catCode: '',
                      catName: '',
                      isTranslate: '',
                    });
                  }}
                >
                  {t('重置')}
                </Button>
              </div>
              <div className={styles.buttonNormalItem}>
                <Button
                  type="primary"
                  icon="plus"
                  // loading={dataLoading}
                  onClick={() => {
                    store.changeData({
                      addCatVisible: true,
                    });
                  }}
                >
                  {t('新增')}
                </Button>
              </div>
            </div>
          </div>
        </form>
        <Table
          empty={hasSearched && !catList?.length ? t('无数据') : t('请点击搜索查询')}
          loading={!loading}
          columns={columns}
          data={catList}
          keygen="id"
          verticalAlign="middle"
          rowClassName={(row) => (row.id === catId ? headerStyles.selectedRow : undefined)}
          onRowClick={(record) => {
            if (record.id === catId) {
              return;
            }
            store.changeData({
              catId: record.id,
              searchCode: record.catCode,
              pageInfoDict: {
                ...pageInfoDict,
                pageNum: 1, // 页码
              },
              curCatName: record.catName,
            }).then(() => {
              store.doSearchDict({
                catCode: record.catCode,
              });
            });
          }}
          pagination={{
            style: {
              marginTop: 10,
            },
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
        <Modal
          maskClosable={false}
          title={t('新增字典分类')}
          visible={addCatVisible}
          onClick={() => {
            store.addCat(catAddData);
          }}
          onClose={() => {
            store.errorSet();
          }}
          footer={[
            <Button
              key="back"
              onClick={() => {
                store.errorSet();
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              key="submit"
              type="primary"
              disabled={catAddData.catCode === '' || catAddData.catName === '' || catAddData.isTranslate === '' || !loading}
              onClick={() => {
                store.addCat(assign({}, catAddData, {
                  catCode: catAddData.catCode,
                  catName: catAddData.catName,
                }));
              }}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={styles.buttonItem}>
            <span className={styles.lab}>
              {t('类型编码')}
              :
            </span>
            <Input
              type="text"
              value={catAddData.catCode}
              style={{ width: 200 }}
              placeholder={t('必填')}
              onChange={(val) => {
                store.changeData({
                  catAddData: assign({}, catAddData, { catCode: val }),
                });
              }}
            />
            <span className={styles.redStar}>*</span>
          </div>
          <div className={styles.buttonItem}>
            <span className={styles.lab}>
              {t('类型名称')}
              :
            </span>
            <Input
              type="text"
              value={catAddData.catName}
              style={{ width: 200 }}
              placeholder={t('必填')}
              onChange={(val) => {
                store.changeData({
                  catAddData: assign({}, catAddData, { catName: val }),
                });
              }}
            />
            <span className={styles.redStar}>*</span>
          </div>
          <div className={styles.buttonItem}>
            <span className={styles.lab}>
              {t('是否翻译')}
              :
            </span>
            <Select
              keygen="id"
              format="id"
              renderItem="name"
              placeholder={t('必填')}
              style={{ width: 200 }}
              value={catAddData.isTranslate}
              data={[{
                id: 1,
                name: t('是'),
              }, {
                id: 0,
                name: t('否'),
              }]}
              onChange={(val) => {
                store.changeData({
                  catAddData: assign({}, catAddData, { isTranslate: val }),
                });
              }}
            />
            <span className={styles.redStar}>*</span>
          </div>
        </Modal>
        <Modal
          maskClosable={false}
          title={t('修改字典分类')}
          visible={checkCatVisible}
          onClick={() => {
            store.checkCat(catCheckData);
          }}
          onClose={() => {
            store.errorSet();
          }}
          footer={[
            <Button
              key="back"
              onClick={() => {
                store.errorSet();
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              key="submit"
              type="primary"
              disabled={catCheckData.catName === '' || catCheckData.isTranslate === '' || !loading}
              onClick={() => {
                store.checkCat(assign({}, catCheckData, {
                  catName: catCheckData.catName,
                }));
              }}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={styles.buttonItem}>
            <span className={styles.lab}>
              {t('类型名称')}
              :
            </span>
            <Input
              type="text"
              value={catCheckData.catName}
              style={{ width: 200 }}
              placeholder={t('必填')}
              onChange={(val) => {
                store.changeData({
                  catCheckData: assign({}, catCheckData, { catName: val }),
                });
              }}
            />
            <span className={styles.redStar}>*</span>
          </div>
          <div className={styles.buttonItem}>
            <span className={styles.lab}>
              {t('是否翻译')}
              :
            </span>
            <Select
              keygen="id"
              format="id"
              renderItem="name"
              data-bind="catCheckData.isTranslate"
              style={{ width: 200 }}
              data={[{
                id: 1,
                name: t('是'),
              }, {
                id: 0,
                name: t('否'),
              }]}
            />
          </div>
        </Modal>
      </div>
    );
  }
}

Header.propTypes = {
  catCode: PropTypes.string,
  catName: PropTypes.string,
  isTranslate: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  dataLoading: PropTypes.bool,
  checkCatVisible: PropTypes.bool,
  addCatVisible: PropTypes.bool,
  catAddData: PropTypes.shape(),
  catCheckData: PropTypes.shape(),
  catList: PropTypes.arrayOf(PropTypes.shape()),
  pageNum: PropTypes.number,
  loading: PropTypes.number,
  pageInfo: PropTypes.shape(),
  pageInfoDict: PropTypes.shape(),
  catId: PropTypes.string,
  hasSearched: PropTypes.bool,
};

export default Header;
