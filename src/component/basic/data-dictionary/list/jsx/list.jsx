import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import {
  Table, Input, Button, Modal, Popover,
} from 'shineout';
import RuleInput from '@shein-components/WmsInput';
import Icon from '@shein-components/Icon';
import styles from '../../../../style.less';
import store from '../reducers';

function List(props) {
  const {
    dictCheckData,
    dictList,
    addDictVisible,
    checkDictVisible,
    dictAddData,
    catId,
    searchCode,
    selectedRows,
    pageInfoDict,
    loading,
    curCatName,
  } = props;
  const nameList = [];
  selectedRows.forEach((item) => {
    dictList.map((listItem) => {
      if (item === listItem.id) {
        nameList.push(`${listItem.dictNameZh} `);
      }
      return nameList;
    });
    return nameList;
  });
  const deleteButtonEnabled = selectedRows?.length > 0;

  const columns = [
    {
      title: t('代码值'),
      render: 'dictCode',
      width: '20%',
    },
    {
      title: t('编码内容(中文)'),
      key: 'dictNameZh',
      width: '40%',
      render: (record) => (
        <div
          style={{
            color: 'blue',
            cursor: 'pointer',
          }}
          onClick={() => {
            store.changeData({
              checkDictVisible: true,
            });
            store.changeData({
              dictCheckData: {
                id: record.id,
                dictNameZh: record.dictNameZh,
                dictNameEn: record.dictNameEn,
              },
            });
          }}
        >
          {record.dictNameZh}
        </div>
      ),
    },
    {
      title: t('编码内容(英文)'),
      key: 'dictNameEn',
      width: '40%',
      render: (record) => (
        <div
          style={{
            color: 'blue',
            cursor: 'pointer',
          }}
          onClick={() => {
            store.changeData({
              checkDictVisible: true,
            });
            store.changeData({
              dictCheckData: {
                id: record.id,
                dictNameZh: record.dictNameZh,
                dictNameEn: record.dictNameEn,
              },
            });
          }}
        >
          {record.dictNameEn}
        </div>
      ),
    }];

  if ([null, undefined, ''].includes(catId)) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
      }}
      >
        <Icon name="pc-resul-empty-multic" fontSize={100} />
        <span style={{ color: '#ccc', marginTop: 10, fontSize: 12 }}>
          {t('请点击选择左侧字典类型')}
        </span>
      </div>
    );
  }

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
    }}
    >
      <div style={{
        paddingTop: 15,
        paddingLeft: 15,
        fontWeight: 500,
      }}
      >
        {t('当前配置类型{}', '：')}
        {curCatName}
        (
        {searchCode}
        )
      </div>
      <div className={styles.headerWrap} style={{ backgroundColor: '#fff' }}>
        <div className={styles.buttonNormalItem}>
          <Button
            type="primary"
            icon="plus"
            disabled={!catId}
            onClick={() => {
              store.changeData({
                addDictVisible: true,
              });
            }}
          >
            {t('新增')}
          </Button>
        </div>
        <div className={styles.inner}>
          <div className={styles.buttonNormalItem}>
            <Button
              type="danger"
              disabled={!deleteButtonEnabled}
            >
              {deleteButtonEnabled && (
                <Popover.Confirm
                  onOk={() => {
                    store.doDelete();
                    store.changeData({
                      isVisible: false,
                    });
                  }}
                  type="warning"
                  okType="primary"
                >
                  {t('是否确定删除{}?', nameList.join(','))}
                </Popover.Confirm>
              )}
              {t('删除')}
            </Button>
          </div>
        </div>
      </div>
      <Table
        loading={!loading}
        columns={columns}
        data={dictList}
        keygen="id"
        value={selectedRows}
        onRowSelect={(rows) => {
          store.changeData({
            selectedRows: rows,
            isVisible: false,
          });
        }}
        pagination={{
          style: { marginTop: 10 },
          align: 'right',
          current: pageInfoDict.pageNum,
          pageSize: pageInfoDict.pageSize,
          layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
          onChange: (page, size) => {
            store.handlePaginationChangeDict({
              searchCode,
              pageNum: page,
              pageSize: size,
            });
          },
          pageSizeList: pageInfoDict.pageSizeList,
          total: pageInfoDict.count,
        }}
      />
      <Modal
        maskClosable={false}
        title={t('新增字典明细')}
        visible={addDictVisible}
        onClose={() => {
          store.errorSet();
        }}
        footer={[
          <Button
            key="back"
            onClick={() => {
              store.errorSet();
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            key="submit"
            type="primary"
            disabled={dictAddData.dictNameZh === '' || dictAddData.dictCode === '' || !loading}
            onClick={() => {
              store.addDict({
                data: assign({}, dictAddData, {
                  catId,
                  dictNameZh: dictAddData.dictNameZh,
                  dictNameEn: dictAddData.dictNameEn,
                  dictCode: dictAddData.dictCode,
                }),
                searchCode,
              });
            }}
          >
            {t('确认')}
          </Button>,
        ]}
      >
        <div className={styles.buttonItem}>
          <span className={styles.labLarger}>
            {t('代码值')}
            :
          </span>
          <RuleInput.Number
            type="text"
            value={dictAddData.dictCode}
            min={0}
            style={{ width: 200 }}
            placeholder={t('必填，数字')}
            onChange={(value) => {
              store.changeData({
                dictAddData: assign({}, dictAddData, { dictCode: value }),
              });
            }}
          />
          <span className={styles.redStar}>*</span>
        </div>
        <div className={styles.buttonItem}>
          <span className={styles.labLarger}>
            {t('编码内容(中文)')}
            :
          </span>
          <Input
            type="text"
            value={dictAddData.dictNameZh}
            style={{ width: 200 }}
            placeholder={t('必填')}
            onChange={(val) => {
              store.changeData({
                dictAddData: assign({}, dictAddData, { dictNameZh: val }),
              });
            }}
            maxLength={searchCode === 'COMBINE_ADDRESS_REMARK' ? 100 : 100}
          />
          <span className={styles.redStar}>*</span>
        </div>
        <div className={styles.buttonItem}>
          <span className={styles.labLarger}>
            {t('编码内容(英文)')}
            :
          </span>
          <Input
            type="text"
            value={dictAddData.dictNameEn}
            style={{ width: 200 }}
            placeholder={t('非必填')}
            onChange={(val) => {
              store.changeData({
                dictAddData: assign({}, dictAddData, { dictNameEn: val }),
              });
            }}
            maxLength={searchCode === 'COMBINE_ADDRESS_REMARK' ? 100 : 100}
          />
        </div>
      </Modal>
      <Modal
        maskClosable={false}
        title={t('修改字典明细')}
        visible={checkDictVisible}
        onClose={() => {
          store.errorSet();
        }}
        footer={[
          <Button
            key="back" onClick={() => {
              store.errorSet();
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            key="submit"
            type="primary"
            disabled={dictCheckData.dictNameZh === ''}
            onClick={() => {
              store.checkDict({
                data: assign({}, dictCheckData, {
                  catId,
                  dictCode: dictCheckData.dictCode,
                  dictNameZh: dictCheckData.dictNameZh,
                  dictNameEn: dictCheckData.dictNameEn,
                }),
                searchCode,
              });
            }}
          >
            {t('确认')}
          </Button>,
        ]}
      >
        <div className={styles.buttonItem}>
          <span className={styles.labLarger}>
            {t('编码内容(中文)')}
            :
          </span>
          <Input
            type="text"
            value={dictCheckData.dictNameZh}
            style={{ width: 200 }}
            placeholder={t('必填')}
            onChange={(val) => {
              store.changeData({
                dictCheckData: assign({}, dictCheckData, { dictNameZh: val }),
              });
            }}
            maxLength={searchCode === 'COMBINE_ADDRESS_REMARK' ? 100 : 100}
          />
          <span className={styles.redStar}>*</span>
        </div>
        <div className={styles.buttonItem}>
          <span className={styles.labLarger}>
            {t('编码内容(英文)')}
            :
          </span>
          <Input
            type="text"
            value={dictCheckData.dictNameEn}
            style={{ width: 200 }}
            placeholder={t('非必填')}
            onChange={(val) => {
              store.changeData({
                dictCheckData: assign({}, dictCheckData, { dictNameEn: val }),
              });
            }}
            maxLength={searchCode === 'COMBINE_ADDRESS_REMARK' ? 100 : 100}
          />
        </div>
      </Modal>
    </div>
  );
}

List.propTypes = {
  addDictVisible: PropTypes.bool,
  dictList: PropTypes.arrayOf(PropTypes.shape()),
  checkDictVisible: PropTypes.bool,
  dictAddData: PropTypes.shape(),
  dictCheckData: PropTypes.shape(),
  catId: PropTypes.number,
  searchCode: PropTypes.string,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  pageInfoDict: PropTypes.shape(),
  loading: PropTypes.number,
};

export default List;
