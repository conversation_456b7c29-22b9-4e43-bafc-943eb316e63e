import { sendPostRequest } from '@src/server/common/public';

// 增加字典明细
export const addDict = (param) => sendPostRequest({
  url: '/dict/add',
  param,
}, process.env.BASE_URI_WMD);

// 查询字典分类
// export const queryDictCat = (param) => sendPostRequest({
//   url: '/dictcat/query',
//   param,
// }, process.env.BASE_URI_WMD);

// 增加字典分类
export const addDictCat = (param) => sendPostRequest({
  url: '/dictcat/add',
  param,
}, process.env.BASE_URI_WMD);

//  编辑字典分类
export const editDictCat = (param) => sendPostRequest({
  url: '/dictcat/edit',
  param,
}, process.env.BASE_URI_WMD);

// 查询字典明细
export const queryDict = (param) => sendPostRequest({
  url: '/dict/query',
  param,
}, process.env.BASE_URI_WMD);

// 编辑字典明细
export const editDict = (param) => sendPostRequest({
  url: '/dict/edit',
  param,
}, process.env.BASE_URI_WMD);

// 删除字典明细
export const delDict = (param) => sendPostRequest({
  url: '/dict/del',
  param,
}, process.env.BASE_URI_WMD);
