import assign from 'object-assign';
import showMessage from '@src/lib/modal';
import { dictCatQuery } from '@src/server/basic/dictionary';
import { t } from '@shein-bbl/react';
import style from '@src/component/style.less';
import { markStatus } from 'rrc-loader-helper';
import {
  addDict, addDictCat,
  delDict, editDict, editDictCat,
  queryDict,
} from './server';
import { getSize } from '../../../../middlewares/pagesize';

const defaultState = {
  ready: false,
  addCatVisible: false,
  addDictVisible: false,
  checkDictVisible: false,
  checkCatVisible: false,
  buttonEnabled: true,
  catName: '',
  catCode: '',
  isTranslate: '',
  searchCode: '',
  catAddData: {
    catName: '',
    catCode: '',
    isTranslate: '',
  },
  dictCheckData: {
    dictNameZh: '',
    dictNameEn: '',
    id: '',
    catId: '',
  },
  dictAddData: {
    dictNameZh: '',
    dictNameEn: '',
    dictCode: '',
    catId: '',
  },
  catCheckData: {
    id: '',
    catName: '',
    isTranslate: '',
  },
  catList: [],
  pageNum: 1,
  pageSize: getSize(),
  pageSizeDict: getSize(),
  pageNumDict: 1,
  pageSizeDd: getSize(),
  list: [],
  loading: 1,
  isVisible: false,
  addDdData: {
    name: '',
    code: '',
  },
  typedictList: {
    name: '',
    code: '',
  },
  dictList: [],
  oldList: [],
  headerHeight: 0,
  rightHeight: 0,
  selectedRows: [],
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  pageInfoDict: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
};
export default {
  state: defaultState,
  // 改state数据
  changeData: (draft, action) => {
    assign(draft, action);
  },
  // 改 周转箱状态查询 条件limit
  changeLimit: (draft, action) => {
    assign(draft.limit, action);
  },
  // 初始化
  $init: (draft) => {
    assign(draft);
  },
  // 数据字典
  * doSearch(action, ctx) {
    const {
      pageInfo,
      catName,
      catCode,
      isTranslate,
    } = this.state;
    markStatus('loading');
    const {
      code,
      info,
      msg,
    } = yield dictCatQuery({
      catName,
      catCode,
      isTranslate,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    });
    if (code === '0') {
      yield ctx.changeData({
        dictList: [],
        catList: info.data,
        searchCode: '',
        catId: null,
        selectedRows: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
        pageInfoDict: {
          ...this.state.pageInfoDict,
          pageNum: 1, // 页码
          count: 0, // 表格总条数
        },
      });
    } else {
      showMessage(msg, false);
    }
  },
  // 数据字典 分页条改变
  * handlePaginationChange(data = {}) {
    yield this.changeData({
      hasSearched: true,
      pageInfo: {
        ...this.state.pageInfo,
        ...data,
      },
    });
    yield this.doSearch();
  },
  // 数据字典【参数查询】
  * doSearchDict(action, ctx) {
    const { pageInfoDict } = this.state;
    const {
      code,
      info,
      msg,
    } = yield queryDict({
      ...action,
      pageNum: pageInfoDict.pageNum,
      pageSize: pageInfoDict.pageSize,
    });
    if (code === '0') {
      yield ctx.changeData({
        dictList: info.data,
        pageInfoDict: {
          ...this.state.pageInfoDict,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      showMessage(msg, false);
    }
  },
  // 数据字典【参数查询】分页条改变
  * handlePaginationChangeDict(data = {}) {
    yield this.changeData({
      pageInfoDict: {
        ...this.state.pageInfoDict,
        ...data,
      },
    });
    const { searchCode } = yield '';
    if (searchCode) {
      yield this.doSearchDict({ catCode: searchCode });
    }
  },
  * errorSet() {
    yield this.changeData({
      catAddData: {
        catName: '',
        catCode: '',
        isTranslate: '',
      },
      addCatVisible: false,
      catCheckData: {
        id: '',
        catName: '',
        catCode: '',
      },
      checkCatVisible: false,
      loading: 1,
      dataLoading: false,
      addDictVisible: false,
      checkDictVisible: false,
      dictCheckData: {
        dictNameZh: '',
        dictNameEn: '',
        id: '',
        catId: '',
      },
      dictAddData: {
        dictNameZh: '',
        dictNameEn: '',
        dictCode: '',
        catId: '',
      },
    });
  },
  * doDelete(_, ctx) {
    const { selectedRows } = yield '';
    const {
      code,
      msg,
    } = yield delDict({ ids: selectedRows.map((item) => item.id) });
    if (code === '0') {
      showMessage(t('删除字典值成功'), true);
      yield this.errorSet();
      const { searchCode } = yield '';
      yield ctx.doSearchDict({ catCode: searchCode });
    } else {
      showMessage(msg || t('后台数据出错'), false);
    }
  },
  * addDict(action, ctx) {
    markStatus('loading');
    const {
      code,
      msg,
    } = yield addDict(action.data);
    if (code === '0') {
      showMessage(t('增加明细成功'), true);
      yield this.errorSet();
      yield ctx.doSearchDict({
        catCode: action.searchCode,
      });
    } else {
      showMessage(msg || t('后台数据出错'), false);
    }
  },
  * addCat(action) {
    markStatus('loading');
    const {
      code,
      msg,
    } = yield addDictCat(action);
    if (code === '0') {
      showMessage(t('增加分类成功'), true);
      document.querySelector(`.${style.buttonSearchItem} button`)?.click();
      yield this.errorSet();
    } else {
      showMessage(msg || t('后台数据出错'), false);
    }
  },
  * checkDict(action, ctx) {
    const {
      code,
      msg,
    } = yield editDict(action.data);
    if (code === '0') {
      showMessage(t('修改明细成功'), true);
      yield this.errorSet();
      yield ctx.doSearchDict({
        catCode: action.searchCode,
      });
    } else {
      showMessage(msg || t('后台数据出错'), false);
    }
  },
  * checkCat(action) {
    markStatus('loading');
    const {
      code,
      msg,
    } = yield editDictCat(action);
    if (code === '0') {
      showMessage(t('修改分类成功'), true);
      document.querySelector(`.${style.buttonSearchItem} button`)?.click();
      yield this.errorSet();
    } else {
      showMessage(msg || t('后台数据出错'), false);
    }
  },
};
