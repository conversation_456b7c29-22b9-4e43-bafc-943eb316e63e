.searchItem {
    display: inline-block;
}

.buttonItem {
    display: inline-block;
}

.btn {
    margin-right: 10px;
}

.headerItemInput {
    width: 110px;
}

.rightDown {
    position: fixed;
    /*right: 0;*/
    bottom: 4px;
}

.page {
    width: 100%;
    position: relative;
}

.lab {
    margin-right: 5px;
    width: 100px;
    display: inline-flex;
    justify-content: flex-end;
}

.labWidth {
    width: 85px;
    display: inline-flex;
    justify-content: flex-end;
}

.tableCell {
    cursor: pointer;
}

.footer {
    position: absolute;
    bottom: 0;
    width: calc(50% - 105px);
}

.sign {
    margin-bottom: 10px;
    margin-right: 10px;
}

.signDict {
    margin-bottom: 10px;
    margin-right: 10px;
    width: 100px;
}

.searchCatItem {
    display: inline-block;
}

.lab {
    margin-right: 5px;
    width: 110px;
    display: inline-flex;
    justify-content: flex-end;
}

.containerBox {
    height: calc(100vh - 100px);
    display: flex;
    justify-content: space-between;
}

.containerBoxChild{
    width: 49%;
    height: 100%;
    background-color: #fff
}