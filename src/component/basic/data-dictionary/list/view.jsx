import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import Header from './jsx/header';
import List from './jsx/list';
import store from './reducers';
import style from './style.css';

class Container extends Component {
  componentDidMount() {
    store.$init();
  }

  render() {
    return (
      <div className={style.containerBox}>
        <div className={style.containerBoxChild}>
          <Header {...this.props} />
        </div>
        <div className={style.containerBoxChild}>
          <List {...this.props} />
        </div>
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  ready: PropTypes.bool,
};

export default i18n(Container);
