import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import { Input, Rule, Select } from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

const rules = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      if (!formData.containerCode && !formData.startTime) {
        callback(new Error(t('请输入容器号或操作时间进行搜索')));
      }
      if (moment(formData.endTime).diff(moment(formData.startTime), 'days') > 1) {
        callback(new Error(t('操作时间最多只能选择{}天', 1)));
      }
      callback(true);
    },
  },
});

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      containerTypeList,
      fntList,
    } = this.props;

    return (
      <section>
        {/* 高级搜索 */}
        <SearchAreaContainer
          value={limit}
          labelStyle={{ width: 90 }}
          collapseOnSearch={false}
          searching={!loading}
          clearUndefined={false}
          onSearch={() => {
            store.handlePaginationChange({ pageNum: 1 });
          }}
          onChange={(val) => {
            // 业务需求隐藏表单就设置默认值
            store.changeLimitData(formatSearchData(defaultLimit, val));
          }}
          onClear={() => store.clearLimitData()}
          onToggle={() => {}}
          formRef={(f) => {
            store.changeData({
              formRef: f,
            });
          }}
        >
          <Input label={t('容器号')} name="containerCode" placeholder={t('请输入')} />
          <Input label={t('TID')} name="tid" placeholder={t('请输入')} />
          <Select
            label={t('容器类型')}
            name="containerTypes"
            data={containerTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            clearable
            multiple
            compressed
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Input label={t('业务单号')} name="businessCode" placeholder={t('请输入')} />
          <Select
            label={t('功能名称')}
            name="functionCode"
            data={fntList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            clearable
            multiple
            compressed
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Input label={t('操作人')} name="userName" placeholder={t('请输入')} />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            rules={[rules.timeRange()]}
            format="yyyy-MM-dd HH:mm:ss"
            name={['startTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('操作时间')}
            span={2}
            clearable={Boolean(limit.containerCode)}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number.isRequired,
  limit: PropTypes.shape(),
  containerTypeList: PropTypes.arrayOf(PropTypes.shape()),
  fntList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
