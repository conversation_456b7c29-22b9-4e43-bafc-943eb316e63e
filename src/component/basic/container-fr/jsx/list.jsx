import React from 'react';
import PropTypes from 'prop-types';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { t } from '@shein-bbl/react';
import { handleTablePros } from '@src/lib/deal-func';
import { Table } from 'shineout';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
    } = this.props;
    const columns = [
      {
        title: t('TID'),
        render: 'tid',
        width: 150,
      },
      {
        title: t('容器类型'),
        render: 'containerTypeName',
        width: 150,
      },
      {
        title: t('容器号'),
        render: 'containerCode',
        width: 120,
      },
      {
        title: t('变动前所在子仓'),
        render: 'beforeSubWarehouseName',
        width: 150,
      },
      {
        title: t('变动后所在子仓'),
        render: 'afterSubWarehouseName',
        width: 150,
      },
      {
        title: t('变动前状态'),
        render: 'beforeUsableStatusName',
        width: 150,
      },
      {
        title: t('变动后状态'),
        render: 'afterUsableStatusName',
        width: 150,
      },
      {
        title: t('业务节点'),
        render: 'functionCodeName',
        width: 150,
      },
      {
        title: t('业务单号'),
        render: 'businessCode',
        width: 150,
      },
      {
        title: t('操作人'),
        render: 'userName',
        width: 150,
      },
      {
        title: t('操作时间'),
        render: 'createTime',
        width: 190,
      },
    ];

    return (
      <section className={[styles.tableSection, styles.listArea].join(' ')}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  pageInfo: PropTypes.shape().isRequired,
};

export default List;
