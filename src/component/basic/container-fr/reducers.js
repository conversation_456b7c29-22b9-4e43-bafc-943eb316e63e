import { markStatus } from 'rrc-loader-helper';
import moment from 'moment';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import { getListAPI } from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  containerCode: '', // 容器号
  tid: '', // TID
  containerTypes: [], // 容器类型
  businessCode: '', // 业务单号
  functionCode: [], // 功能名称
  userName: '', // 操作人
  startTime: moment().format('YYYY-MM-DD 00:00:00'), // 开始时间
  endTime: moment().format('YYYY-MM-DD 23:59:59'), // 结束时间
};

const defaultState = {
  formRef: {},
  loading: 1,
  limit: defaultLimit,
  list: [],
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  containerTypeList: [], // 容器类型下拉数据
  fntList: [], // 功能名称下拉数据
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },

  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },

  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },

  // 页面初始化
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['FR_FUNCTION_CODE', 'CONTAINER_TYPE'] }),
    ]);
    // 数据字典下拉数据
    if (selectData.code === '0') {
      yield this.changeData({
        fntList: selectData.info.data.find((item) => item.catCode === 'FR_FUNCTION_CODE').dictListRsps,
        containerTypeList: selectData.info.data.find((x) => x.catCode === 'CONTAINER_TYPE').dictListRsps,

      });
    } else {
      handleListMsg([selectData]);
    }
  },

  // 搜索
  * search() {
    const { limit, pageInfo } = yield '';
    const { warehouseId } = yield 'nav';
    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  // 校验
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  // 分页条改变
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

};
