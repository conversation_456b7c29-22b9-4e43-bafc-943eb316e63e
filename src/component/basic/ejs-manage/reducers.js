import { t } from '@shein-bbl/react';
// import { t } from '@shein-bbl/react';
// import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { transformToPdfUrl, textToBarcode } from '@src/lib/print-new/pdf';
import srcTplContainerCode from '@src/tpl/container-code.ejs';
import { PDFUrlSwitchSessionStorage } from '@src/lib/storage-new';
import { data as pageData } from './data';
import { authListAPI, getUserManagePageList } from './server';

const originList = pageData.map((item) => ({
  ...item,
  isPublic: item.key.includes('@src/tpl/') ? '1' : '0',
})).sort((a, b) => b.urls.length - a.urls.length);

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  isPublic: '',
  template: '',
  pageUrl: '',
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],

  isPublicList: [ // 属性列表
    { dictCode: '0', dictNameZh: t('私有模板') },
    { dictCode: '1', dictNameZh: t('公共模板') },
  ],

  // 操作区域
  previewVisible: false,
  previewInfo: {},
  allPageList: [],

  filterList: [],
  showTestModal: false,
  testModalInput: '',
  testModalList: [],
  testModalLoading: false,
  showGatherModal: false,
  testGatherInput: '',
  testGatherList: [],
  testGatherLoading: false,
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 页面初始化
  * init() {
    const { pageInfo } = yield '';
    const res = yield authListAPI({ isPage: true });
    const allPageList = [];
    const allChildrenFn = (list) => {
      if (list?.length) {
        list.forEach((v) => {
          allPageList.push({ rule: v.rule, title: v.title });
          if (v.children?.length && v.type === '1') {
            allChildrenFn(v.children);
          }
        });
      }
    };
    if (res.code === '0') {
      allChildrenFn(res.info.list);
    }

    const newList = originList.map((v) => {
      const pageName = [];
      v.urls.forEach((val) => {
        let index = allPageList.findIndex((item) => item.rule === val) === -1 ? allPageList.findIndex((item) => item.rule === val.slice(0, val.length - 5)) : allPageList.findIndex((item) => item.rule === val);
        if (index === -1) {
          index = allPageList.findIndex((item) => item.rule === `${val}/_ALL_`);
        }
        if (index !== -1) {
          pageName.push(allPageList[index].title);
        } else if (val.includes('/in-warehouse/black-code-search')) {
          pageName.push(t('黑码搜图'));
        } else {
          pageName.push(t('无匹配'));
        }
      });
      return {
        ...v,
        pageName,
      };
    });

    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        count: pageData.length,
      },
      list: newList,
      filterList: newList,
    });
  },
  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data) {
    const { pageInfo } = yield '';
    const { pageNum, pageSize } = data;

    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
      list: originList.slice((pageNum - 1) * pageSize, pageNum * pageSize),
    });
  },
  * handleSearch(state) {
    const { pageInfo, limit } = yield '';
    let list = [];

    switch (state) {
      case '0':
      case '1':
        list = originList.filter((item) => item.isPublic === state);
        break;
      case undefined:
      default:
        list = originList;
        break;
    }

    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        pageNum: 1,
        count: list.length,
      },
      list,
      limit: {
        ...limit,
        isPublic: state,
      },
    });
  },
  * changeDataState(data) {
    const { state } = data;
    const { previewInfo } = yield '';
    yield this.changeData({
      previewInfo: {
        ...previewInfo,
        state,
      },
    });
  },
  * filterList() {
    const { limit: { pageUrl, template, isPublic }, list } = yield '';
    const filterList = list.filter((v) => {
      let pageUrlFlag = true;
      let templateFlag2 = true;
      let isPublicFlag = true;
      if (pageUrl !== '') {
        pageUrlFlag = v.pageName.toString().includes(pageUrl);
      }
      if (template !== '') {
        templateFlag2 = v.name.includes(template);
      }
      if (isPublic) {
        isPublicFlag = v.isPublic === isPublic;
      }
      return pageUrlFlag && templateFlag2 && isPublicFlag;
    });
    yield this.changeData({
      filterList,
    });
  },
  // PDF打印验证
  * pdfPrint({ testModalInput }) {
    const testModalList = [];
    const defaultVal = PDFUrlSwitchSessionStorage.getItem();
    yield this.changeData({ testModalLoading: true });
    // ha打印
    try {
      PDFUrlSwitchSessionStorage.setItem('1');
      const printUrlHa = yield transformToPdfUrl(srcTplContainerCode({
        barcodeBaseImg: textToBarcode(testModalInput),
        code: testModalInput,
      }), 20, 70, {
        landscape: true,
        orientation: '',
      });
      testModalList.push(printUrlHa);
    } catch {
      testModalList.push('');
      Modal.error({
        title: t('ha-pdf打印失败'),
      });
    } finally {
      PDFUrlSwitchSessionStorage.setItem('0');
    }
    // 普通打印
    try {
      const printUrlNormal = yield transformToPdfUrl(srcTplContainerCode({
        barcodeBaseImg: textToBarcode(testModalInput),
        code: testModalInput,
      }), 20, 70, {
        landscape: true,
        orientation: '',
      });
      testModalList.push(printUrlNormal);
    } catch {
      testModalList.push('');
      Modal.error({
        title: t('普通pdf打印失败'),
      });
    } finally {
      PDFUrlSwitchSessionStorage.setItem(defaultVal);
    }
    yield this.changeData({ testModalList, testModalLoading: false });
  },
  // 告警信息汇总
  * handleGather({ testGatherInput }) {
    let obj = null;
    try {
      obj = JSON.parse(testGatherInput);
    } catch (error) {
      console.log(error);
    }
    if (!obj?.info?.logs?.length) {
      Modal.error({
        title: t('请输入正确的日志JSON格式'),
      });
      return;
    }
    // 汇总异常人相关信息
    const logsList = [];
    /* eslint-disable @shein-bbl/bbl/translate-i18n-byT */
    const pattern = /^异常人：.+ $/gm;
    obj?.info?.logs.forEach((item) => {
      pattern.lastIndex = 0; // 重置正则表达式的 lastIndex 属性为 0
      const match = pattern.exec(item?.message);
      if (!match) {
        return;
      }
      // 去重并加上数量
      const index = logsList.findIndex((v) => v.name === match[0]);
      if (index !== -1) {
        logsList[index].count += 1;
      } else {
        logsList.push({
          name: match[0].slice(4, -1),
          count: 1,
        });
      }
    });

    // 获取异常人对应的仓库园区信息
    const nameList = [...new Set(logsList.map((v) => v.name))];
    const { warehouseId } = yield 'nav';
    if (!(nameList.join(',').length)) {
      Modal.error({
        title: t('当前无异常人信息'),
      });
      return;
    }

    const param = {
      uid: nameList.join(','),
      useStatus: 0,
      nameType: 0,
      pageNum: 1,
      pageSize: 100,
      warehouseId,
    };
    const { code, info, msg } = yield getUserManagePageList(param);
    // 汇总仓库和子仓数据
    const subWarehouseList = [];
    if (code === '0' && info?.list?.length) {
      logsList.forEach((item) => {
        const indexObj = info?.list?.find((v) => v.uid === item.name);
        if (indexObj) {
          const index = subWarehouseList.findIndex((v) => v.warehouseName === indexObj.warehouseName && v.subWarehouse === indexObj.subWarehouse);
          if (index !== -1) {
            subWarehouseList[index].userCount += 1;
            subWarehouseList[index].logCount += item.count;
            subWarehouseList[index].nameList.push(item.name);
          } else {
            subWarehouseList.push({
              warehouseName: indexObj.warehouseName,
              subWarehouse: indexObj.subWarehouse,
              userCount: 1,
              nameList: [item.name],
              logCount: item.count,
            });
          }
        }
      });
    } else {
      Modal.error({
        title: info?.list?.length ? msg || t('后台数据出错') : t('异常人账号查不到数据'),
        autoFocusButton: 'ok',
      });
    }
    // 按用户数和日志数排序
    subWarehouseList.sort((a, b) => b.userCount - a.userCount).sort((a, b) => b.logCount - a.logCount);
    yield this.changeData({ testGatherList: subWarehouseList });
  },
};
