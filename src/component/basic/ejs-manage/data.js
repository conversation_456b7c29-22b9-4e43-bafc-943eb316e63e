// 文件头部添加
/* eslint-disable @shein-bbl/bbl/translate-i18n-byT */

import { t } from '@shein-bbl/react';
import { textToBarcode } from '@src/lib/print-new/index';
import { textMoreFormat } from '@src/lib/dealFunc';
// 脚本生成部分
import srcTplPackagePrint from '@src/tpl/package-print.ejs';
import srcTplPackageCombinePrint from '@src/tpl/package-combine-print.ejs';
import srcTplGoodsBarcodeSize from '@src/tpl/goods-barcode-size.ejs';
import srcTplContainerCode from '@src/tpl/container-code.ejs';
import srcTplIndiaLabel from '@src/tpl/india-label.ejs';
import srcTplContainerCodeList from '@src/tpl/container-code-list.ejs';
import srcTplContainer from '@src/tpl/container.ejs';
import srcTplFnSkuCode from '@src/tpl/fn-sku-code.ejs';
import srcTPlTransshipmentBoxPrint from '@src/tpl/transshipment-box-print.ejs';
import srcComponentQmsDefectivePrintTemplatePrint from '@src/tpl/qms/print.ejs';
import srcTplApplyCode from '@src/tpl/apply-code.ejs';
import srcTplBarcodeOld from '@src/tpl/barcode-old.ejs';
import srcTplFbaContainerCode from '@src/tpl/fba-container-code.ejs';
import srcTplGoodsBarcode from '@src/tpl/goods-barcode.ejs';
// import srcTplMrp from '@src/tpl/mrp.ejs';
import srcTplReturnSupplyBox from '@src/tpl/return-supply-box.ejs';
import srcComponentPrintTemplatePackageHtml from '@src/component/print-template/packageHtml.ejs';
import srcComponentQmsDefectivePrintTemplateScrap from '@src/tpl/qms/scrap.ejs';
import srcComponentInboundRejectOrderListPrint from '@src/tpl/inbound/rejectOrderPrint.ejs';
import srcComponentMultipleGoodsDealMultipleGoodsManagePrinte from '@src/tpl/multiple-goods-deal/multipleGoodsManagePrint.ejs';
import srcTplHeimaCode from '@src/tpl/heima-code.ejs';
import srcComponentBasicGoodsGatherListEjsPrint from '@src/component/basic/goods-gather/list/ejs/print.ejs';
import srcComponentInboundRejectOrderRebackReceiveScanPrint from '@src/tpl/inbound/rebackReceiveScanPrint.ejs';
import srcComponentQmsDefectiveLessScrapNoticeDetailPrint from '@src/tpl/qms/scrapNoticeDetailPrint.ejs';
import srcComponentQmsDefectiveDeProductScanListPackagedetail from '@src/tpl/qms/packageDetail.ejs';
import srcComponentQmsDefectiveDeProductScanListPackagelabel from '@src/tpl/qms/packageLabel.ejs';
import srcComponentQmsDefectiveReturnBoxQueryInventory from '@src/tpl/qms/inventory.ejs';
import srcComponentQmsReceiptManagementDetailPrintfba from '@src/tpl/qms/printFbaDetail.ejs';
import srcComponentQmsReceiptManagementReceiptListPrintfba from '@src/tpl/qms/printFba.ejs';
import srcComponenInboundRejectOrderReturnPackageScanPrint from '@src/tpl/inbound/returnPackageScanPrint.ejs';

import { userNoSessionStorage, langLocalStorage } from '@src/lib/storage-new';

// eslint-disable-next-line import/prefer-default-export
export const data = [
  {
    key: '@src/tpl/package-print.ejs',
    size: '80*40',
    urls: [
      '/basic-functions/package-print',
      '/outbound/package-record',
      '/combination/package-review-refactor/list',
      '/combination/parcel-package/list',
      '/combination/pre-rechecking-refactor/list',
      '/outbound/packChecking/rechecking-new',
      '/outbound/packChecking/rechecking-refactor',
      '/outbound/package/list',
      '/outbound/packChecking/package-weight-difference/list',
    ],
    tpl: srcTplPackagePrint,
    // 维护部分
    name: '包裹标签',
    remark: '',
    state: {
      data: {
        shippingMethodPredict: 'DPEX-NS',
        shippingMethodPredictCode: 'DPEX-NS',
        packageNo: 'BG2207260032086',
        printTime: '11-10 16:37',
        batchSimpleCode: '1',
        batchSequence: 1,
        packageGoodsNum: 1,
        wellenSimpleCode: '0014',
        country: 'Saudi Arabia',
        packageBarCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJEAAAAZAQAAAAAZQPE7AAAACXBIWXMAAAuJAAALiQE3ycutAAAAEnRFWHRTb2Z0d2FyZQBCYXJjb2RlNEryjnYuAAAAIklEQVR42mPQfTdn94wrTjmRL1pznh3R3W7cxcAwKjasxQDV/Prif0MVRgAAAABJRU5ErkJggg==',
        coBatchSequence: '',
        batchCode: 'SS22100800141',
        sensitiveMark: null,
        regulationCode: null,
        declarationMode: 0,
        shippingProductionType: null,
        whetherPostCreated: false,
        combineWellenType: null,
        combineWellenTypeCode: null,
        combineAddress: null,
        combineAddressId: null,
        combineTypeCode: null,
      },
    },
  },
  {
    key: '@src/tpl/package-combine-print.ejs',
    size: '80*40',
    urls: [
      '/basic-functions/package-print',
      '/combination/pre-rechecking-refactor/list',
      '/outbound/packChecking/rechecking-new',
      '/outbound/package/list',
      '/outbound/patchLabel/list',
      '/outbound/packChecking/package-weight-difference/list',
    ],
    tpl: srcTplPackageCombinePrint,
    name: '合包包裹标签',
    remark: '',
    state: {
      data: [
        {
          shippingMethodPredict: '',
          shippingMethodPredictCode: '',
          packageNo: 'BG2201040000836',
          printTime: '11-13 17:25',
          batchSimpleCode: '',
          batchSequence: 0,
          packageGoodsNum: 4,
          wellenSimpleCode: 'date',
          country: 'Saudi Arabia',
          packageBarCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJEAAAAZAQAAAAAZQPE7AAAACXBIWXMAAAuJAAALiQE3ycutAAAAEnRFWHRTb2Z0d2FyZQBCYXJjb2RlNEryjnYuAAAAIklEQVR42mPQfTdn94wrTjm+N6+r5eVaL7zcxcAwKjasxQAK9vQMAniDdQAAAABJRU5ErkJggg==',
          coBatchSequence: '0',
          batchCode: '',
          sensitiveMark: '',
          regulationCode: null,
          declarationMode: null,
          shippingProductionType: null,
          whetherPostCreated: false,
          combineWellenType: null,
          combineWellenTypeCode: null,
          combineAddress: null,
          combineAddressId: null,
          combineTypeCode: null,
        },
      ],

    },
  },
  {
    key: '@src/tpl/goods-barcode-size.ejs',
    size: '70*20',
    urls: [
      '/basic-functions/barcode-print',
      '/basic/product-info/list',
      '/in-warehouse/black-code-search/overdue/overdue',
      '/in-warehouse/black-code-search/printed/printed',
      '/in-warehouse/black-code-search/searchFail/searchFail',
      '/in-warehouse/black-code-search/searched/searched',
      '/in-warehouse/black-code-search/unsearch/unsearch',
    ],
    tpl: srcTplGoodsBarcodeSize,
    name: '',
    remark: '',
    state: {
      info: [{
        skc: 'sM22103145065564',
        size: 'I0b0dcmec2kv',
        barcodeBaseImg: textToBarcode('1000274773'),
        orderNumber: null,
        supplier: '惠州52055',
        num: 1,
      }],
      userNo: userNoSessionStorage.getItem() || '',
    },
  },
  {
    key: '@src/tpl/apply-code.ejs',
    moduleName: '入库',
    module: 1,
    size: '100*100',
    urls: [
      '/qms/receipt-management/added-abnormal-application',
      '/qms/receipt-management/introduction-abnorma',
    ],
    tpl: srcTplApplyCode,
    name: '入库异常申请单',
    remark: '',
    state: {
      data: [
        {
          applyCode: 'ZZ22110700003',
          serviceName: '家家乐',
          packageNum: 1,
          subWarehouseName: '佛山2号仓',
          printName: 'Amigo Zheng(********)',
          printTime: '2022-11-15 15:55:19',
          purchaseCode: 'B221021502146',
          incrementTypeDesc: '部分增值',
          codeImg: textToBarcode('ZZ22110700003'),
        },
        {
          applyCode: 'ZZ22110700003',
          serviceName: '家家乐',
          packageNum: 1,
          subWarehouseName: '佛山2号仓',
          printName: 'Amigo Zheng(********)',
          printTime: '2022-11-15 15:55:19',
          purchaseCode: 'B221021502146',
          incrementTypeDesc: '部分增值',
          codeImg: textToBarcode('ZZ22110700003'),
        },
      ],
    },
  },
  {
    key: '@src/tpl/barcode-old.ejs',
    moduleName: '入库',
    module: 1,
    size: '70*20',
    urls: [
      '/inbound/reject-check-manage/reject-check-scan',
      '/receive-goods-check/receive-qc-scan/list',
    ],
    tpl: srcTplBarcodeOld,
    name: '打印商品条码',
    remark: '',
    state: {
      barcodeBaseImg: textToBarcode('1000274775', { width: '10mm' }),
      info: {
        skc: 'sM22103145065564',
        size: 'I0b0dcmec2kv',
        saleAttrZh: '红色',
        barcodeBaseImg: textToBarcode('1000274775', { width: '10mm' }),
        barCode: '1000274775',
        orderNumber: null,
        supplier: '惠州52055',
        labelPlaceText: 'Made In China',
      },
      userNo: userNoSessionStorage.getItem(),
    },
  },
  {
    key: '@src/tpl/borrow-type-code.ejs',
    size: '70*20',
    name: '',
    urls: [
      '/special-out/box-scan/list',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/box-label.ejs',
    size: '100*100',
    name: '',
    urls: [
      '/examples/something',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/container-code-list.ejs',
    size: '70*20',
    urls: [
      '/receive-goods-check/receive-qc-manage/list',
      '/special-out/overseas-out/list',
    ],
    tpl: srcTplContainerCodeList,
    module: 2,
    moduleName: '支撑',
    name: '打印箱子',
    remark: '',
    state: {
      data: [
        {
          barcodeBaseImg: textToBarcode('1000274773'),
          code: 'shelfContainerCode1',
        },
        {
          barcodeBaseImg: textToBarcode('1000274773'),
          code: 'shelfContainerCode2',
        },
      ],
    },
  },
  {
    key: '@src/tpl/container-code.ejs',
    moduleName: '入库',
    module: 1,
    size: '70*20',
    urls: [
      '/inbound/nsp-black-pack',
      '/special-out/recall-packing-query',
      '/inbound/reject-check-manage/reject-box-query',
      '/inbound/reject-check-manage/reject-check-scan',
      '/receive-goods-check/receive-qc-scan/list',
      '/transferBill-manage/transferBill-detail/list',
    ],
    tpl: srcTplContainerCode,
    name: '打印箱号',
    remark: '',
    state: {
      barcodeBaseImg: textToBarcode('1000274773'),
      code: '1000274773',
    },
  },
  {
    key: '@src/tpl/container.ejs',
    moduleName: '入库',
    module: 1,
    size: '100*45',
    urls: [
      '/inbound/warehouse-packing',
      '/inbound/storage-query/list',
      '/qms/warehousing/into-scanning-upgrade/list',
    ],
    tpl: srcTplContainer,
    name: '打印箱号',
    remark: '',
    state: {
      saleWarehouseCode: 'saleWarehouseCode',
      containerCode: 'containerCode',
      barcode: textToBarcode('1000274773'),
      weight: [
        {
          skc: 'skc1',
          num: 'num1',
        },
        {
          skc: 'skc2',
          num: 'num2',
        },
        {
          skc: 'skc1',
          num: 'num1',
        },
        {
          skc: 'skc2',
          num: 'num2',
        },
        {
          skc: 'skc1',
          num: 'num1',
        },
      ],
      sumWeight: 2.00,
      numberSum: 2,
    },
  },
  {
    key: '@src/tpl/fba-container-code.ejs',
    size: '80*86',
    urls: [
      '/special-out/box-data-query/list',
      '/special-out/box-scan/list',
    ],
    tpl: srcTplFbaContainerCode,
    name: '打印fba箱号',
    remark: '',
    state: {
      list: [
        {
          sourceCode: 'SH22081604412',
          barcodeBaseImg: textToBarcode('VJD22081600004-1'),
          boxCode: 'VJD22081600004-1',
          boxSerialNo: '1',
          outDate: '2022-08-16 15:00:43',
        },
      ],
    },
  },
  {
    key: '@src/tpl/fn-sku-code.ejs',
    size: '70*50',
    urls: [
      '/basic/product-info/list',
      '/special-out/box-detail-query/list',
      '/special-out/box-scan/list',
    ],
    tpl: srcTplFnSkuCode,
    name: 'FBA条码打印',
    remark: '',
    state: {
      data: [
        {
          barcodeBaseImg: textToBarcode('1000274773'),
          fnSku: 'fnSku',
          size: 'size',
          shopName: 'Amazon FBA',
          amSize: 'amSize',
          goodsName: 'goodsName',
        },
      ],
    },
  },
  {
    key: '@src/tpl/goods-barcode.ejs',
    moduleName: '入库',
    module: 1,
    size: '70*20',
    urls: [
      '/qms/receipt-management/detail',
      '/qms/receipt-management/receipt/list',
    ],
    tpl: srcTplGoodsBarcode,
    name: '收货单',
    remark: '',
    state: {
      info: [
        {
          size: 'M',
          saleAttrZh: '',
          saleAttrEn: '',
          skuCode: 'I21jkcypttix',
          purchaseCode: '************',
          productAddr: 'Made In China',
          userNo: '',
          shopAccount: '',
          goodsSn: 'ss21092699572433',
          goodsName: null,
          supplierGoodsNum: '2323',
          goodsSnPrint: '2200JXA1008',
          orderNumber: '************',
          skc: 'ss21092699572433',
          supplier: '2323',
          amSize: null,
          labelPlaceText: 'Made In China',
          barcodeBaseImg: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQwAAAAmCAYAAAAxzKV3AAAAAXNSR0IArs4c6QAABAJJREFUeF7tndFywjAMBJP//2g6kNDBnih7Mn3r9q21g81FXp8UQ/fH4/HY/FEBFVCBQIFdYAQq2UUFVOClgMAwEFRABWIFBEYslR1VQAUEhjGgAioQKyAwYqnsqAIqIDCMARVQgVgBgRFLZUcVUIF927bhHMb7WMa+P5u2bT6m8f77LB31o/b363XHn/vPr/P+fZ73t/OpdKD3Uc0j1bV6P9X7nvuTXvN9r+Ig1TUdv9KtWqL0Pqpx59ejOK+OKaXxVMUZHX+q7sNqfKdxQ+MKjFNJWrAkZAVQuq4K/BQABO50wVagFhjX5xoFxhlZ3R1+lXjdBUo7WnoD0/l2nYLAOBSgHTkFZNfB0X1N59V1Jt1xCcBdwNO6qBzVarzqMHQYw0I3JTkCghZ2ukGZkpy1DbLCqaAp6Yik3fGsYRw1qtRJ0Q5d7bTpjlnNY44P6tddoNYwRmdIKbQOQ4ehw7gp7q+CchVEKWBpw0vnnW4Yv/PyKcloQVPH082xqapPNZ1uLlr1T+dBtSxyfN3xyTnQfakcTpparDqT7rg0H4FxKkBEJNJRgJqSjNayu2B9SjI+DaGFncZbF0QCQ2BcWv50R121lgJjdI6kY3dhC4xpYVMOVllEcgJVuw5jLEJWVjfVT2AIjDkGnr9T6mvR81Qt3REoV6ZzBAS+ah50IysAELi7FtfHqj3QWMM4v7FPhzHWBIi8q6AhAJGVFhjXj4crUJLz6l4nMATGEFNUDLyyfc+/pdcJjFFB0qNycN2FS2Cw6HkUgWfn6zmMImXpphY6jNGR0U5OKVj3sbXAuHdaMyBX41VgCIyXAuSI0tSI+pGDoHYdxjWYqUYnME4F6CBSJdS350K6OyDNk3bcase2hmEN4yp1IHDrMKbPuBAQqN2UZMxh0wCkfuQgqF2HocOIiozkFMiSddsFhsD4TNXI4aUHv6iYWhVju/FL4NZh6DCGWkS36GgNw6Phn6lN5fQselr0tOjpp1Wx6P3rfPy06iEFFR09uLX2VXWpxabaBFno+fpqXHJe3eu645qSnIpREZHaqWZgDWMMdQIcLQw6ol6lKN1cXmD0Up1UL1pP1jCK1IEeVxJoqnYCmI9Vr50ZBSo9BaF2ciJp8ZGcBDmBtAjZnY/ACB0ILVBa+OkNJAtJ1nc1oAlsq46hmq8OYwRaCjICCcUhXU8gEhgC46WAwBj/zw0BsuvgaCMQGNeOkDZqP0vyRwDrBrTAEBjP0NNhhAuQijREOrKCpiSHQulOatHzPmWhhZ3GmzUM/83AK9J0GNcWdxVY1YYx12KoX3eBVmAQGH68fYi9bx2PwBAYnwGVgopAZNEzTFlMSfxOz7vi8Ow0qCZETyuonRb2f01JfgBKsuadLlWjjwAAAABJRU5ErkJggg==',
        },
        {
          size: 'M',
          saleAttrZh: '',
          saleAttrEn: '',
          skuCode: 'I21jkcypttix',
          purchaseCode: '************',
          productAddr: 'Made In China',
          userNo: '',
          shopAccount: '',
          goodsSn: 'ss21092699572433',
          goodsName: null,
          supplierGoodsNum: '2323',
          goodsSnPrint: '2200JXA1008',
          orderNumber: '************',
          skc: 'ss21092699572433',
          supplier: '2323',
          amSize: null,
          labelPlaceText: 'Made In China',
          barcodeBaseImg: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQwAAAAmCAYAAAAxzKV3AAAAAXNSR0IArs4c6QAABAJJREFUeF7tndFywjAMBJP//2g6kNDBnih7Mn3r9q21g81FXp8UQ/fH4/HY/FEBFVCBQIFdYAQq2UUFVOClgMAwEFRABWIFBEYslR1VQAUEhjGgAioQKyAwYqnsqAIqIDCMARVQgVgBgRFLZUcVUIF927bhHMb7WMa+P5u2bT6m8f77LB31o/b363XHn/vPr/P+fZ73t/OpdKD3Uc0j1bV6P9X7nvuTXvN9r+Ig1TUdv9KtWqL0Pqpx59ejOK+OKaXxVMUZHX+q7sNqfKdxQ+MKjFNJWrAkZAVQuq4K/BQABO50wVagFhjX5xoFxhlZ3R1+lXjdBUo7WnoD0/l2nYLAOBSgHTkFZNfB0X1N59V1Jt1xCcBdwNO6qBzVarzqMHQYw0I3JTkCghZ2ukGZkpy1DbLCqaAp6Yik3fGsYRw1qtRJ0Q5d7bTpjlnNY44P6tddoNYwRmdIKbQOQ4ehw7gp7q+CchVEKWBpw0vnnW4Yv/PyKcloQVPH082xqapPNZ1uLlr1T+dBtSxyfN3xyTnQfakcTpparDqT7rg0H4FxKkBEJNJRgJqSjNayu2B9SjI+DaGFncZbF0QCQ2BcWv50R121lgJjdI6kY3dhC4xpYVMOVllEcgJVuw5jLEJWVjfVT2AIjDkGnr9T6mvR81Qt3REoV6ZzBAS+ah50IysAELi7FtfHqj3QWMM4v7FPhzHWBIi8q6AhAJGVFhjXj4crUJLz6l4nMATGEFNUDLyyfc+/pdcJjFFB0qNycN2FS2Cw6HkUgWfn6zmMImXpphY6jNGR0U5OKVj3sbXAuHdaMyBX41VgCIyXAuSI0tSI+pGDoHYdxjWYqUYnME4F6CBSJdS350K6OyDNk3bcase2hmEN4yp1IHDrMKbPuBAQqN2UZMxh0wCkfuQgqF2HocOIiozkFMiSddsFhsD4TNXI4aUHv6iYWhVju/FL4NZh6DCGWkS36GgNw6Phn6lN5fQselr0tOjpp1Wx6P3rfPy06iEFFR09uLX2VXWpxabaBFno+fpqXHJe3eu645qSnIpREZHaqWZgDWMMdQIcLQw6ol6lKN1cXmD0Up1UL1pP1jCK1IEeVxJoqnYCmI9Vr50ZBSo9BaF2ciJp8ZGcBDmBtAjZnY/ACB0ILVBa+OkNJAtJ1nc1oAlsq46hmq8OYwRaCjICCcUhXU8gEhgC46WAwBj/zw0BsuvgaCMQGNeOkDZqP0vyRwDrBrTAEBjP0NNhhAuQijREOrKCpiSHQulOatHzPmWhhZ3GmzUM/83AK9J0GNcWdxVY1YYx12KoX3eBVmAQGH68fYi9bx2PwBAYnwGVgopAZNEzTFlMSfxOz7vi8Ow0qCZETyuonRb2f01JfgBKsuadLlWjjwAAAABJRU5ErkJggg==',
        },
      ],
      userNo: '********',
      orderCodeShow: true,
      productCodeShow: true,
      printAccountCode: true,
      labelLanguageVal: 1,
    },
  },
  {
    key: '@src/tpl/heima-code.ejs',
    moduleName: '入库',
    module: 1,
    tpl: srcTplHeimaCode,
    size: '70*20',
    name: '黑马标签',
    urls: [
      '/inbound/reject-check-manage/reject-check-scan',
    ],
    state: {
      barcodeBaseImg: textToBarcode('THSS23031400000001'),
      code: 'THSS23031400000001',
    },
    remark: '',
  },
  {
    key: '@src/tpl/india-label.ejs',
    size: '80*40',
    urls: [
      '/special-out/box-scan/list',
      '/outbound/packChecking/distribution-order-scan/list',
    ],
    tpl: srcTplIndiaLabel,
    name: '印度MRP标签',
    remark: '',
    state: {
      info: {
        mrp: 'mrp',
        skc: 'skc',
        size: 'size',
        country: 'country',
        productName: 'productName',
      },
    },
  },
  // 已废弃
  // {
  //   key: '@src/tpl/mrp.ejs',
  //   urls: [
  //     '/special-out/box-scan/list',
  //     '/outbound/packChecking/rechecking/list',
  //   ],
  //   tpl: srcTplMrp,
  //   name: '',
  //   remark: '',
  // },
  {
    key: '@src/tpl/packing-list.ejs',
    size: '76*40',
    name: '',
    urls: [
      '/in-warehouse/breakage/distribution-replace-query/list',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/return-supply-box.ejs',
    moduleName: '入库',
    module: 1,
    size: '100*100',
    urls: [
      '/qms/defective/refund-box-scan-new',
      '/qms/defective/return-box-query',
    ],
    tpl: srcTplReturnSupplyBox,
    name: '退供装箱 打印箱签',
    remark: '',
    state: {
      list: [
        {
          returnBoxCode: 'TG22111500014-1',
          supplierName: 'OBMOO-LOVE&',
          returnNoticeCode: 'TG22111500014',
          returnWayTypeDes: '到仓自提',
          returnWayType: 1,
          incrementDes: '否',
          incrementType: 0,
          skc: 'sM22090553333235',
          returnCount: 6,
          packagedTime: '2022-11-15 13:52:12',
          purchaseCode: '19386761',
          subWarehouseName: '佛山2号仓',
          scanType: 0,
          scanTypeName: '',
          returnType: 1,
          returnTypeName: '质检退供',
          remark: null,
          returnReason: '瑕疵,损破',
          supplierAddress: '广东省惠州市博罗县惠州234323',
          strChnSum: 2,
          imgUrl: 'data:image/png;base64,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',
        },
      ],
      isChinese: true,
      t,
    },
  },
  {
    key: '@src/tpl/transshipment-box-print.ejs',
    size: '100*100',
    urls: [
      '/combination/sub-package-boxing',
      '/combination/pre-rechecking-refactor/list',
      '/combination/transshipment/list',
    ],
    tpl: srcTPlTransshipmentBoxPrint,
    name: '打印转运箱',
    remark: '',
    state: {
      info: [
        {
          combineWellenType: '123',
          combineAddress: '123123',
          whetherPostCreated: false,
          coBatchCode: '123123123',
          codeImg: textToBarcode(
            '1000274773',
            {
              width: '10px',
              height: '600px',
              marginTop: '8px',
            },
            true,
          ),
          transferContainerCode: '12312123',
          combineWellenTypeCode: '1-92',
          fillBoxWarehouseId: 1,
          declareMark: '1:9610',
          packageNum: 20,
          fillBoxTime: '关箱时间',
          fillUser: '关箱人',
        },
      ],
      t,
    },
  },
  {
    key: '@src/tpl/work-location.ejs',
    size: '300*210',
    name: '',
    urls: [
      '/basic/work-location-manage',
    ],
    remark: '',
  },
  // {
  //   key: '@src/component/outbound/packChecking/rechecking/list/packageHtml.ejs',
  //   size: '无',
  //   urls: [
  //     '/outbound/packChecking/printParcels/list',
  //   ],
  //   name: '',
  //   remark: '',
  // },
  {
    key: '@src/component/print-template/packageHtml.ejs',
    size: '100*100',
    urls: [
      '/outbound/packChecking/rechecking-new',
      '/outbound/packChecking/rechecking-refactor',
    ],
    tpl: srcComponentPrintTemplatePackageHtml,
    name: '打印包裹明细',
    remark: '',
    state: {
      barcodeBaseImg: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASIAAAArCAYAAADcxoXKAAAAAXNSR0IArs4c6QAABI9JREFUeF7tnNsS2jAQQ8P/fzSdEshg18uRTZj04fStDc1F3pUl2cntfr/fN/+IgAiIwIUI3CSiC9H30iIgAg8EJCILQQRE4HIEJKLLh8AbEAERkIisAREQgcsRkIguHwJvQAREQCKyBkRABC5HQCK6fAi8AREQgdu2bc0+ote2otvt76Ft67cZVf/eQ0m/64+//v46T7W9qfrd7PWq+62u31+XflfhSOepjvfXI7xo/PrnT8edxud1nMaXnie9P3rOqn7pOX6Nb0U96TikfZn2BdXzLF7V+PbPd9SLRLRDkRZe1SDUgNUAfNuQVEDUiFQw/XNRQRIORDCzDSoRtfWbElQ6rtQX1XmorvrzqoieiBHgpGSoASWiXWFLRGMcqHGr+lr9d4moa/yqQbVmY22Q4kVKgQiBlBS9GaQ1axGmiUgiMiN61ICKaI8KUwKTiD5nqET0sxZ0VfmYERVhN1mYfmboB8ywuiWMCq+UUGZnYhqfdHyr6842KD3nqrJLJyY6/1nPKRF1L+cTwxJgaaFqzbRm7wjQKmQa0hLBzGYnEtGOGGWoZNUNq7vMihRGJbmJYCkjoOM0s642oopo/BUcIixSYlqzHQGaQI6+cfl+zOizM51E1LYezYBErNTI3+63IYspEWV9USlImuBcvi++A5cWnoqonenGBvLf0DuV7tX5aGZdVYSzEQBFD796zvS6KQ6zFnSWuCUiIJpvC08ikojeM5FZ5VwRFTWuRGRY/agBmum0ZlqzpE4kovGqr2H1s3+0Zu4jeqfStB5UROMsiRSeGZEZ0TCGScNfygpIGVIIbUaUTQhpFkTbbVLCpXEnx0CLFyoiFdEDAYlobC2rBnT5/jNeKiLD6ibTWrUO9P98xcNXPD5ZWYlIIpKITnhnLrUsRNhVQ85aUFfNXDVz1Sx4KbhvLMoEzmpQskhEFN9u56Dzn/WcEpFEJBFJRPGXStPQVkXUfpfJsNqw2rB6wApas/GyfKogzYjMiMyIzIgOanX53u8RDQnhbOn+q+Vlyj5cNXPVzFWzNwRo/wuFfP1xauxexdPGsl+HtnS/RCgkoVN8DKvHVsawerxB053V7qwe5qRE6JSZSEQS0aiw6KsJx0Tn94iyUI4sVaW8VETjlxzPUgou37f1Swq9mjBIWVfHK8VMSltFpCJSEQ2s/WqDrlrf4SBMvGqTEo5htWG1YfWgsaqMbfXzGCoiFVFF6sMGnM0Q0v0KlG1Ulml1BqwaSWvWIvNrS0rjTgQ1azVmFcjZhEt1TM8720+V5UrPozVzH9HHiYCyLipoamAi5LMblO6XrFPaWBLRPnIpXhKRRCQRuaHx4HszIjMiMyIzooMQeitpWO27ZsMaoH0yZGnIklAh0vFK6lZWiSwLWaRVS0M40HVnG5Sec/U50nqg89O4ER6zljD9fZoBEQ7VebRmWjOtmdZMa0bpfjpjpqEYrZ7M3g8pjGoGo+cixUPHaWZNZ0JSdqQwKnx6XFw1axFZHd/ZcU1/ryJ6IkCFKhGNTUpV0GkBSkRtNjFbZzSxpfjOWtD0umkd/G9E9AevFkLZhEAS/wAAAABJRU5ErkJggg==',
      info: {
        billNo: 'GSHNCS47X0004W8',
        packageNo: 'BG2207260032086',
        handleTime: '2022-07-26 14:46:12',
        packageGoodsList: [
          {
            goodsSn: 'dress170306106',
            size: 'L',
            goodsName: 'فستان طويل طباعة الزهور - أزرق بحري',
            quantity: 1,
            canReturn: 0,
            skuCode: 'Ibfl292015bh',
          },
        ],
        isAutoPrintReturnIns: false,
        returnInsPrintTemplateUrl: null,
        printList: [
          {
            goodsSn: 'dress170306106',
            size: 'L',
            goodsName: 'فستان طويل طباعة الزهور - أزرق بحري',
            quantity: 1,
            canReturn: 0,
            skuCode: 'Ibfl292015bh',
          },
        ],
      },
    },
  },
  {
    key: '@src/tpl/inbound/returnPackageScanPrint.ejs',
    tpl: srcComponenInboundRejectOrderReturnPackageScanPrint,
    moduleName: '入库',
    module: 1,
    size: '70*20',
    name: '退货收包打印包裹标签',
    urls: [
      '/inbound/reject-order/return-package-scan',
    ],
    state: {
      barcodeBaseImg: textToBarcode('THSS23031400000001'),
      orderNo: 'THSS23031400000001',
    },
    remark: '',
  },
  {
    key: '@src/tpl/qms/print.ejs',
    moduleName: '入库',
    module: 1,
    size: '80*40',
    urls: [
      '/inbound/warehouse-packing',
      '/qms/defective/de-product-scan-new',
      '/qms/defective/return-handover-detail/list',
    ],
    tpl: srcComponentQmsDefectivePrintTemplatePrint,
    name: '打印退供交接箱',
    remark: '',
    state: {
      list: [
        {
          handoverBoxCode: 'CP22111500010',
          supplierName: textMoreFormat('惠州52055'),
          purchaseCode: '19386761',
          returnReason: textMoreFormat('这是一个疵点类别哦这个问题很大需要处理一下的否则就会造成损失，需要严肃处理，各个部门引起重视:1'),
          waitReturnCount: 18,
          creator: 'VOC:杨勇',
          packageNum: null,
          scanType: 0,
          scanTypeName: '',
          returnType: 1,
          returnTypeName: '质检退供',
          returnSubWarehouseName: '佛山2号仓',
          sourceSubWarehouseName: '',
          createTime: '2022-11-15 13:49:03',
          barcodeBaseImg: textToBarcode('CP22111500010'),
          isNew: true,
        },
      ],
      isChinese: langLocalStorage.getItem() === 'zh',
      t,
    },
  },
  {
    key: '@src/tpl/qms/scrap.ejs',
    moduleName: '入库',
    module: 1,
    size: '80*40',
    urls: [
      '/inbound/warehouse-packing',
      '/qms/defective/de-product-scan-new',
    ],
    tpl: srcComponentQmsDefectivePrintTemplateScrap,
    name: '报废交接箱',
    remark: '',
    state: {
      list: [
        {
          handoverBoxCode: 'CP22111600004',
          supplierName: '青岛饰品',
          purchaseCode: '20221107160717',
          returnReason: '多货',
          waitReturnCount: 40,
          creator: 'Amigo Zheng(********)',
          packageNum: '2-1',
          scanType: 2,
          scanTypeName: '原封退回',
          returnType: 3,
          returnTypeName: '异常退供',
          returnSubWarehouseName: '佛山2号仓',
          sourceSubWarehouseName: '佛山2号仓',
          createTime: '2022-11-16 11:52:32',
          barcodeBaseImg: textToBarcode('CP22111600004', {
            width: 2,
            height: 30,
            margin: 0,
            marginTop: '8px',
          }),
        },
        {
          handoverBoxCode: 'CP22111600004',
          supplierName: '青岛饰品',
          purchaseCode: '20221107160717',
          returnReason: '多货',
          waitReturnCount: 40,
          creator: 'Amigo Zheng(********)',
          packageNum: '2-2',
          scanType: 2,
          scanTypeName: '原封退回',
          returnType: 3,
          returnTypeName: '异常退供',
          returnSubWarehouseName: '佛山2号仓',
          sourceSubWarehouseName: '佛山2号仓',
          createTime: '2022-11-16 11:52:32',
          barcodeBaseImg: textToBarcode('CP22111600004', {
            width: 2,
            height: 30,
            margin: 0,
            marginTop: '8px',
          }),
        },
      ],
      isChinese: langLocalStorage.getItem() === 'zh',
      t,
    },
  },
  {
    key: '/basic/container/list/container.ejs',
    size: '80*50',
    name: '',
    urls: [
      '/basic/container/list',
    ],
    remark: '',
  },
  {
    key: '/basic/goods-gather/list/ejs/print.ejs',
    tpl: srcComponentBasicGoodsGatherListEjsPrint,
    moduleName: '入库',
    module: 1,
    size: '80*50',
    name: '包裹号',
    urls: [
      '/basic/goods-gather/list',
    ],
    state: {
      list: [
        {
          barcode: textToBarcode('CP22111600004'),
          code: 'CP22111600004',
        },
      ],
    },
    remark: '',
  },
  {
    key: '/basic/goods/list/ejs/location-big.ejs',
    size: '100*45',
    name: '',
    urls: [
      '/basic/goods/list',
    ],
    remark: '',
  },
  {
    key: '/basic/goods/list/ejs/location.ejs',
    size: '80*50',
    name: '',
    urls: [
      '/basic/goods/list',
    ],
    remark: '',
  },
  {
    key: '/basic/goods/list/ejs/sequence.ejs',
    size: '70*12',
    name: '',
    urls: [
      '/basic/goods/list',
    ],
    remark: '',
  },
  {
    key: '/combination/package-review-refactor/list/ejs/packageHtml.ejs',
    size: '100*100',
    name: '',
    urls: [
      '/combination/package-review-refactor/list',
    ],
    remark: '',
  },
  {
    key: '/combination/transshipment/list/ejs/html.ejs',
    size: '156*100',
    name: '',
    urls: [
      '/combination/transshipment/list',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/inbound/rejectOrderPrint.ejs',
    size: '80*40',
    moduleName: '入库',
    module: 1,
    name: '收货单',
    urls: [
      '/inbound/reject-order/list',
    ],
    tpl: srcComponentInboundRejectOrderListPrint,
    state: {
      barcodeBaseImg: textToBarcode('THSS23031400000001'),
      noticeCode: 'THSS23031400000001',
    },
    remark: '',
  },
  {
    key: '@src/tpl/inbound/rebackReceiveScanPrint.ejs',
    tpl: srcComponentInboundRejectOrderRebackReceiveScanPrint,
    state: {
      barcodeBaseImg: textToBarcode('THSS23031400000001'),
      receiptCode: 'THSS23031400000001',
    },
    moduleName: '入库',
    module: 1,
    size: '80*40',
    name: '打印单号',
    urls: [
      '/inbound/reject-order/reback-receive-scan',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/multiple-goods-deal/multipleGoodsManagePrint.ejs',
    size: '70*20',
    moduleName: '入库',
    module: 1,
    name: '补打箱号',
    tpl: srcComponentMultipleGoodsDealMultipleGoodsManagePrinte,
    urls: [
      '/multiple-goods-deal/multiple-goods-manage',
    ],
    state: {
      barcodeBaseImg: textToBarcode('THSS23031400000001'),
      boxNo: 'THSS23031400000001',
    },
    remark: '',
  },
  {
    key: '/oversea/abroad/bbc-box-scan/print.ejs',
    size: '100*150',
    name: '',
    urls: [
      '/oversea/abroad/bbc-box-scan',
    ],
    remark: '',
  },
  {
    key: '/oversea/abroad/encasement-query/list/print.ejs',
    size: '100*150',
    name: '',
    urls: [
      '/oversea/abroad/encasement-query/list',
    ],
    remark: '',
  },
  {
    key: '/outbound/packChecking/distribution-order-scan/list/packageHtml.ejs',
    size: '无',
    name: '',
    urls: [
      '/outbound/packChecking/distribution-order-scan/list',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/qms/scrapNoticeDetailPrint.ejs',
    tpl: srcComponentQmsDefectiveLessScrapNoticeDetailPrint,
    state: {
      list: [
        {
          barcodeBaseImg: textToBarcode('THSS23031400000001'),
          sourceSubWarehouseName: '佛山2号仓',
          scrapTypeName: '退供',
          waitScrapCount: '10',
          purchaseCode: 'THSS23031400000001',
          // 添加一个标识
          isNew: true,
          scrapReason: textMoreFormat('scrapReason'),
          supplierName: textMoreFormat('supplierName'),
          creator: 'Ebo',
          createTime: '1970-1-1',
        },
      ],
    },
    size: '80*40',
    moduleName: '入库',
    module: 1,
    name: '打印交接箱',
    urls: [
      '/qms/defective-less/scrap-notice-detail',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/qms/packageDetail.ejs',
    size: '80*40',
    tpl: srcComponentQmsDefectiveDeProductScanListPackagedetail,
    state: {
      list: [
        {
          receiptCode: 'THSS23031400000001',
          defectivePackageCode: 'THSS23031400000001',
          detail: [
            [
              {
                goodsSn: 'goodsSn',
                size: 'size',
                rejectedCount: '1',
              },
              {
                goodsSn: 'goodsSn1',
                size: 'size',
                rejectedCount: '2',
              },
              {
                goodsSn: 'goodsSn2',
                size: 'size',
                rejectedCount: '3',
              },
            ],
            [
              {
                goodsSn: 'goodsSn3',
                size: 'size',
                rejectedCount: '4',
              },
            ],
          ],
        },
      ],
    },
    moduleName: '入库',
    module: 1,
    name: '次品包裹明细',
    urls: [
      '/qms/defective/de-product-scan/list',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/qms/packageLabel.ejs',
    tpl: srcComponentQmsDefectiveDeProductScanListPackagelabel,
    state: {
      list: [
        {
          barcode: textToBarcode('THSS23031400000001'),
          defectivePackageCode: 'defectivePackageCode',
          receiptCode: 'receiptCode',
          purchaseCode: 'purchaseCode',
          supplierName: 'supplierName',
          reason: 'reason',
          defectiveCount: 'defectiveCount',
          createTime: 'createTime',
        },
      ],
    },
    size: '80*40',
    moduleName: '入库',
    module: 1,
    name: '包裹标签',
    urls: [
      '/qms/defective/de-product-scan/list',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/qms/inventory.ejs',
    tpl: srcComponentQmsDefectiveReturnBoxQueryInventory,
    state: {
      mapList: [
        'THSS23031400000001',
      ],
      urlMap: {
        THSS23031400000001: textToBarcode('THSS23031400000001'),
      },
      date: 'date',
      printUser: 'Ebo',

    },
    moduleName: '入库',
    module: 1,
    size: '100*100',
    name: '退供交接清单',
    urls: [
      '/qms/defective/return-box-query',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/qms/printFbaDetail.ejs',
    tpl: srcComponentQmsReceiptManagementDetailPrintfba,
    state: {
      data: [
        {
          barcode: textToBarcode('THSS23031400000001'),
          goodsSn: 'goodsSn',
          purchaseCode: 'purchaseCode',
          shopAccount: 'shopAccount',
          amSize: 'amSize',
          size: 'size',
          goodsName: 'goodsName',
          productAddr: 'productAddr',
        },
      ],
    },
    size: '70*50',
    moduleName: '入库',
    module: 1,
    name: 'FBA标签',
    urls: [
      '/qms/receipt-management/detail',
    ],
    remark: '',
  },
  {
    key: '/qms/receipt-management/package-detail/ejs/print.ejs',
    size: '70*20',
    name: '',
    urls: [
      '/qms/receipt-management/package-detail',
    ],
    remark: '',
  },
  {
    key: '@src/tpl/qms/printFba.ejs',
    tpl: srcComponentQmsReceiptManagementReceiptListPrintfba,
    state: {
      data: [
        {
          barcode: textToBarcode('THSS23031400000001'),
          goodsSn: 'goodsSn',
          purchaseCode: 'purchaseCode',
          shopAccount: 'shopAccount',
          amSize: 'amSize',
          size: 'size',
          goodsName: 'goodsName',
          productAddr: 'productAddr',
        },
      ],
    },
    size: '70*50',
    moduleName: '入库',
    module: 1,
    name: 'FBA标签',
    urls: [
      '/qms/receipt-management/receipt/list',
    ],
    remark: '',
  },
  {
    key: '/special-out/box-data-query/list/html.ejs',
    size: '80*40',
    name: '',
    urls: [
      '/special-out/box-data-query/list',
    ],
    remark: '',
  },
  {
    key: '/special-out/box-scan/list/html.ejs',
    size: '80*40',
    name: '',
    urls: [
      '/special-out/box-scan/list',
    ],
    remark: '',
  },
  {
    key: '/special-out/box-scan/list/indiaMrp.ejs',
    size: '90*80',
    name: '',
    urls: [
      '/special-out/box-scan/list',
    ],
    remark: '',
  },
  {
    key: '/special-out/overseas-out-oldPage/list/handoverCode.ejs',
    size: '无',
    name: '',
    urls: [
      '/special-out/overseas-out-oldPage/list',
    ],
    remark: '',
  },
  {
    key: '/special-out/overseas-out/list/handoverCode.ejs',
    size: '210*300',
    name: '',
    urls: [
      '/special-out/overseas-out/list',
    ],
    remark: '',
  },
  {
    key: '/special-out/tag-print/labelHtml.ejs',
    size: '80*50',
    name: '',
    urls: [
      '/special-out/tag-print',
    ],
    remark: '',
  },
  {
    key: '/special-out/tag-print/shoeHtml.ejs',
    size: '50*25',
    name: '',
    urls: [
      '/special-out/tag-print',
    ],
    remark: '',
  },
];
