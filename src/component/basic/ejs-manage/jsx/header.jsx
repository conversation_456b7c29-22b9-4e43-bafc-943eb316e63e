import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select, Input, Button } from 'shineout';
import store from '../reducers';
import styles from '../style.less';

class Header extends Component {
  render() {
    const {
      limit,
      isPublicList,
    } = this.props;

    return (
      <section>
        <div style={{
          display: 'flex',
          backgroundColor: '#fff',
          padding: 12,
        }}
        >
          <span style={{
            marginRight: '10px',
            display: 'flex',
            alignItems: 'center',
          }}
          >
            {t('是否公共')}
          </span>
          <Select
            value={limit.isPublic}
            data={isPublicList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            width={200}
            clearable
            onChange={(val) => {
              store.changeData({
                limit: {
                  ...limit,
                  isPublic: val,
                },
              });
            }}
          />
          <span style={{
            margin: '0 10px',
            display: 'flex',
            alignItems: 'center',
          }}
          >
            {t('模版名')}
          </span>
          <Input
            style={{ width: '200px' }}
            value={limit.template}
            onChange={(val) => {
              store.changeData({
                limit: {
                  ...limit,
                  template: val,
                },
              });
            }}
          />
          <span style={{
            margin: '0 10px',
            display: 'flex',
            alignItems: 'center',
          }}
          >
            {t('涉及页面')}
          </span>
          <Input
            style={{ width: '200px' }}
            value={limit.pageUrl}
            onChange={(val) => {
              store.changeData({
                limit: {
                  ...limit,
                  pageUrl: val,
                },
              });
            }}
          />
          <span style={{
            margin: '0 10px',
            display: 'flex',
            alignItems: 'center',
          }}
          />
          <Button
            type="primary"
            onClick={() => {
              store.filterList();
            }}
          >
            {t('查询')}
          </Button>
        </div>
      </section>
    );
  }
}

Header.propTypes = {
  limit: PropTypes.shape(),
  isPublicList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
