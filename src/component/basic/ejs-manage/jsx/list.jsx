import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Tag, Message,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import styles from '../style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      pageInfo,
      filterList,
    } = this.props;

    const inbound = filterList.filter((v) => v.module === 1);

    const support = filterList.filter((v) => v.module === 2);

    const other = filterList.filter((v) => v.module !== 1 && v.module !== 2);

    const columns = [
      {
        title: t('模板路径'),
        width: 200,
        render: (record) => (
          <div
            className={styles.tableKey}
            onClick={() => {
              if (record.tpl) {
                store.changeData({
                  previewVisible: true,
                  previewInfo: record,
                });
              } else {
                Message.error(t('🔥 该模板火热维护中，暂不支持预览，敬请期待'));
              }
            }}
          >
            {record.key}
          </div>
          // <Button
          //   size="small"
          //   text
          //   type="primary"

          // >
          //   <span style={{
          //     fontSize: '16px',
          //     fontWeight: 'bold',
          //   }}
          //   >

          //   </span>
          // </Button>
        ),
      },
      {
        title: t('模块'),
        width: 100,
        render: (record) => {
          if (record.module === 1) {
            return (
              <Tag type="info">
                {record.moduleName}
              </Tag>
            );
          } if (record.module === 2) {
            return (
              <Tag type="danger">
                {record.moduleName}
              </Tag>
            );
          }
          return (
            <Tag type="warning">{t('出库')}</Tag>
          );
        },
      },
      {
        title: t('模板名'),
        width: 100,
        render: 'name',
      },
      {
        title: t('属性'),
        width: 120,
        render: (record) => {
          if (record.isPublic === '1') {
            return (
              <Tag className={styles.publicTag}>
                {t('公共模板')}
              </Tag>
            );
          }
          return <Tag>{t('私有模板')}</Tag>;
        },
      },
      {
        title: t('被引用次数'),
        width: 100,
        render: (record) => record.urls.length,
      },
      {
        title: t('页面名称'),
        width: 200,
        render: (record) => (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            {record.pageName.map((item, index) => (
              <span>
                {index + 1}
                :
                {item}
              </span>
            ))}
          </div>
        ),
      },
      {
        title: t('涉及页面'),
        width: 400,
        render: (record) => (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            {record.urls.map((item, index) => (
              <span>
                {index + 1}
                :
                {item}
              </span>
            ))}
          </div>
        ),
      },
      {
        title: t('大小'),
        width: 100,
        render: 'size',
      },
      // {
      //   title: t('备注'),
      //   width: 100,
      //   render: 'remark',
      // },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={[...inbound, ...support, ...other]}
            keygen="key"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  pageInfo: PropTypes.shape(),
  previewVisible: PropTypes.bool,
  previewInfo: PropTypes.shape(),
  filterList: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
