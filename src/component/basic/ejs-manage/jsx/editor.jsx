import React, { Component } from 'react';
import PropTypes from 'prop-types';
import J<PERSON><PERSON>ditor from 'jsoneditor';
import 'jsoneditor/dist/jsoneditor.css';
import styles from '../style.less';

class Editor extends Component {
  componentDidMount() {
    const options = {
      mode: 'tree',
      onChangeJSON: this.props.onChangeJSON,
    };

    this.jsoneditor = new JSONEditor(this.container, options);
    this.jsoneditor.set(this.props.json);
  }

  // eslint-disable-next-line react/sort-comp
  componentWillUnmount() {
    if (this.jsoneditor) {
      this.jsoneditor.destroy();
    }
  }

  componentDidUpdate() {
    this.jsoneditor.update(this.props.json);
  }

  render() {
    return (
      // eslint-disable-next-line no-return-assign
      <div className={styles.editorContainer} ref={(elem) => this.container = elem} />
    );
  }
}

Editor.propTypes = {
  json: PropTypes.shape(),
  onChangeJSON: PropTypes.func,
};

export default Editor;
