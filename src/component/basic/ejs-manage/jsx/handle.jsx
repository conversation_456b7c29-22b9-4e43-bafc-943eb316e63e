/* eslint-disable react/no-danger */
import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Modal, Button, Input, Message, Textarea, Table, Popover,
} from 'shineout';
import XssText from '@shein-components/XssText';
import Icon from '@shein-components/Icon';
import Copy from '@shein-components/Copy';
import Editor from './editor';
import style from '../../../style.less';
import styles from '../style.less';
import store from '../reducers';

const testGatherColumns = [
  {
    title: t('仓库'),
    width: 80,
    render: 'warehouseName',
  },
  {
    title: t('子仓'),
    width: 180,
    render: 'subWarehouse',
  },
  {
    title: t('用户数'),
    width: 80,
    render: 'userCount',
  },
  {
    title: t('日志数'),
    width: 80,
    render: 'logCount',
  },
  {
    title: t('用户名'),
    width: 120,
    render: (record) => (
      <div className={styles.nameList}>
        <Popover style={{ maxWidth: 300, padding: 4 }}>{record.nameList.join('，')}</Popover>
        {record.nameList.join('，')}
      </div>
    ),
  },
];

class Handle extends React.Component {
  render() {
    const {
      previewVisible,
      previewInfo,
      showTestModal,
      testModalInput,
      testModalList,
      testModalLoading,
      showGatherModal,
      testGatherInput,
      testGatherList,
      testGatherLoading,
    } = this.props;

    const tpl = previewInfo.tpl || '';
    const htmlText = previewInfo.state ? tpl(previewInfo?.state) : null;

    return (
      <section className={[style.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          onClick={() => {
            store.changeData({ showTestModal: true, testModalInput: 'DZXSS2308070010003' });
          }}
        >
          {t('PDF打印验证')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            store.changeData({ showGatherModal: true, testModalInput: 'DZXSS2308070010003' });
          }}
        >
          {t('汇总告警信息')}
        </Button>
        <Modal
          visible={previewVisible}
          width={1200}
          maskCloseAble={false}
          title={t('打印模板预览')}
          bodyStyle={{ minHeight: '500px', maxHeight: '550px', overflow: 'auto' }}
          onClose={() => store.changeData({ previewVisible: false })}
          footer={(
            <div>
              <Button
                type="primary"
                onClick={() => store.changeData({ previewVisible: false })}
              >
                {t('关闭')}
              </Button>
            </div>
          )}
        >
          <div className={styles.modalWrapper}>
            <div className={styles.editorWrapper}>
              <Editor
                json={previewInfo.state || {}}
                onChangeJSON={(val) => {
                  store.changeDataState({
                    state: val,
                  });
                }}
              />
            </div>
            <div className={styles.previewWrapper}>
              <XssText
                template={htmlText} extraWhiteList={{
                  '!doctype': ['html'],
                  meta: ['name', 'content', 'charset'],
                  html: ['lang'],
                  style: [],
                  head: [],
                  title: [],
                  body: ['class'],
                  footer: [],
                }}
              />
            </div>
          </div>
        </Modal>
        <Modal
          visible={showTestModal}
          width={480}
          maskCloseAble={false}
          title={t('PDF打印验证')}
          bodyStyle={{ minHeight: '140px', overflow: 'auto' }}
          onClose={() => store.changeData({ showTestModal: false })}
          footer={(
            <div>
              <Button
                onClick={() => store.changeData({ showTestModal: false })}
              >
                {t('关闭')}
              </Button>
              <Button
                type="primary"
                disabled={!testModalInput || testModalLoading}
                loading={testModalLoading}
                onClick={() => store.pdfPrint({ testModalInput })}
              >
                {t('打印')}
              </Button>
            </div>
          )}
        >
          <section>
            <div className={style.inner_list}>
              <span className={style.labSmall}>
                <span className={style.redStar}>*</span>
                {t('箱号')}
                :
              </span>
              <Input
                data-bind="testModalInput"
                style={{ width: 220 }}
              />
            </div>
            {
              testModalList?.map((v, idx) => (
                <div key={v} className={styles.copyDiv}>
                  <Copy text={v} onCopy={() => Message.success(idx === 0 ? t('ha-pdf链接已复制到剪贴板') : t('普通-pdf链接已复制到剪贴板'))}>
                    <div>
                      {idx === 0 ? t('ha-pdf') : t('普通pdf')}
                      ：
                      {v}
                      <Icon name="copy" style={{ color: '#39f', marginLeft: '5px' }} />
                      <Icon
                        name="pc-skip"
                        style={{ color: '#ff5833', marginLeft: '5px' }}
                        fontSize={20}
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(v);
                        }}
                      />
                    </div>
                  </Copy>

                </div>
              ))
            }
          </section>
        </Modal>
        <Modal
          visible={showGatherModal}
          width={680}
          maskCloseAble={false}
          title={t('汇总告警信息')}
          bodyStyle={{ minHeight: '460px', overflow: 'auto' }}
          onClose={() => store.changeData({ showGatherModal: false })}
          footer={(
            <div>
              <Button
                onClick={() => store.changeData({ showGatherModal: false })}
              >
                {t('关闭')}
              </Button>
              <Button
                type="primary"
                disabled={!testGatherInput || testGatherLoading}
                loading={testGatherLoading}
                onClick={() => store.handleGather({ testGatherInput })}
              >
                {t('汇总处理')}
              </Button>
            </div>
          )}
        >
          <section>
            <div className={style.inner_list}>
              <span className={style.labSmall}>
                <span className={style.redStar}>*</span>
                {t('告警返回json')}
                :
              </span>
              <Textarea
                width={620}
                data-bind="testGatherInput"
              />
            </div>
            <Table
              bordered
              data={testGatherList}
              columns={testGatherColumns}
              keygen={(r) => JSON.stringify(r)}
              empty={t('暂无数据')}
              size="small"
              width={testGatherColumns.reduce((pre, current) => pre + current.width, 0)}
              style={{ maxHeight: 400 }}
            />
          </section>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  previewVisible: PropTypes.bool,
  showTestModal: PropTypes.bool,
  showGatherModal: PropTypes.bool,
  testModalLoading: PropTypes.bool,
  testGatherLoading: PropTypes.bool,
  testModalInput: PropTypes.string,
  testGatherInput: PropTypes.string,
  testModalList: PropTypes.arrayOf(PropTypes.string),
  testGatherList: PropTypes.arrayOf(PropTypes.string),
  previewInfo: PropTypes.shape(),
};
export default Handle;
