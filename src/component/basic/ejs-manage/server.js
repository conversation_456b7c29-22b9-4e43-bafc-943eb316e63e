import { sendPostRequest } from '@src/server/common/public';

/**
  * 权限节点（菜单）列表
  * authType 是校验类型，分校验登录和校验权限
  * isPage 是否页面查询 可跳过权限
  * @param obj
  * @returns {Promise.<TResult>}
  */
// eslint-disable-next-line import/prefer-default-export
export const authListAPI = (param) => sendPostRequest({
  url: '/authRule/ruleList',
  param,
}, process.env.WAS_URI);

/**
 * 获取用户管理列表数据
 * @param argObj
 * @returns {{credentials}}
 */
export const getUserManagePageList = (param) => sendPostRequest({
  url: '/inf_user/query_user_List',
  param,
}, process.env.OSM_FRONT);
