/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.handleExportButton {
    margin-right: 6px;
}


/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect {
    width: 140px;
}

.headerUnmatchedText {
    color: #bbb;
}

.headerSearchTimeLine {
    display: flex;
}

/* list.jsx
---------------------------------------------------------------- */
.listOperationButton {
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}

.listSection {
    height: 0px;
    /* 必写为了子domheight - 100%有效 */
    flex: 1;
}

.tableKey {
    display: flex;
    color: #3575f7;
    cursor: pointer;
    font-weight: bold;
}

.publicTag {
    color: #fff;
    background-color: #3575f7;
    border: 0;
}

.modalWrapper {
    display: flex;
}

.editorWrapper {
    flex: 2;
    height: 500px;
    overflow: auto;
}

.editorContainer {
    width: 100%;
    height: 100%;
}

.previewWrapper {
    flex: 1;
    padding: 5px;
    border: 1px solid blue;
    overflow: auto;
    max-height: 500px;
}

.copyDiv {
    margin-bottom: 10px;
}
.nameList {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}