import assign from 'object-assign';
import { Message, Modal } from 'shineout';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { selectArea } from '@src/server/basic/area';
import showMessage from '../../../../lib/modal';
import { basicQueryAPI, updateBasicConfigAPI, updateTaskDeliveryConfigAPI } from '../server';

const defaultState = {
  dataLoading: 1,
  limit: {
    warehouseId: 1, // 默认选佛山仓
    subWarehouseId: '',
    areaId: '',
  },
  subWarehouseList: [], // 子仓下拉
  areaList: [], // 库区下拉
  recordVisible: false,
  recordId: false,
  editOrAddObj: {
    isSupportMove: 0,
  },
  editOrAddObjCache: {
    isSupportMove: 0,
  },
  showConfigDetail: false, // 是否显示配置详情
};

// 校验输入值是否步长为0.5
function checkInputValue(inputValue) {
  // 判断范围是否在0和24之间
  if (inputValue < 0 || inputValue > 24) {
    return false;
  }
  // 判断增量是否为0.5
  const modResult = Number((inputValue % 0.5).toFixed(1));
  if (modResult !== 0 && modResult !== 0.5) {
    return false;
  }
  return true;
}

export default {
  defaultState,
  // 改state数据
  changeData: (draft, data) => {
    assign(draft, data);
  },
  changeLimit: (draft, data) => {
    assign(draft.limit, data);
  },
  changeEditOrAdd: (draft, data) => {
    assign(draft.editOrAddObj, data);
  },
  // 重置条件
  reset(draft) {
    assign(
      draft.limit,
      defaultState.limit,
    );
  },
  // 初始化
  $init: (draft) => {
    assign(draft);
  },
  * init(action, ctx) {
    // 获取子仓下拉数据
    const { warehouseId, subWarehouseList } = yield 'nav';
    yield ctx.changeData({
      subWarehouseList,
    });
    yield this.changeLimit({
      warehouseId,
    });
  },
  // 获取库区下拉数据
  * getArea(param) {
    markStatus('dataLoading');
    const data = yield selectArea(param);
    if (data.code === '0') {
      yield this.changeData({
        areaList: data.info.data,
      });
    } else {
      Modal.error({ title: data.msg });
    }
  },
  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { warehouseId, subWarehouseList } = data;
    yield this.changeData({
      subWarehouseList,
      areaList: [],
      showConfigDetail: false,
      editOrAddObj: {
        isSupportMove: 0,
      },
      editOrAddObjCache: {
        isSupportMove: 0,
      },
    });
    yield this.changeLimit({
      warehouseId,
      subWarehouseId: '',
      areaId: '',
    });
  },
  // 搜索基础信息配置
  * basicQuery(data, ctx) {
    const {
      limit,
    } = yield '';
    if (!limit.warehouseId || !limit.subWarehouseId || !limit.areaId) {
      Modal.error({ title: t('仓库、子仓、库区均必填！') });
      return;
    }
    markStatus('dataLoading');
    const res = yield basicQueryAPI(limit);
    if (res.code === '0') {
      const resInfo = res?.info || {};
      yield ctx.changeData({
        editOrAddObj: { ...resInfo, isSupportMove: resInfo.isSupportMove || 0 },
        editOrAddObjCache: { ...resInfo, isSupportMove: resInfo.isSupportMove || 0 },
        showConfigDetail: true,
      });
    } else {
      yield ctx.changeData({
        editOrAddObj: {
          isSupportMove: 0,
        },
        editOrAddObjCache: {
          isSupportMove: 0,
        },
      });
      showMessage(res.msg, false);
    }
  },
  // 更新基础配置
  * updateBasicConfig() {
    const {
      limit,
    } = yield '';
    const { editOrAddObj } = yield '';
    const {
      warehouseId,
      subWarehouseId,
      areaId,
    } = limit;
    if (!warehouseId || !subWarehouseId || !areaId) {
      Modal.error({ title: t('仓库、子仓、库区均必填！') });
      return;
    }
    const {
      urgentReplenishmentTime, // 紧急补货截单时间
      dailyReplenishmentTime, // 日常补货截单时间
      returnSupplyTime, // 退供报废截单时间
      specialOutboundTime, // 特殊出库截单时间
      warehouseShiftTime, // 仓内移位截单时间
    } = editOrAddObj;
    if (!checkInputValue(urgentReplenishmentTime)
      || !checkInputValue(dailyReplenishmentTime)
      || !checkInputValue(returnSupplyTime)
      || !checkInputValue(specialOutboundTime)
      || !checkInputValue(warehouseShiftTime)
    ) {
      Modal.error({ title: t('截单时间只能是0.5小时制') });
      return;
    }
    markStatus('dataLoading');
    const res = yield updateBasicConfigAPI({
      ...editOrAddObj,
      warehouseId,
      subWarehouseId,
      areaId,
    });
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.basicQuery();
    } else {
      showMessage(res.msg, false);
    }
  },

  // 更新基础配置
  * updateTaskDeliveryConfig() {
    const {
      limit,
    } = yield '';
    const { editOrAddObj } = yield '';
    const {
      warehouseId,
      subWarehouseId,
      areaId,
    } = limit;
    if (!warehouseId || !subWarehouseId || !areaId) {
      Modal.error({ title: t('仓库、子仓、库区均必填！') });
      return;
    }
    markStatus('dataLoading');
    const res = yield updateTaskDeliveryConfigAPI({
      inSideUpperTaskSwitch: editOrAddObj.inSideUpperTaskSwitch,
      inStorageDeviceSwitch: editOrAddObj.inStorageDeviceSwitch,
      warehouseId,
      subWarehouseId,
      areaId,
      id: editOrAddObj.id,
    });
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.basicQuery();
    } else {
      showMessage(res.msg, false);
    }
  },
};
