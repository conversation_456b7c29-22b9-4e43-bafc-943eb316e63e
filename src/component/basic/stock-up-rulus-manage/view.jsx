import { i18n, t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import { Tabs } from 'shineout';
import globalStyles from '@src/component/style.less';
import store from './reducers';
import BasicConfig from './basic-config/view';
import CrossConfig from './crossing-config/view';

class Container extends React.Component {
  render() {
    const {
      tabKey,
    } = this.props;
    return (
      <Tabs
        shape="line"
        className={`${globalStyles.tabsSection} ${globalStyles.noBorder}`}
        defaultActive={tabKey}
        onChange={(key) => {
          store.changeData({ tabKey: key });
        }}
      >
        <Tabs.Panel className={globalStyles.tabsPanel} tab={t('基础配置')}>
          <BasicConfig {...this.props} />
        </Tabs.Panel>
        <Tabs.Panel className={globalStyles.tabsPanel} tab={t('道口配置')}>
          <CrossConfig {...this.props} />
        </Tabs.Panel>
      </Tabs>
    );
  }
}

Container.propTypes = {
  ready: PropTypes.bool.isRequired,
  tabKey: PropTypes.number.isRequired,
};

export default i18n(Container);
