import { sendPostRequest } from '@src/server/common/public';

// 基础配置查询
export const basicQueryAPI = (param = {}) => sendPostRequest({
  url: '/prepare_base_config/query',
  param,
}, process.env.BASE_URI_WMD);

// 更新基础配置
export const updateBasicConfigAPI = (param = {}) => sendPostRequest({
  url: '/prepare_base_config/update',
  param,
}, process.env.BASE_URI_WMD);

// 道口配置查询
export const crossingQueryAPI = (param = {}) => sendPostRequest({
  url: '/level_crossing_config/query',
  param,
}, process.env.BASE_URI_WMD);

// 道口更新
export const updateCrossingConfigAPI = (param = {}) => sendPostRequest({
  url: '/level_crossing_config/update',
  param,
}, process.env.BASE_URI_WMD);

// 任务下发开关更新
export const updateTaskDeliveryConfigAPI = (param = {}) => sendPostRequest({
  url: '/task_delivery_config/add_or_update',
  param,
}, process.env.BASE_URI_WMD);
