import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import {
  Select,
} from 'shineout';
import SearchAreaContainer from '@search-queries/searchArea-container';
import { formatSearchData } from '@search-queries/utils';
import { defaultLimit } from './crossing.reducers';

function Header(props) {
  const {
    dataLoading,
    subWarehouseList,
    areaList,
    limit,
    levelCrossingListOptions, // 道口下拉
    categoryTypeListOptions, // 类别下拉
    configStatusOptions, // 状态下拉
    businessTypeListOptions, // 业务类型下拉
    preOccupyTypeListOptions, // 预占模式下拉
    targetParkTypeListOptions, // 目的园区下拉
    storeTypeListOptions, // 存储属性下拉
    formRef,
    store,
    operationModeOptions,
  } = props;

  return (
    <SearchAreaContainer
      clearUndefined={false}
      labelStyle={{ width: 90 }} // 统一修改label宽度，默认60
      searching={!dataLoading}
      value={limit}
      onChange={(val) => {
        if (formRef && formRef.validate) formRef.validate().catch(() => {});
        store.changeLimit(formatSearchData(defaultLimit, val));
      }}
      onClear={() => store.clearLimitData()}
      onSearch={() => {
        // 点搜索按钮，将页码重置为1
        store.handlePaginationChange({ pageNum: 1 });
      }}
      formRef={(f) => store.changeData({ formRef: f })}
    >
      <Select
        absolute
        label={t('子仓')}
        value={limit.subWarehouseId}
        data={subWarehouseList}
        keygen="id"
        format="id"
        width={200}
        renderItem="nameZh"
        onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        placeholder={t('请选择')}
        clearable
        onChange={(value) => {
          store.changeLimit({ areaId: '', subWarehouseId: value });
          store.changeData({ areaList: [] });
          if (value) {
            store.getArea({
              params: { subWarehouseId: value },
              type: 1, // 搜索栏获取库区下拉
            });
          }
        }}
      />
      <Select
        absolute
        label={t('库区')}
        value={limit.areaId}
        data={areaList}
        keygen="id"
        format="id"
        width={200}
        renderItem="area"
        onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        placeholder={t('请选择')}
        clearable
        onChange={(value) => {
          store.changeLimit({ areaId: value });
        }}
      />
      <Select
        absolute
        label={t('道口')}
        value={limit.levelCrossingList}
        data={levelCrossingListOptions}
        placeholder={t('请选择')}
        style={{ width: 200 }}
        keygen="dictCode"
        format="dictCode"
        renderItem="dictNameZh"
        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        clearable
        multiple
        compressed
        onChange={(value) => {
          store.changeLimit({ levelCrossingList: value });
        }}
      />
      <Select
        absolute
        label={t('类别')}
        value={limit.categoryTypeList}
        data={categoryTypeListOptions}
        placeholder={t('请选择')}
        width={200}
        keygen="dictCode"
        format="dictCode"
        renderItem="dictNameZh"
        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        clearable
        multiple
        compressed
        onChange={(value) => {
          store.changeLimit({ categoryTypeList: value });
        }}
      />
      <Select
        absolute
        label={t('状态')}
        value={limit.configStatus}
        data={configStatusOptions}
        placeholder={t('请选择')}
        width={200}
        keygen="dictCode"
        format="dictCode"
        renderItem="dictNameZh"
        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        clearable
        onChange={(value) => {
          store.changeLimit({ configStatus: value });
        }}
      />
      <Select
        absolute
        label={t('业务类型')}
        value={limit.businessTypeList}
        data={businessTypeListOptions}
        placeholder={t('请选择')}
        width={200}
        keygen="dictCode"
        format="dictCode"
        renderItem="dictNameZh"
        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        clearable
        multiple
        compressed
        onChange={(value) => {
          store.changeLimit({ businessTypeList: value });
        }}
      />
      <Select
        absolute
        label={t('预占模式')}
        value={limit.preOccupyTypeList}
        data={preOccupyTypeListOptions}
        placeholder={t('请选择')}
        width={200}
        keygen="dictCode"
        format="dictCode"
        renderItem="dictNameZh"
        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        clearable
        multiple
        compressed
        onChange={(value) => {
          store.changeLimit({ preOccupyTypeList: value });
        }}
      />
      <Select
        absolute
        label={t('操作模式')}
        value={limit.operatingModeList}
        data={operationModeOptions}
        placeholder={t('请选择')}
        width={200}
        keygen="dictCode"
        format="dictCode"
        renderItem="dictNameZh"
        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        clearable
        multiple
        compressed
        onChange={(value) => {
          store.changeLimit({ operatingModeList: value });
        }}
      />
      <Select
        absolute
        label={t('目的园区')}
        value={limit.targetParkTypeList}
        data={targetParkTypeListOptions}
        placeholder={t('请选择')}
        width={200}
        keygen="dictCode"
        format="dictCode"
        renderItem="dictNameZh"
        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        clearable
        multiple
        compressed
        onChange={(value) => {
          store.changeLimit({ targetParkTypeList: value });
        }}
      />
      <Select
        absolute
        label={t('存储属性')}
        value={limit.storeTypeList}
        data={storeTypeListOptions}
        placeholder={t('请选择')}
        width={200}
        keygen="id"
        format="id"
        renderItem="name"
        onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
        clearable
        multiple
        compressed
        onChange={(value) => {
          store.changeLimit({ storeTypeList: value });
        }}
      />
    </SearchAreaContainer>
  );
}

Header.propTypes = {
  dataLoading: PropTypes.number.isRequired,
  limit: PropTypes.shape().isRequired,
  store: PropTypes.shape().isRequired,
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  areaList: PropTypes.arrayOf(PropTypes.shape()),
  levelCrossingListOptions: PropTypes.arrayOf(PropTypes.shape()),
  categoryTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  configStatusOptions: PropTypes.arrayOf(PropTypes.shape()),
  businessTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  preOccupyTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  targetParkTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  storeTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  formRef: PropTypes.shape(),
  operationModeOptions: PropTypes.arrayOf(PropTypes.shape()),
};

export default Header;
