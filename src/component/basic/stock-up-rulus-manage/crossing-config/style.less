/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
  display: inline-block;
  vertical-align: top;
  margin: 16px 0;
}

.handleExportButton{
  margin-right: 6px;
}


/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect{
  width: 140px;
}

.headerUnmatchedText{
  color: #bbb;
}

.headerSearchTimeLine{
  display: flex;
}

/* list.jsx
---------------------------------------------------------------- */
.listOperationButton{
  font-size: 14px;
  line-height: 1px;
  padding: 0px;
}
.listSection{
  height: 0px; /* 必写为了子domheight - 100%有效 */
  flex: 1;
}



.compileModalCont {
  padding-top: 10px;
}
.compileModalCont>.itemWrap {
  margin-bottom: 20px;
}
.itemTitle {
  margin-bottom: 10px;
  position: relative;
  padding-left: 12px;
}
.itemTitle:before{
  content: '';
  position: absolute;
  width: 3px;
  height: 18px;
  background: #197afa;
  border-radius: 2px;
  left: 0;
}
.itemTitle:after{
  content: '';
  position: absolute;
  width: 100px;
  height: 2px;
  border-radius: 2px;
  width: 700px;
  height: 2px;
  right: 0;
  top: 10px;
  background-color: #197afaa3;
}
.itemLabel {
  display: inline-block;
  width: 100px;
  text-align: right;
}
.itemLabel:after {
  content: '*';
  color: #f00;
  margin-right: 12px;
}
.subItemLabel {
  display: inline-block;
  width: 88px;
  text-align: right;
  margin-right: 12px;
}
.radioBox {
  display: inline-block;
}
.selectWrap>div {
  display: inline-block;
  margin-top: 8px;
}

.efficiencyDetailsItem {
  display: flex;
  margin-bottom: 10px;
  align-items: center;

}

.occupationSpan {
  width: 150px;
  text-align: right;
  padding-right: 10px;
  line-height: 32px;
}
.procedureTitle {
  width: 220px;
  text-align: center;
}
.costTimeTitle {
  width: 100px;
  text-align: center;
}
