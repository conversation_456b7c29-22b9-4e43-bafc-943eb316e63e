import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { Message, Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect, getSubWarehouseSelectList, getStoreAttr } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import { selectArea } from '@src/server/basic/area';
import { crossingQueryAPI, updateCrossingConfigAPI } from '../server';

export const defaultLimit = {
  warehouseId: 1, // 默认选佛山仓
  subWarehouseId: '', // 子仓
  areaId: '', // 库区
  levelCrossingList: [], // 道口
  categoryTypeList: [], // 类别
  configStatus: '', // 状态
  businessTypeList: [], // 业务类型
  preOccupyTypeList: [], // 预占模式
  targetParkTypeList: [], // 目的园区
  storeTypeList: [], // 存储属性
};
const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100, 200], // 表格页显示条数
  },
  dataLoading: 1,
  limit: defaultLimit,
  list: [],
  warehouseList: [],
  subWarehouseList: [],
  levelCrossingListOptions: [], // 道口下拉
  categoryTypeListOptions: [], // 类别下拉
  configStatusOptions: '', // 状态下拉
  businessTypeListOptions: [], // 业务类型下拉
  preOccupyTypeListOptions: [], // 预占模式下拉
  targetParkTypeListOptions: [], // 目的园区下拉
  storeTypeListOptions: [], // 存储属性下拉
  showEditModal: false,
  isEdit: false, // 是否是编辑
  editObj: {}, // 新增、编辑数据
  modalSubWarehouseList: [], // 新增、编辑弹框里的子仓下拉
  modalAreaList: [], // 新增、编辑弹框里的库区下拉
  operationModeOptions: [],
};

export default {
  defaultState,
  // 改state数据
  changeData: (draft, data) => {
    assign(draft, data);
  },
  changeLimit: (draft, data) => {
    assign(draft.limit, data);
  },
  changeEdit: (draft, data) => {
    assign(draft.editObj, data);
  },
  // 重置条件
  reset(draft) {
    assign(
      draft.limit,
      defaultState.limit,
    );
  },
  // 初始化
  $init: (draft) => {
    assign(draft);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimit(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  * init(action, ctx) {
    const selectParam = {
      catCode: ['PREPARE_LEVEL_CROSSING', 'PREPARE_CATEGORY_TYPE', 'PREPARE_CROSSING_CONFIG_STATUS', 'PREPARE_CROSSING_BUSINESS_TYPE', 'PREPARE_CROSSING_PRE_OCCUPY_TYPE', 'PARK_ENUM',
        'PREPARE_CROSSING_OPERATING_MODE',
      ],
    };
    markStatus('dataLoading');
    const [dictData, storeAttrData] = yield Promise.all([
      dictSelect(selectParam),
      getStoreAttr({
        enabled: 1,
      }),
    ]);
    if (dictData.code === '0' && storeAttrData.code === '0') {
      yield ctx.changeData({
        levelCrossingListOptions: dictData.info.data.find((v) => v.catCode === 'PREPARE_LEVEL_CROSSING').dictListRsps || [], // 道口下拉
        categoryTypeListOptions: dictData.info.data.find((v) => v.catCode === 'PREPARE_CATEGORY_TYPE').dictListRsps || [], // 类别下拉
        configStatusOptions: dictData.info.data.find((v) => v.catCode === 'PREPARE_CROSSING_CONFIG_STATUS').dictListRsps || [], // 状态下拉
        businessTypeListOptions: dictData.info.data.find((v) => v.catCode === 'PREPARE_CROSSING_BUSINESS_TYPE').dictListRsps || [], // 业务类型下拉
        preOccupyTypeListOptions: dictData.info.data.find((v) => v.catCode === 'PREPARE_CROSSING_PRE_OCCUPY_TYPE').dictListRsps || [], // 预占模式下拉
        targetParkTypeListOptions: dictData.info.data.find((v) => v.catCode === 'PARK_ENUM').dictListRsps || [], // 目的园区下拉
        storeTypeListOptions: storeAttrData.info.data || [], // 存储属性下拉
        operationModeOptions: dictData.info.data.find((v) => v.catCode === 'PREPARE_CROSSING_OPERATING_MODE').dictListRsps || [], // 目的园区下拉
      });
    } else {
      handleListMsg([dictData, storeAttrData], false);
    }
    // 获取子仓下拉数据
    const { warehouseId, currentWarehouseList, subWarehouseList } = yield 'nav';
    yield ctx.changeData({
      warehouseIdList: currentWarehouseList,
      subWarehouseList,
    });
    yield this.changeLimit({
      warehouseId,
    });
  },
  // 选择主仓获取子仓下拉
  * getSubWarehouse(action, ctx) {
    markStatus('dataLoading');
    const res = yield getSubWarehouseSelectList({ ...action.params, enabled: 1 });
    if (res.code === '0') {
      yield ctx.changeData({
        modalSubWarehouseList: res.info.data || [],
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 获取库区下拉数据
  * getArea(action) {
    markStatus('dataLoading');
    const data = yield selectArea({ ...action.params, automateArea: 1 });
    if (data.code === '0') {
      // 获取弹框库区下拉
      if (action.type === 2) {
        yield this.changeData({
          modalAreaList: data.info.data,
        });
      } else {
        // 获取搜索栏库区下拉
        yield this.changeData({
          areaList: data.info.data,
        });
      }
    } else {
      Modal.error({ title: data.msg });
    }
  },
  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { warehouseId, currentWarehouseList, subWarehouseList } = data;
    yield this.changeData({
      warehouseIdList: currentWarehouseList,
      subWarehouseList,
      areaList: [],
    });
    yield this.changeLimit({
      warehouseId,
      subWarehouseId: '',
      areaId: '',
    });
  },
  // 道口配置列表查询
  * crossingQuery(action, ctx) {
    markStatus('dataLoading');
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    const res = yield crossingQueryAPI(param);
    if (res.code !== '0') {
      Modal.error({
        title: res.msg,
      });
    } else {
      yield ctx.changeData({
        list: res.info.data,
        pageInfo: {
          ...pageInfo,
          count: res.info.meta.count,
        },
      });
    }
  },
  // 道口更新
  * updateCrossingConfig(action, ctx) {
    markStatus('dataLoading');
    const {
      editObj, businessTypeListOptions, preOccupyTypeListOptions, targetParkTypeListOptions, storeTypeListOptions,
    } = yield '';
    const param = {
      ...editObj,
      businessTypeList: (editObj.businessTypeList || []).some((item) => item === 'all') ? businessTypeListOptions.map((i) => (i.dictCode)) : editObj.businessTypeList, // 业务类型 不选视为全选
      preOccupyTypeList: (editObj.preOccupyTypeList || []).some((item) => item === 'all') ? preOccupyTypeListOptions.map((i) => (i.dictCode)) : editObj.preOccupyTypeList, // 预占模式 不选视为全选
      targetParkTypeList: (editObj.targetParkTypeList || []).some((item) => item === 'all') ? targetParkTypeListOptions.map((i) => (i.dictCode)) : editObj.targetParkTypeList, // 目的园区 不选视为全选
      storeTypeList: (editObj.storeTypeList || []).some((item) => item === 'all') ? storeTypeListOptions.map((i) => (i.id)) : editObj.storeTypeList, // 存储属性 不选视为全选
    };
    const res = yield updateCrossingConfigAPI(param);
    if (res.code !== '0') {
      Modal.error({
        title: res.msg,
      });
    } else {
      yield ctx.changeData({
        showEditModal: false,
      });
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    }
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.crossingQuery();
  },
};
