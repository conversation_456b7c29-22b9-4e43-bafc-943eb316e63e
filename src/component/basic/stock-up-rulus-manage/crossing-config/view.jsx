import React, { useEffect } from 'react';
import { useStore } from 'rrc-loader-helper';
import ContainerPage from '@search-queries/container';
import { TopAreaHeight } from '@src/lib/constants';
import crossingReducers from './crossing.reducers';
import Header from './header';
import List from './list';
import Handle from './handle';

function View(props) {
  const [crossingState, crossingStore] = useStore(crossingReducers, true);
  useEffect(() => {
    crossingStore.init();
  }, []);
  return (
    <ContainerPage customStyle={{ height: `calc(100vh - ${TopAreaHeight}px - 92px)` }} warehouseChange={(data) => crossingStore.warehouseChange(data)}>
      <Header {...props} {...crossingState} store={crossingStore} />
      <Handle {...props} {...crossingState} store={crossingStore} />
      <List {...props} {...crossingState} store={crossingStore} />
    </ContainerPage>
  );
}

export default View;
