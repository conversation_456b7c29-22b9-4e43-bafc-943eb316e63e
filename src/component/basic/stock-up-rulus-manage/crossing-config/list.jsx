import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import { Button, Table } from 'shineout';
import { handleTablePros } from '@src/lib/deal-func';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import globalStyles from '@src/component/style.less';
import Icon from '@shein-components/Icon';

function List(props) {
  const {
    dataLoading,
    list,
    pageInfo,
    store,
    recordVisible,
    recordId,
  } = props;
  /**
   * 展开收缩
   * @param row 行数据
   * @param specifiedField 该列字段
   * @returns {JSX.Element|string|*}
   */
  const showMore = (row, specifiedField) => {
    const specifiedFieldContent = row[specifiedField];
    if (specifiedFieldContent) {
      const contentToArray = specifiedFieldContent.split(/[,，]/) || [];
      if (contentToArray.length > 3) {
        if (row[`showMoreFlag_${specifiedField}`]) {
          return (
            <div>
              {specifiedFieldContent}
              <Button
                size="small"
                text
                type="primary"
                className={globalStyles.listOperationButton}
                style={{ paddingLeft: '10px', paddingBottom: '2px' }}
                onClick={() => {
                  store.changeData({
                    list: list.map((li) => {
                      if (li.id === row.id) {
                        return { ...li, [`showMoreFlag_${specifiedField}`]: false };
                      }
                      return li;
                    }),
                  });
                }}
              >
                {t('收起')}
                <Icon name="arr-up" />
              </Button>
            </div>
          );
        }
        return (
          <div>
            {contentToArray.slice(0, 3).join(',')}
            <Button
              size="small"
              text
              type="primary"
              style={{ paddingLeft: '10px', paddingBottom: '2px' }}
              className={globalStyles.listOperationButton}
              onClick={() => {
                store.changeData({
                  list: list.map((li) => {
                    if (li.id === row.id) {
                      return { ...li, [`showMoreFlag_${specifiedField}`]: true };
                    }
                    return li;
                  }),
                });
              }}
            >
              {t('更多')}
              <Icon name="arr-down" />
            </Button>
          </div>
        );
      }
      return specifiedFieldContent;
    }
    return t('无');
  };

  const columns = [
    {
      title: t('仓库'),
      width: 120,
      render: 'warehouseName',
    },
    {
      title: t('所属子仓'),
      width: 140,
      render: 'subWarehouseName',
    },
    {
      title: t('所属库区'),
      width: 140,
      render: 'areaName',
    },
    {
      title: t('道口'),
      width: 160,
      align: 'center',
      render: (row) => (
        <Button
          size="small"
          text
          type="primary"
          style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
          onClick={() => {
            store.changeData({ showEditModal: true, isEdit: true, editObj: row || {} });
            // 获取弹框子仓下拉
            if (row.warehouseId) {
              store.getSubWarehouse({
                params: { warehouseId: row.warehouseId },
              });
            }
            // 获取弹框库区下拉
            if (row.subWarehouseId) {
              store.getArea({
                params: { subWarehouseId: row.subWarehouseId },
                type: 2,
              });
            }
          }}
        >
          {row.levelCrossingName}
        </Button>
      ),
    },
    {
      title: t('类别'),
      width: 200,
      render: 'categoryTypeName',
    },
    {
      title: t('任务类型（操作模式）'),
      width: 200,
      render: 'businessTypeName',
    },
    {
      title: t('预占模式'),
      width: 200,
      render: 'preOccupyTypeName',
    },
    {
      title: t('目的园区'),
      width: 300,
      render: (r) => (showMore(r, 'targetParkTypeName')),
    },
    {
      title: t('存储属性'),
      width: 300,
      render: (r) => (showMore(r, 'storeTypeName')),
    },
    {
      title: t('状态'),
      width: 120,
      render: 'configStatusName',
    },
    {
      title: t('操作'),
      width: 120,
      align: 'center',
      fixed: 'right',
      render: (row) => (
        <Button
          size="small"
          text
          type="primary"
          className={globalStyles.tableTextButton}
          onClick={() => store.changeData({ recordVisible: true, recordId: row.id })}
        >
          {t('操作日志')}
        </Button>
      ),
    },
  ];
  return (
    <section className={globalStyles.tableSection}>
      <SearchAreaTable>
        <Table
          {...handleTablePros(columns)}
          loading={!dataLoading}
          keygen="id"
          data={list}
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              // handlePaginationChange(page, size);
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            className: globalStyles.pagination,
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
      </SearchAreaTable>
      {/* 操作记录 */}
      <OperationModal
        visible={recordVisible}
        param={{
          operateId: recordId,
          operateCode: 'PREPARE_LEVEL_CROSSING_CONFIG',
        }}
        onCancel={() => store.changeData({ recordVisible: false })}
      />
    </section>
  );
}

List.propTypes = {
  dataLoading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape).isRequired,
  pageInfo: PropTypes.shape(),
  store: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  recordId: PropTypes.number,
};

export default List;
