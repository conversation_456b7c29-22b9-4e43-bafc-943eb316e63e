import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Select, Form, Rule,
} from 'shineout';
import globalStyles from '@src/component/style.less';
import { t } from '@shein-bbl/react';
import style from './style.less';

const rules = Rule();

class Handle extends React.Component {
  render() {
    const {
      dataLoading,
      store,
      showEditModal,
      editObj,
      isEdit,
      warehouseIdList,
      modalSubWarehouseList,
      modalAreaList,
      levelCrossingListOptions, // 道口下拉
      categoryTypeListOptions, // 类别下拉
      configStatusOptions, // 状态下拉
      businessTypeListOptions, // 业务类型下拉
      preOccupyTypeListOptions, // 预占模式下拉
      targetParkTypeListOptions, // 目的园区下拉
      storeTypeListOptions, // 存储属性下拉
      operationModeOptions,
    } = this.props;
    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          loading={!dataLoading}
          onClick={() => {
            store.changeData({
              showEditModal: true,
              isEdit: false,
              editObj: {
                businessTypeList: ['all'], // 业务类型 默认全选
                preOccupyTypeList: ['all'], // 预占模式 默认全选
                targetParkTypeList: ['all'], // 目的园区 默认全选
                storeTypeList: ['all'], // 存储属性 默认全选
              },
              modalSubWarehouseList: [],
              modalAreaList: [],
            });
          }}
        >
          {t('新增')}
        </Button>
        <Modal
          key={showEditModal}
          visible={showEditModal}
          maskCloseAble={false}
          onClose={() => store.changeData({ showEditModal: false })}
          width={820}
          title={isEdit ? t('编辑') : t('新增')}
          bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ showEditModal: false })}>{t('取消')}</Button>
              <Modal.Submit disabled={!dataLoading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={120}
            labelAlign="right"
            onChange={(value) => {
              store.changeData({
                editObj: value,
              });
            }}
            onSubmit={() => {
              store.updateCrossingConfig();
            }}
            value={editObj}
          >
            <div className={style.compileModalCont}>
              <div className={style.itemWrap}>
                <div className={style.selectWrap}>
                  <div>
                    <Form.Item required label={t('仓库')}>
                      <Select
                        absolute
                        disabled={isEdit}
                        name="warehouseId"
                        placeholder={t('请选择')}
                        width={200}
                        data={warehouseIdList}
                        format="id"
                        keygen="id"
                        renderItem="nameZh"
                        clearable
                        onFilter={(text) => (d) => d.nameZh.indexOf(text) >= 0}
                        rules={[rules.required()]}
                        onChange={(value) => {
                          store.changeEdit({ warehouseId: value, subWarehouseId: '', areaId: '' });
                          store.changeData({ modalSubWarehouseList: [], modalAreaList: [] });
                          if (value) {
                            store.getSubWarehouse({
                              params: { warehouseId: value },
                            });
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                  <div>
                    <Form.Item required label={t('所属子仓')}>
                      <Select
                        absolute
                        name="subWarehouseId"
                        data={modalSubWarehouseList}
                        keygen="id"
                        format="id"
                        width={200}
                        renderItem="nameZh"
                        onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        placeholder={t('请选择')}
                        clearable
                        rules={[rules.required()]}
                        onChange={(value) => {
                          store.changeEdit({ subWarehouseId: value, areaId: '' });
                          store.changeData({ modalAreaList: [] });
                          if (value) {
                            store.getArea({
                              params: { subWarehouseId: value },
                              type: 2, // 弹框获取库区下拉
                            });
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                </div>
                <div className={style.selectWrap}>
                  <div>
                    <Form.Item required label={t('库区')}>
                      <Select
                        absolute
                        name="areaId"
                        data={modalAreaList}
                        keygen="id"
                        format="id"
                        width={200}
                        renderItem="area"
                        rules={[rules.required()]}
                        onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        placeholder={t('请选择')}
                        clearable
                      />
                    </Form.Item>
                  </div>
                  <div>
                    <Form.Item required label={t('道口')}>
                      <Select
                        absolute
                        name="levelCrossing"
                        data={levelCrossingListOptions}
                        placeholder={t('请选择')}
                        style={{ width: 200 }}
                        keygen="dictCode"
                        format="dictCode"
                        renderItem="dictNameZh"
                        rules={[rules.required()]}
                        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        clearable
                      />
                    </Form.Item>
                  </div>
                </div>
                <div className={style.selectWrap}>
                  <div>
                    <Form.Item required label={t('类别')}>
                      <Select
                        absolute
                        name="categoryTypeList"
                        data={categoryTypeListOptions}
                        placeholder={t('请选择')}
                        width={200}
                        keygen="dictCode"
                        format="dictCode"
                        renderItem="dictNameZh"
                        rules={[rules.required()]}
                        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        clearable
                        multiple
                        compressed
                      />
                    </Form.Item>
                  </div>
                  <div>
                    <Form.Item label={t('预占模式')}>
                      <Select
                        absolute
                        name="preOccupyTypeList"
                        data={[{ dictCode: 'all', dictNameZh: t('全选') }, ...preOccupyTypeListOptions]}
                        placeholder={t('请选择')}
                        width={200}
                        keygen="dictCode"
                        format="dictCode"
                        renderItem="dictNameZh"
                        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        clearable
                        multiple
                        compressed
                        onChange={(val) => {
                          if (val.some((i) => i === 'all')) {
                            store.changeEdit({ preOccupyTypeList: ['all'] });
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                </div>
                <div className={style.selectWrap}>
                  <div>
                    <Form.Item label={t('任务类型')}>
                      <Select
                        absolute
                        name="businessTypeList"
                        data={[{ dictCode: 'all', dictNameZh: t('全选') }, ...businessTypeListOptions]}
                        placeholder={t('请选择')}
                        width={200}
                        keygen="dictCode"
                        format="dictCode"
                        renderItem="dictNameZh"
                        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        clearable
                        multiple
                        compressed
                        onChange={(val) => {
                          store.changeEdit({
                            dailyReplenishmentTypeList: [],
                            returnDownShelfTypeList: [],
                          });
                          if (val.some((i) => i === 'all')) {
                            store.changeEdit({ businessTypeList: ['all'] });
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                  <div>
                    <Form.Item label={t('目的园区')}>
                      <Select
                        absolute
                        name="targetParkTypeList"
                        data={[{ dictCode: 'all', dictNameZh: t('全选') }, ...targetParkTypeListOptions]}
                        placeholder={t('请选择')}
                        width={200}
                        keygen="dictCode"
                        format="dictCode"
                        renderItem="dictNameZh"
                        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        clearable
                        multiple
                        compressed
                        onChange={(val) => {
                          if (val.some((i) => i === 'all')) {
                            store.changeEdit({ targetParkTypeList: ['all'] });
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                </div>
                {
                  ['all', '1', '6'].some((key) => editObj.businessTypeList?.find((type) => key === `${type}`)) && (
                    <div className={style.selectWrap}>
                      <div>
                        <Form.Item label={t('操作类型')} required>
                          {['all', '1'].some((key) => editObj.businessTypeList?.find((type) => key === `${type}`)) && (
                            <Form.Field name="dailyReplenishmentTypeList" rules={[rules.required()]}>
                              {({ value, onChange }) => (
                                <div style={{ display: 'inline-flex', flexDirection: 'row', alignItems: 'center' }}>
                                  <span>{t('日常补货')}</span>
                                  <Select
                                    absolute
                                    value={value}
                                    data={operationModeOptions}
                                    placeholder={t('请选择')}
                                    width={150}
                                    style={{ marginLeft: 10 }}
                                    keygen="dictCode"
                                    format="dictCode"
                                    renderItem="dictNameZh"
                                    onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                                    clearable
                                    multiple
                                    compressed
                                    onChange={(val) => {
                                      onChange(val);
                                    }}
                                  />
                                </div>
                              )}
                            </Form.Field>
                          )}
                          {['all', '6'].some((key) => editObj.businessTypeList?.find((type) => key === `${type}`)) && (
                            <Form.Field name="returnDownShelfTypeList" rules={[rules.required()]}>
                              {({ value, onChange }) => (
                                <div style={{
                                  display: 'inline-flex', flexDirection: 'row', alignItems: 'center', marginLeft: 40,
                                }}
                                >
                                  <span>{t('回货下架')}</span>
                                  <Select
                                    absolute
                                    value={value}
                                    data={operationModeOptions}
                                    placeholder={t('请选择')}
                                    width={150}
                                    style={{ marginLeft: 10 }}
                                    keygen="dictCode"
                                    format="dictCode"
                                    renderItem="dictNameZh"
                                    onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                                    clearable
                                    multiple
                                    compressed
                                    onChange={(val) => {
                                      onChange(val);
                                    }}
                                  />
                                </div>
                              )}
                            </Form.Field>
                          )}

                        </Form.Item>
                      </div>
                    </div>
                  )
                }

                <div className={style.selectWrap}>
                  <div>
                    <Form.Item label={t('存储属性')}>
                      <Select
                        absolute
                        name="storeTypeList"
                        data={[{ id: 'all', name: t('全选') }, ...storeTypeListOptions]}
                        placeholder={t('请选择')}
                        width={200}
                        keygen="id"
                        format="id"
                        renderItem="name"
                        onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        clearable
                        multiple
                        compressed
                        onChange={(val) => {
                          if (val.some((i) => i === 'all')) {
                            store.changeEdit({ storeTypeList: ['all'] });
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                  <div>
                    <Form.Item required label={t('状态')}>
                      <Select
                        absolute
                        name="configStatus"
                        data={configStatusOptions}
                        placeholder={t('请选择')}
                        width={200}
                        keygen="dictCode"
                        format="dictCode"
                        renderItem="dictNameZh"
                        rules={[rules.required()]}
                        onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                        clearable
                      />
                    </Form.Item>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  dataLoading: PropTypes.number,
  store: PropTypes.shape(),
  showEditModal: PropTypes.bool.isRequired,
  editObj: PropTypes.shape().isRequired,
  isEdit: PropTypes.bool.isRequired,
  warehouseIdList: PropTypes.arrayOf(PropTypes.shape()),
  modalSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  modalAreaList: PropTypes.arrayOf(PropTypes.shape()),
  levelCrossingListOptions: PropTypes.arrayOf(PropTypes.shape()),
  categoryTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  configStatusOptions: PropTypes.arrayOf(PropTypes.shape()),
  businessTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  preOccupyTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  targetParkTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  storeTypeListOptions: PropTypes.arrayOf(PropTypes.shape()),
  operationModeOptions: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
