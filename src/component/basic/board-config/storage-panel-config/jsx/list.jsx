import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Modal } from 'shineout';
import OperationModal from '@public-component/modal/operation-modal';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store, { EDIT_MODAL_EDIT } from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordVisible,
      recordId,
      longitudeList,
      latitudeList,
    } = this.props;

    const columns = [
      {
        title: t('点位ID'),
        render: 'id',
        width: 200,
      },
      {
        title: t('点位名称'),
        render: 'pointName',
        width: 140,
      },
      {
        title: t('看板维度'),
        render: 'dimensionName',
        width: 140,
      },
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 140,
      },
      {
        title: t('片区'),
        render: 'regionName',
        width: 120,
      },
      {
        title: t('园区'),
        render: 'parkName',
        width: 120,
      },
      {
        title: t('经度'),
        render: (record) => {
          const symbol = longitudeList.find((item) => item.dictCode === record.longitudeSymbol)?.dictNameZh || '';
          return <span>{`${record.longitude}°${record.longitudeMinute}’${symbol}`}</span>;
        },
        width: 120,
      },
      {
        title: t('纬度'),
        render: (record) => {
          const symbol = latitudeList.find((item) => item.dictCode === record.latitudeSymbol)?.dictNameZh || '';
          return <span>{`${record.latitude}°${record.latitudeMinute}’${symbol}`}</span>;
        },
        width: 120,
      },
      {
        title: t('最后更新时间'),
        render: 'lastUpdateTime',
        width: 180,
      },
      {
        title: t('更新人'),
        render: 'operator',
        width: 180,
      },
      {
        title: t('操作'),
        width: 200,
        fixed: 'right',
        render: (record) => (
          <>
            <Button
              size="small"
              text
              type="primary"
              onClick={() => store.handleModalOpenOrClose({
                record,
                modalType: EDIT_MODAL_EDIT,
              })}
            >
              {t('编辑')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              onClick={() => {
                Modal.confirm({
                  title: t('是否删除该数据?'),
                  onOk: () => {
                    store.handleDelete({
                      id: record.id,
                    });
                  },
                  text: { ok: t('确认'), cancel: t('取消') },
                });
              }}
            >
              {t('删除')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.id,
              })}
            >
              {t('操作日志')}
            </Button>
          </>

        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 跟以前接口地址不一样 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'CAPACITY_POINT_OPERATION',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  recordId: PropTypes.string,
  longitudeList: PropTypes.arrayOf(PropTypes.shape()),
  latitudeList: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
