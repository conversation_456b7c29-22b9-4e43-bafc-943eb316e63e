import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Select, Input,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store, {
  EDIT_MODAL_HIDE, EDIT_MODAL_ADD, EDIT_MODAL_EDIT,
} from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
      modalInfo,
      modalType,
      dimensionList, // 看板维度列表
      warehouseList, // 仓库列表
      editRegionList, // 片区列表
      editParkList, // 园区列表
      longitudeList, // 经度列表
      latitudeList, // 纬度列表
      modalLoading,
    } = this.props;

    const isDisabled = () => {
      const keys = ['pointName', 'dimension', 'warehouseId', 'longitude', 'longitudeSymbol', 'latitude', 'latitudeSymbol', 'longitudeMinute', 'latitudeMinute'];
      if (modalInfo.dimension === 2) keys.push('region');
      if (modalInfo.dimension === 3) keys.push(...['region', 'park']);

      const isExistNull = keys.some((key) => [null, undefined, ''].includes(modalInfo[key]));

      return isExistNull;
    };

    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => store.handleModalOpenOrClose({ modalType: EDIT_MODAL_ADD })}
        >
          {t('新增')}
        </Button>
        <Modal
          visible={!!modalType}
          title={modalType === EDIT_MODAL_ADD ? t('新增') : t('编辑')}
          width={650}
          maskCloseAble={false}
          onClose={() => store.handleModalOpenOrClose({ modalType: EDIT_MODAL_HIDE })}
          footer={[
            <Button
              key="cancel"
              onClick={() => store.handleModalOpenOrClose({ modalType: EDIT_MODAL_HIDE })}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              key="save"
              loading={!modalLoading}
              disabled={isDisabled()}
              onClick={() => store.handleRecordUpdate()}
            >
              {t('保存')}
            </Button>,
          ]}
        >
          <Form
            labelWidth="100px"
            labelAlign="right"
            value={modalInfo}
            formRef={(f) => store.changeData({ modalRef: f })}
            onChange={(val) => store.changeModalInfo(val)}
          >
            <Form.Item required label={t('点位名称')}>
              <Input
                label={t('点位名称')}
                name="pointName"
                required
                placeholder={t('请输入')}
                maxLength={20}
                style={{ width: 240 }}
                clearable
              />
            </Form.Item>

            <Form.Item required label={t('看板维度')}>
              <Select
                label={t('看板维度')}
                name="dimension"
                data={dimensionList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                disabled={modalType === EDIT_MODAL_EDIT}
                onChange={(val) => store.handleDemenssionChange({ dimension: val })}
                style={{ width: 240 }}
                clearable
              />
            </Form.Item>

            <Form.Item required label={t('仓库')}>
              <Select
                label={t('仓库')}
                name="warehouseId"
                data={warehouseList}
                keygen="id"
                format="id"
                renderItem="nameZh"
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(val) => store.handleRegionQuery({ warehouseId: val })}
                disabled={modalType === EDIT_MODAL_EDIT}
                style={{ width: 240 }}
              />
            </Form.Item>

            {
              [2, 3].includes(modalInfo.dimension) && (
                <Form.Item required label={t('片区')}>
                  {/* 看板维度为【全球-仓库】时不展示 */}
                  <Select
                    label={t('片区')}
                    name="region"
                    data={editRegionList}
                    keygen="region"
                    format="region"
                    renderItem="regionName"
                    renderUnmatched={(r) => r?.regionName || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                    onFilter={(text) => (d) => d?.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    disabled={modalType === EDIT_MODAL_EDIT}
                    onChange={(val) => store.handleParkListQuery({ region: val })}
                    style={{ width: 240 }}
                  />
                </Form.Item>
              )
            }

            {
              modalInfo.dimension === 3 && (
                <Form.Item required label={t('园区')}>
                  {/* 看板维度仅为【园区-片区】才展示 */}
                  <Select
                    label={t('园区')}
                    name="park"
                    data={editParkList}
                    keygen="parkType"
                    format="parkType"
                    renderItem="parkName"
                    renderUnmatched={(r) => r?.parkName || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                    onFilter={(text) => (d) => d?.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    disabled={modalType === EDIT_MODAL_EDIT}
                    style={{ width: 240 }}
                  />
                </Form.Item>
              )
            }

            <div style={{ display: 'flex' }}>
              <Form.Item required label={t('经度')}>
                <span style={{ marginRight: 10 }}>
                  <Input.Number
                    name="longitude"
                    required
                    placeholder={t('请输入')}
                    min={0}
                    max={180}
                    digits={0}
                    style={{ width: 100 }}
                  />
                  {t('度')}
                </span>
                <span>
                  <Input.Number
                    name="longitudeMinute"
                    required
                    placeholder={t('请输入')}
                    min={0}
                    max={59}
                    digits={0}
                    style={{ width: 100 }}
                  />
                  {t('分')}
                </span>
              </Form.Item>
              <Form.Item required label={t('符号')}>
                <Select
                  label={t('符号')}
                  name="longitudeSymbol"
                  data={longitudeList}
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  renderUnmatched={(r) => r?.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                  onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase()?.indexOf(text.toLowerCase()) >= 0}
                  style={{ width: 100 }}
                />
              </Form.Item>

            </div>
            <div style={{ display: 'flex' }}>
              <Form.Item required label={t('纬度')}>
                <span style={{ marginRight: 10 }}>
                  <Input.Number
                    name="latitude"
                    required
                    placeholder={t('请输入')}
                    style={{ width: 100 }}
                    min={0}
                    max={90}
                    digits={0}
                  />
                  {t('度')}
                </span>
                <span>
                  <Input.Number
                    name="latitudeMinute"
                    required
                    placeholder={t('请输入')}
                    style={{ width: 100 }}
                    min={0}
                    max={59}
                    digits={0}
                  />
                  {t('分')}
                </span>
              </Form.Item>
              <Form.Item required label={t('符号')}>
                <Select
                  label={t('符号')}
                  name="latitudeSymbol"
                  data={latitudeList}
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  renderUnmatched={(r) => r?.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                  onFilter={(text) => (d) => d?.dictNameZh?.toLowerCase()?.indexOf(text.toLowerCase()) >= 0}
                  style={{ width: 100 }}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalInfo: PropTypes.shape(),
  modalType: PropTypes.number,
  modalLoading: PropTypes.number,
  dimensionList: PropTypes.arrayOf(PropTypes.shape()), // 看板维度列表
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  editRegionList: PropTypes.arrayOf(PropTypes.shape()), // 片区列表
  editParkList: PropTypes.arrayOf(PropTypes.shape()), // 园区列表
  longitudeList: PropTypes.arrayOf(PropTypes.shape()),
  latitudeList: PropTypes.arrayOf(PropTypes.shape()),
};

export default Handle;
