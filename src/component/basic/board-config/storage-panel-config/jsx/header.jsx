import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select, Input } from 'shineout';
import { formatSearchData } from '@public-component/search-queries/utils';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      dimensionList, // 看板维度列表
      regionList, // 片区列表
      searchParkList, // 园区列表
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Input
            label={t('点位名称')}
            name="pointName"
            required
            placeholder={t('请输入')}
            clearable
          />
          <Select
            label={t('看板维度')}
            name="dimensions"
            data={dimensionList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compress
          />
          <Select
            label={t('片区')}
            name="regions"
            data={regionList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compress
          />
          <Select
            label={t('园区')}
            name="parks"
            data={searchParkList}
            keygen="parkType"
            format="parkType"
            renderItem="parkName"
            placeholder={t('全部')}
            renderUnmatched={(r) => r.parkName || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compress
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  dimensionList: PropTypes.arrayOf(PropTypes.shape()), // 看板维度列表
  regionList: PropTypes.arrayOf(PropTypes.shape()), // 片区列表
  searchParkList: PropTypes.arrayOf(PropTypes.shape()), // 园区列表
};
export default Header;
