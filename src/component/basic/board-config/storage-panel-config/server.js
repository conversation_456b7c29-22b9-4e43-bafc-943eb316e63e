import { sendPostRequest } from '@src/server/common/public';

// 新增
export const createAPI = (param) => sendPostRequest({
  url: '/capacity_point/create',
  param,
}, process.env.WKB_FRONT);

// 编辑
export const editAPI = (param) => sendPostRequest({
  url: '/capacity_point/edit',
  param,
}, process.env.WKB_FRONT);

// 删除
export const deleteAPI = (param) => sendPostRequest({
  url: '/capacity_point/delete',
  param,
}, process.env.WKB_FRONT);

// 查询
export const queryAPI = (param) => sendPostRequest({
  url: '/capacity_point/query',
  param,
}, process.env.WKB_FRONT);
