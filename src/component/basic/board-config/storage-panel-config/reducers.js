import { markStatus } from 'rrc-loader-helper';
import { t } from '@shein-bbl/react';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { queryParkList } from '@src/lib/dealFunc';
import { dictSelect } from '@src/server/basic/dictionary';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import { queryRegionAPI, getRegionAPI } from '@src/server/common/common';
import {
  queryAPI,
  createAPI,
  deleteAPI,
  editAPI,
} from './server';

export const EDIT_MODAL_HIDE = 0; // 不可见
export const EDIT_MODAL_EDIT = 1; // 编辑
export const EDIT_MODAL_ADD = 2; // 新增

// 搜索默认值
export const defaultLimit = {
  pointName: '', // 点位名称
  dimensions: [], // 看板维度
  regions: [], // 片区
  parks: [], // 园区
};

export const defaultModalInfo = {
  id: '',
  pointName: '', // 点位名称
  dimension: '', // 看板维度
  warehouseId: '', // 仓库
  region: null, // 片区
  park: null, // 园区
  longitude: null, // 经度
  longitudeSymbol: '', // 经度符号
  longitudeMinute: null,
  latitude: null, // 纬度
  latitudeMinute: null,
  latitudeSymbol: '', // 纬度符号
};

// 其他默认值
export const defaultState = {
  formRef: {},
  loading: 1, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  list: [],
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  warehouseId: '', // 仓库id
  dimensionList: [], // 看板维度列表
  regionList: [], // 片区列表
  searchParkList: [], // 园区列表
  // 编辑、新增
  modalRef: {},
  modalLoading: 1,
  modalType: EDIT_MODAL_HIDE, // modal 是否可见
  warehouseList: [], // 仓库列表
  editRegionList: [], // 片区列表 - 编辑
  editParkList: [], // 园区列表 - 编辑
  longitudeList: [
    { dictCode: 0, dictNameZh: 'E' },
    { dictCode: 1, dictNameZh: 'W' },
  ], // 经度符号列表
  latitudeList: [
    { dictCode: 2, dictNameZh: 'N' },
    { dictCode: 3, dictNameZh: 'S' },
  ], // 纬度符号列表
  modalInfo: defaultModalInfo,
};

export default {
  state: defaultState,
  // 改变state的值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  // 修改编辑数据
  changeModalInfo(state, data) {
    Object.assign(state.modalInfo, data);
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  /**
   * 初始化数据
   */
  * init(_, ctx) {
    const { warehouseId, warehouseList } = yield 'nav';
    yield ctx.handleWarehouseChange({ warehouseId });

    yield ctx.changeData({
      warehouseList,
    });

    const { info, code, msg } = yield dictSelect({ catCode: ['REGION_ENUM', 'CAPACITY_POINT_DIMENSION'] });
    if (code === '0') {
      yield ctx.changeData({
        regionList: info.data.find((v) => v.catCode === 'REGION_ENUM').dictListRsps,
        dimensionList: info.data.find((v) => v.catCode === 'CAPACITY_POINT_DIMENSION').dictListRsps,
      });
    } else {
      Modal.error({
        title: msg || t('请求失败,请稍后再试'),
        autoFocusButton: 'ok',
      });
    }
  },
  /**
   * 搜索操作
   */
  * search() {
    const { limit, pageInfo, warehouseId } = yield '';

    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
    };

    // 仓库为必填
    if (!warehouseId) {
      Modal.error({ title: t('请先选择仓库') });
      return;
    }
    markStatus('loading');
    const { code, info, msg } = yield queryAPI(clearEmpty(paramTrim(param), [0, '0', false]));
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  // 关闭或打开弹框
  * handleModalOpenOrClose({ modalType, record = defaultModalInfo }, ctx) {
    // 编辑 - 额外获取园区下拉列表
    if (modalType === EDIT_MODAL_EDIT) {
      if (record.warehouseId) {
        yield ctx.handleRegionQuery({ warehouseId: record.warehouseId });
      }
      if (![null, undefined, ''].includes(record.region)) {
        yield ctx.handleParkListQuery({ region: record.region });
      }
    }

    yield this.changeData({
      modalType,
      modalInfo: record,
    });
  },
  // 编辑或者更新
  * handleRecordUpdate() {
    const { modalType, modalInfo } = yield '';
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }

    markStatus('modalLoading');
    const requestAPI = modalType === EDIT_MODAL_EDIT ? editAPI : createAPI;
    const { code, msg } = yield requestAPI(modalInfo);
    if (code === '0') {
      Message.success(modalType === EDIT_MODAL_EDIT ? t('编辑成功') : t('新增成功'));
      yield this.handleModalOpenOrClose({ modalType: EDIT_MODAL_HIDE });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  // 删除数据
  * handleDelete({ id }) {
    markStatus('loading');
    const { code, msg } = yield deleteAPI({ ids: [id] });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  // 页签变化 - 重新搜索
  * handlePaginationChange(arg = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 看板维度变化
  * handleDemenssionChange({ dimension }) {
    const { modalInfo } = yield '';
    yield this.changeModalInfo({
      dimension,
      park: dimension !== 3 ? null : modalInfo.park,
      region: dimension === 1 ? null : modalInfo.region,
    });
  },
  // 仓库变化 - 片区重新处理
  * handleRegionQuery({ warehouseId }, ctx) {
    yield ctx.changeModalInfo({
      park: null,
      region: null,
      warehouseId,
    });
    const { code, msg, info } = yield queryRegionAPI({ warehouseId }); // 获取片区列表

    if (code === '0') {
      yield this.changeData({
        editParkList: [], // 园区置空
        editRegionList: info?.filter((item) => item.region !== 0) || [],
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
      yield this.changeData({
        editRegionList: [],
        editParkList: [],
      });
    }
  },
  // 获取园区列表
  * handleParkListQuery({ region }, ctx) {
    yield ctx.changeModalInfo({
      region,
      park: null,
    });
    // 获取园区列表
    const { code, info, msg } = yield getRegionAPI({ region });
    if (code === '0') {
      yield this.changeData({
        editParkList: info.parkInfos,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 仓库变化
  * handleWarehouseChange(data) {
    const { warehouseId } = data;
    // 更新园区列表
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      searchParkList: parkList,
      warehouseId,
    });

    // 清空园区选项
    yield this.changeLimitData({
      parks: [],
    });
  },
};
