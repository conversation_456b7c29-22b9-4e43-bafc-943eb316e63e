import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { t } from '@shein-bbl/react';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import { getListAPI, modifyAPI, deleteAPI } from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  systemTypeList: [], // 系统名称
  menuStatus: [], // 状态
};

const defaultState = {
  editFormRef: {},
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域

  editAddModalVisible: false,
  editAddModalObj: {
    id: '',
    systemType: '', // 系统名称
    systemUrl: '', // 系统链接
    priority: null, // 优先级
    imageUrl: '', // 图片
    menuStatus: '',
  },
  fileList: [],
  systemList: [], // 系统下拉框
  isEdit: false,
  imageCropperObj: { // 图片剪裁用到的参数
    crop: { // 默认选中全部
      aspect: 16 / 9,
      unit: '%',
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    },
    src: '',
    blobSrc: '', // blob:url
  },
  uploadImageTmpName: '', // 上传的图片名称暂存
  enabledList: [], // 状态下拉
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  changeEditData(state, data) {
    Object.assign(state.editAddModalObj, data);
  },
  // 图片剪裁数据修改
  changeImageCropperObj(state, data) {
    Object.assign(state.imageCropperObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    yield this.changeLimitData(defaultLimit);
  },

  // 页面初始化
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['WGS_MENU_SYSTEM', 'ENABLED'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        systemList: selectData.info.data.find((item) => item.catCode === 'WGS_MENU_SYSTEM').dictListRsps,
        enabledList: selectData.info.data.find((item) => item.catCode === 'ENABLED').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }
  },

  /**
   * 搜索
   */
  * search() {
    // 获取仓库
    const { warehouseId } = yield 'nav';
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId: warehouseId || 0,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  // 关闭编辑、新增弹框
  * handleEditModalReset() {
    const { editFormRef } = yield '';
    // 清空校验信息
    if (editFormRef && editFormRef.clearValidate) editFormRef.clearValidate();
    yield this.changeData({
      editAddModalVisible: false,
      editAddModalObj: {},
    });
  },

  // 编辑、新增
  * handleModalSubmit(params = {}) {
    // 获取仓库
    const { warehouseId } = yield 'nav';
    const { editAddModalObj, isEdit } = yield '';
    markStatus('loading');
    const { code, msg } = yield modifyAPI({
      ...editAddModalObj,
      warehouseId: warehouseId || 0,
      ...params,
    });
    if (code === '0') {
      Message.success(isEdit ? t('编辑成功') : t('新增成功'));
      yield this.handleEditModalReset();
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  // 删除
  * delete(param) {
    markStatus('loading');
    const { code, msg } = yield deleteAPI(param);
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
};
