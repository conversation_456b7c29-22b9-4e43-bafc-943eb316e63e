import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Modal } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import ImagesPreviewer from '@shein-components/ImagesPreviewer';
import globalStyles from '@src/component/style.less';
import styles from '../style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
    } = this.props;

    const columns = [
      {
        title: t('ID'),
        render: 'id',
        width: 100,
      },
      {
        title: t('系统名称'),
        width: 160,
        render: 'systemName',
      },
      {
        title: t('图片'),
        width: 140,
        render: (record) => (
          <div key={record.id}>
            {record.imageUrl
              ? (
                <ImagesPreviewer
                  width="106px"
                  height="80px"
                  showPopover={false}
                  showRotate
                  dataSource={{
                    thumb: [record.imageUrl],
                    origin: [record.imageUrl],
                  }}
                />
              ) : (
                <span>
                  {t('无')}
                </span>
              )}
          </div>
        ),
      },
      {
        title: t('系统链接'),
        render: 'systemUrl',
        width: 200,
      },
      {
        title: t('状态'),
        render: 'menuStatusName',
        width: 120,
      },
      {
        title: t('优先级'),
        render: 'priority',
        width: 100,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 190,
      },
      {
        title: t('更新人'),
        render: 'operator',
        width: 120,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 190,
        render: (record) => (
          <>
            <Button
              size="small"
              text
              type="primary"
              className={styles.listOperationButton}
              onClick={async () => {
                store.changeData({
                  editAddModalVisible: true,
                  isEdit: true,
                  editAddModalObj: {
                    id: record.id,
                    priority: record.priority,
                    imageUrl: record.imageUrl,
                    systemType: record.systemType,
                    systemUrl: record.systemUrl,
                    menuStatus: record.menuStatus,
                  },
                  fileList: [{
                    imageUrl: record.imageUrl,
                    name: `${record.imageUrl}`.substring(`${record.imageUrl}`.lastIndexOf('/') + 1),
                  }],
                  imageCropperObj: { // 图片剪裁用到的参数
                    crop: { // 默认选中全部
                      aspect: 16 / 9,
                      unit: '%',
                      x: 0,
                      y: 0,
                      width: 100,
                      height: 100,
                    },
                    src: '',
                    blobSrc: '', // blob:url
                  },
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              size="small"
              text
              type="danger"
              key="delete"
              className={styles.listOperationButton}
              onClick={() => {
                Modal.confirm({
                  title: t('是否删除该数据?'),
                  onOk: () => {
                    store.delete({
                      id: [record.id],
                    });
                  },
                  text: { ok: t('确认'), cancel: t('取消') },
                });
              }}
            >
              {t('删除')}
            </Button>
            <Button
              size="small"
              text
              key="record"
              type="primary"
              className={styles.listOperationButton}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'WGS_MENU_SYSTEM_DOCUMENT',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
};

export default List;
