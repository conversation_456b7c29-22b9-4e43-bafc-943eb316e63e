import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Input, Select, Rule, Message, Upload,
} from 'shineout';
import { t } from '@shein-bbl/react';
import UploadPlus from '@shein-components/upload_plus';
import { uploadFileURL } from '@src/server/basic/upload';
import ImageCropper from '@shein-components/ImageCropper';
import { formdataPost } from '@src/server/common/fileFetch';
import styles from '@src/component/style.less';
import store from '../reducers';

const rules = Rule({});

class Handle extends React.Component {
  constructor(props) {
    super(props);
    this.onSelectFile = this.onSelectFile.bind(this);
  }

  /**
   * 选择图片
   * @param options
   */
  // eslint-disable-next-line class-methods-use-this
  onSelectFile(options) {
    const f = options.file;
    if (f) {
      store.changeImageCropperObj({
        src: URL.createObjectURL(f),
      });
    }
    setTimeout(() => {
      options.onLoad({ status: 200 });
    });
  }

  render() {
    const {
      loading,
      editAddModalVisible,
      editAddModalObj,
      systemList,
      isEdit,
      fileList,
      imageCropperObj: {
        src,
        blobSrc,
        crop,
        croppedImage,
      },
      uploadImageTmpName,
      enabledList,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              editAddModalVisible: true,
              isEdit: false,
              fileList: [],
              imageCropperObj: { // 图片剪裁用到的参数
                crop: { // 默认选中全部
                  aspect: 16 / 9,
                  unit: '%',
                  x: 0,
                  y: 0,
                  width: 100,
                  height: 100,
                },
                src: '',
                blobSrc: '', // blob:url
              },
            });
          }}
        >
          {t('新增')}
        </Button>
        <Modal
          visible={editAddModalVisible}
          width={800}
          maskCloseAble={false}
          title={isEdit ? t('编辑') : t('新增')}
          onClose={() => store.handleEditModalReset()}
          footer={(
            <div>
              <Button key="cancel" onClick={() => store.handleEditModalReset()}>{t('取消')}</Button>
              <Modal.Submit key="confirm" disabled={!loading || (!editAddModalObj.imageUrl && !this.props.imageCropperObj.src)}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={120}
            labelAlign="right"
            value={editAddModalObj}
            formRef={(f) => store.changeData({ editFormRef: f })}
            onChange={(value) => {
              store.changeData({
                editAddModalObj: value,
              });
            }}
            onSubmit={() => {
              // 裁剪后提交
              if (this.props.imageCropperObj.src) {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', blobSrc);
                xhr.responseType = 'blob';
                xhr.onload = async () => {
                  if (xhr.status === 200) {
                    const blobData = xhr.response;
                    const formData = new FormData();
                    formData.append('file', blobData, uploadImageTmpName);
                    formData.append('is_use_origin_name', 'true');
                    const rps = await formdataPost(uploadFileURL, formData);
                    store.changeEditData({
                      imageUrl: rps.info?.image_url,
                    });
                    store.handleModalSubmit({ imageUrl: rps.info.image_url });
                  }
                };
                xhr.send();
              } else {
                store.handleModalSubmit();
              }
            }}
          >
            <div>
              <Form.Item required label={t('系统名称')}>
                <Select
                  name="systemType"
                  keygen="dictCode"
                  format="dictCode"
                  placeholder={t('请选择')}
                  renderItem="dictNameZh"
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  data={systemList}
                  disabled={isEdit}
                  absolute
                  rules={[rules.required(t('请选择系统名称'))]}
                />
              </Form.Item>
              <Form.Item required label={t('系统链接')}>
                <Input
                  maxLength={200}
                  delay={0}
                  name="systemUrl"
                  placeholder={t('请输入')}
                  rules={[rules.required(t('请输入系统链接'))]}
                />
              </Form.Item>
              <Form.Item required label={t('优先级')}>
                <Input.Number
                  placeholder={t('请输入')}
                  min={1}
                  max={20} name="priority"
                  onChange={(val) => {
                    if (val <= 20 || val === '') {
                      store.changeEditData({ priority: val });
                    } else {
                      store.changeEditData({ priority: editAddModalObj.priority });
                    }
                  }}
                  rules={[rules.required(t('请输入优先级'))]}
                />
              </Form.Item>
              <Form.Item required label={t('图片')}>
                <Upload.Button
                  accept="image/png, image/jpeg, image/jpg"
                  onSuccess={(res, file) => {
                    store.changeData({
                      uploadImageTmpName: file.name,
                    });
                    return file.name;
                  }}
                  limit={1}
                  validator={{
                    ext: (ext) => {
                      if (!['jpg', 'jpeg', 'png'].includes(ext)) {
                        Message.error(t('支持文件格式JPG、JPEG、PNG'));
                        return false;
                      }
                    },
                    size: (size) => (size > 50 * 1024 * 1024 ? new Error(t('文件需小于{}MB', 50)) : undefined),
                  }}
                  style={{ width: 100, display: 'inline-block' }}
                  request={this.onSelectFile}
                  loading={t('正在上传...')}
                  placeholder={t('选择上传图片')}
                  type="primary"
                />
                <div style={{ paddingTop: '5px', overflow: 'auto' }}>
                  <ImageCropper
                    width="200px"
                    src={src}
                    crop={crop}
                    onChange={(v) => {
                      if (v.width / v.height > (16 / 9)) {
                        store.changeImageCropperObj({
                          crop: { ...v, width: v.height * (16 / 9) },
                        });
                      } else {
                        store.changeImageCropperObj({
                          crop: { ...v, height: v.width * (9 / 16) },
                        });
                      }
                    }}
                    onCropComplete={(image) => {
                      store.changeImageCropperObj({
                        croppedImage: image.base64,
                        blobSrc: image.url,
                      });
                    }}
                  />
                  {croppedImage && (
                    <img alt={t('剪裁后预览图')} style={{ maxWidth: '100%', paddingTop: '5px' }} src={croppedImage} />
                  )}
                </div>
                {isEdit && editAddModalObj.imageUrl && !this.props.imageCropperObj.src && (
                <UploadPlus
                  accept=".jpg,.jpeg,.png"
                  autoUpload
                  action={uploadFileURL}
                  fileList={fileList || []}
                  maxSize={50}
                  autoUploadKeyName="file"
                  filePathKeyName="imageUrl"
                  data={{
                    is_use_origin_name: true,
                  }}
                  onDelete={async (removeItem) => {
                    const newFiles = fileList.filter((file) => file.fileUrl !== removeItem.fileUrl);
                    store.changeEditData({
                      imageUrl: '',
                    });
                    store.changeData({
                      fileList: newFiles,
                    });
                  }}
                  onFailUpload={async (_, info) => Message.error(info)}
                  onSuccessUpload={async ({ file, info }) => {
                    store.changeData({
                      fileList: [{
                        imageUrl: info.image_url,
                        name: file.name,
                      }],
                    });
                    store.changeEditData({
                      imageUrl: info.image_url,
                    });
                  }}
                />
                )}
              </Form.Item>
              <Form.Item required label={t('状态')}>
                <Select
                  label={t('状态')}
                  name="menuStatus"
                  keygen="dictCode"
                  format="dictCode"
                  renderItem={(w) => w.dictNameZh}
                  data={enabledList}
                  clearable
                  placeholder={t('请选择')}
                  rules={[rules.required(t('请选择状态'))]}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  editAddModalVisible: PropTypes.bool,
  editAddModalObj: PropTypes.shape(),
  systemList: PropTypes.arrayOf(PropTypes.shape()),
  isEdit: PropTypes.bool,
  fileList: PropTypes.arrayOf(PropTypes.shape()),
  imageCropperObj: PropTypes.shape(),
  uploadImageTmpName: PropTypes.string,
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
