import { sendPostRequest } from '@src/server/common/public';

/**
 * 常用系统查询
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/menu/system/query',
  param,
}, process.env.WGS_FRONT);

/**
 * 常用系统新增/编辑
 * @param {*} param
 * @returns
 */
export const modifyAPI = (param) => sendPostRequest({
  url: '/menu/system/modify',
  param,
}, process.env.WGS_FRONT);

/**
 * 常用系统删除
 * @param {*} param
 * @returns
 */
export const deleteAPI = (param) => sendPostRequest({
  url: '/menu/system/delete',
  param,
}, process.env.WGS_FRONT);
