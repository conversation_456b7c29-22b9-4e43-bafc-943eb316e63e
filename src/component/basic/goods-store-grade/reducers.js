import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { STATISTICAL_DOWNLOAD } from '@src/lib/jump-url';
import {
  getListApi, exportListApi, delDataApi, addDataApi,
} from './server';

export const MODAL_VISIBLE_CLOSE = 0;
export const MODAL_VISIBLE_ADD = 1;
export const MODAL_VISIBLE_EDIT = 2;

// 搜索默认值
export const defaultLimit = {
  grade: undefined, // 存储等级
  enabled: 1, // 状态
};

// 其他默认值
const defaultState = {
  formRef: {},
  loading: 0, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  list: [],
  selectedRows: [], // 当前table选中的row
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录id

  typeList: [], // 存储等级下拉 来源【商品存储等级】
  statusList: [], // 状态下拉 启用、禁用

  editObjVisible: MODAL_VISIBLE_CLOSE,
  editObj: {
    enabled: 1, // 状态
    grade: '', // 存储等级
    saftyDays: '', // 安全天数
    id: '', // id
  },
};

export default {
  state: defaultState,
  // 改变state的值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  /**
   * 初始化数据
   */
  * init() {
    markStatus('loading');
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['STORAGE_LEVEL', 'ENABLED'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        typeList: selectData.info.data.find((item) => item.catCode === 'STORAGE_LEVEL').dictListRsps,
        statusList: selectData.info.data.find((item) => item.catCode === 'ENABLED').dictListRsps,
      });
    } else {
      Modal.error({
        title: selectData.msg || t('请求失败,请稍后再试'),
        autoFocusButton: 'ok',
      });
    }
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = this.state;
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  /**
   * 搜索操作
   */
  * search() {
    const { limit, pageInfo } = this.state;
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListApi(param);
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        selectedRows: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 页签改变
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  /**
   * 按钮操作-导出
   */
  * exportData() {
    const { limit } = this.state;
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const param = { ...limit };
    markStatus('loading');
    const { code, msg } = yield exportListApi(param);
    if (code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 按钮操作-删除
   */
  * delData() {
    markStatus('loading');
    const ids = yield this.state.selectedRows.map((x) => x.id);
    const { code, msg } = yield delDataApi({
      ids,
    });
    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
    * 按钮操作
    */
  * openModal(type) {
    if (type === MODAL_VISIBLE_EDIT) {
      const { selectedRows } = this.state;
      yield this.changeData({
        editObjVisible: type,
        editObj: {
          enabled: selectedRows[0].enabled, // 状态
          grade: selectedRows[0].grade,
          saftyDays: selectedRows[0].saftyDays, // 安全天数
          id: selectedRows[0].id,
        },
      });
    } else {
      yield this.changeData({
        editObjVisible: type,
        editObj: {
          enabled: 1, // 状态
          grade: '', // 存储等级
          saftyDays: '', // 安全天数
          id: '',
        },
      });
    }
  },
  /**
    * 按钮操作-保存
    */
  * saveData() {
    markStatus('loading');
    const { editObjVisible, editObj, selectedRows } = this.state;
    const param = { ...editObj };
    if (editObjVisible === MODAL_VISIBLE_EDIT) {
      param.id = selectedRows[0].id;
    }

    const { code, msg } = yield addDataApi(param);
    yield this.changeData({
      editObjVisible: MODAL_VISIBLE_CLOSE,
    });
    if (code === '0') {
      Message.success(editObjVisible === MODAL_VISIBLE_ADD ? t('新增成功') : t('编辑成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
};
