import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import { formatSearchData } from '@public-component/search-queries/utils';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      typeList,
      statusList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('存储等级')}
            name="grade"
            data={typeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('状态')}
            name="enabled"
            data={statusList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            placeholder={t('全部')}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  typeList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
