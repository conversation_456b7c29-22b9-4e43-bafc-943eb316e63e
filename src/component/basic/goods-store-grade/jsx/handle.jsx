import React from 'react';
import PropTypes from 'prop-types';
import {
  Modal, Button, Select, Radio, Input,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import style from '../style.css';
import store, { MODAL_VISIBLE_CLOSE, MODAL_VISIBLE_ADD, MODAL_VISIBLE_EDIT } from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
      typeList,
      editObj,
      editObjVisible,
      statusList,
      selectedRows,
    } = this.props;

    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.openModal(MODAL_VISIBLE_ADD);
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || (selectedRows.length !== 1)}
          onClick={() => {
            store.openModal(MODAL_VISIBLE_EDIT);
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.exportData();
          }}
        >
          {t('导出')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || (!selectedRows.length)}
          onClick={() => {
            Modal.confirm({
              content: t('确定删除勾选的配置？'),
              onOk: () => {
                store.delData();
              },
              text: {
                ok: t('确认'),
                cancel: t('取消'),
              },
            });
          }}
        >
          {t('删除')}
        </Button>
        {/* Modal */}
        <Modal
          maskCloseAble={false}
          title={t('{}', editObjVisible === MODAL_VISIBLE_ADD ? '新增' : '编辑')}
          visible={editObjVisible !== MODAL_VISIBLE_CLOSE}
          width="600px"
          onClose={() => store.changeData({
            editObjVisible: MODAL_VISIBLE_CLOSE,
          })}
          footer={[
            <Button
              key="cancel" onClick={() => store.changeData({
                editObjVisible: MODAL_VISIBLE_CLOSE,
              })}
            >
              {t('取消')}
            </Button>,
            <Button
              disabled={!(editObj.enabled && editObj.grade && editObj.saftyDays) || !loading}
              key="ok"
              type="primary"
              onClick={() => store.saveData()}
            >
              {t('确定')}
            </Button>,
          ]}
        >
          <section>
            <div className={style.blockLabel}>
              <span className={`${style.addLabel} ${style.required}`}>
                {t('状态')}
                :
              </span>
              <div style={{ display: 'inline-block' }}>
                <Radio.Group
                  keygen="id"
                  data-bind="editObj.enabled"
                >
                  {(statusList || []).map((d) => (
                    <Radio key={d.dictCode} htmlValue={d.dictCode}>
                      {d.dictNameZh}
                    </Radio>
                  ))}
                </Radio.Group>
              </div>
            </div>
            <div className={style.blockLabel}>
              <span className={`${style.addLabel} ${style.required}`}>
                {t('存储等级')}
                :
              </span>
              <Select
                data-bind="editObj.grade"
                data={typeList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                style={{ width: 220 }}
              />
            </div>
            <div className={style.blockLabel}>
              <span className={`${style.addLabel} ${style.required}`}>
                {t('安全天数')}
                :
              </span>
              <Input.Number
                placeholder={t('请输入')}
                min={1}
                max={99999}
                data-bind="editObj.saftyDays"
                style={{ width: 220 }}
              />
            </div>
          </section>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  typeList: PropTypes.arrayOf(PropTypes.shape),
  editObj: PropTypes.shape(),
  editObjVisible: PropTypes.number,
  statusList: PropTypes.arrayOf(PropTypes.shape),
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
};
export default Handle;
