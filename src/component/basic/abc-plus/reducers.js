import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { Modal } from 'shineout';
import { select } from 'redux-saga/effects';
import { getSubWarehouseSelectList, dictSelect } from '@src/server/basic/dictionary';
import { queryDataByTime, queryDataByDate } from './server';

// 转换数据格式为table、chart需要的格式
function formatData(list, active) {
  // 0 - 国家线维度  1 - 子仓+层维度
  const attrKey = active === 0 ? 'nationalLinePressureData' : 'pickPressureData';
  const attrName = active === 0 ? 'nationalLineName' : 'subWarehouseName';
  const attrValue = active === 0 ? 'curPressure' : 'pickPressure';
  const tableFormatData = {
    columns: [],
    list: [],
  };
  const chartFormatData = {
    legendList: [],
    xAxisList: [],
    seriesList: [],
  };
  if (list.length) {
    (list[0][attrKey] || []).forEach((v, i) => {
      let tableItem = {

      };
      const chartItem = {
        name: '',
        type: 'line',
        data: [],
      };
      list.forEach((sv, si) => {
        const item = list[0][attrKey][i];
        tableItem = {
          ...tableItem,
          name: active === 0 ? item[attrName] : (!item[attrName] || `${item[attrName]}${item.floor}${t('层')}`),
          [`time${si}`]: {
            pressure: sv[attrKey][i][attrValue],
          },
        };
        chartItem.name = list[0][attrKey][i][attrName];
        chartItem.data.push(sv[attrKey][i][attrValue]);
      });
      tableFormatData.list.push(tableItem);
      chartFormatData.legendList.push(v[attrName]);
      chartFormatData.seriesList.push(chartItem);
    });
    chartFormatData.xAxisList = list.map((li) => (li.time));
    tableFormatData.columns = list.map((li) => ({
      time: li.time,
      density: li.density,
      nationalGini: li.nationalGini,
      pickGini: li.pickGini,
    }));
  }
  return { tableFormatData, chartFormatData };
}

const defaultState = {
  ready: true,
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  active: 0,
  limit: {
    nationalLineType: [],
    subWarehouseIds: [], // 子仓
  },
  nationalLineList: [], // 国家线
  subWarehouseList: [], // 子仓
  info: {},
  oldData: {
    tableFormatData: {
      columns: [],
      list: [],
    },
    chartFormatData: {
      legendList: [],
      xAxisList: [],
      seriesList: [],
    },
  }, // 老的数据
  latestData: {
    tableFormatData: {
      columns: [],
      list: [],
    },
    chartFormatData: {
      legendList: [],
      xAxisList: [],
      seriesList: [],
    },

  }, // 新的数据
  isOld: true, // 默认展示老的数据
  isQueryByTime: true, // 按时刻查询
};

export default {
  defaultState,
  // 初始化
  $init: (draft) => {
    assign(draft);
  },
  // 改state数据
  changeData: (draft, action) => {
    assign(draft, action.data);
  },
  // 改搜索条件limit属性值
  changeLimit: (draft, action) => {
    assign(draft.limit, action.data);
  },
  * getDict(action, ctx) {
    const res = yield dictSelect({ catCode: ['NATIONAL_LINE_TYPE'] });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          nationalLineList: (res.info.data || []).find((item) => item.catCode === 'NATIONAL_LINE_TYPE')
            .dictListRsps,
        },
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * getSubWarehouse(action, ctx) {
    const res = yield getSubWarehouseSelectList({ warehouseId: action.value, enabled: 1 });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          subWarehouseList: res.info.data || [],
        },
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * init(action, ctx) {
    yield ctx.getDict();
    const { warehouseId } = yield select((state) => state.nav);
    if (warehouseId) {
      yield ctx.getSubWarehouse({
        value: warehouseId,
      });
    }
    yield ctx.search({
      type: 1,
    });
  },
  * search(action, ctx) {
    // 获取列表
    const { type } = action;
    let res = {};
    // 按时刻
    if (type === 1) {
      res = yield queryDataByTime();
    } else {
      // 按天数
      res = yield queryDataByDate();
    }
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          info: res.info,
        },
      });
      yield ctx.toggleDimension();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * toggleDimension(action, ctx) {
    const { info, active } = yield select((v) => v['basic/abc-plus']);
    yield ctx.changeData({
      data: {
        oldData: formatData(info.oldAbcPlusInfo, active),
        latestData: formatData(info.newAbcPlusInfo, active),
      },
    });
  },
};
