import React from 'react';
import PropTypes from 'prop-types';
import {
  Form, Card, Button,
} from 'shineout';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import styles from '../../../style.less';

function Header(props) {
  const {
    topSearch,
    dataLoading,
    isQueryByTime,
    isOld,
  } = props;

  return (
    <Form
      inline
    >
      <Card
        collapsible
        collapsed={!topSearch}
        onCollapse={() => {
          store.changeData({ data: { topSearch: !topSearch } });
        }}
      >
        <Card.Header style={{ padding: '6px 16px' }}>{t('搜索查询')}</Card.Header>
        <Card.Body style={{ padding: 0 }}>
          <div className={[styles.header_wrap_view, styles.commonBgColor].join(' ')}>
            {/* {active === 0 ? ( */}
            {/*  <div className={styles.inner_list}> */}
            {/*    <span className={styles.labWidth}>{t('国家线')}:</span> */}
            {/*    <Select */}
            {/*      multiple */}
            {/*      style={{ margin: '0 10px' }} */}
            {/*      data-bind="limit.nationalLineType" */}
            {/*      width={200} */}
            {/*      data={nationalLineList.filter(i => i.dictCode !== 3)} */}
            {/*      datum={{ format: 'dictCode' }} */}
            {/*      keygen="dictCode" */}
            {/*      renderItem={w => w.dictNameZh} */}
            {/*      placeholder="请选择" */}
            {/*      clearable */}
            {/*    /> */}
            {/*  </div> */}
            {/* ) : ( */}
            {/*  <div className={styles.inner_list}> */}
            {/*    <span className={styles.labWidth}>{t('子仓')}:</span> */}
            {/*    <Select */}
            {/*      multiple */}
            {/*      style={{ margin: '0 10px' }} */}
            {/*      data-bind="limit.subWarehouseIds" */}
            {/*      width={200} */}
            {/*      data={subWarehouseList} */}
            {/*      datum={{ format: 'id' }} */}
            {/*      keygen="id" */}
            {/*      renderItem={w => w.nameZh} */}
            {/*      placeholder="请选择" */}
            {/*      clearable */}
            {/*    /> */}
            {/*  </div> */}
            {/* )} */}
            <div style={{ margin: '0 10px 10px 10px', display: 'flex' }}>
              <Button
                type={isQueryByTime ? 'primary' : 'secondary'}
                loading={dataLoading === 0}
                onClick={() => {
                  store.search({
                    type: 1, // 按时刻查看数据
                  });
                  store.changeData({
                    data: {
                      isQueryByTime: true,
                    },
                  });
                }}
              >
                {t('按时刻查看数据')}
              </Button>
              <Button
                type={isQueryByTime ? 'secondary' : 'primary'}
                style={{ marginRight: '15px' }}
                loading={dataLoading === 0}
                onClick={() => {
                  store.search({
                    type: 2, // 按天数查看数据
                  });
                  store.changeData({
                    data: {
                      isQueryByTime: false,
                    },
                  });
                }}
              >
                {t('按天数查看数据')}
              </Button>
              <Button
                type={isOld ? 'primary' : 'secondary'}
                loading={dataLoading === 0}
                onClick={() => {
                  store.changeData({
                    data: {
                      isOld: true,
                    },
                  });
                }}
              >
                {t('查看老数据')}
              </Button>
              <Button
                type={isOld ? 'secondary' : 'primary'}
                loading={dataLoading === 0}
                onClick={() => {
                  store.changeData({
                    data: {
                      isOld: false,
                    },
                  });
                }}
              >
                {t('查看新数据')}
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>
    </Form>
  );
}

Header.propTypes = {
  topSearch: PropTypes.bool.isRequired,
  dataLoading: PropTypes.bool.isRequired,
  isQueryByTime: PropTypes.bool.isRequired,
  isOld: PropTypes.bool.isRequired,
};

export default Header;
