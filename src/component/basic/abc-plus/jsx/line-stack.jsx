import React from 'react';
import PropTypes from 'prop-types';
import ReactEcharts from 'echarts-for-react';

const tabsBg = ['#00E4C9', '#0099FF', '#FAF0A0', '#FF6C02', '#F7517F'];

const getOption = (colors, chartData) => (
  {
    tooltip: {
      trigger: 'axis',
      left: 0,
    },
    legend: {
      data: chartData.legendList,
    },
    grid: {
      left: '0%',
      right: '2%',
      bottom: '3%',
      top: '25px',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.xAxisList,
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}',
      },
    },
    series: chartData.seriesList,
  }
);

class LineStack extends React.Component {
  render() {
    const {
      chartData,
    } = this.props;
    const option = getOption(tabsBg, chartData);
    return (
      <div>
        <ReactEcharts option={option} key={JSON.stringify(option)} />
      </div>
    );
  }
}

LineStack.propTypes = {
  chartData: PropTypes.shape(),
};

export default LineStack;
