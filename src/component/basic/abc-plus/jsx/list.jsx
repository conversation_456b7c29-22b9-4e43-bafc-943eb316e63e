import React from 'react';
import PropTypes from 'prop-types';
import { Table } from 'shineout';
import { t } from '@shein-bbl/react';
import LineStack from './line-stack';

function List(props) {
  const {
    dataLoading,
    oldData,
    latestData,
    topSearch,
    headerHeight,
    isOld,
  } = props;
  const columns = [
    {
      title: '',
      width: 100,
      render: 'name',
      fixed: 'left',
    },
  ].concat((isOld ? oldData : latestData).tableFormatData.columns.map((v, i) => (
    {
      title: () => (
        <div>
          <div>{ v.time }</div>
          <div>
            {t('浓度')}
            :
            { v.density }
          </div>
          {/* <div>国家线基尼系数：{ v.nationalGini }</div> */}
          <div>
            {t('拣货基尼系数')}
            :
            { v.pickGini }
          </div>
        </div>
      ),
      width: 100,
      render: (row) => (
        <div>
          <div>
            {t('压力系数')}
            :
            { row[`time${i}`].pressure }
          </div>
        </div>
      ),
    }
  )));
  const oHeight = 190 + (topSearch ? headerHeight : 0);
  return (
    <div>
      <Table
        key={Date.now()}
        columnResizable
        bordered
        fixed="both"
        loading={dataLoading === 0}
        data={(isOld ? oldData : latestData).tableFormatData.list}
        columns={columns}
        keygen={(r) => JSON.stringify(r)}
        empty={t('暂无数据')}
        size="small"
        style={{ maxHeight: window.innerHeight - oHeight, marginBottom: '5px' }}
        width={columns.reduce((pre, current) => pre + current.width, 0)}
      />
      <LineStack isOld={isOld} chartData={(isOld ? oldData : latestData).chartFormatData} />
    </div>
  );
}

List.propTypes = {
  dataLoading: PropTypes.number.isRequired,
  topSearch: PropTypes.bool.isRequired,
  headerHeight: PropTypes.number.isRequired,
  oldData: PropTypes.shape(),
  latestData: PropTypes.shape(),
  isOld: PropTypes.bool.isRequired,
};

export default List;
