import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Spin, Tabs } from 'shineout';
import { i18n, t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store from './reducers';
import Header from './jsx/header';
import List from './jsx/list';

class Container extends React.Component {
  componentDidMount() {
    // 初始化数据
    store.init();
  }

  componentDidUpdate() {
    document.title = t('智能波次PLUS');
  }

  render() {
    const { ready } = this.props;

    if (ready) {
      return (
        <div className={styles.headerArea}>
          <Tabs onChange={(active) => {
            store.changeData({
              data: {
                active,
              },
            });
            store.toggleDimension();
          }}
          >
            <Tabs.Panel tab={t('按国家线纬度')}>
              <div style={{ marginTop: '15px' }}>
                <Header {...this.props} />
                <List {...this.props} headerHeight={52} />
              </div>
            </Tabs.Panel>
            <Tabs.Panel tab={t('按子仓+层纬度')}>
              <div style={{ marginTop: '15px' }}>
                <Header {...this.props} />
                <List {...this.props} headerHeight={52} />
              </div>
            </Tabs.Panel>
          </Tabs>
        </div>
      );
    }
    return (
      <div style={{ textAlign: 'center' }}>
        <Spin size={50} name="default" />
      </div>
    );
  }
}

Container.propTypes = {
  ready: PropTypes.bool.isRequired,
};

const mapStateToProps = (state) => state['basic/abc-plus'];
export default connect(mapStateToProps)(i18n(Container));
