import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import { Select } from 'shineout';

class StatusSelect extends React.Component {
  componentDidMount() {
  }

  render() {
    const {
      value,
      onChange,
      placeholder,
      list,
    } = this.props;

    return (
      <Select
        value={value}
        onChange={(val) => onChange(val)}
        placeholder={placeholder || t('全部')}
        width={200}
        data={list}
        datum={{ format: 'value' }}
        keygen="value"
        renderItem={(w) => w.label}
        clearable
      />
    );
  }
}

StatusSelect.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
};

StatusSelect.defaultProps = {
  list: [{
    label: t('启用'),
    value: '1',
  }, {
    label: t('禁用'),
    value: '0',
  }],
};

export default StatusSelect;
