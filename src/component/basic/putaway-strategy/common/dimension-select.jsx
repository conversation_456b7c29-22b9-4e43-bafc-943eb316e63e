import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import { Select } from 'shineout';

class DimensionSelect extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [{
        id: '1',
        name: t('子仓'),
      }, {
        id: '2',
        name: t('库区'),
      }, {
        id: '3',
        name: t('库位'),
      }],
    };
  }

  render() {
    const { value, onChange, placeholder } = this.props;
    const { list } = this.state;

    return (
      <Select
        value={value}
        onChange={(val) => onChange(val)}
        placeholder={placeholder || t('全部')}
        width={200}
        data={list}
        datum={{ format: 'id' }}
        keygen="id"
        renderItem={(w) => w.name}
        clearable
        {...this.props}
      />
    );
  }
}

DimensionSelect.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
};

export default DimensionSelect;
