import { t } from '@shein-bbl/react';
import React from 'react';
import { Modal, Button, Input } from 'shineout';
import PropTypes from 'prop-types';
import store from '../reducers';

function ModalWaterLevel(props) {
  const {
    waterLevelVisible,
    locationVolume,
    waterLevelList,
    loading,
  } = props;
  const current = waterLevelList[0] || {};
  return (
    <Modal
      maskCloseAble={false}
      visible={waterLevelVisible}
      title={t('水位判断修改')}
      width={400}
      onClose={() => store.changeData({ waterLevelVisible: false })}
      footer={[
        <Button
          key="cancel"
          onClick={() => {
            store.changeData({ waterLevelVisible: false });
          }}
        >
          {t('取消')}
        </Button>,
        <Button
          key="ok"
          type="primary"
          disabled={!locationVolume || !loading}
          onClick={() => store.setWaterLevel({ locationVolume })}
        >
          {t('确认')}
        </Button>,
      ]}
    >
      <div style={{ marginBottom: 5 }}>
        {t('上次修改时间')}
        :
        {current.createTime}
      </div>
      <div>
        <span>
          {t('判断库位可上的水位占比')}
          &lt;&nbsp;
        </span>
        <Input.Number
          data-bind="locationVolume"
          width={120}
          min={0}
          max={100}
        />
        <span>%</span>
      </div>
    </Modal>
  );
}
ModalWaterLevel.propTypes = {
  waterLevelVisible: PropTypes.bool.isRequired,
  locationVolume: PropTypes.number.isRequired,
  waterLevelList: PropTypes.arrayOf(PropTypes.shape()),
  loading: PropTypes.number,
};

export default ModalWaterLevel;
