import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';
import ModalLocation from './modal-location';
import ModalWaterLevel from './modal-waterlevel';
import ShowMore from '../../../../public-component/other/show-more';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      locationVisible,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        width: 130,
        render: 'warehouseName',
      },
      {
        title: t('子仓'),
        width: 130,
        render: 'subWarehouseName',
      },
      {
        title: t('库区'),
        width: 100,
        render: 'areaName',
      },
      {
        title: t('巷道'),
        width: 100,
        render: 'roadway',
      },
      {
        title: t('库位'),
        width: 150,
        render: 'location',
      },
      {
        title: t('拣货顺序编号'),
        width: 135,
        render: 'pickOrder',
      },
      {
        title: t('库位类型'),
        width: 120,
        render: 'locationTypeName',
      },
      {
        title: t('品项（库存/上限）'),
        width: 160,
        render: 'item',
      },
      {
        title: t('水位（库存/容积）'),
        width: 160,
        render: 'waterLevel',
      },
      {
        title: t('库存颜色'),
        width: 100,
        render: 'color',
      },
      {
        title: 'SKU',
        width: 160,
        render: 'skuCode',
      },
      {
        title: t('库存SKC'),
        width: 160,
        render: (r) => <ShowMore text={r.goodsSn} />,
      },
      {
        title: t('是否可上'),
        width: 100,
        render: 'isUpperName',
      },
      {
        title: t('状态'),
        width: 100,
        render: 'enabledName',
      },
      {
        title: t('更新时间'),
        width: 120,
        render: 'updateTime',
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {locationVisible && (
        <ModalLocation {...this.props} />
        )}
        <ModalWaterLevel {...this.props} />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  locationVisible: PropTypes.bool,
};

export default List;
