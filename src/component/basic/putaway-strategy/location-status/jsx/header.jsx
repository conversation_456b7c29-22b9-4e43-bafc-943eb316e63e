import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import { Input, Select, Rule } from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

const rule = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      if (!formData.startTime || !formData.endTime) {
        if (!formData.location) {
          callback(new Error(t('开始时间或结束时间必选')));
        }
      }
      // 限制可选时间段最大不超过31天
      if (moment(formData.endTime).diff(moment(formData.startTime), 'days', true) > 1) {
        if (!formData.location) {
          callback(new Error(t('时间范围不能超过{}天', 1)));
        }
      }
      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      searchSubWarehouseList,
      areaList,
      enableList,
      isUpperList,
      locationList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('子仓'), t('库区'), t('更新时间')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Select
            label={t('子仓')}
            name="subWarehouseIds"
            data={searchSubWarehouseList}
            keygen="id"
            format="id"
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            required
            rules={[rule.required]}
            placeholder={t('全部')}
            onChange={(val) => {
              store.changeLimitData({ areaIds: [] });
              if (val && val.length === 1) {
                store.getArea({ subWarehouseId: val[0] });
              } else {
                store.changeData({ areaList: [] });
              }
            }}
          />
          <Select
            label={t('库区')}
            name="areaIds"
            data={areaList}
            keygen="id"
            format="id"
            renderItem="area"
            onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            required
            placeholder={t('全部')}
          />
          <Input
            label={t('巷道')}
            name="roadway"
            placeholder={t('请输入')}
          />
          <Input
            label={t('库位')}
            name="location"
            placeholder={t('请输入')}
          />
          <Select
            label={t('库位类型')}
            name="locationTypes"
            data={locationList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('是否可上')}
            name="isUpper"
            data={isUpperList}
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('状态')}
            name="enabled"
            data={enableList}
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <DateRangePicker
            label={t('更新时间')}
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['startTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[rule.timeRange()]}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  searchSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  areaList: PropTypes.arrayOf(PropTypes.shape()),
  enableList: PropTypes.arrayOf(PropTypes.shape()),
  isUpperList: PropTypes.arrayOf(PropTypes.shape()),
  locationList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
