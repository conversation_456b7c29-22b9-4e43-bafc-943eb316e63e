import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import DateRangePicker from '@shein-components/dateRangePicker2';
import {
  Table, Modal, Button, Select, Pagination,
} from 'shineout';
import { getWarehouseApi, getSubWarehouseSelectList } from '@src/server/basic/dictionary';
import { STATISTICAL_DOWNLOAD } from '@src/lib/jump-url';
import styles from '../../../../style.less';
import store from '../reducers';
import { queryLocationListApi, exportAPI } from '../../server';
import { selectArea } from '../../../../../server/basic/area';

class ModalLocation extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      dataLoading: false,
      visible: false,
      warehouseId: undefined,
      subWarehouseId: undefined,
      // warehouseList: [],
      area: undefined,
      subWarehouseList: [],
      areaList: [],
      startTime: '',
      endTime: '',
      pageSizeList: [20, 50, 100],
      pageNum: 1,
      pageSize: 20,
      count: 0,
      currentWarehouseList: this.props.currentWarehouseList,
    };
    this.doSearch = this.doSearch.bind(this);
    this.getWarehouseList = this.getWarehouseList.bind(this);
    this.getSubWarehouseList = this.getSubWarehouseList.bind(this);
  }

  componentDidMount() {
    this.getWarehouseList();
  }

  async getWarehouseList() {
    try {
      const res = await getWarehouseApi({ enabled: 1 });
      if (res.code === '0') {
        this.setState({
          // warehouseList: res.info.data || [],
          visible: true,
        });
      } else {
        Modal.error({ title: res.msg });
      }
    } catch (e) {
      Modal.error({ title: e?.message || e?.reason?.message });
    }
  }

  async getSubWarehouseList(warehouseId) {
    try {
      const res = await getSubWarehouseSelectList({
        enabled: 1,
        warehouseId,
      });
      if (res.code === '0') {
        this.setState({
          subWarehouseList: res.info.data || [],
        });
      } else {
        Modal.error({ title: res.msg });
      }
    } catch (e) {
      Modal.error({ title: e?.message || e?.reason?.message });
    }
  }

  async getAreaList(subWarehouseId) {
    try {
      const res = await selectArea({
        enabled: 1,
        subWarehouseId,
      });
      if (res.code === '0') {
        this.setState({
          areaList: res.info.data || [],
        });
      } else {
        Modal.error({ title: res.msg });
      }
    } catch (e) {
      Modal.error({ title: e?.message || e?.reason?.message });
    }
  }

  async doSearch() {
    try {
      this.setState({ dataLoading: true });
      const {
        warehouseId,
        pageNum,
        pageSize,
        subWarehouseId,
        area,
        startTime,
        endTime,
      } = this.state;
      const res = await queryLocationListApi({
        warehouseId,
        subWarehouseId,
        pageNum,
        pageSize,
        areaId: area,
        updateTimeStart: startTime,
        updateTimeEnd: endTime,
      });
      if (res.code === '0') {
        if (res.info) {
          this.setState({
            locationList: res.info.data || [],
            count: res.info.meta.count,
          });
        }
      } else {
        Modal.error({ title: res.msg });
      }
    } catch (e) {
      Modal.error({ title: e?.message || e?.reason?.message || t('获取数据出错') });
    } finally {
      this.setState({ dataLoading: false });
    }
  }

  async exportData() {
    try {
      this.setState({ dataLoading: true });
      const {
        warehouseId,
        pageNum,
        pageSize,
        subWarehouseId,
        area,
        startTime,
        endTime,
      } = this.state;
      const { code, msg } = await exportAPI({
        warehouseId,
        subWarehouseId,
        pageNum,
        pageSize,
        areaId: area,
        updateTimeStart: startTime,
        updateTimeEnd: endTime,
      });
      if (code === '0') {
        window.open(STATISTICAL_DOWNLOAD);
      } else {
        Modal.error({ title: msg });
      }
    } catch (e) {
      Modal.error({ title: e?.message || e?.reason?.message || t('获取数据出错') });
    } finally {
      this.setState({ dataLoading: false });
    }
  }

  render() {
    const {
      locationList,
      dataLoading,
      visible,
      warehouseId,
      subWarehouseId,
      area,
      subWarehouseList,
      areaList,
      startTime,
      endTime,
      pageSize,
      pageNum,
      pageSizeList,
      count,
      currentWarehouseList,
    } = this.state;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouse',
        width: 100,
      },
      {
        title: t('子仓'),
        render: 'subWarehouse',
        width: 100,
      },
      {
        title: t('库区'),
        render: 'area',
        width: 80,
      },
      {
        title: t('巷道'),
        render: 'roadway',
        width: 80,
      },
      {
        title: t('可上库位数量'),
        render: 'availableNum',
        width: 80,
      },
      {
        title: t('更新时间'),
        render: 'updateDate',
        width: 130,
      },
    ];

    const handlePaginationChange = (page, size) => {
      this.setState({
        pageNum: page,
        pageSize: size,
      }, () => {
        this.doSearch();
      });
    };

    return (
      <Modal
        maskCloseAble={false}
        visible={visible}
        title={t('可上库位数量查看')}
        width={800}
        onClose={() => store.changeData({ locationVisible: false })}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              store.changeData({ locationVisible: false });
            }}
          >
            {t('取消')}
          </Button>,
        ]}
      >
        <div>
          <div className={styles.header_wrap_view}>
            <div className={styles.inner_list}>
              <div className={styles.labSmall}>
                {t('仓库')}
                :
              </div>
              <Select
                value={warehouseId}
                onChange={(val) => {
                  this.setState({
                    warehouseId: val,
                    subWarehouseId: '',
                    subWarehouseList: [],
                    area: '',
                    areaList: [],
                  });
                  if (val) {
                    this.getSubWarehouseList(val);
                  }
                }}
                width={150}
                data={currentWarehouseList}
                onFilter={(text) => (v) => v.nameZh.toLowerCase().indexOf(text.toLowerCase()) > -1}
                format="id"
                keygen="id"
                renderItem="nameZh"
                clearable
              />
            </div>
            <div className={styles.inner_list}>
              <div className={styles.labSmall}>
                {t('子仓')}
                :
              </div>
              <Select
                value={subWarehouseId}
                onChange={(val) => {
                  this.setState({
                    subWarehouseId: val,
                    area: '',
                    areaList: [],
                  });
                  if (val) {
                    this.getAreaList(val);
                  }
                }}
                width={150}
                data={subWarehouseList}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                format="id"
                keygen="id"
                renderItem="nameZh"
                clearable
              />
            </div>
            <div className={styles.inner_list}>
              <div className={styles.labSmall}>
                {t('库区')}
                :
              </div>
              <Select
                value={area}
                onChange={(val) => this.setState({ area: val })}
                width={150}
                data={areaList}
                format="id"
                keygen="id"
                renderItem="area"
                clearable
                onFilter={(text) => (v) => v.area.toLowerCase()
                  .indexOf(text.toLowerCase()) > -1}
              />
            </div>
            <div className={styles.inner_list}>
              <div className={styles.labSmall}>
                {t('更新时间')}
                :
              </div>
              <DateRangePicker
                placeholder={[t('开始时间'), t('结束时间')]}
                type="datetime"
                inputable
                format="yyyy-MM-dd HH:mm:ss"
                defaultTime={['00:00:00', '23:59:59']}
                value={[startTime, endTime]}
                onChange={(dates) => {
                  this.setState({
                    startTime: dates[0],
                    endTime: dates[1],
                  });
                }}
              />
            </div>
            <div className={styles.inner_list}>
              <Button
                style={{ marginLeft: 10 }}
                type="primary"
                size="default"
                icon="search"
                onClick={(e) => {
                  e.preventDefault();
                  this.setState({
                    pageNum: 1,
                  }, () => {
                    this.doSearch();
                  });
                }}
              >
                {t('搜索')}
              </Button>
              <Button
                style={{ marginLeft: 10 }}
                type="primary"
                size="default"
                icon="download"
                onClick={(e) => {
                  e.preventDefault();
                  this.exportData();
                }}
              >
                {t('导出')}
              </Button>
            </div>
          </div>
          <Table
            loading={dataLoading}
            columns={columns}
            data={locationList}
            bordered
            pagination={false}
            fixed="both"
            keygen={(d) => JSON.stringify(d)}
            style={{ maxHeight: 300 }}
          />
          <Pagination
            align="left"
            current={pageNum}
            pageSize={pageSize}
            layout={[({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')]}
            onChange={handlePaginationChange}
            pageSizeList={pageSizeList}
            total={count}
          />
        </div>
      </Modal>
    );
  }
}

ModalLocation.propTypes = {
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};

export default ModalLocation;
