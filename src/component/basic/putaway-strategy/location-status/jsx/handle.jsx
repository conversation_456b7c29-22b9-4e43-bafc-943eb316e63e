import React from 'react';
import PropTypes from 'prop-types';
import { Button } from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
      waterLevelList,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          loading={loading === 0}
          onClick={() => store.changeData({
            waterLevelVisible: true,
            locationVolume: (waterLevelList[0] || {}).operateContent,
          })}
        >
          {t('水位判断修改')}
        </Button>
        <Button
          type="primary"
          loading={loading === 0}
          onClick={() => store.changeData({ locationVisible: true })}
        >
          {t('可上库位数量查看')}
        </Button>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  waterLevelList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
