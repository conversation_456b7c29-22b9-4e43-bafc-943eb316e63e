import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      dataLoading,
      limit,
      nationalLineList,
      countryList,
      enabledList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!dataLoading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('编辑时间')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Select
            label={t('目的地')}
            name="countryName"
            data={countryList}
            keygen="countryName"
            format="countryName"
            renderItem={(w) => w.countryName}
            onFilter={(text) => (d) => d.countryName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('国家线')}
            name="nationalLineType"
            data={nationalLineList.filter((i) => i.dictCode !== 3)}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('状态')}
            name="enabled"
            data={enabledList}
            keygen="value"
            format="value"
            renderItem="label"
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            compressed
            clearable
            placeholder={t('全部')}
          />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['beginTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('编辑时间')}
            span={2}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  dataLoading: PropTypes.number,
  limit: PropTypes.shape(),
  nationalLineList: PropTypes.arrayOf(PropTypes.shape()),
  countryList: PropTypes.arrayOf(PropTypes.shape()),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
