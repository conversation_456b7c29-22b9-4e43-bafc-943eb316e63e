import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import { Button, Modal, Select } from 'shineout';
import store from '../reducers';
import styles from '../../../style.less';

function ModalAction(props) {
  const {
    modalVisible,
    actionType,
    modalInput,
    enabledList,
    recordId,
    nationalLineList,
    countryList,
    subWarehouseList,
    dataLoading,
    currentWarehouseList,
  } = props;

  const {
    countryName,
    enabled,
    nationalLineType,
  } = modalInput;

  // 输入框有内容则可点击确认按钮
  const isOkBtnEnable = countryName && enabled && !['', null, undefined].includes(nationalLineType);

  return (
    <Modal
      maskCloseAble={false}
      visible={modalVisible}
      title={actionType === 'CREATE' ? t('新增') : t('修改')}
      width={600}
      onClose={() => store.changeData({ modalVisible: false })}
      footer={[
        <Button
          key="cancel"
          onClick={() => {
            store.changeData({ modalVisible: false });
          }}
        >
          {t('取消')}
        </Button>,
        <Button
          disabled={!isOkBtnEnable || !dataLoading}
          key="ok"
          type="primary"
          onClick={() => {
            // 判断是新增还是修改
            if (actionType === 'CREATE') {
              store.addItem({ params: modalInput });
            } else {
              store.updateItem({
                params: {
                  ...modalInput,
                  id: recordId,
                },
              });
            }
          }}
        >
          {t('确认')}
        </Button>,
      ]}
    >
      <div className={styles.header_wrap_view}>
        <div className={styles.inner_list}>
          <span className={styles.labSLarge}>
            {t('目的地')}
            <span className={styles.redStar}>*</span>
          </span>
          <Select
            style={{ margin: '0 10px' }}
            data-bind="modalInput.countryName"
            width={200}
            data={countryList}
            datum={{ format: 'countryName' }}
            keygen="countryName"
            renderItem={(w) => w.countryName}
            placeholder={t('请选择')}
            onFilter={(text) => (v) => v.countryName.toLowerCase().indexOf(text.toLowerCase()) > -1}
          />
        </div>
        <div className={styles.inner_list}>
          <span className={styles.labSLarge}>
            {t('国家线')}
            <span className={styles.redStar}>*</span>
          </span>
          <Select
            style={{ margin: '0 10px' }}
            data-bind="modalInput.nationalLineType"
            width={200}
            data={nationalLineList.filter((i) => i.dictCode !== 3)}
            datum={{ format: 'dictCode' }}
            keygen="dictCode"
            renderItem={(w) => w.dictNameZh}
            placeholder={t('请选择')}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </div>
        <div className={styles.inner_list}>
          <span className={styles.labSLarge}>
            {t('状态')}
            <span className={styles.redStar}>*</span>
          </span>
          <Select
            style={{ margin: '0 10px' }}
            data-bind="modalInput.enabled"
            width={200}
            data={enabledList}
            datum={{ format: 'value' }}
            keygen="value"
            renderItem={(w) => w.label}
            placeholder={t('请选择')}
          />
        </div>
        <div className={styles.inner_list}>
          <span className={styles.labSLarge}>
            {t('仓库')}
            <span style={{ color: '#fff' }}>*</span>
          </span>
          <Select
            style={{ margin: '0 10px' }}
            data-bind="modalInput.warehouse"
            placeholder={t('请选择')}
              // width={180}
            data={currentWarehouseList}
            datum={{ format: 'id' }}
            keygen="id"
            width={200}
            renderItem={(w) => w.nameZh}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(val) => {
              store.changeData({
                data: {
                  modalInput: { ...modalInput, subWarehouseIds: [], warehouse: val },
                },
              });
              store.getSubWarehouse({ data: { warehouseVal: val } });
            }}
          />
        </div>
        <div className={styles.inner_list}>
          <span className={styles.labSLarge}>
            {t('子仓')}
            <span style={{ color: '#fff' }}>*</span>
          </span>
          <Select
            multiple
            compressed
            style={{ margin: '0 10px' }}
            data-bind="modalInput.subWarehouseIds"
            placeholder={t('请选择')}
            width={200}
            data={subWarehouseList}
            datum={{ format: 'id' }}
            keygen="id"
            renderItem={(w) => w.nameZh}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
        </div>
      </div>
    </Modal>
  );
}

ModalAction.propTypes = {
  modalVisible: PropTypes.bool,
  actionType: PropTypes.string,
  modalInput: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  nationalLineList: PropTypes.arrayOf(PropTypes.shape()),
  countryList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  dataLoading: PropTypes.number,
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};

export default ModalAction;
