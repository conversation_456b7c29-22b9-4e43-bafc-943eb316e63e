import React from 'react';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import { Button, Modal } from 'shineout';
import FileDelayUpload from '@src/component/public-component/upload/fileDelayUpload';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      dataLoading,
      limit,
      importModalVisible, // 导入弹出框是否显示 默认不显示
      file, // 导入文件对象
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          loading={dataLoading === 0}
          onClick={() => {
            store.changeData({
              actionType: 'CREATE',
              modalVisible: true,
              modalInput: {
                countryName: '',
                enabled: '',
                nationalLineType: '',
                warehouse: '',
                subWarehouseIds: [],
              },
            });
          }}
        >
          {t('新建')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            store.changeData({
              importModalVisible: true,
              file: '',
            });
          }}
        >
          {t('导入')}
        </Button>
        <Modal
          maskCloseAble={null}
          visible={importModalVisible}
          title={t('导入')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={!file}
              loading={!dataLoading}
              onClick={() => {
                const formData = new FormData();
                formData.append('file', file);
                store.uploadFile(formData);
                return false;
              }}
            >
              {t('确认上传')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!dataLoading}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
        </Modal>
        <Button
          type="primary"
          loading={dataLoading === 0}
          onClick={() => {
            store.exportList({ params: assign({}, limit, { pageNum: 1 }) });
          }}
        >
          {t('导出')}
        </Button>
        <Button
          type="primary"
          loading={dataLoading === 0}
          onClick={() => {
            store.downloadTemplate();
          }}
        >
          {t('模板下载')}
        </Button>
      </section>
    );
  }
}

Handle.propTypes = {
  limit: PropTypes.shape().isRequired,
  dataLoading: PropTypes.number,
  importModalVisible: PropTypes.bool,
  file: PropTypes.shape(),
};
export default Handle;
