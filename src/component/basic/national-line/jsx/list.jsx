import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      dataLoading,
      list,
      pageInfo,
      recordId,
      operationModalVisible,
    } = this.props;

    const columns = [
      {
        title: t('目的地'),
        width: 100,
        render: (r) => {
          if (r.countryName) {
            return (
              // eslint-disable-next-line jsx-a11y/anchor-is-valid
              <a
                onClick={() => {
                  // 编辑
                  store.changeData({
                    actionType: 'UPDATE',
                    modalVisible: true,
                    modalInput: {
                      countryName: r.countryName,
                      enabled: String(r.enabled),
                      nationalLineType: r.nationalLineType,
                      warehouse: '',
                      subWarehouseIds: r.subWarehouseIds ? r.subWarehouseIds.split(',').map((v) => (+v)) : [],
                    },
                    recordId: r.id,
                  });
                  // 由于后天未返回主仓 所以拿子仓的第一个去查询主仓
                  const subWarehouseIds = r.subWarehouseIds ? r.subWarehouseIds.split(',').map((v) => (+v)) : [];
                  if (subWarehouseIds.length) {
                    store.getEditWarehouse({
                      data: {
                        subWarehouseId: subWarehouseIds[0],
                      },
                    });
                  }
                }}
              >
                {r.countryName}
              </a>
            );
          }
          return '';
        },
      },
      {
        title: t('状态'),
        width: 100,
        render: 'enabledName',
      },
      {
        title: t('编辑时间'),
        width: 130,
        render: 'lastUpdateTime',
      },
      {
        title: t('国家线'),
        width: 120,
        render: 'nationalLineTypeName',
      },
      {
        title: t('子仓'),
        width: 120,
        render: 'subWarehouseNames',
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 160,
        render: (record) => (
          <div>
            <Button
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => store.changeData({ operationModalVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!dataLoading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={operationModalVisible}
          param={{
            operateId: recordId,
            operateCode: 'NATIONAL_LINE_CONFIG_OPERATE',
          }}
          onCancel={() => store.changeData({ operationModalVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  dataLoading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  operationModalVisible: PropTypes.bool,
};

export default List;
