import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { formdataPost } from '@src/server/common/fileFetch';
import fileSaver from 'file-saver';
import { getWarehouseApi, dictSelect, getSubWarehouseSelectList } from '@src/server/basic/dictionary';
import { getSubWarehouseById, countryQueryApi } from '@src/server/common/common';
import { STATISTICAL_DOWNLOAD, STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import {
  downloadTemplateApi,
  nationalLineExportApi,
  nationalLineInsertApi,
  nationalLineQueryApi,
  nationalLineUpdateApi,
  nationalLineImportURL,
} from './server';
import { clearEmpty } from '../../../lib/deal-func';
import showMessage from '../../../lib/modal';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  countryName: '', // 目的地
  nationalLineType: [], // 国家线
  enabled: '', // 状态 0 禁用 1启用
  beginTime: '', // 开始时间
  endTime: '', // 结束时间
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  dataLoading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  count: 0, // 表格总条数
  pageSizeOptions: [20, 50, 100], // 表格页显示条数
  countryList: [], // 国家列表
  nationalLineList: [], // 国家线
  enabledList: [{
    label: t('开启'),
    value: '1',
  }, {
    label: t('关闭'),
    value: '0',
  }],
  modalInput: { // 新增和编辑表单内容
    countryName: '',
    enabled: '',
    nationalLineType: '',
    warehouse: '',
    subWarehouseIds: [],
  },
  modalVisible: false, // 新增修改弹窗
  warehouseList: [],
  subWarehouseList: [],
  importModalVisible: false, // 导入弹出框是否显示 默认不显示
  file: '', // 导入文件对象
  currentWarehouseList: [], // 权限仓库列表
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 更新列表
  * refresh() {
    const { limit } = yield '';
    yield this.handlePaginationChange({ pageNum: 1 });
    yield this.changeData({
      limit: { ...limit, pageNum: 1 },
    });
  },
  * queryCountry() {
    const res = yield countryQueryApi();
    if (res.code === '0') {
      yield this.changeData({
        countryList: res.info,
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * getDict() {
    const [
      res,
    ] = yield Promise.all([
      dictSelect({ catCode: ['NATIONAL_LINE_TYPE', 'COUNTRY_TYPE'] }),
    ]);
    if (res.code === '0') {
      yield this.changeData({
        // 国家线
        nationalLineList: res.info.data.find((item) => item.catCode === 'NATIONAL_LINE_TYPE').dictListRsps,
        // 目的地
        countryList: res.info.data.find((item) => item.catCode === 'COUNTRY_TYPE').dictListRsps.map((v) => ({
          countryName: v.dictNameZh,
        })),
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 页面初始化
  * init() {
    const { currentWarehouseList } = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
    });
    const { warehouseId } = 'nav';
    // 查询仓库
    const warehouseData = yield getWarehouseApi({ enabled: 1 });
    if (warehouseData.code === '0') {
      yield this.changeData({
        warehouseList: warehouseData.info.data,
      });
    } else {
      Modal.error({ title: warehouseData.msg });
    }

    yield this.getSubWarehouse({ data: { warehouseVal: warehouseId } });

    // 通过字典获取国家线
    yield this.getDict();
    // yield this.queryCountry(); // 从字典获取
  },

  /**
   * 搜索
   */
  * search() {
    markStatus('dataLoading');
    const { limit, pageInfo } = yield '';
    const params = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    const res = yield nationalLineQueryApi(clearEmpty(params, [0]));
    if (res.code === '0') {
      yield this.changeData({
        list: res.info.data,
        pageInfo: {
          ...pageInfo,
          count: res.info.meta.count,
        },
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 下载模板
  * downloadTemplate() {
    try {
      const res = yield downloadTemplateApi();
      const b = yield res.blob();
      const blob = new Blob([b], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('导入模板')}.xlsx`);
    } catch (e) {
      Modal.error({ title: e.reason.message });
    }
  },
  * uploadFile(formData) {
    markStatus('dataLoading');
    formData.append('function_node', '23');
    const res = yield formdataPost(nationalLineImportURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  /**
   * 导出
   * @returns
   */
  * exportList(action) {
    const res = yield nationalLineExportApi(clearEmpty({ ...action.params, pageSize: getSize() }, [0]));
    if (res.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 新增
  * addItem(action) {
    markStatus('dataLoading');
    const res = yield nationalLineInsertApi(clearEmpty({ ...action.params, subWarehouseIds: action.params.subWarehouseIds.join(',') }, [0]));
    if (res.code === '0') {
      Modal.success({ title: t('新建成功') });
      yield this.changeData({ modalVisible: false });
      yield this.refresh();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 修改
  * updateItem(action) {
    markStatus('dataLoading');
    const res = yield nationalLineUpdateApi(clearEmpty({ ...action.params, subWarehouseIds: action.params.subWarehouseIds.join(',') }, [0]));
    if (res.code === '0') {
      Modal.success({ title: t('修改成功') });
      yield this.changeData({ modalVisible: false });
      yield this.refresh();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 获取子仓下拉数据
  * getSubWarehouse(action) {
    const res = yield getSubWarehouseSelectList({ warehouseId: action.data.warehouseVal, enabled: 1 });
    const { modalInput } = yield '';
    if (res.code === '0') {
      yield this.changeData({
        subWarehouseList: res.info.data,
        modalInput: {
          ...modalInput,
          subWarehouseIds: [],
        },
      });
    } else {
      showMessage(res.msg, false);
    }
  },
  // 根据子仓查询主仓
  * getEditWarehouse(action) {
    const res = yield getSubWarehouseById({ id: action.data.subWarehouseId, enabled: 1 });
    const { modalInput } = yield '';
    if (res.code === '0') {
      yield this.changeData({
        modalInput: { ...modalInput, warehouse: res.info.warehouseId },
      });
    } else {
      showMessage(res.msg, false);
    }
  },
};
