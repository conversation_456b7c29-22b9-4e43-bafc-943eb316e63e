import { sendPostRequest } from '../../../server/common/public';
import fileFetch from '../../../server/common/fileFetch';

/**
 * 删除
 */
export const nationalLineDelApi = (param) => sendPostRequest({
  url: '/national_line/delete',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 导出
 */
export const nationalLineExportApi = (param) => sendPostRequest({
  url: '/national_line/export',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 下载模板
 */
export const downloadTemplateApi = () => fileFetch('/wmd/front/national_line/export_template', {
  method: 'POST',
  credentials: 'include',
});

/**
 * 新增
 */
export const nationalLineInsertApi = (param) => sendPostRequest({
  url: '/national_line/insert',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 查询
 */
export const nationalLineQueryApi = (param) => sendPostRequest({
  url: '/national_line/query',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 修改
 */
export const nationalLineUpdateApi = (param) => sendPostRequest({
  url: '/national_line/update',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 获取主仓
 * @param param
 * @returns {*}
 */
// export const getEditWarehouseApi = (param) => sendPostRequest({
//   url: '/sub_warehouse/query_detail',
//   param,
// }, process.env.BASE_URI_WMD);

// 导入接口
export const nationalLineImportURL = `${process.env.WGS_FRONT}/file_import/record/wmd/national_line_import`;
