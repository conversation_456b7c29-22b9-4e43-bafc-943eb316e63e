import { t, i18n } from '@shein-bbl/react';
import React, { Component } from 'react';
import ContainerPage from '@public-component/search-queries/container';
import store from './reducers';
import Header from './jsx/header';
import List from './jsx/list';
import Handle from './jsx/handle';
import ModalAction from './jsx/modal-action';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    return (
      <ContainerPage warehouseChange={(data) => console.log(t('右上角仓库改变'), data)}>
        <Header {...this.props} />
        <Handle {...this.props} />
        <List {...this.props} />
        <ModalAction {...this.props} />
      </ContainerPage>
    );
  }
}

export default i18n(Container);
