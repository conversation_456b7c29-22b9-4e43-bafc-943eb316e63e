import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Message, Modal } from 'shineout';
import { queryParkList } from '@src/lib/dealFunc';
import { formdataPost } from '@src/server/common/fileFetch';
import {
  dictCatQuery, dictSelect, getSubWarehouseSelectList, getWarehouseApi,
} from '@src/server/basic/dictionary';
import {
  queryArea,
} from '@src/server/basic/area';

import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import {
  addArea, editArea, offArea, getAreaById, areaLocationImportURL,
} from './server';

const delZero = (list) => {
  if (list.popCode && list.popCode.length === 0) {
    delete list.popCode;
  }
};
// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  subWarehouseIds: [],
  area: '',
  floor: '',
  isProduce: '',
  enabled: 1,
  popCode: '',
  areaType: '',
  specialAreaType: '',
  lastUpdateTimeStart: undefined,
  lastUpdateTimeEnd: undefined,
  warehouseIds: [],
  parkTypeList: [],
  automateArea: '',
  storageTags: [],
  boxSpec: '',
  areaCategory: '',
  aircraftBoxStorage: '',
  /** 是否允许拆零占用 0-否 1-是 */
  isSplitAllowed: '',
};

const defaultState = {
  subWarehouseList: [], // list界面子仓数据
  modalSubWarehouseList: [], // 弹窗界面子仓数据
  areaList: [],
  specialAreaList: [], // 特殊类型列表
  dataLoading: false,
  modelVisible: false,
  warehouseIds: '', // 保留右上角仓库，由于init时判断是否要重置子仓等
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: 50,
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  enabledList: [],
  warehouseList: [],
  isProduceList: [],
  recordList: [],
  parkTypeList: [], // 园区
  addModal: false, // 新增Modal
  modalType: '',
  selectedRows: [], // 选中的表格列
  addInfo: {
    warehouseId: '',
    subWarehouseId: '',
    floor: '',
    area: '',
    popCode: [],
    areaType: '',
    isProduce: 1,
    enabled: 1,
    code: '',
    nameEn: '',
    id: '',
    storageTags: [],
    areaOrder: null,
    boxSpec: '',
    areaCategory: '',
    aircraftBoxStorage: 0,
    /** 是否允许拆零占用 0-否 1-是 */
    isSplitAllowed: 0,
  },
  showPop: false,
  code: '',
  parkList: [], // 园区下拉
  preSubWarehouseList: [],
  modalLoading: 1,
  importModalVisible: false, // 导入弹出框是否显示 默认不显示
  file: '', // 导入文件对象
  storageTagList: [], // 存储标签下拉
  currentWarehouseList: [], // 权限仓库列表
  isBoxSpecList: [
    { dictCode: 1, dictNameZh: t('是') },
    { dictCode: 2, dictNameZh: t('否') },
  ],
  aircraftBoxStorageList: [
    { dictCode: 1, dictNameZh: t('是') },
    { dictCode: 0, dictNameZh: t('否') },
  ],
  isSplitAllowedList: [
    { dictCode: 1, dictNameZh: t('是') },
    { dictCode: 0, dictNameZh: t('否') },
  ],
  areaCategoryList: [],
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改Modal里即addInfo属性值
  changeAddInfoData(state, data) {
    Object.assign(state.addInfo, data);
  },
  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { subWarehouseList, warehouseId } = data;
    // 获取园区列表
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      parkList,
      subWarehouseList,
      preSubWarehouseList: subWarehouseList,
      warehouseIds: warehouseId,
      limit: {
        ...this.state.limit,
        subWarehouseIds: [],
        parkTypeList: [],
      },
    });
  },
  // 获取弹框子仓列表
  * getModalSubWarehouse(param) {
    const { warehouseId, subWarehouseId } = param;
    const res = yield getSubWarehouseSelectList({ warehouseId, enabled: 1 });
    if (res.code === '0') {
      yield this.changeData({
        modalSubWarehouseList: res.info.data,
      });
      // 编辑modal初始化
      if (res.info.data.length && subWarehouseId) {
        yield this.changeData({
          showPop: (res.info.data.filter((x) => x.id === subWarehouseId)[0].subWarehouseType === 3),
        });
      }
    } else {
      Modal.error({ title: res.msg });
    }
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const [data, warehouseList] = yield Promise.all([
      dictSelect({ catCode: ['ENABLED', 'IS_PRODUCE', 'SUB_WAREHOUSE_TYPE', 'AREA_TYPE', 'SPECIAL_AREA_TYPE', 'STORAGE_TAG', 'AREA_CATEGORY'] }),
      getWarehouseApi({ enabled: 1 }),
      dictCatQuery({ pageNum: 1, pageSize: 50 }),
    ]);
    if (data.code === '0') {
      yield this.changeData({
        enabledList: data.info.data.find((x) => x.catCode === 'ENABLED').dictListRsps,
        isProduceList: data.info.data.find((x) => x.catCode === 'IS_PRODUCE').dictListRsps,
        areaList: data.info.data.find((x) => x.catCode === 'AREA_TYPE').dictListRsps,
        specialAreaList: data.info.data.find((x) => x.catCode === 'SPECIAL_AREA_TYPE').dictListRsps,
        warehouseList: warehouseList.info.data,
        storageTagList: data.info.data.find((x) => x.catCode === 'STORAGE_TAG').dictListRsps,
        areaCategoryList: data.info.data.find((x) => x.catCode === 'AREA_CATEGORY').dictListRsps,
      });
    } else {
      Modal.error({ title: data.msg });
    }
    // 获取子仓列表
    const { warehouseIds } = yield this.state;
    const { subWarehouseList, warehouseId, currentWarehouseList } = yield 'nav';
    // 获取园区列表
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      parkList,
      preSubWarehouseList: subWarehouseList,
      currentWarehouseList,
    });
    if (warehouseIds !== warehouseId) {
      yield this.warehouseChange({ subWarehouseList, warehouseId });
    }
  },
  // 搜索
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    const { limit, pageInfo } = this.state;
    const warehouseIds = warehouseId ? [warehouseId] : [];
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseIds,
    };
    markStatus('loading');
    if (param.subWarehouseIds.length === 0) {
      delete param.subWarehouseIds;
    }
    const data = yield queryArea(param);
    if (data.code === '0') {
      yield this.changeData({
        selectedRows: [],
        list: data.info.data,
        pageInfo: {
          ...this.state.pageInfo,
          count: data.info.meta.count,
        },
      });
    } else {
      Modal.error({ title: data.msg });
    }
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  // 注销
  * off(ids) {
    markStatus('loading');
    const res = yield offArea({ ids });
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },

  * editOrAddShow(param) {
    const { modalType, id } = param;
    yield this.changeData({
      modalType,
    });
    yield this.changeData({
      addModal: true,
    });
    if (modalType) {
      // 回填数据
      const [data] = yield Promise.all([
        getAreaById({ id }),
      ]);
      if (data.code === '0') {
        yield this.changeAddInfoData({
          ...data.info,
          areaOrder: data.info.areaOrder ? data.info.areaOrder : null,
          popCode: data.info.popCode ? data.info.popCode.split(',') : [],
          storageTags: data.info.storageTags,
        });
        const params = {
          warehouseId: data.info.warehouseId,
          subWarehouseId: data.info.subWarehouseId,
        };
        // 请求子仓列表
        yield this.getModalSubWarehouse(params);
      } else {
        Modal.error({ title: data.msg });
      }
    }
  },
  // 点击保存 - 即新增
  * commitData(obj) {
    const { modalType } = this.state;
    // 数据处理
    delZero(obj);
    if (Array.isArray(obj.popCode) && obj.popCode.length > 0) {
      obj.popCode = obj.popCode.join(',');
    }
    let res = '';
    markStatus('modalLoading');
    if (!modalType) {
      res = yield addArea(obj);
    } else {
      res = yield editArea(obj);
    }
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
      yield this.closeModal();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * closeModal() {
    const { formRef } = this.state;
    yield this.changeData({
      addModal: false,
      addInfo: {
        warehouseId: '',
        subWarehouseId: '',
        floor: '',
        area: '',
        popCode: [],
        areaType: '',
        isProduce: 1,
        enabled: 1,
        code: '',
        nameEn: '',
        id: '',
        storageTags: [],
        areaOrder: null,
        aircraftBoxStorage: 0,
        /** 是否允许拆零占用 0-否 1-是 */
        isSplitAllowed: 0,
      },
      modalSubWarehouseList: [],
      showPop: false,
    });
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },

  /**
   * 导入
   */
  * uploadFile(formData) {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    markStatus('loading');
    formData.append('function_node', '48');
    formData.append('request_json', JSON.stringify({ warehouse_id: warehouseId }));
    const res = yield formdataPost(areaLocationImportURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
