import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Input, Select } from 'shineout';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      enabledList,
      isProduceList,
      subWarehouseList,
      areaList,
      specialAreaList,
      parkList,
      preSubWarehouseList,
      storageTagList,
      isBoxSpecList,
      areaCategoryList,
      isSplitAllowedList,
      aircraftBoxStorageList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 100 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
        >
          <Select
            label={t('园区')}
            name="parkTypeList"
            required
            multiple
            compressed
            keygen="parkType"
            format="parkType"
            renderItem="parkName"
            data={parkList}
            clearable
            placeholder={t('全部')}
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              if (value && value.length > 0) {
                const { parkSubWarehouseList } = fliterSubwarehouse(value);
                store.changeData({
                  subWarehouseList: parkSubWarehouseList,
                });
              } else {
                store.changeData({
                  subWarehouseList: preSubWarehouseList,
                });
              }
              store.changeLimitData({
                parkTypeList: value,
                subWarehouseIds: [],
              });
            }}
          />
          <Select
            label={t('子仓')}
            name="subWarehouseIds"
            data={subWarehouseList}
            keygen="id"
            format="id"
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Input label={t('库区')} name="area" placeholder={t('请输入')} />
          <Input label={t('楼层')} name="floor" placeholder={t('请输入')} />
          <Select
            label={t('是否箱规库区')}
            name="boxSpec"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...isBoxSpecList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            clearable
            keygen="dictCode"
            format="dictCode"
            name="isSplitAllowed"
            renderItem="dictNameZh"
            placeholder={t('请选择')}
            label={t('是否允许拆零占用')}
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...isSplitAllowedList]}
            onFilter={(text) => (d) => d.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
          />
          <Select
            clearable
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('请选择')}
            name="aircraftBoxStorage"
            label={t('是否飞机盒整箱存储')}
            data={aircraftBoxStorageList}
            onFilter={(text) => (d) => d.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
          />
          <Select
            label={t('是否生成波次')}
            name="isProduce"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...isProduceList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('状态')}
            name="enabled"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...enabledList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Input label={t('道口')} name="popCode" placeholder={t('请输入')} />
          <Select
            label={t('库区类型')}
            name="areaType"
            data={[...[{ dictCode: '', dictNameZh: t('全部') }], ...areaList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('库区类别')}
            name="areaCategory"
            data={[...[{ dictCode: '', dictNameZh: t('全部') }], ...areaCategoryList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('特殊类型')}
            name="specialAreaType"
            data={[...[{ dictCode: '', dictNameZh: t('全部') }], ...specialAreaList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('是否自动化库区')}
            name="automateArea"
            data={[
              { dictCode: 1, dictNameZh: t('是') },
              { dictCode: 0, dictNameZh: t('否') },
            ]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            clearable
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('存储标签')}
            name="storageTags"
            data={storageTagList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            clearable
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
          />
          <DateRangePicker
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['lastUpdateTimeStart', 'lastUpdateTimeEnd']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('编辑时间')}
            span={2}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  isProduceList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  areaList: PropTypes.arrayOf(PropTypes.shape()),
  specialAreaList: PropTypes.arrayOf(PropTypes.shape()),
  parkList: PropTypes.arrayOf(PropTypes.shape()),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  storageTagList: PropTypes.arrayOf(PropTypes.shape()),
  isBoxSpecList: PropTypes.arrayOf(PropTypes.shape()),
  areaCategoryList: PropTypes.arrayOf(PropTypes.shape()),
  isSplitAllowedList: PropTypes.arrayOf(PropTypes.shape()),
  aircraftBoxStorageList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
