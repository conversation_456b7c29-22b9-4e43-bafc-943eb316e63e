import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Button, Popover, Tag,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

const getCol = (d) => {
  const showPopover = d.storageTagNameList.length > 3;

  return (
    <div>
      {
        showPopover
        && (
          <Popover
            type="info"
            position="left"
            style={{ padding: 5 }}
            background="#fff"
            border="#fff"
          >
            {d.storageTagNameList.map((item) => <Tag style={{ marginBottom: 5 }}>{item}</Tag>)}
          </Popover>
        )
      }
      {
        showPopover
          ? (
            <div>
              {d.storageTagNameList.slice(0, 3).map((item) => <Tag style={{ marginBottom: 5 }}>{item}</Tag>)}
              ...
            </div>
          )
          : d.storageTagNameList.map((item) => <Tag style={{ marginBottom: 5 }}>{item}</Tag>)
      }
    </div>

  );
};
class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
    } = this.props;
    const columns = [
      {
        title: t('库区ID'),
        width: 80,
        render: (record) => (
          <Button
            style={{ padding: 0, userSelect: 'text' }}
            type="link"
            onClick={() => {
              store.editOrAddShow({ modalType: 1, id: record.id });
            }}
          >
            {record.id}
          </Button>
        ),
      },
      {
        title: t('库区顺序号'),
        render: (r) => r.areaOrder || '',
        width: 100,
      },
      {
        title: t('库区'),
        render: 'area',
        width: 100,
      },
      {
        title: t('楼层'),
        render: 'floor',
        width: 80,
      },
      {
        title: t('道口'),
        render: 'popCode',
        width: 80,
      },
      {
        title: t('子仓'),
        render: 'subWarehouse',
        width: 100,
      },
      {
        title: t('园区'),
        render: 'parkTypeName',
        width: 100,
      },
      {
        title: t('仓库'),
        render: 'warehouse',
        width: 100,
      },
      {
        title: t('是否生成波次'),
        render: 'isProduceName',
        width: 110,
      },
      {
        title: t('状态'),
        render: 'enabledName',
        width: 100,
      },
      {
        title: t('库区类型'),
        render: 'areaTypeName',
        width: 100,
      },
      {
        title: t('特殊类型'),
        render: 'specialAreaTypeName',
        width: 100,
      },
      {
        title: t('是否自动化库区'),
        render: 'automateAreaName',
        width: 90,
      },
      {
        title: t('存储标签'),
        width: 100,
        render: (d) => getCol(d),
      },
      {
        title: t('是否箱规库区'),
        render: 'boxSpecName',
        width: 120,
      },
      {
        title: t('库区类别'),
        render: 'areaCategoryName',
        width: 120,
      },
      {
        title: t('是否飞机盒整箱存储'),
        render: 'aircraftBoxStorageName',
        width: 80,
      },
      {
        title: t('是否允许拆零占用'),
        render: 'isSplitAllowedName',
        width: 80,
      },
      {
        title: t('操作'),
        width: 100,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];
    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              className: styles.pagination,
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'AREA_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  editAddressModal: PropTypes.bool,
  editAddressInfo: PropTypes.shape(),
  provinceList: PropTypes.arrayOf(PropTypes.shape()),
  cityList: PropTypes.arrayOf(PropTypes.shape()),
  districtList: PropTypes.arrayOf(PropTypes.shape()),
  contactModal: PropTypes.bool,
  contactInfo: PropTypes.shape(),
};

export default List;
