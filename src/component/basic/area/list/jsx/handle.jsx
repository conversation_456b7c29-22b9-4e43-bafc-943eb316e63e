import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Input, Modal, Select, Rule, Popover,
} from 'shineout';
import styles from '@src/component/style.less';
import FileDelayUpload from '@src/component/public-component/upload/fileDelayUpload';
import { t } from '@shein-bbl/react';
import store from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      selectedRows,
      addModal,
      addInfo,
      enabledList,
      isProduceList,
      modalSubWarehouseList,
      areaList,
      specialAreaList,
      showPop,
      modalType,
      modalLoading,
      loading,
      importModalVisible, // 导入弹出框是否显示 默认不显示
      file, // 导入文件对象
      storageTagList,
      currentWarehouseList, // 权限仓库
      isBoxSpecList,
      areaCategoryList,
      isSplitAllowedList,
      aircraftBoxStorageList,
    } = this.props;
    const rules = Rule({
      popCodeRule: {
        func: (val, formData, callback) => {
          if (showPop && !formData.popCode.length) {
            callback(new Error(t('请输入道口')));
          }
          callback(true);
        },
      },
    });
    const crossList = Array.from(new Array(100), (val, index) => index);
    const ids = selectedRows.map((v) => v.id);
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          icon="plus"
          onClick={() => store.editOrAddShow({ modalType: 0 })}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          loading={!loading}
          disabled={ids.length === 0}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => {
              store.off(ids);
            }}
          >
            {t('您确认要注销吗?')}
          </Popover.Confirm>
          {t('注销')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            store.changeData({
              importModalVisible: true,
              file: '',
            });
          }}
        >
          {t('导入')}
        </Button>
        <Button
          size="default"
          onClick={() => {
            window.location.href = `${process.env.BASE_URI_WMD}/excel/export_template?type=area_import.xlsx`;
          }}
        >
          {t('下载导入模板')}
        </Button>
        <Modal
          maskCloseAble={null}
          visible={importModalVisible}
          title={t('导入')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={!file}
              loading={!loading}
              onClick={() => {
                const formData = new FormData();
                formData.append('file', file);
                store.uploadFile(formData);
                return false;
              }}
            >
              {t('确认上传')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!loading}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
        </Modal>
        <Modal
          maskCloseAble={null}
          visible={addModal}
          width={800}
          title={!modalType ? t('新增') : t('编辑')}
          onClose={() => {
            store.closeModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!modalLoading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              store.commitData(addInfo);
            }}
            onChange={(value) => {
              store.changeData({
                addInfo: value,
              });
            }}
            value={addInfo}
            inline
            formRef={(f) => store.changeData({ formRef: f })}
          >
            <Form.Item required label={t('仓库')}>
              <Select
                name="warehouseId"
                data={currentWarehouseList}
                keygen="id"
                format="id"
                rules={[rules.required(t('请选择仓库'))]}
                placeholder={t('请选择')}
                renderItem="nameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.changeAddInfoData({
                    warehouseId: value,
                    subWarehouseId: '',
                  });
                  store.getModalSubWarehouse({ warehouseId: value });
                }}
              />
            </Form.Item>
            <Form.Item required label={t('子仓')}>
              <Select
                name="subWarehouseId"
                data={modalSubWarehouseList}
                keygen="id"
                format="id"
                rules={[rules.required(t('请选择子仓'))]}
                placeholder={t('请选择')}
                renderItem="nameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  // eslint-disable-next-line max-len
                  if (modalSubWarehouseList.filter((item) => item.id === value)[0].subWarehouseType === 3) {
                    store.changeData({
                      showPop: true,
                    });
                  } else {
                    store.changeData({
                      showPop: false,
                    });
                    store.changeAddInfoData({
                      popCode: [],
                    });
                  }
                  store.changeAddInfoData({
                    subWarehouseId: value,
                  });
                }}
              />
            </Form.Item>
            <Form.Item required label={t('楼层')}>
              <Input.Number
                hideArrow
                style={{ width: 180 }}
                name="floor" placeholder={t('必填,数字最多2位')} min={0} max={99} digits={0}
                rules={[rules.required(t('请输入楼层'))]}
              />
            </Form.Item>
            <Form.Item required label={t('库区')}>
              <Input style={{ width: 180 }} disabled={!!modalType} name="area" rules={[rules.required(t('请输入库区'))]} />
            </Form.Item>
            <Form.Item required label={t('道口')}>
              {
                !modalType ? (
                  <Select
                    name="popCode"
                    disabled={!showPop}
                    data={crossList}
                    keygen
                    format={(r) => r + 1}
                    placeholder={t('请选择')}
                    renderItem={(r) => r + 1}
                    style={{ width: 180 }}
                    rules={[rules.popCodeRule()]}
                    renderUnmatched={(r) => r + 1 || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                    onFilter={(text) => (d) => String(d + 1).toLowerCase().indexOf(String(text).toLowerCase()) >= 0}
                    clearable
                    multiple
                    compressed
                  />
                ) : (
                  <Input
                    disabled={!showPop}
                    rules={[rules.popCodeRule()]}
                    name="popCode" placeholder={t('多个道口，请用英文隔开')}
                    onChange={(e) => {
                      const inputValue = e.split(',');
                      const lastValue = inputValue.slice(0, 2);
                      store.changeAddInfoData({
                        popCode: lastValue,
                      });
                    }}
                  />
                )
              }
            </Form.Item>
            <Form.Item required label={t('库区类型')}>
              <Select
                name="areaType"
                data={areaList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择库区类型'))]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('特殊类型')}>
              <Select
                name="specialAreaType"
                data={specialAreaList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                  // rules={[rules.required(t('请选择特殊类型'))]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('是否生成波次')}>
              <Select
                name="isProduce"
                data={isProduceList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择是否生成波次'))]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('状态')}>
              <Select
                name="enabled"
                data={enabledList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择状态'))]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('是否自动化库区')}>
              <Select
                name="automateArea"
                data={[
                  { dictCode: 1, dictNameZh: t('是') },
                  { dictCode: 0, dictNameZh: t('否') },
                ]}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择是否自动化库区'))]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item label={t('存储标签')}>
              <Select
                name="storageTags"
                data={storageTagList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                // rules={[rules.required(t('请选择存储标签'))]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                multiple
                compressed
              />
            </Form.Item>
            <Form.Item label={t('是否箱规库区')}>
              <Select
                name="boxSpec"
                data={isBoxSpecList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r?.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
              />
            </Form.Item>
            <Form.Item label={t('库区类别')}>
              <Select
                name="areaCategory"
                data={areaCategoryList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r?.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
              />
            </Form.Item>
            <Form.Item label={t('库区顺序号')}>
              <Input.Number
                style={{ width: 180 }}
                name="areaOrder"
                hideArrow
                allowNull
                digits={0}
                max={999}
                min={1}
              />
            </Form.Item>
            <Form.Item required label={t('是否飞机盒整箱存储')}>
              <Select
                defaultValue={0}
                keygen="dictCode"
                format="dictCode"
                style={{ width: 180 }}
                renderItem="dictNameZh"
                placeholder={t('请选择')}
                name="aircraftBoxStorage"
                data={aircraftBoxStorageList}
                rules={[rules.required(t('请选择'))]}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                renderUnmatched={(r) => r?.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
              />
            </Form.Item>
            <Form.Item required label={t('是否允许拆零占用')}>
              <Select
                defaultValue={0}
                keygen="dictCode"
                format="dictCode"
                name="isSplitAllowed"
                style={{ width: 180 }}
                renderItem="dictNameZh"
                placeholder={t('请选择')}
                data={isSplitAllowedList}
                label={t('是否允许拆零占用')}
                onFilter={(text) => (d) => d.dictNameZh?.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  addModal: PropTypes.bool,
  modalType: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  addInfo: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  isProduceList: PropTypes.arrayOf(PropTypes.shape()),
  modalSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  areaList: PropTypes.arrayOf(PropTypes.shape()),
  specialAreaList: PropTypes.arrayOf(PropTypes.shape()),
  showPop: PropTypes.bool,
  modalLoading: PropTypes.number,
  loading: PropTypes.number,
  importModalVisible: PropTypes.bool,
  file: PropTypes.shape(),
  storageTagList: PropTypes.arrayOf(PropTypes.shape()),
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  isBoxSpecList: PropTypes.arrayOf(PropTypes.shape()),
  areaCategoryList: PropTypes.arrayOf(PropTypes.shape()),
  isSplitAllowedList: PropTypes.arrayOf(PropTypes.shape()),
  aircraftBoxStorageList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
