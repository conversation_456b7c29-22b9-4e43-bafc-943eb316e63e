import { sendPostRequest } from '@src/server/common/public';

// 查询
export const getAreaById = (param) => sendPostRequest({
  url: '/area/get_area_by_id',
  param,
}, process.env.BASE_URI_WMD);

// 添加
export const addArea = (param) => sendPostRequest({
  url: '/area/add',
  param,
}, process.env.BASE_URI_WMD);

// 修改
export const editArea = (param) => sendPostRequest({
  url: '/area/modify',
  param,
}, process.env.BASE_URI_WMD);

// 禁止
export const offArea = (param) => sendPostRequest({
  url: '/area/disable',
  param,
}, process.env.BASE_URI_WMD);

export const areaLocationImportURL = `${process.env.WGS_FRONT}/file_import/record/wmd/area_import`;
