import { i18n } from '@shein-bbl/react';
import React, { Component } from 'react';
import ContainerPage from '@public-component/search-queries/container';
import { TopAreaHeight } from '@src/lib/constants';
import store from './reducers';
import Header from './jsx/header';
import List from './jsx/list';
import Handle from './jsx/handle';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    return (
      <ContainerPage
             // 减去顶部
        customStyle={{ height: `calc(100vh - ${TopAreaHeight}px)` }}
      >
        <Header {...this.props} />
        <Handle {...this.props} />
        <List {...this.props} />
      </ContainerPage>
    );
  }
}

export default i18n(Container);
