import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
// import { paramTrim } from '@src/lib/deal-func';
import { authListAPI } from '@src/server/user/auth';
// import { getWarehouseId } from '../../../lib/dealFunc';
import { commonWmsCloud } from '../../../lib/cloud';

/**
 * 递归处理树形数据
 * @param menu
 * @returns {*}
 */
function normalizeMenu(menu, parentIdMap = '') {
  const allLink = menu.map((item) => {
    if (item.type === '1') {
      const { id } = item;
      const currentIdMap = parentIdMap ? `${parentIdMap}-${id}` : id;
      return {
        ...item,
        children: normalizeMenu(item.children, currentIdMap),
        parentIdMap: currentIdMap, // 便于层级查找
      };
    }
    return {};
  });
  return allLink.filter((item) => Object.keys(item).length);
}

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  title: '', // 菜单名称
  rule: '', // 菜单路由
  system: undefined, // 系统
};

const defaultState = {
  loading: 1, // 0 loading, 1 load success, 2 load fail
  collapseStatus: false, // header 是否折叠
  pdaUrlRE: /^\/new-pda/, // pda 路由匹配
  limit: defaultLimit,
  list: [], // 过滤条件后展示列表
  systemList: [
    {
      dictCode: '1',
      dictNameZh: 'WMS',
    },
    {
      dictCode: '2',
      dictNameZh: 'MOT',
    },
  ], // 系统下拉框
  originalPdaList: [], // 原始pda菜单，来自于系统配置的菜单接口。
  originalWmsList: [], // 原始wms菜单，来自于系统配置的菜单接口。
  assetList: [], // 存储到云配置的列表，提供给 pda 使用，最重要的对象
  ossFileDir: '/pda-guideline', // oss 文件存储目录
  addVisible: false, // 新增新手指引弹窗
  addObj: { id: '', title: '', rule: '' },
  assetType: { image: t('图片'), video: t('视频') },
  viewVisible: false, // 查看弹窗
  viewObj: { title: '', rule: '', assets: [] }, // 弹窗所用对象
  uploadVisible: false, // 上传资源
  uploadObj: { title: '', file: [] }, // 上传的资源
  // 编辑
  editVisible: false,
  // 图片/视频的数据结构: {title: '', type: '', url: '', name: ''}
  editAsset: {
    title: '', type: '', url: '', name: '', file: [], index: '',
  },
  isWms: false,
};

/**
 * @description 根据图片/视频名称返回类型
 * @param {string} name 文件名称
 * @return {string} 'image' or 'video' or ''
 */
function distinguishType(name) {
  const imageExt = ['JPEG', 'TIFF', 'GIF', 'PNG', 'BMP', 'SVG', 'JPG', 'PCX', 'TGA', 'TIF', 'WEBP'];
  const videoExt = ['AVI', 'MP4', 'MOV', 'RMVB', 'FLV', '3GP'];
  const ext = name.split('.').pop().toUpperCase();
  if (imageExt.includes(ext)) {
    return 'image';
  }
  if (videoExt.includes(ext)) {
    return 'video';
  }
  return '';
}

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    yield this.changeLimitData(defaultLimit);
  },
  changeAddObjData(state, data) {
    Object.assign(state, {
      addObj: {
        ...state.addObj,
        ...data,
      },
    });
  },
  changeUploadObjData(state, data) {
    Object.assign(state, {
      uploadObj: {
        ...state.uploadObj,
        ...data,
      },
    });
  },
  changeAssetObjData(state, data) {
    Object.assign(state, {
      editAsset: {
        ...state.editAsset,
        ...data,
      },
    });
  },
  // 页面初始化
  * init() {
    const [listRes, pdaGuidelineRes] = yield Promise.all([
      authListAPI({ is_page: true }),
      commonWmsCloud.get(process.env.CLOUD_KEY),
    ]);
    // 1、获取pda菜单列表
    if (listRes.code === '0') {
      // 需要过滤掉接口的节点
      const filterMenu = (srcList) => srcList.filter((item) => this.state.pdaUrlRE.test(item.rule))
        .map((item) => {
          const {
            id, rule, title, children,
          } = item;
          let newChildren = null;
          if (children && children.length) {
            newChildren = filterMenu(children);
          }
          return {
            id,
            title,
            rule,
            children: newChildren,
          };
        });
      const allList = listRes.info && listRes.info.list;
      if (allList.length) {
        const pdaList = allList.find(({ rule }) => this.state.pdaUrlRE.test(rule));
        const wmsList = normalizeMenu(allList || []).filter((v) => v.rule !== '/pda/main-menu' && v.rule !== '/new-pda/main-menu' && v.rule !== '/home-page');
        yield this.changeData({
          originalPdaList: filterMenu(pdaList.children),
          originalWmsList: wmsList,
        });
      }
    } else {
      Modal.error({
        title: listRes.msg,
      });
    }
    // 2、获取云配置
    this.state.assetList = pdaGuidelineRes;
  },

  /**
   * 搜索
   */
  * search() {
    markStatus('loading');
    const { limit, pdaUrlRE } = yield '';
    const {
      title, rule, system,
    } = limit;
    this.state.assetList = yield commonWmsCloud.get(process.env.CLOUD_KEY);
    if (this.state.assetList?.length) {
      this.state.list = this.state.assetList
        .filter((item) => (item.title.includes(title) && item.rule.includes(rule)))
        .filter((item) => {
          if (system) {
          // system：2代表下拉选择mot
            return system === '2' ? pdaUrlRE.test(item.rule) : !pdaUrlRE.test(item.rule);
          }
          return true;
        });
    } else {
      this.state.assetList = [];
      this.state.list = [];
    }
  },
  // 新增配置菜单对象，对象格式为：{ title, rule, assets: [] }
  * confirmAddObj() {
    markStatus('loading');
    const { title, rule } = this.state.addObj;
    // 检查是否已存在
    const matchObj = this.state.assetList.find((item) => item.rule === rule);
    if (matchObj) {
      Modal.error({
        title: t('菜单已添加，请勿重复操作'),
      });
    } else {
      const cloudStore = [...this.state.assetList, { title, rule, assets: [] }];
      const saveSuccess = yield this.savePdaGuideline({ list: cloudStore });
      if (saveSuccess) {
        Message.success(t('新增成功'));
        yield this.changeData({ assetList: cloudStore });
        yield this.search();
      } else {
        Modal.error({ title: t('新增失败，请稍后再试。') });
      }
    }
    yield this.changeData({ addVisible: false });
  },
  /**
  * @description 菜单关联图片/视频
  * @return void
  */
  * associateResources() {
    markStatus('loading');
    // 构造资源对象
    const { title, file: [{ name, url }] } = this.state.uploadObj;
    // 图片/视频资源数据结构
    const asset = {
      title,
      name,
      type: distinguishType(name),
      url,
    };
    const matchObj = this.state.assetList.find((item) => item.rule === this.state.viewObj.rule);
    if (matchObj) {
      matchObj.assets.unshift(asset);
    } else {
      Modal.error({
        title: t('菜单匹配失败，请稍后重试'),
      });
    }
    const saveSuccess = yield this.savePdaGuideline();
    if (saveSuccess) {
      Message.success(t('保存成功'));
      this.state.viewObj.assets.unshift(asset);
    } else {
      Modal.error({
        title: t('保存失败，请稍后重试'),
      });
    }
    yield this.changeData({ uploadVisible: false });
  },
  // 整个菜单删除
  * deleteMenu({ menu }) {
    markStatus('loading');
    // 查找资源
    const index = this.state.assetList.findIndex((item) => item.rule === menu.rule);
    this.state.assetList.splice(index, 1);
    const saveSuccess = yield this.savePdaGuideline();
    if (saveSuccess) {
      Message.success(t('删除成功'));
      yield this.search();
    } else {
      Modal.error({
        title: t('删除失败，请稍后再试'),
      });
      this.state.assetList.splice(index, 0, menu);
    }
  },
  /**
  * @description 菜单删除图片/视频
  * @param {object} asset 删除的图片/视频对象
  * @param number index 删除的图片/视频对象在assets数组中的索引
  * @return void
  */
  * deleteAsset({ asset, index }) {
    markStatus('loading');
    // 查找资源
    const matchObj = this.state.assetList.find((item) => item.rule === this.state.viewObj.rule);
    if (matchObj) {
      matchObj.assets.splice(index, 1);
    } else {
      Modal.error({
        title: t('菜单资源匹配失败，请稍后重试'),
      });
    }
    const saveSuccess = yield this.savePdaGuideline();
    if (saveSuccess) {
      Message.success(t('删除成功'));
      this.state.viewObj.assets.splice(index, 1);
    } else {
      Modal.error({
        title: t('删除失败，请稍后重试'),
      });
      // 恢复
      matchObj.assets.splice(index, 0, asset);
    }
  },
  /**
  * @description 菜单编辑图片/视频
  * @return void
  */
  * confirmEditAsset() {
    markStatus('loading');
    const {
      title, file, index,
    } = this.state.editAsset;
    let { name, url } = this.state.editAsset;
    if (file.length) {
      name = file[0].name;
      url = file[0].url;
    }
    // 构造新的资源对象
    const asset = {
      title,
      name,
      type: distinguishType(name),
      url,
    };
    const matchObj = this.state.assetList.find((item) => item.rule === this.state.viewObj.rule);
    if (matchObj) {
      matchObj.assets.splice(index, 1, asset);
    } else {
      Modal.error({
        title: t('菜单资源匹配失败，请稍后重试'),
      });
    }
    const saveSuccess = yield this.savePdaGuideline();
    if (saveSuccess) {
      Message.success(t('保存成功'));
      this.state.viewObj.assets.splice(index, 1, asset);
    } else {
      Modal.error({
        title: t('保存失败，请稍后重试'),
      });
    }
    yield this.changeData({ editVisible: false });
  },
  // 保存数据
  * savePdaGuideline({ list }) {
    markStatus('loading');
    if (!list || !Array.isArray(list)) {
    // 再判断下 this.state.assetList
      if (Array.isArray(this.state.assetList)) {
        list = this.state.assetList;
      } else {
        Modal.error({
          title: t('系统错误，请联系管理员'),
        });
        throw new Error('this.state.assetList should be an array');
      }
    }
    const saveSuccess = yield new Promise((r) => {
      commonWmsCloud.set(process.env.CLOUD_KEY, list).then(() => r(true));
    });
    // 每次保存完数据后更新下列表数据
    if (saveSuccess) {
      this.state.assetList = yield commonWmsCloud.get(process.env.CLOUD_KEY);
      yield this.search();
    }
    return saveSuccess;
  },
};
