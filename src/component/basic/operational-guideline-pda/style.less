/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.handleExportButton{
    margin-right: 6px;
}

.input {
    width: 220px;
  }
  .search_block {
    display: inline-block;
    margin-bottom: 20px;
    padding-left: 20px;
  }
  .search_btn {
    display: inline-block;
    margin-bottom: 10px;
    padding-left: 20px;
  }
  .search_label {
    padding-right: 10px;
  }
  .block_label {
    padding: 0 10px 10px 0;
  }
  .add_label {
    display: inline-block;
    padding-right: 10px;
    width: 120px;
    vertical-align: top;
    text-align: right;
  }
  .required::before {
    content: '*';
    color: #ff0000;
  }
  .handle {
    display: inline-block;
    vertical-align: top;
    margin: 0 20px;
  }
  .mr_b_14 {
    margin-bottom: 14px;
  }

/* header.jsx
---------------------------------------------------------------- */
.headerTimeSelect{
    width: 140px;
 }
 
 .headerUnmatchedText{
     color: #bbb;
 }

.headerSearchTimeLine{
    display: flex;
}

/* list.jsx
---------------------------------------------------------------- */
.listOperationButton{
    font-size: 14px;
    line-height: 1px;
    padding: 0px;
}
.listSection{
    height: 0px; /* 必写为了子domheight - 100%有效 */
    flex: 1;    
}




