import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Button, Modal, Select } from 'shineout';
import globalStyles from '@src/component/style.less';
import store from '../reducers';
import styles from '../style.less';

class Handle extends React.Component {
  render() {
    const {
      loading,
      addVisible,
      addObj,
      originalPdaList,
      originalWmsList,
      systemList,
    } = this.props;
    const inputStyle = {
      width: '200px',
    };
    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          size="default"
          onClick={() => store.changeData({
            addVisible: true,
            addObj: {
              id: '', title: '', rule: '',
            },
          })}
        >
          {t('新增')}
        </Button>
        <Modal
          visible={addVisible}
          maskCloseAble={null}
          onClose={() => store.changeData({ addVisible: false })}
          title={t('新增新手操作指引菜单')}
          footer={[
            <Button
              key="cancel"
              onClick={() => store.changeData({ addVisible: false })}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              key="comfirm"
              loading={!loading}
              disabled={!addObj.id}
              onClick={() => store.confirmAddObj()}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={`${styles.block_label} guideline-add`}>
            <span className={styles.add_label}>
              {t('系统')}
              :
            </span>
            <Select
              name="systemName"
              data={systemList}
              style={inputStyle}
              value={addObj.system}
              keygen="dictCode"
              format="dictCode"
              placeholder={t('请选择')}
              renderItem="dictNameZh"
              onChange={(val) => {
                store.changeAddObjData({
                  system: val,
                  id: '',
                });
              }}
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            />
          </div>
          <div className={`${styles.block_label} guideline-add`}>
            <span className={styles.add_label}>
              {t('菜单名称')}
              :
            </span>
            <Select
              keygen="id"
              renderItem="title"
              datum={{ format: 'id' }}
              value={addObj.id}
              disabled={({ children }) => (children && children.length)}
              onFilter={(text) => (v) => v.title.indexOf(text) > -1}
              onChange={(val, { id, title, rule }) => {
                if (val) {
                  store.changeAddObjData({ id, title, rule });
                  return;
                }
                store.changeAddObjData({ id: val, title: '', rule: '' });
              }}
              style={inputStyle}
              treeData={addObj.system === '1' ? originalWmsList : originalPdaList}
              placeholder={t('请选择')}
            />
          </div>
          <div className={styles.block_label}>
            <span className={styles.add_label}>
              {t('菜单地址')}
              :
            </span>
            <span>{addObj.rule}</span>
          </div>
          <p style={{ textAlign: 'center', color: 'red', fontSize: '16px' }}>{t('请在新增菜单成功后再上传图片、视频资源。')}</p>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  addVisible: PropTypes.bool,
  addObj: PropTypes.shape(),
  originalPdaList: PropTypes.arrayOf(PropTypes.shape()),
  originalWmsList: PropTypes.arrayOf(PropTypes.shape()),
  systemList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
