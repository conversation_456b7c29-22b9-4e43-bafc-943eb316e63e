import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Button, Modal, Input,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { uploadFileURL } from '@src/server/basic/upload';
import UploadPlus from '@shein-components/upload_plus';
import globalStyles from '@src/component/style.less';
import style from '../style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      // pageInfo,
      viewVisible,
      uploadVisible,
      uploadObj,
      assetType,
      viewObj,
      editVisible,
      editAsset,
      isWms,
    } = this.props;

    const inputStyle = {
      width: '200px',
    };
    const columns = [
      {
        title: t('系统'),
        width: 120,
        render: (d) => ((/^\/new-pda/).test(d.rule) ? 'MOT' : 'WMS'),
      },
      {
        title: t('菜单名称'),
        width: 200,
        render: 'title',
      },
      {
        title: t('菜单地址'),
        width: 300,
        render: 'rule',
      },
      {
        title: t('操作'),
        width: 250,
        fixed: 'right',
        render: (d) => (
          <>
            <Button
              type="primary"
              onClick={() => {
                store.changeData({ viewObj: d, viewVisible: true, isWms: !(/^\/new-pda/).test(d.rule) });
              }}
            >
              {t('编辑已上传资源')}
            </Button>
            <Button
              type="danger"
              onClick={() => Modal.confirm({
                title: t('删除菜单'),
                content: t('是否确认删除菜单?'),
                onOk: () => store.deleteMenu({ menu: d }),
                text: { ok: t('确认'), cancel: t('取消') },
              })}
            >
              {t('删除')}
            </Button>
          </>
        ),
      },
    ];
    const assetColumns = [
      {
        title: t('名称'),
        width: 200,
        render: (d) => (
          <a style={{ color: 'rgb(24,121,255)', textDecoration: 'none' }} href={d.url} target="_blank" rel="noreferrer">{d.title}</a>
        ),
      },
      {
        title: t('类型'),
        width: 40,
        render: (d) => (<span>{assetType[d.type] || t('文本')}</span>),
      },
      {
        title: t('操作'),
        width: 80,
        fixed: 'right',
        render: (d, i) => (
          <>
            <Button
              type="primary"
              onClick={() => store.changeData({
                editVisible: true,
                editAsset: { ...d, file: [], index: i },
              })}
            >
              {t('编辑')}
            </Button>
            <Button
              type="danger"
              onClick={() => Modal.confirm({
                title: t('删除资源'),
                content: t('是否确认删除资源?'),
                onOk: () => store.deleteAsset({ asset: d, index: i }),
                text: { ok: t('确认'), cancel: t('取消') },
              })}
            >
              {t('删除')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            bordered
            fixed="both"
            loading={!loading}
            data={list}
            columns={columns}
            keygen="rule"
            empty={t('暂无数据')}
            size="small"
            style={{ height: '100%' }}
            width={columns.reduce((pre, current) => pre + current.width, 0)}
          />
        </SearchAreaTable>

        <Modal
          width={750}
          visible={viewVisible}
          maskCloseAble={null}
          onClose={() => store.changeData({ viewVisible: false })}
          title={t('编辑已上传资源')}
          footer={(
            <Button
              type="primary"
              key="confrim"
              onClick={() => store.changeData({ viewVisible: false })}
            >
              {t('确定')}
            </Button>
          )}
        >
          <div style={{ paddingBottom: '10px' }}>
            <Button
              type="primary"
              key="upload"
              onClick={() => {
                store.changeData({
                  uploadObj: { title: '', file: [] },
                  uploadVisible: true,
                });
              }}
            >
              {isWms ? t('上传图片/视频/文本') : t('上传图片/视频')}
            </Button>
          </div>
          <div style={{ height: 500 }}>
            <Table
              bordered
              fixed="both"
              loading={!loading}
              data={viewObj.assets}
              columns={assetColumns}
              keygen="title"
              empty={t('暂无数据')}
              size="small"
              width={assetColumns.reduce((pre, current) => pre + current.width, 0)}
            />
          </div>
        </Modal>
        {/* 上传弹窗 */}
        <Modal
          destroy
          visible={uploadVisible}
          maskCloseAble={false}
          title={t('上传图片、视频')}
          onClose={() => store.changeData({ uploadVisible: false })}
          footer={[
            <Button
              loading={!loading}
              key="cancel"
              onClick={() => store.changeData({ uploadVisible: false })}
            >
              {t('取消')}
            </Button>,
            <Button
              key="confrim"
              type="primary"
              loading={!loading}
              disabled={!uploadObj.title || !uploadObj.file.length}
              onClick={() => store.associateResources()}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={style.block_label}>
            <span className={style.add_label}>
              {t('菜单名称')}
              :
            </span>
            <span>{viewObj.title}</span>
          </div>
          <div className={style.block_label}>
            <span className={style.add_label}>
              {t('菜单地址')}
              :
            </span>
            <span>{viewObj.rule}</span>
          </div>
          <div className={style.block_label}>
            <span className={`${style.add_label} ${style.required}`}>
              {t('资源展示名称')}
              :
            </span>
            <Input
              placeholder={t('请输入')}
              style={inputStyle}
              maxLength={30}
              value={uploadObj.title}
              onChange={(val) => store.changeUploadObjData({ title: val })}
            />
          </div>
          <div className={style.block_label} style={{ display: 'flex' }}>
            <span className={`${style.add_label} ${style.required}`}>
              {isWms ? t('上传图片/视频/文本') : t('上传图片/视频')}
              :
            </span>
            <UploadPlus
              name="file"
              accept={isWms ? 'image/*,video/*,.xls,.xlsx,.pdf,.doc,.docx' : 'image/*,video/*'}
              action={uploadFileURL}
              autoUpload
              // fileList={uploadFileList}
              renderResult={(d) => (<a target="_blank" href={uploadObj.file && uploadObj.file[0]?.url} rel="noreferrer">{d.name}</a>)}
              maxSize={300}
              autoUploadKeyName="file"
              // onChange={(f) => {
              //   console.log('f', f.file);
              //   store.changeData({
              //     uploadFileList: [f.file],
              //   });
              // }}
              onSuccessUpload={({ info, file }) => {
                store.changeUploadObjData({
                  file: [{
                    url: info.image_url,
                    name: file.name,
                  }],
                });
              }}
            />
          </div>
        </Modal>
        {/* 编辑弹窗 */}
        <Modal
          destroy
          visible={editVisible}
          maskCloseAble={false}
          title={t('编辑图片/视频')}
          onClose={() => store.changeData({ editVisible: false })}
          footer={[
            <Button
              key="cancel"
              loading={!loading}
              onClick={() => store.changeData({ editVisible: false })}
            >
              {t('取消')}
            </Button>,
            <Button
              key="confrim"
              type="primary"
              loading={!loading}
              disabled={!editAsset.title}
              onClick={() => store.confirmEditAsset()}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={style.block_label}>
            <span className={`${style.add_label} ${style.required}`}>
              {t('资源展示名称')}
              :
            </span>
            <Input
              placeholder={t('请输入')}
              style={inputStyle}
              maxLength={30}
              value={editAsset.title}
              onChange={(val) => store.changeAssetObjData({ title: val })}
            />
          </div>
          <div className={style.block_label}>
            <span className={style.add_label}>
              {t('已上传资源预览')}
              :
            </span>
            <a style={{ color: 'rgb(24,121,255)', textDecoration: 'none' }} href={editAsset.url} target="_blank" rel="noreferrer">{editAsset.name}</a>
          </div>
          <div className={style.block_label} style={{ display: 'flex' }}>
            <span className={style.add_label}>
              {isWms ? t('重新上传图片/视频/文本') : t('重新上传图片/视频')}
              :
            </span>
            <UploadPlus
              name="file"
              accept={isWms ? 'image/*,video/*,.xls,.xlsx,.pdf,.doc,.docx' : 'image/*,video/*'}
              action={uploadFileURL}
              autoUpload
              // value={editAsset.file}
              renderResult={(d) => (<a target="_blank" href={editAsset.file[0] && editAsset.file[0]?.url} rel="noreferrer">{d.name}</a>)}
              maxSize={300}
              autoUploadKeyName="file"
              onSuccessUpload={({ info, file }) => {
                store.changeAssetObjData({
                  file: [{
                    url: info.image_url,
                    name: file.name,
                  }],
                });
              }}
            />
          </div>
        </Modal>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  viewVisible: PropTypes.bool,
  uploadVisible: PropTypes.bool,
  uploadObj: PropTypes.shape(),
  list: PropTypes.arrayOf(PropTypes.shape()),
  assetType: PropTypes.shape(),
  viewObj: PropTypes.shape(),
  editVisible: PropTypes.bool,
  editAsset: PropTypes.shape(),
  isWms: PropTypes.bool,
};

export default List;
