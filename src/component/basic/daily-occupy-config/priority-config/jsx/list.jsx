import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import OperationModal from '@public-component/modal/operation-modal';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      store,
      recordVisible,
      recordId,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('优先级'),
        render: 'priority',
        width: 120,
      },
      {
        title: t('编码'),
        width: 120,
        render: 'configCode',
      },
      {
        title: t('说明'),
        width: 180,
        render: 'configDesc',
      },
      {
        title: t('配置值'),
        width: 180,
        render: 'configValue',
      },
      {
        title: t('状态'),
        render: 'configStatusName',
        width: 120,
      },
      {
        title: t('更新人'),
        render: 'updateUser',
        width: 160,
      },
      {
        title: t('更新时间'),
        width: 180,
        render: 'lastUpdateTime',
      },
      {
        title: t('操作查询'),
        fixed: 'right',
        width: 140,
        render: (record) => (
          <Button
            text
            type="primary"
            onClick={() => store.changeData({
              recordVisible: true,
              recordId: record.id,
            })}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'DAILY_OCCUPY_PRIORITY_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  store: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
