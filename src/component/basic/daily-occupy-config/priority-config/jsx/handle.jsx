import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Modal, Select, Rule, Input,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import { defaultModalInfo } from '../reducers';

const rules = Rule({
  codeRule: {
    func: (_, formData, callback) => {
      // approvalProcessCode仅支持填写大写英文字母
      const reg = /^[A-Z]*$/;
      if (!reg.test(formData.configCode)) {
        callback(new Error(t('仅支持填写大写英文字母，不能超过10个字符')));
        return;
      }
      callback(true);
    },
  },
});
class Handle extends React.Component {
  render() {
    const {
      loading,
      modalType,
      modalInfo,
      configStatusList,
      store,
      selectedRows,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              modalType: 1,
              modalInfo: defaultModalInfo,
            });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            store.changeData({
              modalType: 0,
              modalInfo: selectedRows[0],
            });
          }}
        >
          {t('编辑')}
        </Button>

        {/* 新增、编辑弹窗 */}
        <Modal
          destroy
          maskCloseAble={null}
          visible={[0, 1].includes(modalType)}
          title={modalType ? t('新增') : t('编辑')}
          width={600}
          bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
          onClose={() => {
            store.changeData({
              modalType: '',
            });
          }}
          footer={(
            <div>
              <Button onClick={() => store.changeData({
                modalType: '',
              })}
              >
                {t('取消')}
              </Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            onSubmit={() => {
              store.saveData();
            }}
            onChange={(value) => {
              store.changeData({
                modalInfo: value,
              });
            }}
            value={modalInfo}
            formRef={(f) => store.changeData({ modalFormRef: f })}
          >
            <Form.Item required label={`${t('优先级')}`}>
              <Input.Number
                allowNull
                digits={0}
                hideArrow
                min={1}
                max={99}
                placeholder={t('请输入')}
                style={{ width: 200 }}
                name="priority"
                autocomplete="off"
                rules={[rules.required()]}
                clearable
                maxLength={2}
              />
            </Form.Item>
            <Form.Item required label={t('编码')}>
              <Input
                name="configCode"
                maxLength={10}
                style={{ width: 200 }}
                clearable
                rules={[rules.codeRule()]}
                placeholder={t('请输入')}
                disabled={modalType === 0}
              />
            </Form.Item>
            <Form.Item required label={t('状态')}>
              <Select
                name="configStatus"
                data={configStatusList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 200 }}
                rules={[rules.required()]}
                absolute
                clearable
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('说明')}>
              <Input
                name="configDesc"
                maxLength={256}
                style={{ width: 350 }}
                clearable
                rules={[rules.required()]}
                placeholder={t('请输入')}
              />
            </Form.Item>
            <Form.Item required label={t('配置值')}>
              <Input
                name="configValue"
                maxLength={256}
                style={{ width: 350 }}
                clearable
                rules={[rules.required()]}
                placeholder={t('请输入')}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalInfo: PropTypes.shape(),
  configStatusList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
