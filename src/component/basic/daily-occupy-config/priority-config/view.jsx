import React, { useEffect } from 'react';
import ContainerPage from '@public-component/search-queries/container';
import { TopAreaHeight } from '@src/lib/constants';
import { useStore } from 'rrc-loader-helper';
import dynamicCheckReducers from './reducers';
import Header from './jsx/header';
import Handle from './jsx/handle';
import List from './jsx/list';

function View(props) {
  const [dynamicCheckState, dynamicCheckStore] = useStore(dynamicCheckReducers);
  useEffect(() => {
    dynamicCheckStore.init();
  }, []);
  return (
    <ContainerPage
      customStyle={{ height: `calc(100vh - ${TopAreaHeight}px - 92px)` }}
    >
      <Header {...props} {...dynamicCheckState} store={dynamicCheckStore} />
      <Handle {...props} {...dynamicCheckState} store={dynamicCheckStore} />
      <List {...props} {...dynamicCheckState} store={dynamicCheckStore} />
    </ContainerPage>
  );
}

export default View;
