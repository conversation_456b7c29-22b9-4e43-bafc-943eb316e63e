import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/daily_occupy_priority_config_api/query',
  param,
}, process.env.WWS_URI);

/**
 * 新增
 * @param {*} param
 * @returns
 */
export const addAPI = (param) => sendPostRequest({
  url: '/daily_occupy_priority_config_api/add',
  param,
}, process.env.WWS_URI);

/**
 * 编辑
 * @param {*} param
 * @returns
 */
export const editAPI = (param) => sendPostRequest({
  url: '/daily_occupy_priority_config_api/edit',
  param,
}, process.env.WWS_URI);
