import { i18n, t } from '@shein-bbl/react';
import React, { Component } from 'react';
import globalStyles from '@src/component/style.less';
import { Tabs } from 'shineout';
import PropTypes from 'prop-types';
import store from './reducers';
import DensityConfig from './density-config/view';
import PriorityConfig from './priority-config/view';

class Container extends Component {
  render() {
    const {
      tabKey,
    } = this.props;
    return (
      <Tabs
        shape="line"
        className={`${globalStyles.tabsSection} ${globalStyles.noBorder}`}
        defaultActive={tabKey}
        onChange={(key) => {
          store.changeData({ tabKey: key });
        }}
      >
        <Tabs.Panel className={globalStyles.tabsPanel} tab={t('浓度配置')}>
          <DensityConfig {...this.props} />
        </Tabs.Panel>
        <Tabs.Panel className={globalStyles.tabsPanel} tab={t('预占优先级')}>
          <PriorityConfig {...this.props} />
        </Tabs.Panel>
      </Tabs>
    );
  }
}

Container.propTypes = {
  tabKey: PropTypes.number.isRequired,
};

export default i18n(Container);
