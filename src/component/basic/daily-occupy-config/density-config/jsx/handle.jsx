import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Modal, Select, Rule,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import { defaultModalInfo } from '../reducers';

const rules = Rule();
class Handle extends React.Component {
  render() {
    const {
      loading,
      modalType,
      modalInfo,
      dimensionList,
      regionModalList,
      configStatusList,
      store,
      selectedRows,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              modalType: 1,
              modalInfo: defaultModalInfo,
            });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            store.changeData({
              modalType: 0,
              modalInfo: selectedRows[0],
            });
          }}
        >
          {t('编辑')}
        </Button>

        {/* 新增、编辑弹窗 */}
        <Modal
          destroy
          maskCloseAble={null}
          visible={[0, 1].includes(modalType)}
          title={modalType ? t('新增') : t('编辑')}
          bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
          onClose={() => {
            store.changeData({
              modalType: '',
            });
          }}
          footer={(
            <div>
              <Button onClick={() => store.changeData({
                modalType: '',
              })}
              >
                {t('取消')}
              </Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              store.saveData();
            }}
            onChange={(value) => {
              store.changeData({
                modalInfo: value,
              });
            }}
            value={modalInfo}
            formRef={(f) => store.changeData({ modalFormRef: f })}
          >
            <Form.Item required label={t('上架片区')}>
              <Select
                name="upRegion"
                data={regionModalList}
                keygen="region"
                format="region"
                placeholder={t('请选择')}
                renderItem={(record) => <span>{record.regionName ? record.regionName : '-'}</span>}
                style={{ width: 200 }}
                rules={[rules.required()]}
                onFilter={(text) => (d) => d.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                disabled={modalType === 0}
                absolute
              />
            </Form.Item>
            <Form.Item required label={t('状态')}>
              <Select
                name="configStatus"
                data={configStatusList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 200 }}
                rules={[rules.required()]}
                absolute
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('浓度维度')}>
              <Select
                name="densityDimension"
                data={dimensionList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                style={{ width: 200 }}
                clearable
                rules={[rules.required()]}
                absolute
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalInfo: PropTypes.shape(),
  dimensionList: PropTypes.arrayOf(PropTypes.shape()),
  regionModalList: PropTypes.arrayOf(PropTypes.shape()),
  configStatusList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
