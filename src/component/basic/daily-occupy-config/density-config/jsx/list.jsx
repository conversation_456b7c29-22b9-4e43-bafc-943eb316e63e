import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import OperationModal from '@public-component/modal/operation-modal';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      store,
      recordVisible,
      recordId,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('上架片区'),
        render: 'upRegionName',
        width: 120,
      },
      {
        title: t('状态'),
        render: 'configStatusName',
        width: 160,
      },
      {
        title: t('预占维度'),
        width: 120,
        render: 'densityDimensionName',
      },
      {
        title: t('创建时间'),
        width: 180,
        render: 'createTime',
      },
      {
        title: t('更新人'),
        render: 'updateUser',
        width: 160,
      },
      {
        title: t('操作查询'),
        fixed: 'right',
        width: 140,
        render: (record) => (
          <Button
            text
            type="primary"
            onClick={() => store.changeData({
              recordVisible: true,
              recordId: record.id,
            })}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'DAILY_DENSITY_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  store: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
