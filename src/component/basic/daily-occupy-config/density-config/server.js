import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/daily_up_shelf_density_config/query',
  param,
}, process.env.WWS_URI);

/**
 * 新增
 * @param {*} param
 * @returns
 */
export const addAPI = (param) => sendPostRequest({
  url: '/daily_up_shelf_density_config/add',
  param,
}, process.env.WWS_URI);

/**
 * 编辑
 * @param {*} param
 * @returns
 */
export const editAPI = (param) => sendPostRequest({
  url: '/daily_up_shelf_density_config/edit',
  param,
}, process.env.WWS_URI);
