import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Input, Modal, Select, Rule,
} from 'shineout';
import { t } from '@shein-bbl/react';
import { validNum } from '@src/lib/deal-func';
import styles from '@src/component/style.less';

const rules = Rule({
  dynamicRate: {
    func: (val, formData, callback) => {
      if (Number.isNaN(Number(val))) {
        callback(new Error(t('请输入数字')));
      }
      if (Number(val) < 1 || Number(val) > 100 || !validNum(val)) {
        callback(new Error(t('请输入{}的正整数', '1-100')));
      }
      callback(true);
    },
  },
  replaceDate: {
    func: (val, formData, callback) => {
      if (Number.isNaN(Number(val))) {
        callback(new Error(t('请输入数字')));
      }
      if (Number(val) < 1 || Number(val) > 28 || !validNum(val)) {
        callback(new Error(t('请输入{}的正整数', '1-28')));
      }
      callback(true);
    },
  },
  replaceFrequency: {
    func: (val, formData, callback) => {
      if (Number.isNaN(Number(val))) {
        callback(new Error(t('请输入数字')));
      }
      if (Number(val) < 1 || Number(val) > 999 || !validNum(val)) {
        callback(new Error(t('请输入{}的整数', '1-999')));
      }
      callback(true);
    },
  },

});
class Handle extends React.Component {
  render() {
    const {
      loading,
      modalType,
      modalInfo,
      warehouseList,
      regionModalList,
      areaTypeList,
      store,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          loading={!loading}
          onClick={() => {
            store.openModal(1);
          }}
        >
          {t('新增')}
        </Button>

        {/* 新增、编辑弹窗 */}
        <Modal
          maskCloseAble={null}
          visible={[0, 1].includes(modalType)}
          width={500}
          title={modalType ? t('新增') : t('编辑')}
          // bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
          onClose={() => {
            store.closeModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              store.saveData();
            }}
            onChange={(value) => {
              store.changeData({
                modalInfo: value,
              });
            }}
            value={modalInfo}
            formRef={(f) => store.changeData({ modalFormRef: f })}
          >
            <Form.Item required label={t('仓库')}>
              <Select
                name="warehouse"
                data={warehouseList}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="nameZh"
                style={{ width: 200 }}
                rules={[rules.required(t('请选择'))]}
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                disabled={modalType === 0}
                onChange={() => {
                  store.handleRegioin();
                }}
              />
            </Form.Item>
            <Form.Item required label={t('片区')}>
              <Select
                name="region"
                data={regionModalList}
                keygen="region"
                format="region"
                placeholder={t('请选择')}
                renderItem={(record) => <span>{record.regionName ? record.regionName : '-'}</span>}
                style={{ width: 200 }}
                rules={[rules.required(t('请选择'))]}
                renderUnmatched={(r) => r.regionName || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                disabled={modalType === 0}
              />
            </Form.Item>
            <Form.Item required label={t('库区类型')}>
              <Select
                name="areaType"
                data={areaTypeList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 200 }}
                rules={[rules.required(t('请选择'))]}
                renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                disabled={modalType === 0}
              />
            </Form.Item>

            <Form.Item required label={t('动碰比例')}>
              <Input
                name="dynamicRate"
                digits={0}
                placeholder={t('必填,请输入{}的正整数', '1-100')}
                rules={[rules.required(t('请输入')), rules.dynamicRate()]}
                style={{ width: 200 }}
              />
              %
            </Form.Item>

            <Form.Item required label={t('兜底时间')}>
              <Input
                name="replaceDate"
                digits={0}
                placeholder={t('必填,请输入{}的正整数', '1-28')}
                rules={[rules.required(t('请输入')), rules.replaceDate()]}
                style={{ width: 200 }}
              />
              {t('日')}
            </Form.Item>

            <Form.Item required label={t('触发兜底频率')}>
              <Input
                name="replaceFrequency"
                digits={0}
                placeholder={t('必填,请输入{}的正整数', '1-999')}
                rules={[rules.required(t('请输入')), rules.replaceFrequency()]}
                style={{ width: 200 }}
              />
              {t('个月')}
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalInfo: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  regionModalList: PropTypes.arrayOf(PropTypes.shape()),
  areaTypeList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
};
export default Handle;
