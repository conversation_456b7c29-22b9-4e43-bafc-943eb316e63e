import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Popover } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      store,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('片区'),
        render: 'regionName',
        width: 120,
      },
      {
        title: t('库区类型'),
        render: 'areaTypeName',
        width: 160,
      },
      {
        title: t('动碰比例'),
        width: 160,
        render: (record) => (
          <span>
            {record.dynamicRate}
            %
          </span>
        ),
      },
      {
        title: t('兜底时间'),
        width: 100,
        render: (record) => (
          <span>
            {record.replaceDate}
            {t('日')}
          </span>
        ),
      },
      {
        title: t('触发兜底频率(个月)'),
        render: 'replaceFrequency',
        width: 120,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 190,
      },
      {
        title: t('更新人'),
        render: 'updater',
        width: 100,
      },
      {
        title: t('操作查询'),
        fixed: 'right',
        width: 140,
        render: (record) => (
          <>
            <Button
              text
              type="primary"
              onClick={() => {
                store.changeData({ selectedRows: record });
                store.openModal(0);
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              text
              type="primary"
            >
              <Popover.Confirm
                type="warning"
                okType="primary"
                text={{ ok: t('确认'), cancel: t('取消') }}
                onOk={() => {
                  store.changeData({ selectedRows: record });
                  store.deleteData();
                }}
              >
                {t('是否确定删除?')}
              </Popover.Confirm>
              {t('删除')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  store: PropTypes.shape(),
};

export default List;
