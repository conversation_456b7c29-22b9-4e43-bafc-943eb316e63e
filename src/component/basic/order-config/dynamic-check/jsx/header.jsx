import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import { defaultLimit } from '../reducers';
import styles from '../../style.less';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      areaTypeList,
      regionList,
      store,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('片区'), t('库区类型')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Select
            label={t('片区')}
            name="region"
            data={regionList}
            keygen="region"
            format="region"
            placeholder={t('请选择')}
            renderItem={(record) => <span>{record.regionName ? record.regionName : '-'}</span>}
            renderUnmatched={(r) => r.regionName || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('库区类型')}
            name="areaType"
            data={areaTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  areaTypeList: PropTypes.arrayOf(PropTypes.shape()),
  regionList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
};
export default Header;
