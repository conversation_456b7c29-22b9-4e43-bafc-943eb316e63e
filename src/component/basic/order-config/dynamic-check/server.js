import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/check_param_config/list',
  param,
}, process.env.WWS_URI);

/**
 * 删除
 * @param {*} param
 * @returns
 */
export const deleteAPI = (param) => sendPostRequest({
  url: '/check_param_config/del',
  param,
}, process.env.WWS_URI);

/**
 * 新增
 * @param {*} param
 * @returns
 */
export const addAPI = (param) => sendPostRequest({
  url: '/check_param_config/add',
  param,
}, process.env.WWS_URI);

/**
 * 编辑
 * @param {*} param
 * @returns
 */
export const editAPI = (param) => sendPostRequest({
  url: '/check_param_config/edit',
  param,
}, process.env.WWS_URI);
