import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
// import { paramTrim } from '@src/lib/deal-func';
import { handleListMsg } from '@src/lib/dealFunc';
import { queryRegionAPI } from '@src/server/common/common';
import {
  getListAPI, deleteAPI, addAPI, editAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  region: '', // 片区
  areaType: '', // 库区类型
};

// 弹窗表单数据
export const defaultModalInfo = {
  warehouse: '', // 仓库
  region: '', // 片区
  areaType: '', // 库区类型
  dynamicRate: '', // 动碰比例
  replaceDate: '', // 兜底时间
  replaceFrequency: '', // 触发兜底频率
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  regionList: [], // 片区 下拉
  areaTypeList: [], // 库区类型 下拉
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  selectedRows: {}, // 勾选的数据-单选
  modalFormRef: {}, // 用于校验弹窗内的表单项
  modalType: '', // 弹窗类型 1:新增 0:编辑
  modalInfo: defaultModalInfo,
  warehouseList: [], // 弹窗 仓库 下拉
  regionModalList: [], // 弹窗 片区 下拉
  areaMdalList: [], // 弹窗 库区类型 下拉
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['AREA_TYPE'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        areaTypeList: selectData.info.data.find((item) => item.catCode === 'AREA_TYPE').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }

    const { warehouseList } = yield 'nav';
    yield this.changeData({ warehouseList });

    yield this.warehouseChange();
  },

  // 右上角仓库事件派发
  * warehouseChange() {
    yield this.handleRegioin();
    yield this.clearLimitData({
      region: '',
    });
  },

  /**
   * 搜索
   */
  * search() {
    // 获取仓库
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        selectedRows: {},
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  /**
   * 根据仓库获取片区
   */
  * handleRegioin() {
    const { modalInfo, modalType } = yield '';
    const { warehouseId } = yield 'nav';
    const { code, info, msg } = yield queryRegionAPI({ warehouseId: modalInfo.warehouse || warehouseId });
    if (code === '0') {
      if (modalInfo.warehouse) {
        yield this.changeData({
          regionModalList: info || [],
        });
        if (modalType === 1) {
          yield this.changeData({
            modalInfo: {
              ...modalInfo,
              region: '',
            },
          });
        }
      } else {
        yield this.changeData({
          regionList: info || [],
        });
      }
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 打开弹窗
   * @returns
   */
  * openModal(modalType) {
    // 请求弹窗数据 + 编辑赋值
    const { selectedRows } = yield '';
    if (modalType === 0) {
      yield this.changeData({
        modalInfo: {
          ...selectedRows,
        },
      });
    }

    yield this.changeData({
      modalType,
    });
    yield this.handleRegioin();
  },

  /**
   * 关闭弹窗-清空数据及表单验证
   * @returns
   */
  * closeModal() {
    const { modalFormRef } = yield '';
    yield this.changeData({
      modalType: '',
      modalInfo: defaultModalInfo,
      regionModalList: [],
    });
    if (modalFormRef && modalFormRef.clearValidate) modalFormRef.clearValidate();
  },

  /**
   * 保存
   * @returns
   */
  * saveData() {
    const { modalInfo, modalType, selectedRows } = yield '';
    const {
      warehouse, region, areaType, dynamicRate, replaceDate, replaceFrequency,
    } = modalInfo;
    const param = {
      warehouse,
      region,
      areaType,
      dynamicRate,
      replaceDate,
      replaceFrequency,
      id: modalType ? undefined : selectedRows.id,
    };
    markStatus('loading');
    const API = modalType ? addAPI : editAPI;
    const { code, msg } = yield API(param);
    if (code === '0') {
      Message.success(modalType ? t('新增成功') : t('编辑成功！'));
      yield this.closeModal();
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 删除
   * @returns
   */
  * deleteData() {
    const { selectedRows } = yield '';

    markStatus('loading');
    const { code, msg } = yield deleteAPI({
      id: selectedRows.id,
    });
    if (code === '0') {
      Message.success(t('删除成功！'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg || t('后台接口出错') });
    }
  },
};
