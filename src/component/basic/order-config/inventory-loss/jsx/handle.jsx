import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Modal, Select, Rule, Checkbox, Tag, Message,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import style from '../../style.less';

const rules = Rule();
class Handle extends React.Component {
  render() {
    const {
      loading,
      modalType,
      modalInfo,
      warehouseList,
      businessTypeList,
      store,
      inBusinessSubTypeList,
      outBusinessSubTypeList,
      inventoryBusinessSubTypeList,
      functionNameList,
      strategyTypeList,
      parkModalList,
      subWarehouseModalList,
    } = this.props;

    let subBusinessTypeList = [];
    switch (modalInfo.businessBigCategory) {
      case 0:
        subBusinessTypeList = inBusinessSubTypeList;
        break;
      case 1:
        subBusinessTypeList = outBusinessSubTypeList;
        break;
      case 2:
        subBusinessTypeList = inventoryBusinessSubTypeList;
        break;
      default:
        break;
    }

    const isEdit = modalType === 0;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.handleAdd();
          }}
        >
          {t('新增')}
        </Button>
        {/* 新增、编辑弹窗 */}
        <Modal
          maskCloseAble={null}
          visible={[0, 1].includes(modalType)}
          width={700}
          bodyStyle={{ maxHeight: 550, overflow: 'auto' }}
          title={modalType ? t('新增') : t('编辑')}
          destroy
          onClose={() => {
            store.closeModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={130}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              // 启用园区必填
              if (modalInfo.enablePolicy === 0 && modalInfo.policyValueList.length === 0) {
                Message.error(t('启用园区必填'));
                return;
              }
              store.saveData();
            }}
            onChange={(value) => {
              store.changeData({
                modalInfo: value,
              });
            }}
            value={modalInfo}
          >
            <Form.Item required label={t('仓库')}>
              <Select
                name="warehouseId"
                data={warehouseList}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="nameZh"
                clearable
                absolute
                disabled={isEdit}
                style={{ width: 200 }}
                rules={[rules.required()]}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(val) => {
                  store.getSubWarehouseList({ warehouseId: val });
                  store.changeModalInfo({
                    policyValueList: [],
                  });
                }}
              />
            </Form.Item>
            <Form.Item required label={t('业务大类')}>
              <Select
                name="businessBigCategory"
                data={businessTypeList}
                keygen="dictCode"
                format="dictCode"
                disabled={isEdit}
                absolute
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                rules={[rules.required()]}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                style={{ width: 200 }}
                onChange={() => {
                  store.changeModalInfo({ businessSubCategory: '' });
                }}
              />
            </Form.Item>
            <Form.Item required label={t('业务子类')}>
              <Select
                name="businessSubCategory"
                data={subBusinessTypeList}
                keygen="dictCode"
                format="dictCode"
                disabled={isEdit}
                absolute
                rules={[rules.required()]}
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                style={{ width: 200 }}
              />
            </Form.Item>
            <Form.Item required label={t('功能名称')}>
              <Select
                name="functionNameCode"
                data={functionNameList}
                rules={[rules.required()]}
                keygen="dictCode"
                format="dictCode"
                disabled={isEdit}
                absolute
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                style={{ width: 200 }}
              />
            </Form.Item>
            <Form.Item required label={t('启用策略')}>
              <Select
                name="enablePolicy"
                data={strategyTypeList}
                absolute
                rules={[rules.required()]}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('全部')}
                renderItem="dictNameZh"
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                style={{ width: 200 }}
                onChange={(val) => {
                  store.changeModalInfo({
                    enablePolicy: val,
                    policyValueList: [],
                  });
                }}

              />
            </Form.Item>
            {
              modalInfo.enablePolicy === 1 && (
                <>
                  <Form.Item required label={t('启用子仓')}>
                    <Select
                      name="policyValueList"
                      data={subWarehouseModalList}
                      keygen="id"
                      format="id"
                      placeholder={t('请选择')}
                      renderItem="nameZh"
                      clearable
                      absolute
                      style={{ width: 200 }}
                      multiple
                      compressed
                      rules={[rules.required()]}
                      onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                    />
                  </Form.Item>
                  {
                    modalInfo.policyValueList?.length > 0 && (
                      <div className={style.subWarehouseNameText}>
                        {
                          subWarehouseModalList.filter((d) => modalInfo.policyValueList.includes(d.id)).map((d) => (
                            <Tag key={d.id}>
                              {d.nameZh}
                            </Tag>
                          ))
                        }
                      </div>
                    )
                  }
                </>
              )
            }
            {
              modalInfo.enablePolicy === 0 && (
                <h4>
                  <span style={{ color: 'red' }}>*</span>
                  {t('启用园区')}
                </h4>
              )
            }
            {
              modalInfo.enablePolicy === 0 && parkModalList.length > 0 ? (
                <Form.Item>
                  <div style={{ marginLeft: 10, padding: '4px 6px', border: '1px solid rgb(212,210,210)' }}>
                    <Checkbox.Group
                      keygen
                      value={modalInfo.policyValueList}
                      onChange={(val) => {
                        store.changeModalInfo({
                          policyValueList: val,
                        });
                      }}
                    >
                      {parkModalList.map((d) => (
                        <Checkbox key={d.dictCode} htmlValue={d.dictCode}>
                          {d.dictNameZh}
                        </Checkbox>
                      ))}
                    </Checkbox.Group>
                  </div>
                </Form.Item>
              ) : ''
              }
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalInfo: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  businessTypeList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
  inBusinessSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
  outBusinessSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
  inventoryBusinessSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
  functionNameList: PropTypes.arrayOf(PropTypes.shape()),
  strategyTypeList: PropTypes.arrayOf(PropTypes.shape()),
  parkModalList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseModalList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
