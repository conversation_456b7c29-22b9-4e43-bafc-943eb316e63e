import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Button, Popover, Tag,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import OperationModal from '@public-component/modal/operation-modal';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      store,
      recordVisible,
      recordId,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 130,
      },
      {
        title: t('业务大类'),
        render: 'businessBigCategoryName',
        width: 120,
      },
      {
        title: t('业务子类'),
        render: 'businessSubCategoryName',
        width: 140,
      },
      {
        title: t('功能名称'),
        render: 'functionName',
        width: 140,
      },
      {
        title: t('启用策略'),
        render: 'enablePolicyName',
        width: 100,
      },
      {
        title: t('启用园区'),
        render: (row) => ((row.parkTypeNames || []).length > 0 ? (row.parkTypeNames || []).map((d) => (
          <Tag key={d.policyValue}>
            {d.policyValueName}
          </Tag>
        )) : '/'),
        width: 180,
      },
      {
        title: t('启用子仓'),
        render: (row) => ((row.subWarehouseNames || []).length > 0 ? (row.subWarehouseNames || []).map((d) => (
          <Tag key={d.policyValue}>
            {d.policyValueName}
          </Tag>
        )) : '/'),
        width: 180,
      },
      {
        title: t('创建时间'),
        render: 'createTime',
        width: 190,
      },
      {
        title: t('创建人'),
        render: 'creator',
        width: 160,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 190,
      },
      {
        title: t('更新人'),
        render: 'updater',
        width: 160,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 200,
        render: (record) => (
        // 编辑、删除、操作记录
          <>
            <Button
              text
              type="primary"
              onClick={() => {
                if (record.warehouseId) {
                  store.getSubWarehouseList({ warehouseId: record.warehouseId });
                }
                store.changeData({
                  modalType: 0,
                  modalInfo: {
                    ...record,
                    policyValueList: (record.policyInfoList || []).map((d) => (d.policyValue)),
                  },
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              text
              type="danger"
            >
              <Popover.Confirm
                type="warning"
                okType="primary"
                text={{ ok: t('确认'), cancel: t('取消') }}
                onOk={() => {
                  store.deleteConfig(record.id);
                }}
              >
                {t('是否确定删除?')}
              </Popover.Confirm>
              {t('删除')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'CHECK_LOSS_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  store: PropTypes.shape(),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
