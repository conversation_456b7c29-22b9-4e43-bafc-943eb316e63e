import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      parkTypeList,
      store,
      subWarehouseList,
      businessTypeList,
      inBusinessSubTypeList,
      outBusinessSubTypeList,
      inventoryBusinessSubTypeList,
      functionNameList,
      strategyTypeList,
    } = this.props;

    let subBusinessTypeList = [];
    switch (limit.businessBigCategory) {
      case 0:
        subBusinessTypeList = inBusinessSubTypeList;
        break;
      case 1:
        subBusinessTypeList = outBusinessSubTypeList;
        break;
      case 2:
        subBusinessTypeList = inventoryBusinessSubTypeList;
        break;
      default:
        break;
    }

    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('业务大类')}
            name="businessBigCategory"
            data={businessTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            onChange={() => {
              store.changeLimitData({ businessSubCategoryList: [] });
            }}
          />
          {/* 业务子类 */}
          <Select
            label={t('业务子类')}
            name="businessSubCategoryList"
            data={subBusinessTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
          />
          {/* 功能名称 */}
          <Select
            label={t('功能名称')}
            name="functionNameCodeList"
            data={functionNameList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            multiple
            compressed
          />
          {/* 启用策略 */}
          <Select
            label={t('启用策略')}
            name="enablePolicy"
            data={strategyTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            onChange={() => {
              store.changeLimitData({ policyValueList: [] });
            }}
          />
          {
            limit.enablePolicy === 0 ? (
              <Select
                label={t('启用园区')}
                name="policyValueList"
                data={parkTypeList}
                keygen="parkType"
                format="parkType"
                placeholder={t('全部')}
                renderItem="parkName"
                onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                multiple
                compressed
                disabled={limit.enablePolicy === null}
              />
            ) : (
              <Select
                label={t('启用子仓')}
                name="policyValueList"
                data={subWarehouseList}
                keygen="id"
                format="id"
                renderItem="nameZh"
                disabled={limit.enablePolicy === null}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                placeholder={t('全部')}
                multiple
                compressed
              />
            )
          }
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  businessTypeList: PropTypes.arrayOf(PropTypes.shape()),
  functionNameList: PropTypes.arrayOf(PropTypes.shape()),
  strategyTypeList: PropTypes.arrayOf(PropTypes.shape()),
  inBusinessSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
  outBusinessSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
  inventoryBusinessSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
