import React, { useEffect } from 'react';
import ContainerPage from '@public-component/search-queries/container';
import { TopAreaHeight } from '@src/lib/constants';
import { useStore } from 'rrc-loader-helper';
import cycleCheckReducers from './reducers';
import Header from './jsx/header';
import Handle from './jsx/handle';
import List from './jsx/list';

function View(props) {
  const [postState, postStore] = useStore(cycleCheckReducers);
  useEffect(() => {
    postStore.init();
  }, []);
  return (
    <ContainerPage
      customStyle={{ height: `calc(100vh - ${TopAreaHeight}px - 92px)` }}
      warehouseChange={({ warehouseId, subWarehouseList }) => postStore.warehouseChange({ warehouseId, subWarehouseList })}
    >
      <Header {...props} {...postState} store={postStore} />
      <Handle {...props} {...postState} store={postStore} />
      <List {...props} {...postState} store={postStore} />
    </ContainerPage>
  );
}

export default View;
