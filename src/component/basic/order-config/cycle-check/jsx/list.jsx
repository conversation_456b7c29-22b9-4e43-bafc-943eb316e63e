import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import { fliterSubwarehouse } from '@src/lib/dealFunc';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      store,
      selectedRows,
      preSubWarehouseList,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('园区'),
        render: 'parkTypeName',
        width: 120,
      },
      {
        title: t('子仓'),
        render: 'subWarehouseName',
        width: 120,
      },
      {
        title: t('库区类型'),
        render: 'areaTypeName',
        width: 160,
      },
      {
        title: t('兜底时间（日）'),
        width: 100,
        render: 'undertakeTime',
      },
      {
        title: t('触发兜底频率(月)'),
        render: 'undertakeFrequency',
        width: 120,
      },
      {
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 190,
      },
      {
        title: t('更新人'),
        render: 'updateUser',
        width: 150,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 120,
        render: (record) => (
          <Button
            text
            type="primary"
            onClick={() => {
              store.changeData({
                modalType: 0,
                modalInfo: {
                  ...record,
                },
              });
              const { newPermissionSubWarehouseList } = fliterSubwarehouse([record?.parkType], preSubWarehouseList);
              store.changeData({
                subWarehouseModalList: newPermissionSubWarehouseList,
              });
              store.handleParkModalList(record.warehouseId);
            }}
          >
            {t('编辑')}
          </Button>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            onRowSelect={(rows) => {
              store.changeData({
                selectedRows: rows,
              });
            }}
            value={selectedRows}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  store: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
