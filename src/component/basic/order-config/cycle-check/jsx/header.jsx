import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      areaTypeList,
      parkTypeList,
      store,
      permissionSubWarehouseList,
      preSubWarehouseList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('片区'), t('库区类型')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Select
            label={t('园区')}
            name="parkType"
            data={parkTypeList}
            keygen="parkType"
            format="parkType"
            placeholder={t('全部')}
            renderItem="parkName"
            onChange={(val, d) => {
              if (d?.parkType) {
                const { newPermissionSubWarehouseList } = fliterSubwarehouse([d?.parkType], preSubWarehouseList);
                store.changeData({
                  permissionSubWarehouseList: newPermissionSubWarehouseList,
                });
              } else {
                store.changeData({
                  permissionSubWarehouseList: preSubWarehouseList,
                });
              }
              store.changeLimitData({
                parkType: val,
                subWarehouseId: '',
              });
            }}
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('子仓')}
            name="subWarehouseId"
            data={permissionSubWarehouseList}
            keygen="id"
            format="id"
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('库区类型')}
            name="areaType"
            data={areaTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  areaTypeList: PropTypes.arrayOf(PropTypes.shape()),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
  permissionSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
