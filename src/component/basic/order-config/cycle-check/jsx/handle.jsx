import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Input, Modal, Select, Rule, Popover,
} from 'shineout';
import { fliterSubwarehouse } from '@src/lib/dealFunc';
import { t } from '@shein-bbl/react';
import { validNum } from '@src/lib/deal-func';
import styles from '@src/component/style.less';

const rules = Rule({
  undertakeTime: {
    func: (val, formData, callback) => {
      if (Number.isNaN(Number(val))) {
        callback(new Error(t('请输入数字')));
      }
      if (Number(val) < 1 || Number(val) > 28 || !validNum(val)) {
        callback(new Error(t('请输入{}的正整数', '1-28')));
      }
      callback(true);
    },
  },
  undertakeFrequency: {
    func: (val, formData, callback) => {
      if (Number.isNaN(Number(val))) {
        callback(new Error(t('请输入数字')));
      }
      if (Number(val) < 1 || Number(val) > 3 || !validNum(val)) {
        callback(new Error(t('请输入{}的整数', '1-3')));
      }
      callback(true);
    },
  },

});
class Handle extends React.Component {
  render() {
    const {
      loading,
      modalType,
      modalInfo,
      warehouseList,
      parkModalList,
      areaTypeList,
      subWarehouseModalList,
      store,
      selectedRows,
      preSubWarehouseList,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.handleAdd();
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length === 0}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => {
              store.deleteData();
            }}
          >
            {t('是否确定删除?')}
          </Popover.Confirm>
          {t('删除')}
        </Button>

        {/* 新增、编辑弹窗 */}
        <Modal
          maskCloseAble={null}
          visible={[0, 1].includes(modalType)}
          width={500}
          title={modalType ? t('新增') : t('编辑')}
          destroy
          onClose={() => {
            store.closeModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={130}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              store.saveData();
            }}
            onChange={(value) => {
              store.changeData({
                modalInfo: value,
              });
            }}
            value={modalInfo}
          >
            <Form.Item required label={t('仓库')}>
              <Select
                name="warehouseId"
                data={warehouseList}
                keygen="id"
                format="id"
                disabled
                placeholder={t('请选择')}
                renderItem="nameZh"
                clearable
                style={{ width: 200 }}
                rules={[rules.required(t('请选择'))]}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(warehouseId) => {
                  store.handleParkModalList(warehouseId);
                  store.changeData({
                    parkType: '',
                    subWarehouseId: '',
                    warehouseId,
                  });
                }}
              />
            </Form.Item>
            <Form.Item required label={t('园区')}>
              <Select
                name="parkType"
                data={parkModalList}
                keygen="parkType"
                format="parkType"
                placeholder={t('请选择')}
                rules={[rules.required(t('请选择'))]}
                renderItem="parkName"
                style={{ width: 200 }}
                onChange={(val, d) => {
                  if (d?.parkType) {
                    const { newPermissionSubWarehouseList } = fliterSubwarehouse([d?.parkType], preSubWarehouseList);
                    store.changeData({
                      subWarehouseModalList: newPermissionSubWarehouseList,
                    });
                  } else {
                    store.changeData({
                      subWarehouseModalList: [],
                    });
                  }
                  store.changeModalInfo({
                    parkType: val,
                    subWarehouseId: '',
                  });
                }}
                onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                disabled={modalType === 0}
              />
            </Form.Item>
            <Form.Item required label={t('子仓')}>
              <Select
                style={{ width: 200 }}
                name="subWarehouseId"
                data={subWarehouseModalList}
                keygen="id"
                format="id"
                renderItem="nameZh"
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                rules={[rules.required(t('请选择'))]}
                placeholder={t('请选择')}
                disabled={modalType === 0}
              />
            </Form.Item>
            <Form.Item required label={t('库区类型')}>
              <Select
                name="areaType"
                data={areaTypeList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 200 }}
                rules={[rules.required(t('请选择'))]}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                disabled={modalType === 0}
              />
            </Form.Item>
            <Form.Item required label={t('兜底日期')}>
              <Input
                disabled={modalType === 0}
                name="undertakeTime"
                digits={0}
                placeholder={t('必填,请输入{}的正整数', '1-28')}
                rules={[rules.required(t('请输入')), rules.undertakeTime()]}
                style={{ width: 200 }}
              />
              {t('日')}
            </Form.Item>

            <Form.Item required label={t('触发兜底频率 每')}>
              <Input
                name="undertakeFrequency"
                digits={0}
                placeholder={t('必填,请输入{}的正整数', '1-3')}
                rules={[rules.required(t('请输入')), rules.undertakeFrequency()]}
                style={{ width: 200 }}
              />
              {t('个月')}
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalInfo: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  parkModalList: PropTypes.arrayOf(PropTypes.shape()),
  areaTypeList: PropTypes.arrayOf(PropTypes.shape()),
  store: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseModalList: PropTypes.arrayOf(PropTypes.shape()),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
