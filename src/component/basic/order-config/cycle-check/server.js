import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 * @param {*} param
 * @returns
 */
export const getCycleListAPI = (param) => sendPostRequest({
  url: '/cycle_check_config_api/query',
  param,
}, process.env.WWS_URI);

/**
 * 删除
 * @param {*} param
 * @returns
 */
export const deleteCycleAPI = (param) => sendPostRequest({
  url: '/cycle_check_config_api/delete',
  param,
}, process.env.WWS_URI);

export const editCycleAPI = (param) => sendPostRequest({
  url: '/cycle_check_config_api/edit',
  param,
}, process.env.WWS_URI);
