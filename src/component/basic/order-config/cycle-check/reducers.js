import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { queryParkList } from '@src/lib/dealFunc';
import {
  getCycleListAPI, deleteCycleAPI, editCycleAPI,
} from './server';
// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  parkType: '', // 园区
  subWarehouseId: '', // 子仓
  areaType: '', // 库区类型
};

// 弹窗表单数据
export const defaultModalInfo = {
  warehouseId: '', // 仓库
  parkType: '', // 园区
  subWarehouseId: '', // 子仓
  areaType: '', // 库区类型
  undertakeTime: '', // 兜底时间
  undertakeFrequency: '', // 触发兜底频率
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  parkTypeList: [], // 片区 下拉
  areaTypeList: [], // 库区类型 下拉
  permissionSubWarehouseList: [],
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  // 操作区域
  selectedRows: [], // 勾选的数据
  modalType: '', // 弹窗类型 1:新增 0:编辑
  modalInfo: defaultModalInfo,
  warehouseList: [], // 弹窗 仓库 下拉
  parkModalList: [], // 弹窗 片区 下拉
  areaMdalList: [], // 弹窗 库区类型 下拉
  subWarehouseModalList: [], // 弹窗 子仓 下拉
  preSubWarehouseList: [],
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  changeModalInfo(state, data) {
    Object.assign(state.modalInfo, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const selectData = yield dictSelect({ catCode: ['AREA_TYPE'] });
    if (selectData.code === '0') {
      yield this.changeData({
        areaTypeList: selectData.info.data.find((item) => item.catCode === 'AREA_TYPE').dictListRsps,
      });
    } else {
      Modal.error({ title: selectData.msg });
    }

    const { warehouseList, warehouseId, permissionSubWarehouseList } = yield 'nav';
    yield this.changeData({
      warehouseList,
    });
    yield this.warehouseChange({ warehouseId, permissionSubWarehouseList });
  },

  // 右上角仓库事件派发
  * warehouseChange({ warehouseId, permissionSubWarehouseList }) {
    const { parkList } = yield queryParkList(warehouseId);
    yield this.clearLimitData({
      parkType: '', // 园区
      subWarehouseId: '', // 子仓
    });
    yield this.changeData({
      parkTypeList: parkList,
      permissionSubWarehouseList,
      preSubWarehouseList: permissionSubWarehouseList,
    });
  },

  /**
   * 搜索
   */
  * search() {
    // 获取仓库
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      warehouseId,
      subWarehouseId: limit.subWarehouseId ? [limit.subWarehouseId] : [],
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getCycleListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        selectedRows: [],
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  * handleAdd() {
    const { warehouseId } = yield 'nav';
    yield this.changeData({
      modalType: 1,
      modalInfo: {
        ...defaultModalInfo,
        warehouseId,
      },
    });
    yield this.handleParkModalList(warehouseId);
  },
  /**
   * 根据仓库获取园区
   */
  * handleParkModalList(warehouseId) {
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      parkModalList: parkList || [],
    });
  },
  /**
   * 关闭弹窗-清空数据及表单验证
   * @returns
   */
  * closeModal() {
    yield this.changeData({
      modalType: '',
      parkModalList: [],
      subWarehouseModalList: [],
    });
  },

  /**
   * 保存
   * @returns
   */
  * saveData() {
    const { modalInfo, modalType } = yield '';
    markStatus('loading');
    const { code, msg } = yield editCycleAPI({
      ...modalInfo,
    });
    if (code === '0') {
      Message.success(modalType ? t('新增成功') : t('编辑成功！'));
      yield this.closeModal();
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 删除
   * @returns
   */
  * deleteData() {
    const { selectedRows } = yield '';
    markStatus('loading');
    const { code, msg } = yield deleteCycleAPI({
      ids: selectedRows.map((e) => e.id),
    });
    if (code === '0') {
      Message.success(t('删除成功！'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg || t('后台接口出错') });
    }
  },
};
