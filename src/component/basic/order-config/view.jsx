import { i18n, t } from '@shein-bbl/react';
import React, { Component } from 'react';
import globalStyles from '@src/component/style.less';
import { Tabs } from 'shineout';
import PropTypes from 'prop-types';
import store from './reducers';
import DynamicCheck from './dynamic-check/view';
import CycleCheck from './cycle-check/view';
import InventoryLoss from './inventory-loss/view';

class Container extends Component {
  render() {
    const {
      tabKey,
    } = this.props;
    return (
      <Tabs
        shape="line"
        className={`${globalStyles.tabsSection} ${globalStyles.noBorder}`}
        defaultActive={tabKey}
        onChange={(key) => {
          store.changeData({ tabKey: key });
        }}
      >
        <Tabs.Panel className={globalStyles.tabsPanel} tab={t('动碰盘点兜底配置')}>
          <DynamicCheck {...this.props} />
        </Tabs.Panel>
        <Tabs.Panel className={globalStyles.tabsPanel} tab={t('循环盘点兜底配置')}>
          <CycleCheck {...this.props} />
        </Tabs.Panel>
        <Tabs.Panel className={globalStyles.tabsPanel} tab={t('盘亏配置')}>
          <InventoryLoss {...this.props} />
        </Tabs.Panel>
      </Tabs>
    );
  }
}

Container.propTypes = {
  tabKey: PropTypes.number.isRequired,
};

export default i18n(Container);
