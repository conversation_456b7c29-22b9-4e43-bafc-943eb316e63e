import React, { Component } from 'react';
import { i18n } from '@shein-bbl/react';
import ContainerPage from '@public-component/search-queries/container';
import store from './reducers';
import Header from './jsx/header';
import List from './jsx/list';
import Handle from './jsx/handle';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    return (
      <ContainerPage>
        <Header {...this.props} />
        <Handle {...this.props} />
        <List {...this.props} />
      </ContainerPage>
    );
  }
}

export default i18n(Container);
