
.handle {
  display: inline-block;
  vertical-align: top;
  margin: 16px 0;
}

.borderInner > td{
    border-bottom: 1px solid #e8e8e8 !important;
}

.modalWrapper {
  display: flex;
  flex-direction: column;
  color: gray;
  line-height: 1.8;
  margin-bottom: 5px;
}
.modalLabel {
  flex-shrink: 0;
  margin-right: 10px;
  display: flex;
  align-items: center;
}

.modalLabel::before {
  content: '*';
  color: #ff0000;
  margin-right: 3px;
}