import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import OperationModal from '@public-component/modal/operation-modal';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      selectedRows,
      recordId,
      recordVisible,
    } = this.props;

    const columns = [
      {
        title: t('库龄类型'),
        render: 'typeName',
        width: 100,
      },
      {
        title: t('库龄等级'),
        render: 'grade',
        width: 100,
      },
      {
        title: t('库龄天数区间'),
        render: (val) => (`${val.minAge}-${val.maxAge}`),
        width: 100,
      },
      {
        title: t('状态'),
        render: 'enabledName',
        width: 80,
      },
      {
        title: t('更新人'),
        render: 'username',
        width: 120,
      },
      {
        title: t('更新时间'),
        render: 'updateTime',
        width: 120,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 80,
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
          >
            {t('查看')}
          </Button>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            empty={t('暂无数据')}
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({
                selectedRows: rows,
              });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'GOODS_AGE_GRADE',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
  recordVisible: PropTypes.bool,
};

export default List;
