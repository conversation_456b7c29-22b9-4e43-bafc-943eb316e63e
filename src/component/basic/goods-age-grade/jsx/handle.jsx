import React from 'react';
import PropTypes from 'prop-types';
import {
  Modal, Button, Select, Radio, Input,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import style from '../style.css';
import store, {
  MODAL_VISIBLE_CLOSE,
  MODAL_VISIBLE_ADD,
  MODAL_VISIBLE_EDIT,
  STATUS_DISENABLED,
} from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
      selectedRows,
      editObjVisible,
      editObj,
      enabledList,
      goodsAgeTypeList,
    } = this.props;

    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.openModal(MODAL_VISIBLE_ADD);
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            store.openModal(MODAL_VISIBLE_EDIT);
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.exportData();
          }}
        >
          {t('导出')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || !selectedRows.length}
          onClick={() => {
            Modal.confirm({
              content: t('确定删除勾选的配置？'),
              onOk: () => {
                store.delData();
              },
              text: {
                ok: t('确认'),
                cancel: t('取消'),
              },
            });
          }}
        >
          {t('删除')}
        </Button>
        {/* Modal */}
        <Modal
          maskCloseAble={false}
          title={t('{}库龄等级', editObjVisible === MODAL_VISIBLE_ADD ? '新增' : '编辑')}
          visible={editObjVisible !== MODAL_VISIBLE_CLOSE}
          width="500px"
          onClose={() => store.changeData({
            editObjVisible: MODAL_VISIBLE_CLOSE,
          })}
          footer={[
            <Button
              key="cancel" onClick={() => store.changeData({
                editObjVisible: MODAL_VISIBLE_CLOSE,
              })}
            >
              {t('取消')}
            </Button>,
            <Button
              key="ok"
              type="primary"
              disabled={
                !(editObj.type !== '' && editObj.grade !== '' && editObj.minAge !== '' && editObj.maxAge !== '')
                || (editObjVisible === MODAL_VISIBLE_ADD && editObj.enabled === STATUS_DISENABLED) || !loading
              }
              onClick={() => store.saveData()}
            >
              {t('确定')}
            </Button>,
          ]}
        >
          <section>
            <div style={{ display: 'flex', marginBottom: '5px' }}>
              <span className={style.modalLabel} style={{ marginLeft: '25px' }}>
                {t('状态')}
                :
              </span>
              <Radio.Group
                data-bind="editObj.enabled"
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                data={enabledList}
                disabled={editObjVisible === MODAL_VISIBLE_ADD}
              />
            </div>
            <div style={{ color: 'red', marginBottom: '5px', marginLeft: '70px' }}>{t('注销后，当前库龄类型对应所有配置都会注销')}</div>
            <div style={{ display: 'flex', marginBottom: '5px' }}>
              <span className={style.modalLabel}>
                {t('库龄类型')}
                :
              </span>
              <Select
                data-bind="editObj.type"
                data={goodsAgeTypeList}
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                width={220}
              />
            </div>
            <div style={{ display: 'flex', marginBottom: '5px' }}>
              <span className={style.modalLabel}>
                {t('库龄等级')}
                :
              </span>
              <Input
                data-bind="editObj.grade"
                maxLength={10}
                style={{ width: 220 }}
              />
            </div>
            <div style={{ display: 'flex', marginBottom: '5px' }}>
              <span className={style.modalLabel}>
                {t('天数区间')}
                :
              </span>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Input.Number
                  data-bind="editObj.minAge"
                  digits={0}
                  min={1}
                  max={99999}
                  style={{ width: 100 }}
                />
                &ensp;-&ensp;
                <Input.Number
                  data-bind="editObj.maxAge"
                  digits={0}
                  min={1}
                  max={99999}
                  style={{ width: 100 }}
                />
              </div>
            </div>
          </section>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
  editObjVisible: PropTypes.number,
  editObj: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape),
  goodsAgeTypeList: PropTypes.arrayOf(PropTypes.shape),
};
export default Handle;
