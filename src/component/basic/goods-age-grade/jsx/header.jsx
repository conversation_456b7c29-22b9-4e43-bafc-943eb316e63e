import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import { formatSearchData } from '@public-component/search-queries/utils';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      goodsAgeTypeList,
      enabledList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('库龄类型')}
            name="type"
            data={goodsAgeTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('状态')}
            name="enabled"
            data={enabledList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  goodsAgeTypeList: PropTypes.arrayOf(PropTypes.shape()),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
