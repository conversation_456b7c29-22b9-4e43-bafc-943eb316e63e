import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { dictSelect, getSubWarehouseSelectList } from '@src/server/basic/dictionary';
import { handleListMsg, queryParkList } from '@src/lib/dealFunc';
import { getSize } from '@src/middlewares/pagesize';
import {
  getListAPI, saveAPI, deleteAPI,
} from './server';

// 搜索区域
export const defaultLimit = {
  warehouseId: '', // 仓库
  parkType: '', // 园区
  subWarehouseId: '', // 子仓
  onShelfType: '', //  上架类型
};

// 弹窗表单数据
export const defaultModalInfo = {
  warehouseId: '', // 仓库
  parkType: '', // 园区
  subWarehouseId: '', // 子仓
  onShelfType: '', //  上架类型
  config: [], // 隔离规则配置
  storeType: [], // 存储属性限制
  skcType: [], // SKC维度限制
  color: [], // 颜色限制
  items: [], // 品项限制
  highValue: [], // 贵重品库区限制
  authType: [], // 权限控制
  boxStorageType: [], // 拣货区整箱存储限制
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  warehouseList: [], // 仓库 下拉
  parkTypeList: [], // 园区 下拉
  subWarehouseList: [], // 子仓 下拉
  onShelfTypeList: [], // 上架类型 下拉
  preSubWarehouseList: [],
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  // 操作区域
  selectedRows: [], // 勾选的数据
  modalFormRef: {}, // 用于校验弹窗内的表单项
  modalType: '', // 弹窗类型 0:新增 1:编辑 2:全局配置
  modalText: [t('新增'), t('编辑'), t('全局配置')],
  modalInfo: defaultModalInfo,
  modalWarehouseList: [], // 弹窗 仓库 下拉
  modalParkTypeList: [], // 弹窗 园区 下拉
  modalSubWarehouseList: [], // 弹窗 子仓 下拉
  globalWarehouseList: [// 弹窗 全局配置 仓库 下拉
    { id: 1, nameZh: t('佛山仓') },
    { id: -1, nameZh: t('非佛山仓') },
  ],
  configList: [// 隔离规则配置
    { id: 0, name: t('存储属性限制') },
    { id: 1, name: t('SKC维度隔离') },
    { id: 2, name: t('颜色限制') },
    { id: 3, name: t('品项限制') },
    { id: 4, name: t('贵重品库区限制') },
    { id: 5, name: t('权限控制') },
    { id: 6, name: t('拣货区整箱存储限制') },
  ],
  storeTypeList: [], // 存储属性限制
  skcTypeList: [], // SKC维度限制
  colorList: [], // 颜色限制
  itemsList: [], // 品项限制
  highValueList: [], // 贵重品库区限制
  authList: [], // 权限控制
  boxStorageTypeList: [], // 拣货区整箱存储限制
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  changeModalData(state, data) {
    Object.assign(state.modalInfo, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    yield this.changeData({
      parkTypeList: [],
      subWarehouseList: [],
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },

  /**
   * 页面初始化
   */
  * init() {
    markStatus('loading');
    const [selectData] = yield Promise.all([
      dictSelect({
        catCode: [
          'ON_SHELF_CONFIG_TYPE', 'WMD_ON_SHELF_STORE_TYPE', 'WMD_ON_SHELF_SKC_TYPE',
          'WMD_ON_SHELF_COLOR', 'WMD_ON_SHELF_ITEMS', 'IS_HIGH_VALUE', 'WMD_ON_SHELF_AUTH_TYPE', 'WMD_ON_SHELF_BOX_STORAGE_TYPE',
        ],
      }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        onShelfTypeList: selectData.info.data.find((v) => v.catCode === 'ON_SHELF_CONFIG_TYPE').dictListRsps,
        storeTypeList: selectData.info.data.find((v) => v.catCode === 'WMD_ON_SHELF_STORE_TYPE').dictListRsps,
        skcTypeList: selectData.info.data.find((v) => v.catCode === 'WMD_ON_SHELF_SKC_TYPE').dictListRsps,
        colorList: selectData.info.data.find((v) => v.catCode === 'WMD_ON_SHELF_COLOR').dictListRsps,
        itemsList: selectData.info.data.find((v) => v.catCode === 'WMD_ON_SHELF_ITEMS').dictListRsps,
        highValueList: selectData.info.data.find((v) => v.catCode === 'IS_HIGH_VALUE')?.dictListRsps,
        authList: selectData.info.data.find((v) => v.catCode === 'WMD_ON_SHELF_AUTH_TYPE')?.dictListRsps,
        boxStorageTypeList: selectData.info.data.find((v) => v.catCode === 'WMD_ON_SHELF_BOX_STORAGE_TYPE')?.dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }

    // 获取仓库
    const { currentWarehouseList } = yield 'nav';
    yield this.changeData({
      warehouseList: currentWarehouseList,
      modalWarehouseList: currentWarehouseList,
    });
  },

  /**
   * 切换仓库，获取园区、子仓
   */
  * handleWarehouse() {
    const { limit } = yield '';
    const { parkList } = yield queryParkList(limit.warehouseId);
    const { code, info, msg } = yield getSubWarehouseSelectList({
      warehouseId: limit.warehouseId,
      enabled: 1,
    });
    if (code === '0') {
      yield this.changeData({
        parkTypeList: parkList,
        subWarehouseList: info.data || [],
        preSubWarehouseList: info.data || [],
        limit: {
          ...limit,
          parkType: '',
          subWarehouseId: '',
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 搜索
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      // 处理返回的列表字段
      const {
        configList, skcTypeList, storeTypeList, colorList, itemsList, highValueList, authList, boxStorageTypeList,
      } = yield '';
      info?.data?.forEach((currentRow) => {
        const configDesc = [];
        let storeTypeName = '';
        let skcTypeName = '';
        let colorName = '';
        let itemsName = '';
        let highValuesName = '';
        let authTypeName = '';
        let boxStorageTypeName = '';
        const contentDesc = [];
        const config = [];
        // 存储属性限制
        storeTypeList.forEach((item) => {
          if (currentRow.storeType.includes(item.dictCode)) {
            storeTypeName += `${item.dictNameZh};`;
            configDesc.push(`${configList[0].name};`);
            config.push(0);
          }
        });
        if (storeTypeName) contentDesc.push(storeTypeName);
        // SKC维度限制
        skcTypeList.forEach((item) => {
          if (currentRow.skcType.includes(item.dictCode)) {
            skcTypeName += `${item.dictNameZh};`;
            configDesc.push(`${configList[1].name};`);
            config.push(1);
          }
        });
        if (skcTypeName) contentDesc.push(skcTypeName);
        // 同色限制
        colorList.forEach((item) => {
          if (currentRow.color.includes(item.dictCode)) {
            colorName += `${item.dictNameZh};`;
            configDesc.push(`${configList[2].name};`);
            config.push(2);
          }
        });
        if (colorName) contentDesc.push(colorName);
        // 品项限制
        itemsList.forEach((item) => {
          if (currentRow.items.includes(item.dictCode)) {
            itemsName += `${item.dictNameZh};`;
            configDesc.push(`${configList[3].name};`);
            config.push(3);
          }
        });
        if (itemsName) contentDesc.push(itemsName);
        // 贵重品库区限制
        highValueList.forEach((item) => {
          if ((currentRow.highValue || []).includes(item.dictCode)) {
            highValuesName += `${item.dictNameZh};`;
            configDesc.push(`${configList[4].name};`);
            config.push(4);
          }
        });
        if (highValuesName) contentDesc.push(highValuesName);

        // 权限限制
        authList.forEach((item) => {
          if ((currentRow.authType || []).includes(item.dictCode)) {
            authTypeName += `${item.dictNameZh};`;
            configDesc.push(`${configList[5].name};`);
            config.push(5);
          }
        });
        if (authTypeName) contentDesc.push(authTypeName);

        // 拣货区整箱存储限制
        boxStorageTypeList.forEach((item) => {
          if ((currentRow.boxStorageType || []).includes(item.dictCode)) {
            boxStorageTypeName += `${item.dictNameZh};`;
            configDesc.push(`${configList[6].name};`);
            config.push(6);
          }
        });
        if (boxStorageTypeName) contentDesc.push(boxStorageTypeName);

        currentRow.configDesc = Array.from(new Set(configDesc));
        currentRow.contentDesc = contentDesc;
        currentRow.config = Array.from(new Set(config));
      });

      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data || [],
        selectedRows: [],
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  /**
   * 打开弹窗-0:新增 1:编辑 2:全局配置
   * @returns
   */
  * openModal(modalType) {
    // 请求弹窗数据 + 编辑赋值
    const { selectedRows } = yield '';
    if (modalType === 1) {
      // 编辑赋值
      const currentRow = selectedRows[0];
      yield this.changeData({
        modalInfo: currentRow,
      });
      yield this.handleModal({ modalType, warehouseId: currentRow.warehouseId });
    }

    yield this.changeData({
      modalType,
    });
  },

  /**
   * 弹窗-切换仓库，获取园区、子仓
   * @returns
   */
  * handleModal({ modalType, warehouseId }) {
    markStatus('loading');
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      modalParkTypeList: parkList,
    });
    // 新增 切换仓库，子仓为[]；编辑 切换仓库，子仓请求接口 回显
    if (modalType === 1) {
      const { code, info, msg } = yield getSubWarehouseSelectList({
        warehouseId,
        enabled: 1,
      });
      if (code === '0') {
        yield this.changeData({
          modalSubWarehouseList: info.data || [],
        });
      } else {
        Modal.error({ title: msg });
      }
    }
  },

  /**
   * 关闭弹窗-清空数据及表单验证
   * @returns
   */
  * closeModal() {
    const { modalFormRef } = yield '';
    yield this.changeData({
      modalType: '',
      modalInfo: defaultModalInfo,
      modalParkTypeList: [],
      modalSubWarehouseList: [],
    });
    if (modalFormRef && modalFormRef.clearValidate) modalFormRef.clearValidate();
  },

  /**
   * 保存-0:新增 1:编辑 2:全局配置
   * @returns
   */
  * saveData() {
    const {
      modalInfo, modalType, modalText,
    } = yield '';
    const {
      warehouseId, parkType, subWarehouseId, onShelfType, storeType, skcType, color, items, highValue, authType, boxStorageType,
    } = modalInfo;
    const param = {
      storeType,
      skcType,
      color,
      items,
      highValue,
      subWarehouseId,
      onShelfType,
      operateType: modalType + 1, // 操作类型 1-新增 2-修改 3-全局修改
      parkType,
      warehouseId,
      id: modalInfo.id || undefined,
      authType,
      /** 整箱存储限制:1 整箱上架任务限制 2非整箱上架任务限制 */
      boxStorageType,
    };
    markStatus('loading');
    const { code, msg } = yield saveAPI(param);
    if (code === '0') {
      Message.success(`${modalText[modalType]}${t('成功')}`);
      yield this.closeModal();
      yield this.search();
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 删除
   * @returns
   */
  * deleteData() {
    const { selectedRows } = yield '';
    markStatus('loading');
    const { code, msg } = yield deleteAPI({
      id: selectedRows[0].id,
    });
    if (code === '0') {
      Message.success(t('删除成功！'));
      yield this.search();
    } else {
      Modal.error({ title: msg || t('后台接口出错') });
    }
  },

};
