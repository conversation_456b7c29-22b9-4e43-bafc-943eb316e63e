import { sendPostRequest } from '@src/server/common/public';

/**
 * 搜索
 * @param {*} param
 * @returns
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/on_shelf_config/query',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 弹窗保存
 * @param {*} param
 * @returns
 */
export const saveAPI = (param) => sendPostRequest({
  url: '/on_shelf_config/insert_update',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 删除
 * @param {*} param
 * @returns
 */
export const deleteAPI = (param) => sendPostRequest({
  url: '/on_shelf_config/delete',
  param,
}, process.env.BASE_URI_WMD);
