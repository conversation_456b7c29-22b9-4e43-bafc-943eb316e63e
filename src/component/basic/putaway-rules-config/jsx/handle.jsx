import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Modal, Select, Popover, Checkbox, Radio,
} from 'shineout';
import { t } from '@shein-bbl/react';
import globalStyles from '@src/component/style.less';
import styles from '../style.less';
import store from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
      modalType,
      modalText,
      modalInfo,
      selectedRows,
      modalWarehouseList,
      modalParkTypeList,
      modalSubWarehouseList,
      globalWarehouseList,
      onShelfTypeList,
      configList,
      storeTypeList,
      skcTypeList,
      colorList,
      itemsList,
      highValueList,
      authList,
      boxStorageTypeList,
    } = this.props;

    const validate = () => {
      const {
        warehouseId, onShelfType, config, storeType, skcType, color, items, highValue, boxStorageType,
      } = modalInfo;
      if (!warehouseId || !onShelfType || config.length === 0) {
        return true;
      }
      // 存储属性限制
      if (config.includes(0) && storeType.length === 0) {
        return true;
      }
      // SKC维度限制
      if (config.includes(1) && skcType.length === 0) {
        return true;
      }
      // 颜色限制
      if (config.includes(2) && color.length === 0) {
        return true;
      }
      // 品项限制
      if (config.includes(3) && items.length === 0) {
        return true;
      }
      // 贵重品库区限制
      if (config.includes(4) && highValue.length === 0) {
        return true;
      }
      // 拣货区整箱存储限制
      if (config.includes(6) && boxStorageType?.length === 0) {
        return true;
      }
      return false;
    };

    const getFinalConfigList = () => {
      const { onShelfType } = modalInfo;
      /**
       * 仅上架类型=移位上架展示: 拣货区整箱存储限制
       */
      if (onShelfType !== 3) {
        return configList.filter((c) => c.id !== 6);
      }
      return configList;
    };

    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          loading={!loading}
          onClick={() => {
            store.openModal(0);
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          loading={!loading}
          disabled={selectedRows.length !== 1}
          onClick={() => {
            store.openModal(1);
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="primary"
          loading={!loading}
          disabled={selectedRows.length !== 1}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => {
              store.deleteData();
            }}
          >
            {t('是否确定删除?')}
          </Popover.Confirm>
          {t('删除')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.openModal(2);
          }}
        >
          {t('修改全局配置')}
        </Button>

        {/* 新增、编辑弹窗 0:新增 1:编辑 2:全局配置 */}
        <Modal
          maskCloseAble={null}
          visible={[0, 1, 2].includes(modalType)}
          width={920}
          title={modalText[modalType]}
          onClose={() => store.closeModal()}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading || validate()}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            onSubmit={() => store.saveData()}
            onChange={(value) => {
              store.changeData({
                modalInfo: value,
              });
            }}
            value={modalInfo}
            formRef={(f) => store.changeData({ modalFormRef: f })}
          >
            {modalType !== 2 && (
              <Form.Item required label={t('仓库')} labelWidth={153}>
                <Select
                  name="warehouseId"
                  data={modalWarehouseList}
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  style={{ width: 200 }}
                  clearable
                  disabled={modalType === 1}
                  renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择仓库')}</span>}
                  onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  onChange={(value) => {
                    if (value) {
                      store.handleModal({ modalType, warehouseId: value });
                    } else {
                      store.changeData({
                        modalParkTypeList: [],
                        modalSubWarehouseList: [],
                      });
                    }
                    store.changeData({
                      modalInfo: {
                        ...modalInfo,
                        warehouseId: value,
                        parkType: '',
                        subWarehouseId: '',
                      },
                    });
                  }}
                />
                <Select
                  name="parkType"
                  data={modalParkTypeList}
                  keygen="parkType"
                  format="parkType"
                  renderItem="parkName"
                  clearable
                  disabled={modalType === 1}
                  style={{ width: 200, marginLeft: 20 }}
                  renderUnmatched={(r) => r.parkName || <span style={{ color: '#bbb' }}>{t('请选择园区')}</span>}
                  onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  onChange={(value, d) => {
                    if (typeof value === 'number') {
                      store.changeData({
                        modalSubWarehouseList: d.subWarehouseList,
                      });
                    } else {
                      store.changeData({
                        modalSubWarehouseList: [],
                      });
                    }
                    store.changeData({
                      modalInfo: {
                        ...modalInfo,
                        parkType: value,
                        subWarehouseId: '',
                      },
                    });
                  }}
                />
                <Select
                  name="subWarehouseId"
                  data={modalSubWarehouseList}
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  clearable
                  disabled={modalType === 1}
                  style={{ width: 200, marginLeft: 20 }}
                  renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择子仓')}</span>}
                  onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                />
              </Form.Item>
            )}
            {modalType === 2 && (
              <Form.Item required label={t('仓库')} labelWidth={153}>
                <Select
                  name="warehouseId"
                  data={globalWarehouseList}
                  keygen="id"
                  format="id"
                  renderItem="nameZh"
                  style={{ width: 200 }}
                  renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择仓库')}</span>}
                  onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                />
              </Form.Item>
            )}
            <Form.Item required label={t('上架类型')} labelWidth={153}>
              <Radio.Group
                name="onShelfType"
                keygen="dictCode"
                data={onShelfTypeList}
                format="dictCode"
                renderItem="dictNameZh"
                disabled={modalType === 1}
                onChange={(value) => {
                  const newConfig = {
                    color: [],
                  };
                  /**
                   * 当勾选的上架类型=移位上架时，展示拣货区整箱存储限制，否则移除拣货区整箱存储限制
                   */
                  if (value !== 3) {
                    newConfig.config = modalInfo.config.filter((c) => c !== 6);
                    newConfig.boxStorageType = [];
                  }
                  store.changeModalData(newConfig);
                }}
              />
            </Form.Item>
            <Form.Item required label={t('隔离规则配置')} labelWidth={153}>
              <div className={styles.defaultBg}>
                <div className={styles.firstBox}>
                  <Checkbox.Group
                    name="config"
                    keygen="id"
                    data={getFinalConfigList()}
                    format="id"
                    renderItem="name"
                    onChange={(value) => {
                      if (!value.includes(0)) {
                        store.changeModalData({
                          storeType: [],
                          config: value,
                        });
                      }
                      if (!value.includes(1)) {
                        store.changeModalData({
                          skcType: [],
                          config: value,
                        });
                      }
                      if (!value.includes(2)) {
                        store.changeModalData({
                          config: value,
                          color: [],
                        });
                      }
                      if (!value.includes(3)) {
                        store.changeModalData({
                          items: [],
                          authType: [],
                          config: value,
                        });
                      }
                      if (!value.includes(4)) {
                        store.changeModalData({
                          highValue: [],
                          config: value,
                        });
                      }
                      if (!value.includes(6)) {
                        store.changeModalData({
                          boxStorageType: [],
                          config: value,
                        });
                      }
                    }}
                  />
                </div>
                {modalInfo.config.includes(0) && (
                  <div className={styles.box}>
                    <Form.Item required label={t('存储属性限制')}>
                      <Checkbox.Group
                        name="storeType"
                        keygen="dictCode"
                        data={storeTypeList}
                        format="dictCode"
                        renderItem="dictNameZh"
                      />
                    </Form.Item>
                  </div>
                )}
                {modalInfo.config.includes(1) && (
                  <div className={styles.box}>
                    <Form.Item required label={t('SKC维度限制')}>
                      <Checkbox.Group
                        name="skcType"
                        keygen="dictCode"
                        data={skcTypeList}
                        format="dictCode"
                        renderItem="dictNameZh"
                      />
                      {modalInfo.config}
                    </Form.Item>
                  </div>
                )}
                {/* 当勾选的上架类型=移位上架时，颜色限制展示为：大货同色限制、散货同色限制，否则为同色限制 */}
                {modalInfo.config.includes(2) && (
                  <div className={styles.box}>
                    <Form.Item required label={t('颜色限制')}>
                      <Checkbox.Group
                        name="color"
                        keygen="dictCode"
                        data={Number(modalInfo.onShelfType) === 3 ? colorList.filter((c) => ([2, 3].indexOf(Number(c.dictCode)) >= 0)) : colorList.filter((c) => ([1].indexOf(Number(c.dictCode)) >= 0))}
                        format="dictCode"
                        renderItem="dictNameZh"
                      />
                    </Form.Item>
                  </div>
                )}
                {modalInfo.config.includes(3) && (
                  <div className={styles.box}>
                    <Form.Item required label={t('品项限制')}>
                      <Checkbox.Group
                        name="items"
                        keygen="dictCode"
                        data={itemsList}
                        format="dictCode"
                        renderItem="dictNameZh"
                      />
                    </Form.Item>
                  </div>
                )}
                {modalInfo.config.includes(4) && (
                  <div className={styles.box}>
                    <Form.Item required label={t('贵重品库区限制')}>
                      <Checkbox.Group
                        name="highValue"
                        keygen="dictCode"
                        data={highValueList}
                        format="dictCode"
                        renderItem="dictNameZh"
                      />
                    </Form.Item>
                  </div>
                )}
                {modalInfo.config.includes(5) && (
                  <div className={styles.box}>
                    <Form.Item required label={t('权限限制')}>
                      <Checkbox.Group
                        name="authType"
                        keygen="dictCode"
                        data={authList}
                        format="dictCode"
                        renderItem="dictNameZh"
                      />
                    </Form.Item>
                  </div>
                )}

                {/* 当勾选的上架类型=移位上架时，展示拣货区整箱存储限制 */}
                {modalInfo.config.includes(6) && (
                  <div className={styles.box}>
                    <Form.Item required label={t('拣货区整箱存储限制')}>
                      <Checkbox.Group
                        keygen="dictCode"
                        format="dictCode"
                        name="boxStorageType"
                        renderItem="dictNameZh"
                        data={boxStorageTypeList}
                      />
                    </Form.Item>
                  </div>
                )}
              </div>
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  modalType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalText: PropTypes.arrayOf(PropTypes.string),
  modalInfo: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  modalWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  modalParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  modalSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  globalWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  onShelfTypeList: PropTypes.arrayOf(PropTypes.shape()),
  configList: PropTypes.arrayOf(PropTypes.shape()),
  storeTypeList: PropTypes.arrayOf(PropTypes.shape()),
  skcTypeList: PropTypes.arrayOf(PropTypes.shape()),
  colorList: PropTypes.arrayOf(PropTypes.shape()),
  itemsList: PropTypes.arrayOf(PropTypes.shape()),
  highValueList: PropTypes.arrayOf(PropTypes.shape()),
  authList: PropTypes.arrayOf(PropTypes.shape()),
  boxStorageTypeList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
