import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('园区'),
        render: 'parkTypeName',
        width: 100,
      },
      {
        title: t('子仓'),
        render: 'subWarehouseName',
        width: 120,
      },
      {
        title: t('上架类型'),
        render: 'onShelfTypeName',
        width: 160,
      },
      {
        title: t('上架规则'),
        width: 200,
        render: (row) => {
          const arr = row.configDesc || [];
          return (
            arr.map((item, index) => (
              <div key={item}>
                {index + 1}
                、
                {item}
              </div>
            ))
          );
        },
      },
      {
        title: t('规则说明'),
        width: 260,
        render: (row) => {
          const arr = row.contentDesc || [];
          return (
            arr.map((item, index) => (
              <div key={item}>
                {index + 1}
                、
                {item}
              </div>
            ))
          );
        },
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape),
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
  pageInfo: PropTypes.shape(),
};

export default List;
