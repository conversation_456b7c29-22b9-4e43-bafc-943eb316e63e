import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';
import styles from '../style.less';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      warehouseList,
      parkTypeList,
      subWarehouseList,
      onShelfTypeList,
      preSubWarehouseList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.search();
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('仓库'), t('园区')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <Select
            label={t('仓库')}
            name="warehouseId"
            data={warehouseList}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            renderUnmatched={(r) => r.nameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            onChange={(value) => {
              if (value) {
                store.handleWarehouse();
              } else {
                store.changeData({
                  parkTypeList: [],
                  subWarehouseList: [],
                });
              }
            }}
          />
          <Select
            label={t('园区')}
            name="parkType"
            data={parkTypeList}
            keygen="parkType"
            format="parkType"
            placeholder={t('请选择')}
            renderItem="parkName"
            renderUnmatched={(r) => r.parkName || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            onChange={(value, d) => {
              if (typeof value === 'number') {
                store.changeData({
                  subWarehouseList: d.subWarehouseList,
                });
              } else {
                store.changeData({
                  subWarehouseList: preSubWarehouseList,
                });
              }
              store.changeLimitData({
                subWarehouseId: '',
              });
            }}
          />
          <Select
            label={t('子仓')}
            name="subWarehouseId"
            data={subWarehouseList}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            renderUnmatched={(r) => r.nameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('上架类型')}
            name="onShelfType"
            data={onShelfTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape),
  onShelfTypeList: PropTypes.arrayOf(PropTypes.shape),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape),
};
export default Header;
