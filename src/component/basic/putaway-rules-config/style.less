/* handle.jsx
---------------------------------------------------------------- */
.handleHandle {
    display: inline-block;
    vertical-align: top;
    margin: 16px 0;
}

.handleExportButton {
    margin-right: 6px;
}


/* header.jsx
---------------------------------------------------------------- */
.headerUnmatchedText {
    color: #bbb;
}


/* list.jsx
---------------------------------------------------------------- */
.listSection {
    height: 0px;
    /* 必写为了子domheight - 100%有效 */
    flex: 1;
}

.defaultBg {
    top: -12px;
    width: 702px;
    background: #f4f5f8;
    border-radius: 8px;
    padding: 16px 17px 14px 17px;

    .box {
        border-bottom: 1px dashed #c8cacd;
        margin-top: 20px;
    }

    .firstBox {
        border-bottom: 1px dashed #c8cacd;
        margin-bottom: 20px;
    }
}
