// 全部环节
export const LINK_MAP = new Map([
  [1, 'OVERSTOCK_STOCK_WAREHOUSING_LINK'], // 采购入库 [入库]
  [2, 'OVERSTOCK_RETREAT_LINK'], // 退供 [入库]
  [3, 'OVERSTOCK_REPLENISHMENT_LINK'], // 补货 [库内]
  [4, 'OVERSTOCK_RETURN_LINK'], // 回货 [库内]
  [5, 'OVERSTOCK_WAREHOUSE_SHIFT_LINK'], // 仓内移位 [库内]
  [6, 'OVERSTOCK_RETURN_WAREHOUSE_LINK'], // 返仓 [库内]
  [7, 'OVERSTOCK_CUSTOMER_REFUND_LINK'], // 客退 [支撑]
  [8, 'OVERSTOCK_RETURN_CLOTHES_LINK'], // 还衣 [支撑]
  [9, 'OVERSTOCK_CHECK_LINK'], // 盘点 [库内]
]);
// 订单类型
export const ORDER_TYPE_MAP = new Map([
  [1, 'STOCK_WAREHOUSING_ORDER_TYPE'], // 采购入库
  [3, 'REPLENISHMENT_ORDER_TYPE'], // 补货
  [9, 'CHECK_ORDER_TYPE'], // 盘点
  [2, 'RETURN_TYPE'], // 退供
]);
