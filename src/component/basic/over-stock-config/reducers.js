import moment from 'moment';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import fileSaver from 'file-saver';
import { formdataPost } from '@src/server/common/fileFetch';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect, getSubWarehouseSelectList } from '@src/server/basic/dictionary';
import { clearEmpty } from '@src/lib/deal-func';
import { getSubWarehouseApi } from '@src/server/basic/sub-warehouse';
import { STATISTICAL_DOWNLOAD, STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import {
  queryAPI,
  exportAPI,
  modifyAPI,
  deleteAPI,
  downloadTemplateAPI,
  monitoringOverstockConfigImportURL,
} from './server';
import {
  LINK_MAP,
  ORDER_TYPE_MAP,
} from './constants';

const format = 'YYYY-MM-DD HH:mm:ss';
const WAREHOUESE_ID_FOSHAN = 1; // 仓库id 佛山仓

// 搜索默认值
export const defaultLimit = {
  parkIdList: '', // 园区
  subWarehouseIdList: '', // 子仓
  businessTypeList: '', // 业务类型
  businessSubTypeList: '', // 环节
  orderTypeList: '', // 订单类型
  updateUser: '', // 更新人
  startLastUpdateTime: moment(new Date() // 更新日期 头
    .setHours(0, 0, 0, 0))
    .format(format),
  endLastUpdateTime: moment(new Date() // 更新日期 尾
    .setHours(23, 59, 59, 999))
    .format(format),
};
// 其他默认值
export const defaultState = {
  formRef: {},
  loading: 0, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  list: [],
  selectedRows: [], // 选中的表格列
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  warehouseId: '', // 当前仓库id
  warehouseList: [], // 仓库 下拉
  // header
  businessTypeList: [], // 业务类型 下拉
  businessSubTypeList: [], // 环节 下拉
  orderTypeList: [], // 订单类型 下拉
  parkTypeList: [], // 园区 下拉
  subWarehouseList: [], // 子仓 下拉
  businessTypeData: [], // 业务类型数据，包括对应的环节和订单类型
  // list
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录id
  // 编辑 modal
  editModalVisible: false,
  editModalLimit: {
    warehouseId: WAREHOUESE_ID_FOSHAN, // 仓库 默认佛山仓
    parkId: '', // 园区
    subWarehouseId: '', // 子仓
    businessType: '', // 业务类型
    orderType: '', // 订单类型
    businessSubType: '', // 环节
    preOverstockLimit: '', // 预超时阈值
    overstockLimit: '', // 超时阈值
  },
  editModalData: {
    subWarehouseList: [], // 子仓
    parkTypeList: [], // 园区
    businessSubTypeList: [], // 环节
    orderTypeList: [], // // 订单类型
  },
  // file modal
  modalLoading: 1,
  importModalVisible: false, // 导入弹出框是否显示 默认不显示
  file: '', // 导入文件对象
  editFormRef: {}, // 新增/编辑表单
};

export default {
  state: defaultState,
  // 改变state的值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  // 改编辑框limit属性值
  changeEditModalLimitData(state, data) {
    Object.assign(state, {
      editModalLimit: {
        ...state.editModalLimit,
        ...data,
      },
    });
  },
  /**
   * 初始化数据
   */
  * init() {
    markStatus('loading');
    const [selectData] = yield Promise.all([
      dictSelect({
        catCode: [
          'CAPSCITY_BOARD_BUSINESS_TYPE', // 业务类型
          ...Array.from(LINK_MAP.values()), // 环节
          ...Array.from(ORDER_TYPE_MAP.values()), // 订单类型
        ],
      }),
    ]);
    if (selectData.code === '0') {
      const businessTypeData = JSON.parse(JSON.stringify(selectData.info.data
        .find((x) => x.catCode === 'CAPSCITY_BOARD_BUSINESS_TYPE').dictListRsps));

      businessTypeData.forEach((item) => {
        const { dictCode } = item;
        // 环节
        if (LINK_MAP.has(dictCode)) {
          const catCode = LINK_MAP.get(dictCode);
          item.businessSubTypeList = selectData.info.data.find((x) => x.catCode === catCode).dictListRsps || [];
        }
        // 订单类型
        if (ORDER_TYPE_MAP.has(dictCode)) {
          const catCode = ORDER_TYPE_MAP.get(dictCode);
          item.orderTypeList = selectData.info.data.find((x) => x.catCode === catCode).dictListRsps || [];
        }
      });

      yield this.changeData({
        businessTypeList: selectData.info.data.find((x) => x.catCode === 'CAPSCITY_BOARD_BUSINESS_TYPE').dictListRsps,
        businessTypeData,
      });
    } else {
      Modal.error({
        title: selectData.msg || t('请求失败,请稍后再试'),
        autoFocusButton: 'ok',
      });
    }
    // 子仓下拉
    const { warehouseId, warehouseList, permissionSubWarehouseList } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    // 园区下拉
    const parkTypeList = yield this.getParkTypeList(warehouseId);

    yield this.changeData({
      warehouseId,
      warehouseList,
      subWarehouseList: permissionSubWarehouseList,
      parkTypeList,
    });
  },
  /**
   * 右上角更换仓库触发
   * @param {*} action
   */
  * changeSubWarehouseList(action) {
    const { warehouseId, subWarehouseList } = action;
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      yield this.changeData({
        subWarehouseList: [],
        parkTypeList: [],
        limit: {
          ...this.state.limit,
          parkIdList: '',
          subWarehouseIdList: '',
        },
      });
      return;
    }
    const parkTypeList = yield this.getParkTypeList(warehouseId);

    yield this.changeData({
      warehouseId,
      subWarehouseList,
      parkTypeList,
      limit: {
        ...this.state.limit,
        parkIdList: '',
        subWarehouseIdList: '',
      },
    });
  },
  /**
   * 切换业务类型
   */
  * changeBusinessType(val) {
    const { businessTypeData } = this.state;
    const data = businessTypeData.find(({ dictCode }) => dictCode === val) || {};

    yield this.changeData({
      businessSubTypeList: data.businessSubTypeList || [],
      orderTypeList: data.orderTypeList || [],
      limit: {
        ...this.state.limit,
        businessTypeList: val,
        businessSubTypeList: '',
        orderTypeList: '',
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef } = this.state;
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  /**
   * 搜索操作
   */
  * search() {
    const { limit, pageInfo } = this.state;
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    const params = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      warehouseId,
      businessSubTypeList: limit.businessSubTypeList !== '' ? [limit.businessSubTypeList] : '',
      businessTypeList: limit.businessTypeList !== '' ? [limit.businessTypeList] : '',
      orderTypeList: limit.orderTypeList !== '' ? [limit.orderTypeList] : '',
      parkIdList: limit.parkIdList !== '' ? [limit.parkIdList] : '',
      subWarehouseIdList: limit.subWarehouseIdList !== '' ? [limit.subWarehouseIdList] : '',
    };
    markStatus('loading');
    const { code, info, msg } = yield queryAPI(clearEmpty(params, ['0', 0]));
    if (code === '0') {
      yield this.changeData({
        list: info.data || [],
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  /**
   * 校验
   */
  * handleFormValidate() {
    const { formRef } = this.state;
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(arg = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }

    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  * uploadFile(formData) {
    markStatus('loading');
    formData.append('function_node', '34');
    const res = yield formdataPost(monitoringOverstockConfigImportURL, formData);
    if (res.code === '0') {
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
      window.open(STATISTICAL_IMPORT_CENTER);
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
   * handle 下载导入模板
   */
  * downloadTemplate() {
    markStatus('loading');

    yield downloadTemplateAPI().then((d) => d.blob()).then((b) => {
      const blob = new Blob([b], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('导入模板')}.xlsx`);
    });
  },
  /**
   * handle 按钮-删除
   */
  * deleteData() {
    const { selectedRows } = this.state;

    const { code, msg } = yield deleteAPI({
      id: selectedRows[0].id,
    });

    if (code === '0') {
      Message.success(t('删除成功'));
      yield this.search();
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  /**
   * handle 按钮操作-导出
   */
  * exportData() {
    const { limit, pageInfo } = this.state;
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请在右上角选择仓库') });
      return;
    }
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    const params = {
      ...limit,
      pageNum: 1,
      pageSize: pageInfo.pageSize,
      warehouseId,
      businessSubTypeList: limit.businessSubTypeList !== '' ? [limit.businessSubTypeList] : '',
      businessTypeList: limit.businessTypeList !== '' ? [limit.businessTypeList] : '',
      orderTypeList: limit.orderTypeList !== '' ? [limit.orderTypeList] : '',
      parkIdList: limit.parkIdList !== '' ? [limit.parkIdList] : '',
      subWarehouseIdList: limit.subWarehouseIdList !== '' ? [limit.subWarehouseIdList] : '',
    };
    markStatus('loading');
    const data = yield exportAPI(clearEmpty(params, ['0', 0]));
    if (data.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({
        title: data.msg || t('后台数据出错'),
        autoFocusButton: 'ok',
      });
    }
  },
  /**
   * handle 按钮操作-编辑
   */
  * handleEdit() {
    const { selectedRows, editModalLimit, warehouseId } = this.state;
    const selectedData = selectedRows[0];

    yield this.changeModalWarehouseId({
      warehouseId,
      isClear: false,
    });
    yield this.changeModalBusinessType({
      businessType: selectedData.businessType,
      isClear: false,
    });

    Object.entries(selectedData).map(([key, value]) => {
      if (Reflect.has(editModalLimit, key)) {
        editModalLimit[key] = value;
      }
      return key;
    });

    // 匹配不到不显示 子仓/订单类型/环节
    if (!this.state.editModalData.subWarehouseList.map((x) => x.id).includes(editModalLimit.subWarehouseId)) {
      editModalLimit.subWarehouseId = '';
    }
    if (!this.state.editModalData.businessSubTypeList.map((x) => x.dictCode).includes(editModalLimit.businessSubType)) {
      editModalLimit.businessSubType = '';
    }
    if (!this.state.editModalData.orderTypeList.map((x) => x.dictCode).includes(editModalLimit.orderType)) {
      editModalLimit.orderType = '';
    }

    yield this.changeData({
      editModalVisible: true,
      editModalLimit,
    });
  },
  /**
   * modal 编辑
   */
  * handleModalSubmit(params) {
    if (Number(params.preOverstockLimit) >= Number(params.overstockLimit)) {
      Modal.error({
        title: t('预超时阈值必须要小于超时阈值'),
      });
      return;
    }
    markStatus('modalLoading');
    const { code, msg } = yield modifyAPI(params);

    if (code === '0') {
      Message.success(t('编辑成功'));
      yield this.changeData({
        editModalVisible: false,
        editModalLimit: defaultState.editModalLimit,
        editModalData: defaultState.editModalData,
      });
      yield this.search();
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  /**
   * modal 改变仓库
   * @param {String} warehouseId 仓库id
   */
  * changeModalWarehouseId({ warehouseId, isClear = true }) {
    const parkTypeList = yield this.getParkTypeList(warehouseId);
    const subWarehouseList = yield this.getSubWarehouseList(warehouseId); // 获取子仓列表

    yield this.changeData({
      editModalLimit: {
        ...this.state.editModalLimit,
        warehouseId,
      },
      editModalData: {
        ...this.state.editModalData,
        subWarehouseList: subWarehouseList || [],
        parkTypeList: parkTypeList || [],
      },
    });
    if (isClear) {
      yield this.changeLimitData({
        parkId: '',
        subWarehouseId: '',
      });
    }
  },
  /**
   * modal 改变业务类型
   * @param {*} val 业务类型
   */
  * changeModalBusinessType({ businessType, isClear = true }) {
    const { businessTypeData } = this.state;
    const data = businessTypeData.find(({ dictCode }) => dictCode === businessType);

    yield this.changeData({
      editModalLimit: {
        ...this.state.editModalLimit,
        businessType,
      },
      editModalData: {
        ...this.state.editModalData,
        businessSubTypeList: data.businessSubTypeList || [],
        orderTypeList: data.orderTypeList || [],
      },
    });
    if (isClear) {
      yield this.changeLimitData({
        businessSubType: '',
        orderType: '',
      });
    }
  },
  /**
   * api 获取园区列表
   * @param {String} warehouseId
   */
  * getParkTypeList(warehouseId) {
    markStatus('loading');
    const { code, info, msg } = yield getSubWarehouseApi({ warehouseId });
    if (code === '0') {
      const arr = [];

      info.data
        .filter((x) => ![undefined, null].includes(x.parkType))
        .map((x) => ({
          parkName: x.parkName,
          parkType: x.parkType,
        }))
        .forEach((item) => {
          if (!arr.map((x) => x.parkType).includes(item.parkType)) {
            arr.push(item);
          }
        });

      // 排序
      arr.sort((a, b) => {
        if (a.parkType < b.parkType) {
          return -1;
        }
        if (a.parkType > b.parkType) {
          return 1;
        }
        return 0;
      });

      return arr;
    }
    Modal.error({
      title: msg,
    });
    return [];
  },
  /**
   * api 获取子仓列表
   * @param {String} warehouseId
   */
  * getSubWarehouseList(warehouseId) {
    markStatus('loading');
    const { code, info, msg } = yield getSubWarehouseSelectList({ warehouseId, enabled: 1 });
    if (code === '0') {
      return info.data;
    }
    Modal.error({
      title: msg,
      autoFocusButton: 'ok',
    });
    return [];
  },
  /**
   * 关闭 编辑弹窗，清空数据及校验
   */
  * handleEditModalReset() {
    const { editFormRef } = yield '';
    yield this.changeData({
      editModalVisible: false,
      editModalLimit: defaultState.editModalLimit,
      editModalData: defaultState.editModalData,
    });
    if (editFormRef && editFormRef.clearValidate) editFormRef.clearValidate();
  },
};
