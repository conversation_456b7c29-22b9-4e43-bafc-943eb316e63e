import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';
import { camel2Under } from '@src/lib/camal-case-convertor';

// 下载模板
export const downloadTemplateAPI = () => {
  const uri = `${process.env.WKB_FRONT}/monitoring_overstock_config/download_template`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under()),
  });
};

// 导出
export const exportAPI = (param) => sendPostRequest({
  url: '/monitoring_overstock_config/export',
  param,
}, process.env.WKB_FRONT);

// 新增/修改
export const modifyAPI = (param) => sendPostRequest({
  url: '/monitoring_overstock_config/modify',
  param,
}, process.env.WKB_FRONT);

// 删除
export const deleteAPI = (param) => sendPostRequest({
  url: '/monitoring_overstock_config/delete',
  param,
}, process.env.WKB_FRONT);

// 搜索
export const queryAPI = (param) => sendPostRequest({
  url: '/monitoring_overstock_config/query',
  param,
}, process.env.WKB_FRONT);

// 根据仓库id 查询子仓和园区信息(级联)
// export const getParkTypeListAPI = (param) => sendPostRequest({
//   url: '/sub_warehouse/query_park',
//   param,
// }, process.env.BASE_URI_WMD);

// 获取子仓数据
// export const getSubWarehouseListApi = (param) => sendPostRequest({
//   url: '/sub_warehouse/select',
//   param,
// }, process.env.BASE_URI_WMD);

// 导入接口
export const monitoringOverstockConfigImportURL = `${process.env.WGS_FRONT}/file_import/record/wkb/monitoring_overstock_config_import`;
