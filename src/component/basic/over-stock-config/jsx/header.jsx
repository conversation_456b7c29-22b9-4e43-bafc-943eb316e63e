import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import { formatSearchData } from '@public-component/search-queries/utils';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import FilterSearchSelect from '@src/component/public-component/select/filter-search-select';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      businessTypeList,
      businessSubTypeList,
      orderTypeList,
      parkTypeList,
      subWarehouseList,
    } = this.props;

    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('园区')}
            name="parkIdList"
            data={parkTypeList}
            keygen="parkType"
            format="parkType"
            renderItem="parkName"
            renderUnmatched={(r) => r?.parkName || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('子仓')}
            name="subWarehouseIdList"
            data={subWarehouseList}
            keygen="id"
            format="id"
            renderItem="nameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('业务类型')}
            name="businessTypeList"
            data={businessTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
            onChange={(val) => {
              store.changeBusinessType(val);
            }}
          />
          <Select
            label={t('订单类型')}
            disabled={!limit.businessTypeList}
            name="orderTypeList"
            data={orderTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            noCache
            placeholder={t('全部')}
          />
          <Select
            label={t('环节')}
            disabled={!limit.businessTypeList}
            name="businessSubTypeList"
            data={businessSubTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            noCache
            placeholder={t('全部')}
          />
          <FilterSearchSelect
            label={t('更新人')}
            name="updateUser"
            clearable
            placeholder={t('请输入')}
          />
          <DateRangePicker
            label={t('更新日期')}
            name={['startLastUpdateTime', 'endLastUpdateTime']}
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            defaultTime={['00:00:00', '23:59:59']}
            range
            clearable
            span={2}
          />

        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  businessTypeList: PropTypes.arrayOf(PropTypes.shape()),
  businessSubTypeList: PropTypes.arrayOf(PropTypes.shape()),
  orderTypeList: PropTypes.arrayOf(PropTypes.shape()),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
