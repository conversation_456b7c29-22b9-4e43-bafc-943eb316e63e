import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
    } = this.props;

    const columns = [
      {
        title: t('园区'),
        render: 'parkName',
        width: 100,
      },
      {
        title: t('子仓'),
        render: 'subWarehouseName',
        width: 100,
      },
      {
        title: t('业务类型'),
        render: 'businessTypeName',
        width: 100,
      },
      {
        title: t('订单类型'),
        render: 'orderTypeName',
        width: 100,
      },
      {
        title: t('环节'),
        render: 'businessSubTypeName',
        width: 100,
      },
      {
        title: t('预超时阈值'),
        render: 'preOverstockLimit',
        width: 140,
      },
      {
        title: t('超时阈值'),
        render: 'overstockLimit',
        width: 140,
      },
      {
        title: t('更新人'),
        render: 'updateUser',
        width: 100,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 80,
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'MONITORING_OVERSTOCK_CONFIG_OPERATE',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.string,
  recordVisible: PropTypes.bool,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
