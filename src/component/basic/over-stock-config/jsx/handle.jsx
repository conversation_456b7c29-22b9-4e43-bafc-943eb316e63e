import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Select, Input, Rule,
} from 'shineout';
import FileDelayUpload from '@src/component/public-component/upload/fileDelayUpload';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store from '../reducers';

const rule = Rule({
  isRequired: (val, formData, callback) => {
    if (!val) {
      callback(new Error(t('必填')));
      return;
    }
    callback(true);
  },
  overstockLimit: (val, formData, callback) => {
    if (Number(val) > 99999) {
      callback(new Error(t('阈值不能大于{}位', 6)));
      return;
    }
    if (Number(val) < 0 || val.includes('-')) {
      callback(new Error(t('阈值不能为负数')));
      return;
    }
    callback(true);
  },
});

class Handle extends React.Component {
  render() {
    const {
      loading,
      selectedRows,
      editModalVisible,
      editModalLimit,
      editModalData,
      warehouseList,
      businessTypeData,
      modalLoading,
      importModalVisible, // 导入弹出框是否显示 默认不显示
      file, // 导入文件对象
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              importModalVisible: true,
            });
          }}
        >
          {t('批量导入')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            store.handleEdit();
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="danger"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            Modal.confirm({
              content: t('确认要将该配置删除吗?'),
              onOk: () => {
                store.deleteData();
              },
            });
          }}
        >
          {t('删除')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.exportData();
          }}
        >
          {t('导出')}
        </Button>
        <Modal
          visible={editModalVisible}
          width={500}
          maskCloseAble={false}
          title={t('编辑')}
          onClose={() => store.handleEditModalReset()}
          footer={(
            <div>
              <Button onClick={() => store.handleEditModalReset()}>{t('取消')}</Button>
              <Modal.Submit disabled={!modalLoading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={120}
            labelAlign="right"
            style={{ maxWidth: 400 }}
            value={editModalLimit}
            formRef={(f) => store.changeData({ editFormRef: f })}
            onSubmit={() => {
              store.handleModalSubmit({
                id: selectedRows[0].id,
                ...editModalLimit,
              });
            }}
          >
            <div>
              <div style={{ marginBottom: '16px', borderBottom: '1px dashed #000', paddingBottom: '10px' }}>
                {t('基础信息')}
              </div>
              <Form.Item required label={t('仓库')}>
                <Select
                  name="warehouseId"
                  keygen="id"
                  format="id"
                  rules={[rule.isRequired()]}
                  data={warehouseList}
                  renderItem={(w) => w.nameZh}
                  onChange={(val) => {
                    store.changeModalWarehouseId({ warehouseId: val });
                  }}
                  disabled
                />
              </Form.Item>
              <Form.Item required label={t('园区')}>
                <Select
                  name="parkId"
                  keygen="parkType"
                  format="parkType"
                  rules={[rule.isRequired()]}
                  data={editModalData.parkTypeList}
                  renderItem={(w) => w.parkName}
                  onChange={(val) => {
                    store.changeEditModalLimitData({
                      parkId: val,
                    });
                  }}
                  disabled
                  noCache
                />
              </Form.Item>
              <Form.Item label={t('子仓')}>
                <Select
                  name="subWarehouseId"
                  keygen="id"
                  format="id"
                  data={editModalData.subWarehouseList}
                  renderItem={(w) => w.nameZh}
                  onChange={(val) => {
                    store.changeEditModalLimitData({
                      subWarehouseId: val,
                    });
                  }}
                  disabled
                  clearable
                  noCache
                />
              </Form.Item>
              <Form.Item required label={t('业务类型')}>
                <Select
                  name="businessType"
                  keygen="dictCode"
                  format="dictCode"
                  rules={[rule.isRequired()]}
                  data={businessTypeData}
                  renderItem={(w) => w.dictNameZh}
                  onChange={(val) => {
                    store.changeModalBusinessType({ businessType: val });
                  }}
                  disabled
                />
              </Form.Item>
              <Form.Item label={t('订单类型')}>
                <Select
                  name="orderType"
                  keygen="dictCode"
                  format="dictCode"
                  data={editModalData.orderTypeList}
                  renderItem={(w) => w.dictNameZh}
                  onChange={(val) => {
                    store.changeEditModalLimitData({
                      orderType: val,
                    });
                  }}
                  disabled
                  clearable
                  noCache
                />
              </Form.Item>
              <Form.Item label={t('环节')}>
                <Select
                  name="businessSubType"
                  keygen="dictCode"
                  format="dictCode"
                  data={editModalData.businessSubTypeList}
                  renderItem={(w) => w.dictNameZh}
                  onChange={(val) => {
                    store.changeEditModalLimitData({
                      businessSubType: val,
                    });
                  }}
                  disabled
                  clearable
                  noCache
                />
              </Form.Item>
            </div>
            <div>
              <div style={{ marginBottom: '16px', borderBottom: '1px dashed #000', paddingBottom: '10px' }}>
                {t('积压阈值配置')}
              </div>
              <Form.Item required label={t('预超时阈值')}>
                <Input
                  name="preOverstockLimit"
                  rules={[rule.isRequired(), rule.overstockLimit()]}
                  onChange={(val) => {
                    store.changeEditModalLimitData({
                      preOverstockLimit: val,
                    });
                  }}
                  digits={1}
                  type="number"
                  clearable
                />
              </Form.Item>
              <Form.Item required label={t('超时阈值')}>
                <Input
                  name="overstockLimit"
                  rules={[rule.isRequired(), rule.overstockLimit()]}
                  onChange={(val) => {
                    store.changeEditModalLimitData({
                      overstockLimit: val,
                    });
                  }}
                  digits={1}
                  type="number"
                  clearable
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
        <Button
          type="primary"
          onClick={() => {
            store.changeData({
              importModalVisible: true,
              file: '',
            });
          }}
        >
          {t('导入')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            store.downloadTemplate();
          }}
        >
          {t('下载导入模板')}
        </Button>
        <Modal
          maskCloseAble={null}
          visible={importModalVisible}
          title={t('批量导入配置')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={!file}
              loading={!loading}
              onClick={() => {
                const formData = new FormData();
                formData.append('file', file);
                store.uploadFile(formData);
                return false;
              }}
            >
              {t('确认上传')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!loading}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  editModalVisible: PropTypes.bool,
  editModalLimit: PropTypes.shape(),
  editModalData: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  businessTypeData: PropTypes.arrayOf(PropTypes.shape()),
  modalLoading: PropTypes.number,
  importModalVisible: PropTypes.bool,
  file: PropTypes.shape(),
};
export default Handle;
