import { sendPostRequest } from '@src/server/common/public';

// 搜索
export const getListAPI = (param) => sendPostRequest({
  url: '/reflux_config/strategy/query',
  param,
}, process.env.BASE_URI_WMD);

// 新增
export const addStrategyAPI = (param) => sendPostRequest({
  url: '/reflux_config/strategy/add',
  param,
}, process.env.BASE_URI_WMD);

// 编辑
export const modifyStrategyAPI = (param) => sendPostRequest({
  url: '/reflux_config/strategy/modify',
  param,
}, process.env.BASE_URI_WMD);

// 删除
export const deleteStrategyAPI = (param) => sendPostRequest({
  url: '/reflux_config/strategy/delete',
  param,
}, process.env.BASE_URI_WMD);
