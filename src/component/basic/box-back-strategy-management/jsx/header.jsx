import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form, Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      parkList,
      subWarehouseList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Form.Field
            name={['parkType', 'subWarehouseIds']}
            label={t('园区')}
            required
          >
            {({ value, onChange }) => (
              <Select
                label={t('园区')}
                name="parkType"
                keygen="parkType"
                format="parkType"
                renderItem="parkName"
                data={parkList}
                clearable
                placeholder={t('全部')}
                onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                value={value[0]}
                onChange={(val, d) => {
                  onChange([val, []]);
                  if (typeof val === 'number') {
                    store.changeData({
                      subWarehouseList: d.subWarehouseList,
                    });
                  }
                }}
              />
            )}
          </Form.Field>
          <Select
            label={t('子仓')}
            name="subWarehouseIds"
            keygen="id"
            format="id"
            renderItem="nameZh"
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            data={subWarehouseList}
            multiple
            compressed
            clearable
            required
            placeholder={t('全部')}
          />
        </SearchAreaContainer>

      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  parkList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
