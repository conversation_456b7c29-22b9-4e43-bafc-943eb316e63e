import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Select, Modal, Input, Popover,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store, { defaultEditObj } from '../reducers';

class Handle extends React.Component {
  render() {
    const {
      loading,
      warehouseId,
      selectedRows,
      editObj,
      editObjVisible,
      warehouseList, // 仓库列表
      parkList,
      modalSubWarehouseList, // 编辑的子仓列表
    } = this.props;
    const inputStyle = {
      width: '200px',
    };
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              editObjVisible: 1,
              editObj: { ...defaultEditObj, warehouseId },
            });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            const d = selectedRows[0] || {};
            const parkObj = parkList.find(({ parkType }) => parkType === d.parkType);
            store.changeData({
              modalSubWarehouseList: parkObj ? parkObj.subWarehouseList : [],
              editObjVisible: 2,
              editObj: { ...d },
            });
          }}
        >
          {t('编辑')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || !selectedRows.length}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => {
              store.deleteData();
            }}
          >
            {t('是否确认删除所选数据？')}
          </Popover.Confirm>
          {t('删除')}
        </Button>
        <Modal
          maskCloseAble={null}
          visible={editObjVisible}
          title={editObjVisible === 1 ? t('新增') : t('编辑')}
          onClose={() => store.changeData({
            editObjVisible: 0,
          })}
          footer={[
            <Button
              onClick={() => store.changeData({ editObjVisible: 0 })}
            >
              {t('取消')}
            </Button>,
            <Button
              loading={!loading}
              disabled={
                !editObj.warehouseId || !editObj.refluxRatio
                || !editObj.firstRatio || !editObj.secondRatio
                || !editObj.thirdRatio
                || (typeof editObj.parkType !== 'number')
                || (typeof editObj.subWarehouseId !== 'number')
              }
              type="primary"
              onClick={() => {
                store.confirmEdit();
              }}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={styles.block_label}>
            <span className={`${styles.add_label} ${styles.label_required}`}>
              {t('仓库')}
              :
            </span>
            <Select
              disabled
              keygen="id"
              format="id"
              renderItem="nameZh"
              value={editObj.warehouseId}
              style={inputStyle}
              data={warehouseList}
              placeholder={t('请选择')}
            />
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label} ${styles.label_required}`}>
              {t('园区')}
              :
            </span>
            <Select
              keygen="parkType"
              format="parkType"
              renderItem="parkName"
              value={editObj.parkType}
              onChange={(val, d) => {
                store.changeEditObjData({ parkType: val, subWarehouseId: '' });
                store.changeData({
                  modalSubWarehouseList: d.subWarehouseList,
                });
              }}
              style={inputStyle}
              data={parkList}
              placeholder={t('请选择')}
            />
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label} ${styles.label_required}`}>
              {t('子仓')}
              :
            </span>
            <Select
              disabled={typeof editObj.parkType !== 'number'}
              keygen="id"
              format="id"
              renderItem="nameZh"
              value={editObj.subWarehouseId}
              onChange={(val) => {
                store.changeEditObjData({ subWarehouseId: val });
              }}
              style={inputStyle}
              data={modalSubWarehouseList}
              placeholder={t('请选择')}
            />
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label} ${styles.label_required}`}>
              {t('回流比率')}
              :
            </span>
            <Input.Number
              allowNull
              digits={0}
              hideArrow
              min={1}
              max={100}
              placeholder={t('请输入')}
              style={inputStyle}
              value={editObj.refluxRatio}
              onChange={(val) => {
                store.changeEditObjData({ refluxRatio: val });
              }}
              autocomplete="off"
            />
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label} ${styles.label_required}`}>
              {t('第一层比例值')}
              :
            </span>
            <Input.Number
              allowNull
              digits={0}
              hideArrow
              min={1}
              max={100}
              placeholder={t('请输入')}
              style={inputStyle}
              value={editObj.firstRatio}
              onChange={(val) => {
                store.changeEditObjData({ firstRatio: val });
              }}
              autocomplete="off"
            />
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label} ${styles.label_required}`}>
              {t('第二层比例值')}
              :
            </span>
            <Input.Number
              allowNull
              digits={0}
              hideArrow
              min={1}
              max={100}
              placeholder={t('请输入')}
              style={inputStyle}
              value={editObj.secondRatio}
              onChange={(val) => {
                store.changeEditObjData({ secondRatio: val });
              }}
              autocomplete="off"
            />
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label} ${styles.label_required}`}>
              {t('第三层比例值')}
              :
            </span>
            <Input.Number
              allowNull
              digits={0}
              hideArrow
              min={1}
              max={100}
              placeholder={t('请输入')}
              style={inputStyle}
              value={editObj.thirdRatio}
              onChange={(val) => {
                store.changeEditObjData({ thirdRatio: val });
              }}
              autocomplete="off"
            />
          </div>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  warehouseId: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  editObj: PropTypes.shape(),
  editObjVisible: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  parkList: PropTypes.arrayOf(PropTypes.shape),
  modalSubWarehouseList: PropTypes.arrayOf(PropTypes.shape),
};
export default Handle;
