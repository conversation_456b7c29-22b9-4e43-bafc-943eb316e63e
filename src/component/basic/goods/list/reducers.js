import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Message, Modal } from 'shineout';
import {
  dictCatQuery, dictSelect, getWarehouseApi,
  // getStoreAttr,
} from '@src/server/basic/dictionary';
import { handleListMsg, queryParkList } from '@src/lib/dealFunc';
import { formdataPost } from '@src/server/common/fileFetch';
import { isPDFHistoryMode, setPDFHistory } from '@src/lib/storage';
import { getSize } from '@src/middlewares/pagesize';
import fileSaver from 'file-saver';
import { classFocus } from '@src/lib/deal-func';
import {
  transformToPdfUrl, textToBarcode,
} from '@src/lib/print-new';
import {
  getAreaList,
  getRoadwaySelectList,
  getRegionList,
  getParkSelectList,
  getSubWarehouseSelectList,
  getParkList,
  getEnableStockAttrList,
} from '@src/server/common/cache-api';
import { validatebMax } from '@src/lib/validate';
import { STATISTICAL_DOWNLOAD, STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import {
  addGoods, editGoods,
  exportFile, getGoodsById,
  getSpecConfigList,
  offGoods, printLocation, printLocationSequence,
  queryEnableExtend,
  queryGoods,
  saveSpecConfigServer,
  goodsLocationImportURL,
  batchOperationGoodsLocationAPI,
  batchUpdateStopUpAPI,
  batchUpdatePlanCatogoryAPI,
  downloadTemplate,
  downloadTemplate2, planCategoryImportURL,
} from './server';
import tpl100 from './ejs/location-big.ejs';
import tpl from './ejs/location.ejs';
import tpl40 from './ejs/location-forty.ejs';
import sequenceTpl from './ejs/sequence.ejs';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  location: '', // 库位
  roadway: '', // 巷道
  roadwayList: [], // 巷道下拉框入参
  locationGroup: '', // 货架组
  locationLevel: '', // 货架层
  locationType: [1], // 库位类型
  locationCategory: [], // 库位类别
  extendId: 0, // 货位规格id
  stopUp: '',
  subWarehouseIds: [], // 子仓
  areaIds: [], // 库区
  enabled: 1, // 状态
  printNum: undefined, // 打印次数
  modifyDateBegin: undefined, // 开始时间
  modifyDateEnd: undefined, // 结束时间
  // warehouseIds: [],
  parkTypeList: [],
  boxUp: '', // 是否整箱上架
  locationRow: '', // 行
  locationColumn: '', // 列
  isMultiSkc: '', // 是否同属性混SKC
  automateArea: '',
  planCategory: '', // 规划品类
  isEliminateCapacity: '', // 剔除库容
  physicalArea: '', // 物理库区
};

let CLASS_NUM = 1;
let CLASS_NUM_ENTER = false;
let IS_CHANGE_ROW = false;

const defaultState = {
  warehouseIds: '', // 保留右上角仓库，由于init时判断是否要重置子仓等
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  modalFormRef: {}, // 用于校验弹窗内的表单项
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(1),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100, 1000], // 表格页显示条数
  },
  list: [],
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  // 操作区域
  enabledList: [],
  warehouseList: [],
  isProduceList: [],
  recordList: [],
  parkTypeList: [], // 园区
  addModal: false, // 新增Modal
  modalType: '', // 弹窗类型 0:新增 1:编辑
  addInfo: {
    location: '',
    pickerOrder: '',
    roadway: '',
    enabled: 1,
    areaId: '',
    subWarehouseId: '',
    warehouseId: '',
    locationType: '',
    maxItemNum: '', // 品项混存上限
    extendId: 0, // 货位规格id
    locationCategory: '',
    storeTypes: [], // 存储属性
    stopUp: 0,
    boxUp: 0,
    locationRow: '',
    locationColumn: '',
    isMultiSkc: 1, // 是否同属性混SKC
  },
  selectedRows: [], // 选中的表格列
  exportVisible: true,
  locationTypeList: [],
  areaList: [], // list库区列表
  modalAreaList: [], // 弹窗库区列表
  pauseAreaList: [], // 库区弹框
  subWarehouseList: [], // list子仓列表
  modalSubWarehouseList: [], // 弹窗子仓列表
  specConfigList: [], // 货位规格配置列表数据
  specConfigModalVisiable: false, // 规格弹窗是否显示
  enableExtends: [], // 货位规格列表数据
  specDic: [], // 货位规格的状态查字典
  sequenceModalVisible: false,
  locationSequenceNum: undefined,
  locationCategoryList: [],
  planCategoryList: [], // 规划品类
  togglePaperVisible: false, // 切换打印纸张尺寸弹框 库位规格默认100*45。规格枚举值：100*45 80*50
  paperSize: 1, // 打印尺寸 默认 100*45
  storeAttrData: [], // 存储属性列表
  pauseUpperList: [{
    label: t('否'),
    value: 0,
  }, {
    label: t('是'),
    value: 1,
  }],
  isSkcList: [{ // 是否同属性混SKC
    label: t('否'),
    value: 0,
  }, {
    label: t('是'),
    value: 1,
  }],
  parkList: [],
  preSubWarehouseList: [],
  isInputing: false,
  maxPageNumber: 1, // 最大页数
  importModalVisible: false, // 导入弹出框是否显示 默认不显示
  file: '', // 导入文件对象
  batchModalVisible: false, // 批量新增modal 是否可见
  batchModalType: '', // 批量新增modal 类型 add:新增 edit:编辑
  currentWarehouseList: [], // 权限仓库列表
  regionList: [], // 片区下拉
  modalParkList: [], // 园区下拉
  pauseModalVisible: false, // 暂停上架维护弹框
  planCategoryVisible: false, // 规划品类弹框
  sum: '--',
  roadwayTypeList: [],
};

const maxValue = 99999.99;

const delZero = (list) => {
  if (list.areaIds && list.areaIds.length === 0) {
    delete list.areaIds;
  }
  if (list.subWarehouseIds && list.subWarehouseIds.length === 0) {
    delete list.subWarehouseIds;
  }
  if (list.warehouseIds && list.warehouseIds.length === 0) {
    delete list.warehouseIds;
  }
};

/**
 * 根据最大值对数组进行分组，默认为200
 * @param list
 * @param max
 * @returns {*[]}
 */
function getGroupArrs(list = [], max = 200) {
  if (!list.length) {
    return [];
  }
  // 根据数组数量和最大值计算需要分成几组
  const num = Math.ceil(list.length / max);
  // 根据num生成递增数组，如 [0,1,2,3...]
  const numArr = new Array(num).fill(0)
    .map((i, index) => i + index);
  return numArr.map((i) => list.slice(i * max, (i + 1) * max));
}

/**
 * @desc 大数组分成固定长度数组
 * @param data {any[]} 数组对象
 * @param  group {number} 分组，默认 10000
 * @returns {any[][]}
 */
function groupingArray(data, group) {
  if (!Array.isArray(data)) { return []; }
  const groupsNumber = Math.ceil(data.length / group);
  return Array.from(
    { length: groupsNumber },
    (_v, i) => data.slice(i * group, (i + 1) * group),
  );
}

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  changeAddInfoData(state, data) {
    Object.assign(state.addInfo, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef, preSubWarehouseList } = yield '';
    yield this.changeLimitData({
      ...defaultLimit,
    });
    yield this.changeData({
      subWarehouseList: preSubWarehouseList,
      areaList: [],
    });
    // 清空校验信息
    if (formRef && formRef.validate) formRef.validate();
  },
  // 右上角仓库事件派发
  * warehouseChange(data) {
    const { subWarehouseList, warehouseId } = data;
    // 获取园区列表
    const parkList = yield getParkList({ warehouseId });
    yield this.changeData({
      parkList,
      warehouseIds: warehouseId,
      subWarehouseList,
      preSubWarehouseList: subWarehouseList,
      areaList: [],
    });
    yield this.changeLimitData({
      subWarehouseIds: [],
      areaIds: [],
      parkTypeList: [],
      locationColumn: '',
      locationRow: '',
    });
  },
  // 弹框内仓库变化更新子仓列表
  * getSubWarehouse(id) {
    const list = yield getSubWarehouseSelectList({ warehouseId: id, enabled: 1 });
    yield this.changeData({
      modalSubWarehouseList: list,
    });
  },
  // 弹框内仓库变化更新片区列表
  * getRegionList(id) {
    const list = yield getRegionList({ warehouseId: id });
    yield this.changeData({
      regionList: list,
    });
  },
  // 获取园区列表
  * handleParkListQuery(region) {
    // 获取园区列表
    const list = yield getParkSelectList({ region });
    yield this.changeData({
      modalParkList: list,
    });
  },
  * getRoadwayTypeList(value) {
    markStatus('loading');
    const list = yield getRoadwaySelectList({ id: value });
    yield this.changeData({
      roadwayTypeList: list.map((e) => ({
        value: e,
      })),
    });
  },
  // 获取弹框巷道
  * getModalRoadway(value) {
    markStatus('loading');
    const list = yield getRoadwaySelectList({ id: value });
    yield this.changeData({
      modalRoadwayList: list.map((e) => ({
        value: e,
      })),
    });
  },
  // 页面初始化
  * init() {
    const { currentWarehouseList } = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
    });
    const [data, warehouseList, enableExtends] = yield Promise.all([
      dictSelect({ catCode: ['ENABLED', 'LOCATION_TYPE', 'LOCATION_CATEGORY', 'AREA_TYPE', 'PLAN_CATEGORY', 'LOCATION_MANAGEMENT_IMPORT_TYPE'] }),
      getWarehouseApi({ enabled: 1 }),
      queryEnableExtend(),
      dictCatQuery({
        pageNum: 1,
        pageSize: 50,
      }),
    ]);
    if (data.code === '0') {
      yield this.changeData({
        enabledList: data.info.data.find((x) => x.catCode === 'ENABLED').dictListRsps,
        locationTypeList: data.info.data.find((x) => x.catCode === 'LOCATION_TYPE').dictListRsps,
        locationCategoryList: data.info.data.find((x) => x.catCode === 'LOCATION_CATEGORY').dictListRsps,
        planCategoryList: data.info.data.find((x) => x.catCode === 'PLAN_CATEGORY').dictListRsps,
        importTypeSelectList: data.info.data.find((x) => x.catCode === 'LOCATION_MANAGEMENT_IMPORT_TYPE').dictListRsps,
        warehouseList: warehouseList.info.data,
        enableExtends: enableExtends.info.data,
      });
    } else {
      handleListMsg([data, warehouseList, enableExtends]);
    }
    // 获取存储属性
    const storeAttrList = yield getEnableStockAttrList();
    yield this.changeData({
      storeAttrData: storeAttrList,
    });
    // 获取子仓列表
    const { warehouseIds } = yield '';
    const { subWarehouseList, warehouseId } = yield 'nav';
    // 获取园区列表
    const { parkList } = yield queryParkList(warehouseId);
    yield this.changeData({
      parkList,
      preSubWarehouseList: subWarehouseList,
    });
    if (warehouseIds !== warehouseId) {
      yield this.warehouseChange({ subWarehouseList, warehouseId });
    }
  },
  // 搜索
  * search() {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    markStatus('loading');
    const { limit, pageInfo } = yield '';
    const warehouseIds = warehouseId ? [warehouseId] : [];
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      location: limit.location && limit.location.split(',').length > 0 ? limit.location.split(',') : [],
    };
    delZero(param);
    const data = yield queryGoods({ ...param, warehouseIds });
    if (data.code === '0') {
      yield this.changeData({
        selectedRows: [],
        list: data.info.data,
        exportVisible: false,
        pageInfo: {
          ...pageInfo,
          count: data.info.meta.count,
        },
        sum: data.info.meta.count,
      });
    } else {
      Modal.error({ title: data.msg });
    }
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = yield '';
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 分页条改变
  * handlePaginationChange(data = {}) {
    const { pageInfo, maxPageNumber } = yield '';
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    if (data.pageNum > 100) {
      Message.warn(t('最多查询到100页'));
      return;
    }
    let newMaxPageNumber = maxPageNumber;
    const { isPageClick, ...newPageInfo } = data;
    if (data.pageNum >= maxPageNumber) {
      newMaxPageNumber = data.pageNum;
    } else if (data.pageNum === 1 && !isPageClick) {
      newMaxPageNumber = 1;
    }
    // 校验通过后执行
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...newPageInfo,
      },
      maxPageNumber: newMaxPageNumber,
    });
    yield this.search();
  },
  // 注销
  * off(ids) {
    markStatus('loading');
    const res = yield offGoods({ ids });
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },

  * editOrAddShow(param, ctx) {
    const { modalType, id } = param;
    // 显示弹窗
    yield ctx.changeData({
      addModal: true,
    });
    yield this.changeData({
      modalType,
    });
    if (modalType) {
      // 回填数据
      const [data] = yield Promise.all([
        getGoodsById({ id }),
      ]);
      if (data.code === '0') {
        yield this.changeAddInfoData(data.info);
        // 请求子仓列表
        yield this.getSubWarehouse(data.info.warehouseId);
        // 请求库区
        yield this.getArea({ modalType: 1, id: data.info.subWarehouseId });
      } else {
        Modal.error({ title: data.msg });
      }
    }
  },
  // 点击保存 - 即新增
  * commitData(obj) {
    markStatus('loading');
    const { modalType } = yield '';
    let res = '';
    if (!modalType) {
      res = yield addGoods(obj);
    } else {
      res = yield editGoods(obj);
    }
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.closeModal();
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * closeModal() {
    const { modalFormRef } = yield '';
    yield this.changeData({
      addModal: false,
      addInfo: {
        location: '',
        pickerOrder: '',
        roadway: '',
        enabled: 1,
        areaId: '',
        subWarehouseId: '',
        warehouseId: '',
        locationType: '',
        maxItemNum: '', // 品项混存上限
        extendId: 0, // 货位规格id
        locationCategory: '',
        storeTypes: [], // 存储属性
        stopUp: 0,
        boxUp: 0,
        locationRow: '',
        locationColumn: '',
        isMultiSkc: 1,
      },
      modalSubWarehouseList: [],
      modalAreaList: [],
    });
    if (modalFormRef && modalFormRef.clearValidate) modalFormRef.clearValidate();
  },
  // 获取库区 list:0 modal:1
  * getArea(param) {
    const { id, modalType } = param;
    const list = yield getAreaList({
      subWarehouseId: id,
      enabled: 1,
    });
    if (!modalType) {
      yield this.changeData({
        areaList: list,
      });
    } else {
      yield this.changeData({
        modalAreaList: list,
      });
    }
  },
  * getPauseAreaList(value) {
    const list = yield getAreaList({
      subWarehouseId: value,
      enabled: 1,
    });
    yield this.changeData({
      pauseAreaList: list,
    });
  },
  * exportData(param) {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    const warehouseIds = warehouseId ? [warehouseId] : [];
    markStatus('loading');
    const params = {
      ...param,
      warehouseIds,
      location: param.location && param.location.split(',').length > 0 ? param.location.split(',') : [],
    };
    const data = yield exportFile(params);
    if (data.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: data.msg });
    }
  },
  * doSpecConfig() {
    // 查询货位规格下拉
    // 货位规格状态下拉
    yield this.changeData({
      specConfigModalVisiable: true,
    });
    markStatus('loading');
    const [specConfigList, specDic] = yield Promise.all([
      getSpecConfigList(),
      dictSelect({ catCode: ['ENABLED', 'ENABLED'] }),
    ]);
    if (specConfigList.code === '0' && specDic.code === '0') {
      // yield put(changeValue('dataLoading', false));
      yield this.changeData({
        specConfigList: specConfigList.info.data.map((si) => ({ ...si, title: si.name })),
        specDic: specDic.info.data.find((x) => x.catCode === 'ENABLED')
          .dictListRsps
          .map((v) => ({
            ...v,
            id: v.dictCode,
            title: v.dictNameZh,
            status: '1',
          })),
      });
    } else {
      let msg = '';
      msg = specConfigList.code !== '0' ? specConfigList.msg : msg;
      msg = specDic.code !== '0' ? specDic.msg : msg;
      Modal.error({ title: msg });
    }
  },
  /* eslint-disable */
  // 库位规格配置项
  * saveSpecConfig() {
    markStatus('loading');
    const { specConfigList, limit } = yield '';
    let reg = validatebMax();
    for (const di of specConfigList) {
      if (di.name === '') {
        Modal.error({ title: t('库位规格名称不能为空') })
        return false;
      }
      if (!(reg.test(di.maxAvailableRate))) {
        Modal.error({ title: t('最大可用率必须为整数类型') })
        return false;
      }
      if (!reg.test(di.length) || di.length > maxValue) {
        Modal.error({ title: `${t('库位长为')}: ${t('最大值是{},且小数点最多{}位的数字!', maxValue, 2)}` })
        return false;
      }
      if (!reg.test(di.width) || di.width > maxValue) {
        Modal.error({ title: `${t('库位宽为')}: ${t('最大值是{},且小数点最多{}位的数字!', maxValue, 2)}` })

        return false;
      }
      if (!reg.test(di.height) || di.height > maxValue) {
        Modal.error({ title: `${t('库位高为')}: ${t('最大值是{},且小数点最多{}位的数字!', maxValue, 2)}` })
        return false;
      }
    }
    /** eslint-enable */
    const dataListData = specConfigList.map(di => ({
      id: di.isNew ? '' : di.id,
      height: di.height,
      length: di.length,
      maxAvailableRate: di.maxAvailableRate,
      name: di.name,
      status: di.status,
      volume: di.volume,
      width: di.width,
    }));
    const res = yield saveSpecConfigServer({ dataList: dataListData });
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.changeData({
        specConfigModalVisiable: false,
      })
      if (limit.locationType.length === 0) {
        yield this.changeData({
          locationType: [1],
        })
      }
      yield this.handlePaginationChange({ pageNum: 1 })
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * modifySpecConfigList(data) {
    const { specConfigList } = yield ''
    yield this.changeData({
      specConfigList: [data, ...specConfigList]
    })
  },
  * nextClass(param) {
    const { i, num, isChangeRow } = param
    if (isChangeRow) {
      IS_CHANGE_ROW = true
      return false;
    }
    CLASS_NUM = num
    CLASS_NUM_ENTER = true
    classFocus(`${i}_${num}`)
  },
  * changeArrayList(param) {
    const { specConfigList } = yield ''
    const { index, data, nextClassNum } = param;
    if (nextClassNum) {
      if (CLASS_NUM_ENTER) {
        CLASS_NUM_ENTER = false
        classFocus(`${index}_${CLASS_NUM}`)
      } else {
        classFocus(`${index}_${nextClassNum}`)
      }
    }
    // 跳转换行
    if (IS_CHANGE_ROW) {
      IS_CHANGE_ROW = false;
      let editRowlength = 0
      specConfigList.filter(v => {
        if (v.editting) {
          editRowlength++
        }
      })
      let newRow = index + 1;
      if (newRow < editRowlength) {
        classFocus(`${newRow}_1`)
      }
    }
    yield this.changeData({
      specConfigList: specConfigList.map((si, idx) => {
        if (idx === index) {
          si = data;
          si.volume = si.length * si.width * si.height;
        }
        return si;
      })
    })

  },
  * printSaga() {
    const { selectedRows, paperSize } = yield '';
    const res = yield printLocation({ locationIds: selectedRows.map(i => i.id) });
    if (res.code !== '0') {
      Modal.error({ title: res.msg });
    } else {
      const { data } = res.info;
      let printUrl = ''

      const list = (data || []).map(({ location, locationText }) =>
      ({
        // 该字段用来展示在打印页的文本
        locationText,
        barcode: location ? textToBarcode(location, { width: 3, height: 35, marginTop: '8px' }) : null,
      }));
      // 【全球交付】【谢聪】【WMS】库位标签增加打印规格
      yield this.changeData({
        togglePaperVisible: false,
      })

      switch (paperSize) {
        case 1:
          // 100* 45
          printUrl = yield transformToPdfUrl(tpl100({ list }), 45, 100, { landscape: true }, true)
          break;
        case 2:
          // 80 * 50
          printUrl = yield transformToPdfUrl(tpl({ list }), 50, 80, { landscape: true }, true)
          break;
        case 3:
          // 80 * 40
          printUrl = yield transformToPdfUrl(tpl40({ list }), 40, 80, { landscape: true }, true)
          break;
        default:
          break;
      }

      if (printUrl) {
        // 如果PDF历史记录模式开启，则记录当前url
        if (isPDFHistoryMode()) {
          setPDFHistory(printUrl)
        }
      }
    }
  },
  /**
   * 获取散货最大序列号和登记操作记录
   * @param action
   * @returns {IterableIterator<CallEffect | *|*>}
   */
  * printSequenceSaga(action) {
    const { selectedRows } = yield '';
    // 对数据进行分组请求，默认是200，一次最多200条
    const arrs = getGroupArrs(selectedRows.map(i => i.id));
    // 添加loading
    markStatus('loading');
    // 根据数据分成多次请求
    const resList = yield Promise.all(arrs.map(i => printLocationSequence({ locationIds: i })));
    // 去掉loading
    if (resList.every(i => i.code === '0')) {
      // 请求成功后对数据进行组装
      const sequenceList = resList.reduce((results, cur) => {
        // 将数据合并到一个数组中
        results = [...results, ...(cur.info.data || [])];
        return results;
      }, []);
      // 对数据进行打印
      yield this.doSequencePrint({ sequenceList })
      // 关闭弹窗
      yield this.changeData({
        sequenceModalVisible: false,
      })
    } else {
      Modal.error({ title: resList.find(({ code }) => code !== '0').msg });
    }
  },
  /**
   * 打印散货序列号
   * @description 最大一万页打印一次
   * @param action
   */
  * doSequencePrint(action) {
    const { locationSequenceNum } = yield '';
    // try {
    const { sequenceList } = action;
    const list = [];
    for (let i = 0; i < sequenceList.length; i++) {
      for (let n = 0; n < locationSequenceNum; n++) {
        list.push({ sequence: sequenceList[i].locationText + '-' + (sequenceList[i].sequence + n + 1) });
      }
    }
    // 打印散货序列号 功能时，将数组传给pdf服务，让服务那边做循环生成dom
    const handleData = data => data;
    // 拆分打印页数。一次最大打印一万条数据
    const printList = groupingArray(list, 10000)
    const printUrls = [] // 记录打印url
    for (let i = 0; i < printList.length; i++) {
      let printUrl = ''
      printUrl = yield transformToPdfUrl(sequenceTpl({ list: printList[i] }), 13, 70, { landscape: true }, false)
      printUrls.push(printUrl)
      if (printUrl) {
        // 如果PDF历史记录模式开启，则记录当前url
        if (isPDFHistoryMode()) {
          setPDFHistory(printUrl)
        }
      }
    }
    // 大于等于2个就弹窗
    if (printUrls.length > 1) {
      Modal.info({
        width: 600,
        title: t('标签被拆分成{}页，请点击链接打印标签', printUrls.length),
        content: <div>{printUrls.map((url, i) => (<div><span>{t('第{}页', i + 1)}. </span><a href={url} target="_blank">{url}</a></div>))}</div>,
        maskCloseAble: false,
        hideClose: true,
        text: { 'ok': t('关闭') }
      })
    } else {
      window.open(printUrls[0], '_blank');
    }

    // 原先打印方式：浏览器打开
    // yield printToDataLandscape(sequenceTpl({ list }), {
    //   width: '13mm',
    //   height: '70mm'
    // });
    // } catch (e) {
    //   throw e;
    // } finally {
    //   yield put(changeValue('dataLoading', false));
    // }
  },
  // 库位规格-弹窗-编辑
  * editSpecConfigRow(id) {
    const { specConfigList } = yield ''
    yield this.changeData({
      specConfigList: specConfigList.map((si) => {
        if (si.id === id) {
          si.editting = true;
        }
        return si;
      }),
    })
  },

  // 批量新增/修改库位
  * batchOperationGoodsLocation(action) {
    markStatus('loading');
    const { type, params } = action;

    const {
      roadwayBeginSerial,
      roadwayEndSerial,
      shelfBeginSerial,
      shelfEndSerial,
      rowBeginSerial,
      rowEndSerial,
      columnBeginSerial,
      columnEndSerial,
    } = params

    const typeText = type === 'add' ? t('新增') : t('修改')

    /**
     * 1.计算巷道、货架、行、列差值
     * （1）巷道差值 = 截止巷道 - 起始巷道 + 1
     * （2）货架差值 = 截止货架 - 起始货架 + 1
     * （3）行差值 = 截止行 - 起始行 + 1
     * （4）列差值 = 截止列 - 起始列 + 1
     * 2.计算修改数据量
     * 修改数据量 = 巷道差值*货架差值*行差值*列差值
     */
    const roadwayDiff = roadwayEndSerial - roadwayBeginSerial + 1
    const shelfDiff = shelfEndSerial - shelfBeginSerial + 1
    const rowDiff = rowEndSerial - rowBeginSerial + 1
    const columnDiff = columnEndSerial - columnBeginSerial + 1
    const modifyCount = roadwayDiff * shelfDiff * rowDiff * columnDiff

    // 修改数据量是否小于等于3w
    // 否 弹出提示“修改数据量***，已大于3W条，无法批量修改”
    // 是 前端弹窗提示：“修改数据量***，是否修改库位数据？”
    if (modifyCount > 30000) {
      Modal.error({ content: t('{}数据量{}，已大于{}条，无法批量{}', typeText, modifyCount, '3W', typeText) });
      return;
    } else {
      const confirm = yield new Promise((resolve, reject) => {
        Modal.confirm({
          title: t('{}数据量{}，是否{}库位数据？', typeText, modifyCount, typeText),
          okText: t('确定'),
          cancelText: t('取消'),
          onOk: () => {
            resolve(true)
          },
          onCancel: () => {
            resolve(false)
          },
          onClose: () => {
            resolve(false)
          },
        });
      })

      if (!confirm) {
        return;
      }
    }

    const { code, msg } = yield batchOperationGoodsLocationAPI({
      ...params,
      operationType: type === 'add' ? 1 : 2, // 操作类型 1-新增 2-修改
    });

    if (code === '0') {
      Message.success(t('{}成功', typeText));

      setTimeout(() => {
        window.open(STATISTICAL_DOWNLOAD);
      }, 1000);

      yield this.changeData({
        batchModalVisible: false,
      })
      yield this.handlePaginationChange({ pageNum: 1 })
    } else {
      Modal.error({ content: msg });
    }
  },
  // 暂停上架维护
  * batchUpdateStopUp(params) {
    markStatus('loading');
    const { code, msg } = yield batchUpdateStopUpAPI({
      ...params,
    });
    if (code === '0') {
      Message.success(t('暂停上架维护成功'));
      yield this.changeData({
        pauseModalVisible: false,
      })
      yield this.handlePaginationChange({ pageNum: 1 })
    } else {
      Modal.error({ content: msg });
    }
  },

  * batchUpdatePlanCatogory(params) {
    markStatus('loading');
    const { code, msg } = yield batchUpdatePlanCatogoryAPI({
      ...params,
    });
    if (code === '0') {
      Message.success(t('提交成功，正在处理中，请稍后查看结果'));
      yield this.changeData({
        planCategoryVisible: false,
      })
      yield this.handlePaginationChange({ pageNum: 1 })
    } else {
      Modal.error({ content: msg });
    }
  },
  /**
   * 下载新增&修改库位模版
   */
  * downloadTemplate() {
    yield this.changeData({
      dataLoading: true,
    });
    const res = yield downloadTemplate();
    yield this.changeData({
      dataLoading: false,
    });
    if (res.ok) {
      res.blob().then((b) => {
        const blob = new Blob([b], { type: 'application/octet-stream' });
        fileSaver.saveAs(blob, t('新增&修改库位导入模板.xls'));
      });
    } else {
      res.json().then((err) => {
        Modal.error({ title: err.msg });
      });
    }
  },
  * uploadFile(formData) {
    const { warehouseId } = yield 'nav';
    if (!warehouseId) {
      Modal.error({ title: t('请选择右上角仓库') });
      return;
    }
    yield this.changeData({
      dataLoading: true,
    });
    formData.append('function_node', '18');
    formData.append('request_json', JSON.stringify({ warehouse_id: warehouseId }));
    const res = yield formdataPost(goodsLocationImportURL, formData);
    yield this.changeData({
      dataLoading: false,
    });
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      })
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 批量维护规划品类
  * uploadFile2(formData) {
    yield this.changeData({
      dataLoading: true,
    });
    formData.append('function_node', '162');
    const res = yield formdataPost(planCategoryImportURL, formData);
    yield this.changeData({
      dataLoading: false,
    });
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
   * 下载批量维护规划品类
   */
  * downloadTemplate2() {
    yield this.changeData({
      dataLoading: true,
    });
    const res = yield downloadTemplate2();
    yield this.changeData({
      dataLoading: false,
    });
    if (res.ok) {
      res.blob().then((b) => {
        const blob = new Blob([b], { type: 'application/octet-stream' });
        fileSaver.saveAs(blob, t('批量维护规划品类导入模板.xls'));
      });
    } else {
      res.json().then((err) => {
        Modal.error({ title: err.msg });
      });
    }
  },
};
