<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family:serif;
        }
        @page {
            margin: 0.5mm 0mm;
        }
        .box{
            width: 100%;
            height: 100%;
            page-break-before: always;
            page-break-after: always;
            color: #000;
            font-size: 16px;
        }
        .boxWrap{
            width: 100mm;
            height: 44mm;
            overflow: hidden;
            text-align: center;
            /*display: flex;*/
            /*flex-direction: column;*/
            /*align-items: center;*/
            /*justify-content: stretch;*/
        }
        .codeWrap{
            width: 80mm;
            height: 24mm;
            margin-left: 10mm;
        }
        .codeWrap>img{
            width: 100%;
            height: 100%;
        }
        .code{
            width: 100%;
            height: 20mm;
            line-height: 20mm;
            font-size: 38px;
        }
        .codeBig{
            width: 100%;
            height: 20mm;
            line-height: 20mm;
            font-size: 44px;
        }
        .barCode{
            font-family: "Bar-Code 39";
            font-size: 1.3em;
        }
    </style>
</head>
<body>
<div class="box">
    <% for (var i = 0; i < list.length; i ++) { %>
        <% var item = list[i] %>
        <div class="boxWrap">
            <div class="codeWrap barCode">
                <img src=<%= item.barcode %>>
            </div>
            <div class=<%= item.locationText.length > 12 ? 'code' : 'codeBig' %>><%= item.locationText %></div>
        </div>
    <% } %>
</div>
</body>
</html>
