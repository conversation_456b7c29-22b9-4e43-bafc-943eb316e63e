import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';
import { camel2Under } from '@src/lib/camal-case-convertor';

// 下载货位模板
export const downloadTemplate = () => {
  const uri = `${process.env.BASE_URI_WMD}/goods_location/download_template`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under()),
  });
};

export const addGoods = (param) => sendPostRequest({
  url: '/goods_location/add',
  param,
}, process.env.BASE_URI_WMD);

export const editGoods = (param) => sendPostRequest({
  url: '/goods_location/modify',
  param,
}, process.env.BASE_URI_WMD);

export const exportFile = (param) => sendPostRequest({
  url: '/goods_location/export',
  param,
}, process.env.BASE_URI_WMD);

export const getGoodsById = (param) => sendPostRequest({
  url: '/goods_location/query_by_id',
  param,
}, process.env.BASE_URI_WMD);

export const getSpecConfigList = (param) => sendPostRequest({
  url: '/goods_location/query_all_extend',
  param,
}, process.env.BASE_URI_WMD);

// export const getSpecDic = (param) => sendPostRequest({
//   url: '/dict/select',
//   param,
// }, process.env.BASE_URI_WMD);

export const offGoods = (param) => sendPostRequest({
  url: '/goods_location/retreat',
  param,
}, process.env.BASE_URI_WMD);

export const printLocation = (param) => sendPostRequest({
  url: '/goods_location/print_location',
  param,
}, process.env.BASE_URI_WMD);

export const printLocationSequence = (param) => sendPostRequest({
  url: '/goods_location/print_location_sequence',
  param,
}, process.env.BASE_URI_WMD);

export const queryEnableExtend = (param) => sendPostRequest({
  url: '/goods_location/query_enable_extend',
  param,
}, process.env.BASE_URI_WMD);

export const queryGoods = (param) => sendPostRequest({
  url: '/goods_location/query',
  param,
}, process.env.BASE_URI_WMD);

export const saveSpecConfigServer = (param) => sendPostRequest({
  url: '/goods_location/save_extend',
  param,
}, process.env.BASE_URI_WMD);

// 导入接口
export const goodsLocationImportURL = `${process.env.WGS_FRONT}/file_import/record/wmd/goods_location_import`;

// 批量操作库位
export const batchOperationGoodsLocationAPI = (param) => sendPostRequest({
  url: '/goods_location/batch_operation_goods_location',
  param,
}, process.env.BASE_URI_WMD);

export const batchUpdateStopUpAPI = (param) => sendPostRequest({
  url: '/goods_location/batch_update_stop_up',
  param,
}, process.env.BASE_URI_WMD);

// 批量更新规划品类
export const batchUpdatePlanCatogoryAPI = (param) => sendPostRequest({
  url: '/goods_location/batch_update_plan_category',
  param,
}, process.env.BASE_URI_WMD);

// 批量维护规划品类导入接口
export const planCategoryImportURL = `${process.env.WGS_FRONT}/file_import/record/wmd/plan_category_import`;

export const downloadTemplate2 = () => {
  const uri = `${process.env.BASE_URI_WMD}/goods_location/plan_category_template_download`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under()),
  });
};
