// file: PlanCategoryModal.jsx
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Button, Form, Modal, Select, Rule, Input,
} from 'shineout';
import store from '../reducers';
import styles from '../styles.less';
// 用于modal表单校验
const rules = Rule({
  roadwaySerialRange: { // 巷道
    func: (val, formData, callback) => {
      if (formData.roadwayBeginSerial && formData.roadwayEndSerial) {
        if (formData.roadwayBeginSerial > formData.roadwayEndSerial) {
          callback(new Error(t('后输入框必须大于等于前输入框')));
        }
      }
      callback(true);
    },
  },
  shelfSerialRange: { // 货架
    func: (val, formData, callback) => {
      if (formData.shelfBeginSerial && formData.shelfEndSerial) {
        if (formData.shelfBeginSerial > formData.shelfEndSerial) {
          callback(new Error(t('后输入框必须大于等于前输入框')));
        }
      }
      callback(true);
    },
  },
  rowSerialRange: { // 行
    func: (val, formData, callback) => {
      if (formData.rowBeginSerial && formData.rowEndSerial) {
        if (formData.rowBeginSerial > formData.rowEndSerial) {
          callback(new Error(t('后输入框必须大于等于前输入框')));
        }
      }
      callback(true);
    },
  },
  columnSerialRange: { // 行
    func: (val, formData, callback) => {
      if (formData.columnBeginSerial && formData.columnEndSerial) {
        if (formData.columnBeginSerial > formData.columnEndSerial) {
          callback(new Error(t('后输入框必须大于等于前输入框')));
        }
      }
      callback(true);
    },
  },
});

function PlanCategoryModal(props) {
  const {
    loading,
    visible,
    modalInfo,
    onClose,
  } = props;

  const {
    modalSubWarehouseList = [],
    pauseAreaList = [],
    currentWarehouseList = [],
    planCategoryList = [],
  } = modalInfo;

  const defaultModalValue = {
    warehouseId: 1, // Number 所属仓库ID
    subWarehouseId: null, // Number 所属子仓ID
    areaIds: [], // 所属库区ID
    roadwayBeginSerial: null, // Number 巷道开始序号
    roadwayEndSerial: null, // Number 巷道结束序号
    shelfBeginSerial: null, // Number 货架开始序号
    shelfEndSerial: null, // Number 货架结束序号
    rowBeginSerial: null, // Number 行开始序号
    rowEndSerial: null, // Number 行结束序号
    columnBeginSerial: null, // Number 列开始序号
    columnEndSerial: null, // Number 列结束序号
  };

  const [modalValue, setModalValue] = useState(defaultModalValue);
  const [formRef, setFormRef] = useState(null);

  // 重置modalValue
  useEffect(() => {
    if (!visible) return;
    setModalValue(defaultModalValue);
    if (defaultModalValue.warehouseId) {
      store.getSubWarehouse(defaultModalValue.warehouseId);
    }
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  }, [visible]);

  return (
    <Modal
      maskCloseAble={null}
      visible={visible}
      width={800}
      bodyStyle={{ maxHeight: 550, overflow: 'auto' }}
      title={t('维护规划品类')}
      onClose={() => {
        onClose();
      }}
      footer={(
        <div>
          <Button onClick={() => onClose()}>{t('取消')}</Button>
          <Modal.Submit disabled={!loading}>{t('保存')}</Modal.Submit>
        </div>
      )}
    >
      <Form
        labelWidth={110}
        labelAlign="right"
        style={{ maxWidth: 800 }}
        onSubmit={() => {
          store.batchUpdatePlanCatogory({
            ...modalValue,
          });
        }}
        onChange={(value) => {
          setModalValue(value);
        }}
        value={modalValue}
        inline
        formRef={(f) => setFormRef(f)}
      >
        <div className={styles.modalLabel}>
          {t('查询条件')}
        </div>
        <Form.Item label={t('仓库')} required>
          <Select
            name="warehouseId"
            data={currentWarehouseList}
            absolute
            clearable
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            style={{ width: 200 }}
            rules={[rules.required(t('仓库必填'))]}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              // 重置子仓、库区
              setModalValue({
                ...modalValue,
                warehouseId: value,
                subWarehouseId: '',
                areaIds: [],
              });

              store.changeData({
                modalSubWarehouseList: [],
                pauseAreaList: [],
              });
              if (!['', undefined, null].includes(value)) {
                store.getSubWarehouse(value);
              }
            }}
          />
        </Form.Item>
        <Form.Item label={t('子仓')} required>
          <Select
            name="subWarehouseId"
            data={modalSubWarehouseList}
            rules={[rules.required(t('子仓必填'))]}
            absolute
            keygen="id"
            format="id"
            clearable
            placeholder={t('请选择')}
            renderItem="nameZh"
            style={{ width: 200 }}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              // 重置库区
              setModalValue({
                ...modalValue,
                subWarehouseId: value,
                areaIds: [],
              });
              store.changeData({
                pauseAreaList: [],
              });
              if (value) {
                store.getPauseAreaList(value);
              }
            }}
          />
        </Form.Item>
        <Form.Item label={t('库区')}>
          <Select
            name="areaIds"
            data={pauseAreaList}
            absolute
            keygen="id"
            format="id"
            clearable
            placeholder={t('请选择')}
            renderItem="area"
            style={{ width: 200 }}
            onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              setModalValue({
                ...modalValue,
                areaIds: value,
              });
            }}
            multiple
            compressed
          />
        </Form.Item>
        <Form.Item label={t('巷道')}>
          <Input.Number
            name="roadwayBeginSerial"
            allowNull
            hideArrow
            digits={0}
            min={1}
            max={99}
            width={92}
            placeholder={t('请输入')}
            rules={[rules.roadwaySerialRange()]}
            bind="roadwayEndSerial"
          />
          &nbsp;~&nbsp;
          <Input.Number
            name="roadwayEndSerial"
            allowNull
            hideArrow
            digits={0}
            min={1}
            max={99}
            width={92}
            placeholder={t('请输入')}
            // rules={[rules.numberRange()]}
            bind="roadwayBeginSerial"
          />
        </Form.Item>
        <Form.Item label={t('货架')}>
          <Input.Number
            name="shelfBeginSerial"
            allowNull
            hideArrow
            digits={0}
            min={1}
            max={99}
            width={92}
            placeholder={t('请输入')}
            rules={[rules.shelfSerialRange()]}
            bind="shelfEndSerial"
          />
          &nbsp;~&nbsp;
          <Input.Number
            name="shelfEndSerial"
            allowNull
            hideArrow
            digits={0}
            min={1}
            max={99}
            width={92}
            placeholder={t('请输入')}
            bind="shelfBeginSerial"
          />
        </Form.Item>
        <Form.Item label={t('行')}>
          <Input.Number
            name="rowBeginSerial"
            allowNull
            hideArrow
            digits={0}
            min={1}
            max={9}
            width={92}
            placeholder={t('请输入')}
            rules={[rules.rowSerialRange()]}
            bind="rowEndSerial"
          />
          &nbsp;~&nbsp;
          <Input.Number
            name="rowEndSerial"
            allowNull
            hideArrow
            digits={0}
            min={1}
            max={9}
            width={92}
            placeholder={t('请输入')}
            bind="rowBeginSerial"
          />
        </Form.Item>
        <Form.Item label={t('列')}>
          <Input.Number
            name="columnBeginSerial"
            allowNull
            hideArrow
            digits={0}
            min={1}
            max={9}
            width={92}
            placeholder={t('请输入')}
            rules={[rules.columnSerialRange()]}
            bind="columnEndSerial"
          />
          &nbsp;~&nbsp;
          <Input.Number
            name="columnEndSerial"
            allowNull
            hideArrow
            digits={0}
            min={1}
            max={9}
            width={92}
            placeholder={t('请输入')}
            bind="columnBeginSerial"
          />
        </Form.Item>
        <div className={styles.modalLabel}>
          {t('修改内容')}
        </div>
        <Form.Item label={t('规划品类')} required>
          <Select
            label={t('规划品类')}
            absolute
            name="planCategory"
            style={{ width: 200 }}
            data={planCategoryList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            clearable
            rules={[rules.required(t('规划品类必填'))]}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
        <Form.Item label={t('剔除库容')} required>
          <Select
            label={t('剔除库容')}
            name="isEliminateCapacity"
            absolute
            data={[
              { dictCode: 1, dictNameZh: t('是') },
              { dictCode: 0, dictNameZh: t('否') },
            ]}
            style={{ width: 200 }}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            clearable
            rules={[rules.required(t('剔除库容必填'))]}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

PlanCategoryModal.defaultProps = {
  modalInfo: {},
};

PlanCategoryModal.propTypes = {
  loading: PropTypes.bool.isRequired,
  visible: PropTypes.bool.isRequired,
  modalInfo: PropTypes.shape(),
  onClose: PropTypes.func.isRequired,
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  planCategoryList: PropTypes.arrayOf(PropTypes.shape()),
};

export default PlanCategoryModal;
