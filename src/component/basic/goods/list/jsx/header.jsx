import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
// import { fliterSubwarehouse } from '@src/lib/dealFunc';
import { getSubwarehouseListByParkList } from '@src/server/common/cache-api';
import { Input, Rule, Select } from 'shineout';
import InputMore from '@shein-components/inputMore';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import { validatebInterval, validatebIntervalTwo } from '@src/lib/validate';
import store, { defaultLimit } from '../reducers';

const rule = Rule({
  locationTypeRule: {
    func: (val, formData, callback) => {
      if (!formData.locationType || !formData.locationType.length) {
        callback(new Error(t('请先选择库位类型')));
      }
      callback(true);
    },
  },
  locationGroupRule: {
    func: (val, formData, callback) => {
      if (formData.locationGroup && !validatebIntervalTwo().test(formData.locationGroup)) {
        callback(new Error(t('只允许输入数字，输入区间{}', '1-99')));
      }
      callback(true);
    },
  },
  locationLevelRule: {
    func: (val, formData, callback) => {
      if (formData.locationLevel && !validatebInterval().test(formData.locationLevel)) {
        callback(new Error(t('只允许输入数字，输入区间{}', '1-9')));
      }
      callback(true);
    },
  },
  physicalAreaValidate: {
    func: (val, formData, callback) => {
      if (val && (val < 10 || val > 99)) {
        callback(new Error(t('只允许输入2位正整数')));
      }
      callback(true);
    },
  },
});

const rowList = [{ value: '', label: t('全部') }];
for (let i = 0; i < 10; i++) {
  rowList.push({ value: i, label: i });
}
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      enabledList,
      pauseUpperList,
      locationTypeList,
      subWarehouseList,
      areaList,
      enableExtends,
      locationCategoryList,
      parkList,
      preSubWarehouseList,
      isSkcList,
      roadwayTypeList,
      planCategoryList,
    } = this.props;
    const isOneAreaId = limit.areaIds?.length === 1;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 100 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => {
            store.changeLimitData(formatSearchData(defaultLimit, val));
          }}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => {
            store.changeData({
              formRef: f,
            });
          }}
          alwaysVisible={[t('库位类型')]} // 需要校验的字段，需在这里配置且需要加required
        >
          {/* <Input label={t('库位')} name="location" placeholder={t('请输入')} /> */}
          <InputMore
            label={t('库位')}
            title={t('添加多个库位,以回车键隔开')}
            placeholder={t('请输入')}
            modalplaceholder={t('支持输入多个库位')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            name="location"
            max={100}
            maskCloseAble={false}
            overDisabled
            clearable
            required
          />
          {
           isOneAreaId ? (
             <Select
               required
               label={t('巷道')}
               name="roadwayList"
               data={roadwayTypeList}
               keygen="value"
               format="value"
               placeholder={t('请选择')}
               renderItem="value"
               onFilter={(text) => (d) => d.value.toLowerCase().indexOf(text.toLowerCase()) >= 0}
               multiple
               compressed
               clearable
             />
           ) : (
             <Input label={t('巷道')} name="roadway" placeholder={t('请输入')} />
           )
         }
          <Input
            label={t('货架组')}
            name="locationGroup"
            rules={[rule.locationGroupRule]}
            disabled={!(limit.parkTypeList.length === 1 && limit.areaIds?.length === 1 && limit.roadwayList.length > 0)}
            placeholder={t('请输入')}
            clearable
            onChange={(val) => {
              // 粘贴逻辑
              if (val.includes('-')) {
                const arr = val.split('-');

                store.changeLimitData({
                  locationGroup: arr[1].slice(2),
                });
              } else {
                store.changeLimitData({
                  locationGroup: val,
                });
              }
            }}
          />
          <Input
            label={t('货架层')}
            name="locationLevel"
            rules={[rule.locationLevelRule]}
            disabled={!(limit.parkTypeList.length === 1 && limit.areaIds?.length === 1 && limit.roadwayList.length > 0)}
            placeholder={t('请输入')}
            clearable
            onChange={(val) => {
              // 粘贴逻辑
              if (val.includes('-')) {
                const arr = val.split('-');

                store.changeLimitData({
                  locationLevel: arr[2]?.slice(0, 1),
                });
              } else {
                store.changeLimitData({
                  locationLevel: val,
                });
              }
            }}
          />
          <Select
            required
            label={t('库位类型')}
            name="locationType"
            data={locationTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            rules={[rule.locationTypeRule]}
          />
          <Select
            label={t('库位类别')}
            name="locationCategory"
            data={locationCategoryList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('库位规格')}
            name="extendId"
            data={[{
              id: 0,
              name: t('请选择'),
            }, ...enableExtends]}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="name"
            renderUnmatched={(r) => r.name || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('是否暂停上架')}
            name="stopUp"
            data={pauseUpperList}
            keygen="value"
            format="value"
            renderItem="label"
            placeholder={t('全部')}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('是否同属性混SKC')}
            name="isMultiSkc"
            data={[{ value: '', label: t('全部') }, ...isSkcList]}
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Input.Number
            label={t('物理库区')}
            name="physicalArea"
            digits={0}
            maxLength={2}
            numType="positive"
            placeholder={t('请输入')}
            clearable
            allowNull
            rules={[rule.physicalAreaValidate]}
          />
          <Select
            label={t('园区')}
            name="parkTypeList"
            required
            multiple
            compressed
            keygen="parkType"
            format="parkType"
            renderItem="parkName"
            data={parkList}
            clearable
            placeholder={t('全部')}
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              if (value && value.length > 0) {
                // const { parkSubWarehouseList } = fliterSubwarehouse(value);
                const { parkSubWarehouseList } = getSubwarehouseListByParkList({ parkTypeList: value, parkList });
                store.changeData({
                  subWarehouseList: parkSubWarehouseList,
                });
              } else {
                store.changeData({
                  subWarehouseList: preSubWarehouseList,
                });
              }
              store.changeLimitData({
                parkTypeList: value,
                subWarehouseIds: [],
                areaIds: [],
                locationColumn: '',
                locationRow: '',
                roadway: '',
                roadwayList: [],
              });
              store.changeData({
                areaList: [],
              });
            }}
          />
          <Select
            label={t('子仓')}
            required
            name="subWarehouseIds"
            data={subWarehouseList}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            onChange={(value) => {
              store.changeLimitData({
                subWarehouseIds: value,
                areaIds: [],
                locationColumn: '',
                locationRow: '',
                roadway: '',
                roadwayList: [],
              });
              if (value.length === 1) {
                // dispatch(getArea(value[0]));
                store.getArea({ id: value[0], modalType: 0 });
              } else {
                store.changeData({
                  areaList: [],
                });
              }
            }}
          />
          <Select
            label={t('库区')}
            required
            name="areaIds"
            data={areaList}
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="area"
            renderUnmatched={(r) => r.area || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple={limit.subWarehouseIds && limit.subWarehouseIds.length === 1}
            compressed={limit.subWarehouseIds && limit.subWarehouseIds.length === 1}
            clearable
            onChange={(value) => {
              if (value.length === 0) {
                store.changeLimitData({
                  locationColumn: '',
                  locationRow: '',
                });
              }
              if (value.length === 1) {
                store.getRoadwayTypeList(value[0]);
              }
              store.changeLimitData({
                roadway: '',
                roadwayList: [],
              });
            }}
          />
          <Select
            label={t('状态')}
            name="enabled"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...enabledList]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Input.Number
            label={t('打印次数>')}
            name="printNum" min={0} max={1000000} digits={0}
          />
          <Select
            label={t('是否整箱上架')}
            name="boxUp"
            data={[{ value: '', label: t('全部') }, ...pauseUpperList]}
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('行')}
            name="locationRow"
            data={rowList}
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            disabled={!limit.subWarehouseIds || !limit.areaIds || (limit.subWarehouseIds && limit.subWarehouseIds.length === 0) || (limit.areaIds && limit.areaIds.length === 0)}
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => (`${d.label}`).toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('列')}
            name="locationColumn"
            data={rowList}
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            disabled={!limit.subWarehouseIds || !limit.areaIds || (limit.subWarehouseIds && limit.subWarehouseIds.length === 0) || (limit.areaIds && limit.areaIds.length === 0)}
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => (`${d.label}`).toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('是否自动化库区')}
            name="automateArea"
            data={[
              { dictCode: '1', dictNameZh: t('是') },
              { dictCode: '0', dictNameZh: t('否') },
            ]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            clearable
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('规划品类')}
            name="planCategory"
            data={planCategoryList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            clearable
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('剔除库容')}
            name="isEliminateCapacity"
            data={[
              { dictCode: 1, dictNameZh: t('是') },
              { dictCode: 0, dictNameZh: t('否') },
            ]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            clearable
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <DateRangePicker
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['modifyDateBegin', 'modifyDateEnd']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('编辑时间')}
            span={2}
            clearable
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  pauseUpperList: PropTypes.arrayOf(PropTypes.shape()),
  locationTypeList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  areaList: PropTypes.arrayOf(PropTypes.shape()),
  enableExtends: PropTypes.arrayOf(PropTypes.shape()),
  locationCategoryList: PropTypes.arrayOf(PropTypes.shape()),
  parkList: PropTypes.arrayOf(),
  preSubWarehouseList: PropTypes.arrayOf(),
  isSkcList: PropTypes.arrayOf(PropTypes.shape()),
  roadwayTypeList: PropTypes.arrayOf(PropTypes.shape()),
  planCategoryList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
