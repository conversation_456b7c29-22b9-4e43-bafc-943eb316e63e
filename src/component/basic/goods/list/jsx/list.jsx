import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Select } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import Icon from '@shein-components/Icon';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
      selectedRows,
      maxPageNumber,
    } = this.props;
    const columns = [
      {
        title: t('库位ID'),
        width: 120,
        render: (record) => (
          <Button
            style={{ padding: 0, userSelect: 'text' }}
            type="link"
            onClick={() => {
              store.editOrAddShow({ modalType: 1, id: record.id });
            }}
          >
            {record.id}
          </Button>
        ),
      },
      {
        title: t('库位'),
        render: 'location',
        width: 150,
      },
      {
        title: t('物理库区'),
        render: 'physicalArea',
        width: 150,
      },
      {
        title: t('库区'),
        render: 'area',
        width: 80,
      },
      {
        title: t('巷道'),
        render: 'roadway',
        width: 80,
      },
      {
        title: t('行'),
        render: 'locationRow',
        width: 80,
      },
      {
        title: t('列'),
        render: 'locationColumn',
        width: 80,
      },
      {
        title: t('拣货顺序编号'),
        render: 'pickOrder',
        width: 135,
      },
      {
        title: t('园区'),
        render: 'parkTypeName',
        width: 100,
      },
      {
        title: t('仓库'),
        render: 'warehouse',
        width: 100,
      },
      {
        title: t('子仓'),
        render: 'subWarehouse',
        width: 130,
      },
      {
        title: t('库位类型'),
        render: 'locationTypeName',
        width: 100,
      },
      {
        title: t('库位类别'),
        render: 'locationCategoryName',
        width: 100,
      },
      {
        title: t('品项混存上限'),
        render: 'maxItemNum',
        width: 135,
      },
      {
        title: t('库位规格'),
        render: 'extendName',
        width: 100,
      },
      {
        title: t('打印次数'),
        render: 'printNum',
        width: 100,
      },
      {
        title: t('状态'),
        render: 'enabledName',
        width: 100,
      },
      {
        title: t('存储属性'),
        render: (record) => (
          (record.storeTypesName || []).join(',')
        ),
        width: 200,
      },
      {
        title: t('是否暂停上架'),
        render: 'stopUpName',
        width: 135,
      },
      {
        title: t('是否整箱上架'),
        render: 'boxName',
        width: 135,
      },
      {
        title: t('是否同属性混SKC'),
        render: 'isMultiSkcName',
        width: 150,
      },
      {
        title: t('规划品类'),
        render: 'planCategoryName',
        width: 150,
      },
      {
        title: t('剔除库容'),
        render: 'isEliminateCapacityName',
        width: 150,
      },
      {
        title: t('是否自动化库区'),
        render: 'automateAreaName',
        width: 150,
      },
      {
        title: t('操作'),
        width: 100,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              size="small"
              text
              type="primary"
              style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];
    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
          />
          <div
            className={styles.pagination}
            style={{ textAlign: 'right' }}
          >
            <Button
              type="primary"
              disabled={pageInfo.pageNum === 1}
              onClick={() => {
                store.handlePaginationChange({
                  pageNum: pageInfo.pageNum - 1,
                  pageSize: pageInfo.pageSize,
                  isPageClick: true,
                });
              }}
            >
              <Icon name="pc-arrow-left" />
            </Button>
            {
              (pageInfo.pageNum > 10 || maxPageNumber > 10) ? (
                <>
                  {Array.from({ length: 10 }).map((pi, pIdx) => (
                    <Button
                      type={pageInfo.pageNum === (pIdx + 1) ? 'primary' : 'default'}
                      onClick={() => {
                        store.handlePaginationChange({
                          pageNum: pIdx + 1,
                          pageSize: pageInfo.pageSize,
                          isPageClick: true,
                        });
                      }}
                    >
                      {pIdx + 1}
                    </Button>
                  ))}
                  <Button
                    disabled
                    type="default"
                  >
                    <Icon name="more" />
                  </Button>
                </>
              ) : Array.from({ length: maxPageNumber }).map((pi, pIdx) => (
                <Button
                  type={pageInfo.pageNum === (pIdx + 1) ? 'primary' : 'default'}
                  onClick={() => {
                    store.handlePaginationChange({
                      pageNum: pIdx + 1,
                      pageSize: pageInfo.pageSize,
                      isPageClick: true,
                    });
                  }}
                >
                  {pIdx + 1}
                </Button>
              ))
            }
            <Button
              type="primary"
              disabled={!list.length || list.length < pageInfo.pageSize}
              onClick={() => {
                store.handlePaginationChange({
                  pageNum: pageInfo.pageNum + 1,
                  pageSize: pageInfo.pageSize,
                  isPageClick: true,
                });
              }}
            >
              {pageInfo.pageNum > 10 && pageInfo.pageNum}
              <Icon name="pc-arrow-right" />
            </Button>
            <Select
              keygen
              style={{ marginLeft: '10px', width: 80 }}
              data={pageInfo.pageSizeList}
              value={pageInfo.pageSize}
              onChange={(value) => {
                store.handlePaginationChange({
                  pageNum: 1,
                  pageSize: value,
                });
              }}
            />
          </div>
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'LOCATION_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  recordId: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  maxPageNumber: PropTypes.number,
};

export default List;
