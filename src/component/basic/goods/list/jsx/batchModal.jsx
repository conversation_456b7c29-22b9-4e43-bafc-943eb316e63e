// file: BatchModal.jsx
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Button, Form, Input, Modal, Select, Rule,
} from 'shineout';
import RuleInput from '@shein-components/WmsInput';
import { validatebBigString, validatebInteger, validatebIntegerOneToNine } from '@src/lib/validate';
import styles from '../styles.less';

// 用于modal表单校验
const rules = Rule({
  prefixRule: {
    func: (val, formData, callback) => {
      if (!val) {
        callback(new Error(t('必填')));
      }
      // 限制：只能输入2位大写字符
      if (!['', null].includes(formData.prefix) && !validatebBigString().test(formData.prefix)) {
        callback(new Error(t('请输入{}位大写字符', 2)));
      }
      callback(true);
    },
  },
  roadwayRule: {
    func: (val, formData, callback) => {
      // 必填
      if (!formData.roadwayBeginSerial || !formData.roadwayEndSerial) {
        callback(new Error(t('必填')));
      }
      // 限制：只能输入1-99的正整数
      if (!['', null].includes(formData.roadwayBeginSerial) && !validatebInteger().test(formData.roadwayBeginSerial)) {
        callback(new Error(t('请输入{}-{}的正整数', 1, 99)));
      }
      // 限制：只能输入1-99的正整数
      if (!['', null].includes(formData.roadwayEndSerial) && !validatebInteger().test(formData.roadwayEndSerial)) {
        callback(new Error(t('请输入{}-{}的正整数', 1, 99)));
      }
      // 后输入框正整数 >= 前输入框正整数
      if (formData.roadwayBeginSerial && formData.roadwayEndSerial) {
        if (Number(formData.roadwayBeginSerial) > Number(formData.roadwayEndSerial)) {
          callback(new Error(t('后输入框必须大于等于前输入框')));
        }
      }
      callback(true);
    },
  },
  shelfRule: {
    func: (val, formData, callback) => {
      // 必填
      if (!formData.shelfBeginSerial || !formData.shelfEndSerial) {
        callback(new Error(t('必填')));
      }
      // 限制：只能输入1-99的正整数
      if (!['', null].includes(formData.shelfBeginSerial) && !validatebInteger().test(formData.shelfBeginSerial)) {
        callback(new Error(t('请输入{}-{}的正整数', 1, 99)));
      }
      // 限制：只能输入1-99的正整数
      if (!['', null].includes(formData.shelfEndSerial) && !validatebInteger().test(formData.shelfEndSerial)) {
        callback(new Error(t('请输入{}-{}的正整数', 1, 99)));
      }
      // 后输入框正整数 >= 前输入框正整数
      if (formData.shelfBeginSerial && formData.shelfEndSerial) {
        if (Number(formData.shelfBeginSerial) > Number(formData.shelfEndSerial)) {
          callback(new Error(t('后输入框必须大于等于前输入框')));
        }
      }
      callback(true);
    },
  },
  rowRule: {
    func: (val, formData, callback) => {
      // 必填
      if (!formData.rowBeginSerial || !formData.rowEndSerial) {
        callback(new Error(t('必填')));
      }
      // 限制：只能输入1-9的正整数
      if (!['', null].includes(formData.rowBeginSerial) && !validatebIntegerOneToNine().test(formData.rowBeginSerial)) {
        callback(new Error(t('请输入{}-{}的正整数', 1, 9)));
      }
      // 限制：只能输入1-9的正整数
      if (!['', null].includes(formData.rowEndSerial) && !validatebIntegerOneToNine().test(formData.rowEndSerial)) {
        callback(new Error(t('请输入{}-{}的正整数', 1, 9)));
      }
      // 后输入框正整数 >= 前输入框正整数
      if (formData.rowBeginSerial && formData.rowEndSerial) {
        if (Number(formData.rowBeginSerial) > Number(formData.rowEndSerial)) {
          callback(new Error(t('后输入框必须大于等于前输入框')));
        }
      }
      callback(true);
    },
  },
  columnRule: {
    func: (val, formData, callback) => {
      // 必填
      if (!formData.columnBeginSerial || !formData.columnEndSerial) {
        callback(new Error(t('必填')));
      }
      // 限制：只能输入1-9的正整数
      if (!['', null].includes(formData.columnBeginSerial) && !validatebIntegerOneToNine().test(formData.columnBeginSerial)) {
        callback(new Error(t('请输入{}-{}的正整数', 1, 9)));
      }
      // 限制：只能输入1-9的正整数
      if (!['', null].includes(formData.columnEndSerial) && !validatebIntegerOneToNine().test(formData.columnEndSerial)) {
        callback(new Error(t('请输入{}-{}的正整数', 1, 9)));
      }
      // 后输入框正整数 >= 前输入框正整数
      if (formData.columnBeginSerial && formData.columnEndSerial) {
        if (Number(formData.columnBeginSerial) > Number(formData.columnEndSerial)) {
          callback(new Error(t('后输入框必须大于等于前输入框')));
        }
      }
      callback(true);
    },
  },
  physicalAreaValidate: {
    func: (val, formData, callback) => {
      if (val < 10 || val > 99) {
        callback(new Error(t('只允许输入2位正整数')));
      }
      callback(true);
    },
  },
});

function BatchModal(props) {
  const {
    loading,
    type,
    visible,
    modalInfo,
    onClose,
    onSubmit,
    onChange,
  } = props;

  const {
    // warehouseList = [],
    modalSubWarehouseList = [],
    modalAreaList = [],
    locationTypeList = [],
    locationCategoryList = [],
    pauseUpperList = [],
    enabledList = [],
    enableExtends = [],
    isSkcList = [],
    storeAttrData = [],
    currentWarehouseList = [],
  } = modalInfo;

  const defaultModalValue = {
    warehouseId: '', // Number 所属仓库ID
    prefix: '', // 前缀
    subWarehouseId: '', // Number 所属子仓ID
    subWarehouseCode: '', // 子仓编码
    areaId: '', // Number 所属库区ID
    areaCode: '', // 库区编码
    roadwayBeginSerial: '', // Number 巷道前缀
    roadwayEndSerial: '', // Number 巷道后缀
    shelfBeginSerial: '', // Number 货架前缀
    shelfEndSerial: '', // Number 货架后缀
    rowBeginSerial: '', // Number 行前缀
    rowEndSerial: '', // Number 行后缀
    columnBeginSerial: '', // Number 列前缀
    columnEndSerial: '', // Number 列后缀
    locationType: '', // Number 货位类型
    locationCategory: '', // Number 货位类别
    boxUp: 0, // Number 是否整箱上架 0-no(默认) 1-yes
    maxItemNum: '', // Number 品项混存上限
    enabled: 1, // Number 状态 启用(默认)/禁用
    extendId: 0, // Number 货位规格id
    storeTypes: [], // 存储属性集合
    stopUp: 0, // Number 是否暂停上架 0-no(默认) 1-yes
    isMultiSkc: 1, // Bool 是否同属性混SKC 0否 1是(默认)
  };

  const [modalValue, setModalValue] = useState(defaultModalValue);
  const [formRef, setFormRef] = useState(null);

  // 重置modalValue
  useEffect(() => {
    if (!visible) return;
    setModalValue(defaultModalValue);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  }, [visible]);

  const typeText = type === 'add' ? t('新增') : t('修改');

  return (
    <Modal
      maskCloseAble={null}
      visible={visible}
      width={800}
      bodyStyle={{ maxHeight: 550, overflow: 'auto' }}
      title={t('批量{}', typeText)}
      onClose={() => {
        onClose();
      }}
      footer={(
        <div>
          <Button onClick={() => onClose()}>{t('取消')}</Button>
          <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
        </div>
      )}
    >
      <Form
        labelWidth={110}
        labelAlign="right"
        style={{ maxWidth: 800 }}
        onSubmit={() => {
          onSubmit({
            ...modalValue,
            roadwayBeginSerial: Number(modalValue.roadwayBeginSerial),
            roadwayEndSerial: Number(modalValue.roadwayEndSerial),
            shelfBeginSerial: Number(modalValue.shelfBeginSerial),
            shelfEndSerial: Number(modalValue.shelfEndSerial),
            rowBeginSerial: Number(modalValue.rowBeginSerial),
            rowEndSerial: Number(modalValue.rowEndSerial),
            columnBeginSerial: Number(modalValue.columnBeginSerial),
            columnEndSerial: Number(modalValue.columnEndSerial),
          });
        }}
        onChange={(value) => {
          setModalValue(value);
        }}
        value={modalValue}
        inline
        formRef={(f) => setFormRef(f)}
      >
        {
          type === 'edit' && (
            <div className={styles.modalLabel}>
              {t('查询条件')}
            </div>
          )
        }
        <Form.Item label={t('仓库')} required>
          <Select
            name="warehouseId"
            data={currentWarehouseList}
            absolute
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            style={{ width: 180 }}
            rules={[rules.required(t('必填'))]}
            renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              // 重置子仓、库区、存储属性
              setModalValue({
                ...modalValue,
                warehouseId: value,
                subWarehouseId: '',
                areaId: '',
                storeTypes: [],
              });

              // 改变仓库时，获取子仓列表，重置库区列表
              onChange({
                type: 'warehouse',
                value,
              });
            }}
          />
        </Form.Item>
        <Form.Item label={t('前缀')} required>
          <Input
            name="prefix"
            rules={[rules.prefixRule()]}
            style={{ width: 180 }}
            clearable
          />
        </Form.Item>
        <Form.Item label={t('子仓')} required>
          <Select
            name="subWarehouseId"
            data={modalSubWarehouseList}
            rules={[rules.required(t('必填'))]}
            absolute
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              // 重置库区
              setModalValue({
                ...modalValue,
                subWarehouseId: value,
                areaId: '',
              });

              // 改变仓库时，获取子仓列表，重置库区列表
              onChange({
                type: 'subWarehouse',
                value,
              });
            }}
          />
        </Form.Item>
        <Form.Item label={t('子仓编码')} required>
          <RuleInput.Number
            name="subWarehouseCode"
            rules={[rules.required(t('请输入{}-{}的正整数', 0, 9))]}
            style={{ width: 180 }}
            width={180}
            min={0}
            max={9}
            digits={0}
          />
        </Form.Item>
        <Form.Item required label={t('物理库区')}>
          <Input.Number
            name="physicalArea"
            digits={0}
            maxLength={2}
            numType="positive"
            placeholder={t('请输入')}
            clearable
            allowNull
            width={180}
            rules={[rules.required(), rules.physicalAreaValidate()]}
          />
        </Form.Item>
        <Form.Item label={t('库区')} required>
          <Select
            name="areaId"
            data={modalAreaList}
            rules={[rules.required(t('必填'))]}
            absolute
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="area"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.area || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
        <Form.Item label={t('库区编码')} required>
          <RuleInput.Number
            name="areaCode"
            rules={[rules.required(t('请输入{}-{}的正整数', 1, 99))]}
            style={{ width: 180 }}
            width={180}
            min={1}
            max={99}
            digits={0}
          />
        </Form.Item>
        <Form.Item label={t('巷道')} required>
          <Input.Group
            style={{ width: 180 }}
          >
            <Input
              name="roadwayBeginSerial"
              clearable
              rules={[rules.roadwayRule()]}
              onChange={() => {
                formRef.validateFields(['roadwayEndSerial']);
              }}
            />
            -
            <Input
              name="roadwayEndSerial"
              clearable
              onChange={() => {
                formRef.validateFields(['roadwayBeginSerial']);
              }}
            />
          </Input.Group>
        </Form.Item>
        <Form.Item label={t('货架')} required>
          <Input.Group
            style={{ width: 180 }}
          >
            <Input
              name="shelfBeginSerial"
              clearable
              rules={[rules.shelfRule()]}
              onChange={() => {
                formRef.validateFields(['shelfEndSerial']);
              }}
            />
            -
            <Input
              name="shelfEndSerial"
              clearable
              onChange={() => {
                formRef.validateFields(['shelfBeginSerial']);
              }}
            />
          </Input.Group>
        </Form.Item>
        <Form.Item label={t('行')} required>
          <Input.Group
            style={{ width: 180 }}
          >
            <Input
              name="rowBeginSerial"
              clearable
              rules={[rules.rowRule()]}
              onChange={() => {
                formRef.validateFields(['rowEndSerial']);
              }}
            />
            -
            <Input
              name="rowEndSerial"
              clearable
              onChange={() => {
                formRef.validateFields(['rowBeginSerial']);
              }}
            />
          </Input.Group>
        </Form.Item>
        <Form.Item label={t('列')} required>
          <Input.Group
            style={{ width: 180 }}
          >
            <Input
              name="columnBeginSerial"
              clearable
              rules={[rules.columnRule()]}
              onChange={() => {
                formRef.validateFields(['columnEndSerial']);
              }}
            />
            -
            <Input
              name="columnEndSerial"
              clearable
              onChange={() => {
                formRef.validateFields(['columnBeginSerial']);
              }}
            />
          </Input.Group>
        </Form.Item>
        {
          type === 'edit' && (
            <div className={styles.modalLabel}>
              {t('修改内容')}
            </div>
          )
        }
        <Form.Item label={t('库位类型')} required>
          <Select
            name="locationType"
            data={locationTypeList}
            rules={[rules.required(t('必填'))]}
            absolute
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              // 非大货类型库位不允许维护整箱上架为"是"
              if (value !== 1) {
                setModalValue({
                  ...modalValue,
                  locationType: value,
                  boxUp: 0,
                });
              }
            }}
          />
        </Form.Item>
        <Form.Item label={t('库位类别')}>
          <Select
            name="locationCategory"
            data={locationCategoryList}
            absolute
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
        <Form.Item label={t('是否整箱上架')} required>
          <Select
            name="boxUp"
            data={pauseUpperList}
            disabled={![1, ''].includes(modalValue.locationType)}
            rules={[rules.required(t('必填'))]}
            absolute
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
        <Form.Item label={t('品项混存上限')} required>
          <Input.Number
            name="maxItemNum"
            rules={[rules.required(t('请输入不超过{}位数的正整数', 5))]}
            style={{ width: 180 }}
            min={1}
            max={99999}
            digits={0}
          />
        </Form.Item>
        <Form.Item label={t('状态')} required>
          <Select
            name="enabled"
            data={enabledList}
            rules={[rules.required(t('必填'))]}
            absolute
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
        <Form.Item label={t('库位规格')}>
          <Select
            name="extendId"
            data={[
              {
                id: 0,
                name: t('请选择'),
              },
              ...enableExtends,
            ]}
            absolute
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="name"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.name || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
        <Form.Item label={t('存储属性')}>
          <Select
            name="storeTypes"
            data={storeAttrData}
            absolute
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="name"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.name || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
          />
        </Form.Item>
        <Form.Item label={t('是否暂停上架')} required>
          <Select
            name="stopUp"
            data={pauseUpperList}
            rules={[rules.required(t('必填'))]}
            absolute
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
        <Form.Item label={t('是否同属性混SKC')} required>
          <Select
            name="isMultiSkc"
            data={isSkcList}
            rules={[rules.required(t('必填'))]}
            absolute
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            style={{ width: 180 }}
            renderUnmatched={(r) => r.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

BatchModal.defaultProps = {
  modalInfo: {},
};

BatchModal.propTypes = {
  loading: PropTypes.bool.isRequired,
  type: PropTypes.string.isRequired, // add, edit
  visible: PropTypes.bool.isRequired,
  modalInfo: PropTypes.shape(),
  onSubmit: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};

export default BatchModal;
