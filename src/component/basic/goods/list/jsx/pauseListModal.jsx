// file: BatchModal.jsx
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Button, Form, Modal, Select, Rule,
} from 'shineout';
// import { fliterSubwarehouse } from '@src/lib/dealFunc';
import { getSubwarehouseListByParkList } from '@src/server/common/cache-api';
import store from '../reducers';
import styles from '../styles.less';
// 用于modal表单校验
const rules = Rule();

function BatchModal(props) {
  const {
    loading,
    visible,
    modalInfo,
    onClose,
  } = props;

  const {
    modalSubWarehouseList = [],
    pauseAreaList = [],
    pauseUpperList = [],
    modalRoadwayList = [],
    modalParkList = [],
    regionList = [],
    currentWarehouseList = [],
  } = modalInfo;

  const defaultModalValue = {
    warehouseId: 1, // Number 所属仓库ID
    region: '', // 片区
    park: '', // 园区
    subWarehouseId: '', // Number 所属子仓ID
    areaId: '', // Number 所属库区ID
    roadwayList: [], // 巷道集合
    stopUp: '', // Number 是否暂停上架 0-no(默认) 1-yes
  };

  const [modalValue, setModalValue] = useState(defaultModalValue);
  const [formRef, setFormRef] = useState(null);

  // 重置modalValue
  useEffect(() => {
    if (!visible) return;
    setModalValue(defaultModalValue);
    if (defaultModalValue.warehouseId) {
      store.getRegionList(defaultModalValue.warehouseId);
    }
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  }, [visible]);

  return (
    <Modal
      maskCloseAble={null}
      visible={visible}
      width={800}
      bodyStyle={{ maxHeight: 550, overflow: 'auto' }}
      title={t('暂停上架维护')}
      onClose={() => {
        onClose();
      }}
      footer={(
        <div>
          <Button onClick={() => onClose()}>{t('取消')}</Button>
          <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
        </div>
      )}
    >
      <Form
        labelWidth={110}
        labelAlign="right"
        style={{ maxWidth: 800 }}
        onSubmit={() => {
          store.batchUpdateStopUp({
            ...modalValue,
          });
        }}
        onChange={(value) => {
          setModalValue(value);
        }}
        value={modalValue}
        inline
        formRef={(f) => setFormRef(f)}
      >
        <div className={styles.modalLabel}>
          {t('查询条件')}
        </div>
        <Form.Item label={t('仓库')} required>
          <Select
            name="warehouseId"
            data={currentWarehouseList}
            absolute
            clearable
            keygen="id"
            format="id"
            placeholder={t('请选择')}
            renderItem="nameZh"
            style={{ width: 180 }}
            rules={[rules.required(t('必填'))]}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              // 重置片区、园区、子仓、库区、存储属性
              setModalValue({
                ...modalValue,
                warehouseId: value,
                subWarehouseId: '',
                region: '',
                park: '',
                areaId: '',
                roadwayList: [],
              });

              // 改变仓库时，获取片区列表，重置园区、子仓、库区、巷道列表
              if (value) {
                store.getRegionList(value);
              }
              store.changeData({
                modalParkList: [],
                modalSubWarehouseList: [],
                pauseAreaList: [],
                modalRoadwayList: [],
              });
            }}
          />
        </Form.Item>
        <Form.Item required label={t('片区')}>
          <Select
            name="region"
            clearable
            data={regionList}
            absolute
            keygen="region"
            format="region"
            placeholder={t('请选择')}
            renderItem={(record) => <span>{record.regionName ? record.regionName : '-'}</span>}
            style={{ width: 180 }}
            rules={[rules.required(t('必填'))]}
            onFilter={(text) => (d) => d.regionName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(val) => {
              // 重置园区、子仓、库区、存储属性
              setModalValue({
                ...modalValue,
                region: val,
                park: '',
                subWarehouseId: '',
                areaId: '',
                roadwayList: [],
              });
              store.changeData({
                modalSubWarehouseList: [],
                pauseAreaList: [],
                modalRoadwayList: [],
              });
              if (val) {
                store.handleParkListQuery(val);
              }
            }}
          />
        </Form.Item>
        <Form.Item required label={t('园区')}>
          <Select
            label={t('园区')}
            name="park"
            clearable
            data={modalParkList}
            absolute
            keygen="parkType"
            format="parkType"
            renderItem="parkName"
            placeholder={t('请选择')}
            rules={[rules.required(t('必填'))]}
            onFilter={(text) => (d) => d?.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            style={{ width: 180 }}
            onChange={(val) => {
              // 重置子仓、库区、存储属性
              setModalValue({
                ...modalValue,
                park: val,
                subWarehouseId: '',
                areaId: '',
                roadwayList: [],
              });
              store.changeData({
                pauseAreaList: [],
                modalRoadwayList: [],
              });
              if (!['', undefined, null].includes(val)) {
                // const { parkSubWarehouseList } = fliterSubwarehouse([val]);
                const { parkSubWarehouseList } = getSubwarehouseListByParkList({ parkTypeList: [val], parkList: modalInfo.parkList });
                store.changeData({
                  modalSubWarehouseList: parkSubWarehouseList,
                });
              }
            }}
          />
        </Form.Item>
        <Form.Item label={t('子仓')} required>
          <Select
            name="subWarehouseId"
            data={modalSubWarehouseList}
            rules={[rules.required(t('必填'))]}
            absolute
            keygen="id"
            format="id"
            clearable
            placeholder={t('请选择')}
            renderItem="nameZh"
            style={{ width: 180 }}
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              // 重置库区
              setModalValue({
                ...modalValue,
                subWarehouseId: value,
                areaId: '',
                roadwayList: [],
              });
              if (value) {
                store.getPauseAreaList(value);
              }
            }}
          />
        </Form.Item>
        <Form.Item label={t('库区')} required>
          <Select
            name="areaId"
            data={pauseAreaList}
            absolute
            keygen="id"
            format="id"
            clearable
            rules={[rules.required(t('必填'))]}
            placeholder={t('请选择')}
            renderItem="area"
            style={{ width: 180 }}
            onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(value) => {
              setModalValue({
                ...modalValue,
                areaId: value,
                roadwayList: [],
              });
              if (value) {
                store.getModalRoadway(value);
              }
            }}
          />
        </Form.Item>
        <Form.Item label={t('巷道')}>
          <Select
            name="roadwayList"
            data={modalRoadwayList}
            clearable
            multiple
            compressed
            absolute
            style={{ width: 180 }}
            placeholder={t('全部')}
            keygen="value"
            format="value"
            renderItem="value"
            onFilter={(text) => (d) => d.value.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
        <div className={styles.modalLabel}>
          {t('修改内容')}
        </div>
        <Form.Item label={t('是否暂停上架')} required>
          <Select
            name="stopUp"
            data={pauseUpperList}
            rules={[rules.required(t('必填'))]}
            absolute
            clearable
            keygen="value"
            format="value"
            placeholder={t('请选择')}
            renderItem="label"
            style={{ width: 180 }}
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

BatchModal.defaultProps = {
  modalInfo: {},
};

BatchModal.propTypes = {
  loading: PropTypes.bool.isRequired,
  visible: PropTypes.bool.isRequired,
  modalInfo: PropTypes.shape(),
  onClose: PropTypes.func.isRequired,
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};

export default BatchModal;
