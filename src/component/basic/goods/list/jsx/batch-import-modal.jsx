import { t } from '@shein-bbl/react';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Modal, Button, Rule, Form, Select,
} from 'shineout';
import FileDelayUpload from '@public-component/upload/fileDelayUpload';
import store from '../reducers';

const rules = Rule();

function uploadFile({ formData, importModalType }) {
  if (Number(importModalType) === 1) {
    store.uploadFile(formData);
  } else if (Number(importModalType) === 2) {
    store.uploadFile2(formData);
  }
}
function downloadTemplate(importModalType) {
  if (Number(importModalType) === 1) {
    store.downloadTemplate();
  } else if (Number(importModalType) === 2) {
    store.downloadTemplate2();
  }
}

const defaultState = {
  importModalType: '', // 1 批量修改存储属性 2 重判存储标签
  multiChangePropFile: '', // 上传文件路径
};
function BatchImportModal(props) {
  const {
    importTypeSelectList,
    importModalVisible, // 导入弹框
    dataLoading,
  } = props;

  const [batchFormData, setBatchFormData] = useState(defaultState);

  const [ref, setRef] = useState();

  useEffect(() => {
    // 隐藏弹窗时，重置state
    if (!importModalVisible) {
      setBatchFormData(defaultState);
    }
  }, [importModalVisible]);

  return (
    <Modal
      maskCloseAble={null}
      title={t('批量导入修改')}
      visible={importModalVisible}
      onClose={() => {
        store.changeData({
          importModalVisible: false,
        });
      }}
      footer={(
        <div>
          <Button
            onClick={() => {
              store.changeData({
                importModalVisible: false,
              });
            }}
          >
            {t('取消')}
          </Button>
          <Button
            type="primary"
            // disabled={!batchFormData.multiChangePropFile}
            loading={dataLoading}
            style={{ marginRight: 15 }}
            onClick={() => {
              if (ref && ref.validate) {
                ref.validate().then(() => {
                  const formData = new FormData();
                  formData.append('file', batchFormData.multiChangePropFile);
                  uploadFile({
                    formData,
                    importModalType: batchFormData.importModalType,
                  });
                }).catch(() => {});
              }
            }}
          >
            {t('确定')}
          </Button>
        </div>
      )}
    >
      <Form
        value={batchFormData}
        onChange={(v) => setBatchFormData(v)}
        formRef={(f) => setRef(f)}
      >
        <Form.Item required label={t('导入类型')}>
          <Form.Field name="importModalType" rules={[rules.required(t('请选择导入类型'))]}>
            {({ value }) => (
              <Select
                keygen="dictCode"
                format="dictCode"
                renderItem="dictNameZh"
                data={importTypeSelectList || []}
                value={value}
                onChange={(v) => {
                  setBatchFormData({ importModalType: v, multiChangePropFile: '' });
                }}
              />
            )}
          </Form.Field>
        </Form.Item>
        <Form.Item required label={t('导入文件')}>
          <Form.Field name="multiChangePropFile" rules={[rules.required(t('请选择导入文件'))]}>
            {() => (
              <FileDelayUpload
                value={batchFormData.multiChangePropFile}
                loading={dataLoading}
                onChange={(v) => {
                  // 延时修复上传报错
                  setTimeout(() => {
                    setBatchFormData({ ...batchFormData, multiChangePropFile: v });
                  }, 100);
                }}
                accept=".xls,.xlsx"
              />
            )}
          </Form.Field>
        </Form.Item>
        <Form.Item label={t('下载模版')}>
          <Button
            type="primary"
            size="default"
            text
            disabled={!batchFormData.importModalType}
            onClick={() => downloadTemplate(batchFormData.importModalType)}
          >
            {t('下载导入模版')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
}

BatchImportModal.propTypes = {
  dataLoading: PropTypes.bool,
  importModalVisible: PropTypes.bool, // 导入弹框
  importTypeSelectList: PropTypes.arrayOf(PropTypes.shape()),
};

export default BatchImportModal;
