import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Form, Input, Modal, Select, Rule, Table, Popover,
} from 'shineout';
import Icon from '@shein-components/Icon';
import styles from '@src/component/style.less';
import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import EditTableCellSelect from '@public-component/inputItem/editTableCellSelectShow';
import { isInt } from '@src/lib/is';
import { validatebIntegerCant, validatebIntegerZeroToNine } from '@src/lib/validate';
// import { classFocus } from '@src/lib/deal-func';
import BatchModal from './batchModal';
import PauseListModal from './pauseListModal';
import PlanCategoryModal from './planCategoryModal';
import store from '../reducers';
import BatchImportModal from './batch-import-modal';

const sliceRoadWayRule = (v) => {
  if (v?.length >= 2) {
    return v.slice(0, 2);
  }
  if (v > 0) {
    return `0${v}`.slice(-2);
  }
  return '';
};

// 用于modal表单校验
const rules = Rule({
  rowRule: {
    func: (val, formData, callback) => {
      if (!['', null].includes(val) && !validatebIntegerZeroToNine().test(val)) {
        callback(new Error(t('请填写0-9的整数')));
      }
      callback(true);
    },
  },
  maxItemNumRule: {
    func: (val, formData, callback) => {
      if (!formData.maxItemNum) {
        callback(new Error(t('请输入品项混存上限')));
      }
      if (!validatebIntegerCant().test(formData.maxItemNum)) {
        callback(new Error(t('品项混存上限必须为不超过{}位的整数', 5)));
      }
      callback(true);
    },
  },
  /**
    1.录入库位时，判断库位类型是否已录入。
    1.1 若已录入库位类型，则判断库位类型是否为大货库位、散货库位。
    a) 若为是（库位类型为大货/散货库位），则以货位-为拆分符进行拆分，拆分后的第一个字符串。判断该字符串是否为5-8位且最后2位是否都为整数（01-99组成）。若不满足条件，则提示错误：货位必须为大货或散货库位，且必须有字符串-，且位数必须为5-8位，且最后2位必须为整数。
    b)若为否（库位类型非 大货/散货库位），则不提示错误
    2.若未录入库位类型，则不提示错误
   */
  locationCodeValidate: {
    func: (val, formData, callback) => {
      // 库位类型为大货库位、散货库位
      if ([1, 2].includes(formData.locationType) && val) {
        const list = val?.split('-') || [];
        if (list.length < 2 || !/^.{3,6}[0-9]{2}$/.test(list[0])) {
          callback(new Error(t('货位必须为大货或散货库位，且必须有字符串-，以第一个-拆分后，第一个字符串位数必须为5-8位，且最后2位必须为整数')));
          return;
        }
      }
      callback(true);
    },
  },
  roadwayValidate: {
    func: (val, formData, callback) => {
      // 库位类型为大货库位、散货库位
      if ([1, 2].includes(formData.locationType) && !(val <= 99 && val >= 1)) {
        callback(new Error(t('若库位类型为大货库位、散货库位，只能输入1-99的正整数，若为只有1位数，如1，则系统自动补齐01')));
      }
      callback(true);
    },
  },
  physicalAreaValidate: {
    func: (val, formData, callback) => {
      if (val < 10 || val > 99) {
        callback(new Error(t('只允许输入2位正整数')));
      }
      callback(true);
    },
  },
});

const errorTip = (value) => {
  if (/\.$/.test(value)) {
    Modal.error({
      title: t('输入的数字不能以.结尾!'),
    });
  }
};
class Handle extends React.Component {
  render() {
    const {
      limit,
      loading,
      selectedRows,
      addModal,
      addInfo,
      enabledList,
      exportVisible,
      specConfigModalVisiable,
      specDic,
      specConfigList,
      togglePaperVisible,
      locationSequenceNum,
      sequenceModalVisible,
      locationTypeList,
      modalAreaList,
      modalSubWarehouseList,
      enableExtends,
      locationCategoryList,
      storeAttrData,
      pauseUpperList,
      modalType, // 弹窗标题
      isSkcList,
      importModalVisible, // 导入弹出框是否显示 默认不显示
      batchModalVisible, // 批量添加弹出框是否显示 默认不显示
      batchModalType, // 批量添加弹出框类型
      currentWarehouseList,
      pauseModalVisible,
      planCategoryVisible,
      sum,
      dataLoading,
      importTypeSelectList,
    } = this.props;
    // const getEnabledName = val => (enabledList.find(v => v.dictCode === val) || {}).dictNameZh;
    // 库位规格配置列表项
    let CHINESE_INPUT_FLAG = true;
    const specConfigColumns = [
      {
        title: t('序号'),
        width: '10%',
        render: (record, index) => index + 1,
      },
      {
        title: t('库位规格名称'),
        width: '12.5%',
        render: (record, i) => (
          record.editting ? (
            <Input
              maxLength={20}
              value={record.name}
              key={`${i}_1_name`}
              className={`${i}_1`}
              onChange={() => { }}
              onKeyUp={(v) => {
                if (CHINESE_INPUT_FLAG) {
                  const data = assign({}, record, { name: v.target.value });
                  store.changeArrayList({ index: i, data, nextClassNum: 1 });
                }
              }}
              onEnterPress={() => {
                store.nextClass({ i, num: 2 });
              }}
              onCompositionStart={() => {
                CHINESE_INPUT_FLAG = false;
              }}
              onCompositionEnd={() => {
                CHINESE_INPUT_FLAG = true;
              }}
              onBlur={(v) => {
                errorTip(v.target.value);
              }}
            />
          ) : record.name
        ),
      },
      {
        title: t('最大可用率%'),
        width: '10%',
        render: (record, i) => (
          record.editting ? (
            <Input
              maxLength={3}
              value={record.maxAvailableRate}
              key={`${i}_2_maxAvailableRate`}
              className={`${i}_2`}
              onChange={() => { }}
              onKeyUp={(v) => {
                const data = assign({}, record, { maxAvailableRate: v.target.value });
                store.changeArrayList({
                  index: i, data, nextClassNum: 2,
                });
              }}
              onEnterPress={() => {
                store.nextClass({ i, num: 3 });
              }}
              onBlur={(v) => {
                errorTip(v.target.value);
              }}
            />
          ) : record.maxAvailableRate
        ),
      },
      {
        title: `${t('库位长')}(cm)`,
        width: '10%',
        render: (record, i) => (
          record.editting ? (
            <Input
              value={record.length}
              key={`${i}_3_length`}
              className={`${i}_3`}
              onChange={() => { }}
              onKeyUp={(v) => {
                const data = assign({}, record, { length: v.target.value });
                store.changeArrayList({ index: i, data, nextClassNum: 3 });
              }}
              onEnterPress={() => {
                store.nextClass({ i, num: 4 });
              }}
              onBlur={(v) => {
                errorTip(v.target.value);
              }}
            />
          ) : record.length
        ),
      },
      {
        title: `${t('库位宽')}(cm)`,
        width: '10%',
        render: (record, i) => (
          record.editting ? (
            <Input
              value={record.width}
              delay={800}
              key={`${i}_4_width`}
              onChange={() => { }}
              className={`${i}_4`}
              onKeyUp={(v) => {
                const data = assign({}, record, { width: v.target.value });
                store.changeArrayList({ index: i, data, nextClassNum: 4 });
              }}
              onEnterPress={() => {
                store.nextClass({ i, num: 5 });
              }}
              onBlur={(v) => {
                errorTip(v.target.value);
              }}
            />
          ) : record.width
        ),
      },
      {
        title: `${t('库位高')}(cm)`,
        width: '10%',
        render: (record, i) => (
          record.editting ? (
            <Input
              value={record.height}
              key={`${i}_5_width`}
              onChange={() => { }}
              className={`${i}_5`}
              onKeyUp={(v) => {
                const data = assign({}, record, { height: v.target.value });
                store.changeArrayList({
                  index: i, data, nextClassNum: 5,
                });
              }}
              onEnterPress={() => {
                store.nextClass({ i, num: 5, isChangeRow: true });
              }}
              onBlur={(v) => {
                errorTip(v.target.value);
              }}
            />
          ) : record.height
        ),
      },
      {
        title: `${t('库位体积')}(cm³)`,
        width: '10%',
        render: (record) => {
          const v = record.length * record.width * record.height;
          if (!v) {
            return '';
          }
          const arr = String(v).split('.');
          if (arr.length === 1 || (arr.length === 2 && arr[1].length <= 3)) {
            return v;
          }
          return `${arr[0]}.${arr[1].slice(0, 2)}`;
        },
      },
      {
        title: t('状态'),
        width: '15%',
        render: (record, i) => (
          // eslint-disable-next-line no-nested-ternary
          record.editting
            ? (
              <EditTableCellSelect
                list={specDic}
                value={String(record.status)}
                // showSearch
                style={{ width: '80px' }}
                onChange={(v) => {
                  const data = assign({}, record, { status: v });
                  store.changeArrayList({ index: i, data });
                }}
              />
            ) : Number(record.status) === 1 ? t('启用') : t('注销')
        ),
      },
      {
        title: t('操作'),
        width: '12.5%',
        render: (record) => (
          <Button
            size="small"
            onClick={() => store.editSpecConfigRow(record.id)}
          >
            {t('编辑')}
          </Button>
        ),
      },
    ];
    const ids = selectedRows.map((v) => v.id);
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <div className={styles.handleArea}>
          <div>
            <Button
              type="primary"
              icon="plus"
              onClick={() => store.editOrAddShow({ modalType: 0 })}
            >
              {t('新增')}
            </Button>
            <Button
              type="primary"
              loading={!loading}
              disabled={ids.length === 0}
            >
              <Popover.Confirm
                type="warning"
                okType="primary"
                text={{ ok: t('确认'), cancel: t('取消') }}
                onOk={() => {
                  store.off(ids);
                }}
              >
                {t('您确认要注销吗?')}
              </Popover.Confirm>
              {t('注销')}
            </Button>
            {/* 批量导入 */}
            <BatchImportModal
              importModalVisible={importModalVisible}
              dataLoading={dataLoading}
              importTypeSelectList={importTypeSelectList}
            />
            <Button
              type="primary"
              onClick={() => {
                store.changeData({
                  importModalVisible: true,
                });
              }}
            >
              {t('导入')}
            </Button>
            <Button
              type="primary"
              loading={!loading}
              disabled={exportVisible}
              onClick={() => {
                if (!limit.locationType || !limit.locationType.length) {
                  Modal.error({ title: t('请先选择库位类型') });
                  return;
                }
                if (limit.areaIds && limit.areaIds.length === 0) {
                  delete limit.areaIds;
                }
                if (limit.subWarehouseIds && limit.subWarehouseIds.length === 0) {
                  delete limit.subWarehouseIds;
                }
                if (limit.warehouseIds && limit.warehouseIds.length === 0) {
                  delete limit.warehouseIds;
                }
                store.exportData(limit);
              }}
            >
              {t('导出')}
            </Button>
            <Button
              type="primary"
              loading={!loading}
              onClick={() => {
                store.doSpecConfig();
              }}
            >
              {t('库位规格')}
            </Button>
            <Button
              type="primary"
              icon="printer"
              loading={!loading}
              disabled={ids.length === 0}
              onClick={() => {
                store.changeData({
                  paperSize: 1,
                  togglePaperVisible: true,
                });
              }}
            >
              {t('打印')}
            </Button>
            <Button
              type="primary"
              icon="printer"
              loading={!loading}
              disabled={selectedRows.length === 0 || selectedRows.some((i) => i.locationType !== 2)}
              onClick={() => {
                store.changeData({
                  sequenceModalVisible: true,
                  locationSequenceNum: undefined,
                });
              }}
            >
              {t('打印散货序列号')}
            </Button>
            <Button
              type="primary"
              loading={!loading}
              onClick={() => {
                store.changeData({
                  batchModalVisible: true,
                  batchModalType: 'add',
                });
              }}
            >
              {t('批量新增')}
            </Button>
            <Button
              type="primary"
              loading={!loading}
              onClick={() => {
                store.changeData({
                  batchModalVisible: true,
                  batchModalType: 'edit',
                });
              }}
            >
              {t('批量修改')}
            </Button>
            <Button
              type="primary"
              loading={!loading}
              onClick={() => {
                store.changeData({
                  pauseModalVisible: true,
                });
              }}
            >
              {t('暂停上架维护')}
            </Button>
            <Button
              type="primary"
              loading={!loading}
              onClick={() => {
                store.changeData({
                  planCategoryVisible: true,
                });
              }}
            >
              {t('规划品类维护')}
            </Button>
          </div>
          <span>{t('共有{}个库位', sum)}</span>
        </div>
        <Modal
          maskCloseAble={null}
          visible={addModal}
          width={800}
          title={!modalType ? t('新增') : t('编辑')}
          onClose={() => {
            store.closeModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.closeModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={110}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              store.commitData(addInfo);
            }}
            onChange={(value) => {
              store.changeData({
                addInfo: value,
              });
            }}
            value={addInfo}
            inline
            formRef={(f) => store.changeData({ modalFormRef: f })}
          >
            <Form.Item required label={t('库位')}>
              <Input
                name="location" placeholder={t('必填')}
                // 仅新增才校验
                // rules={[rules.required(t('请输入库位')), !modalType && rules.locationCodeValidate()].filter(Boolean)}
                rules={[rules.required(t('请输入库位'))]}
                disabled={!!modalType}
              />
            </Form.Item>

            <Form.Item required label={t('仓库')}>
              <Select
                name="warehouseId"
                data={currentWarehouseList}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="nameZh"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择仓库'))]}
                renderUnmatched={(r) => r?.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.changeAddInfoData({
                    warehouseId: value,
                    subWarehouseId: '',
                    areaId: '',
                    storeTypes: [],
                  });
                  store.changeData({
                    areaList: [],
                  });
                  store.getSubWarehouse(value);
                }}
              />
            </Form.Item>
            <Form.Item required label={t('子仓')}>
              <Select
                name="subWarehouseId"
                data={modalSubWarehouseList}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="nameZh"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择子仓'))]}
                renderUnmatched={(r) => r?.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.changeAddInfoData({
                    subWarehouseId: value,
                    areaId: '',
                  });
                  // modalType 用于判断是list获取库区还是modal获取库区
                  store.getArea({ modalType: 1, id: value });
                }}
              />
            </Form.Item>
            <Form.Item required label={t('物理库区')}>
              <Input.Number
                name="physicalArea"
                digits={0}
                maxLength={2}
                numType="positive"
                placeholder={t('请输入')}
                clearable
                allowNull
                style={{ width: 180 }}
                rules={[rules.required(), rules.physicalAreaValidate()]}
              />
            </Form.Item>
            <Form.Item required label={t('库区')}>
              <Select
                name="areaId"
                data={modalAreaList}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="area"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择库区'))]}
                renderUnmatched={(r) => r?.area || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.area.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('库位类型')}>
              <Select
                name="locationType"
                data={locationTypeList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                // 仅编辑才限制库位类型切换
                // disabled={(d) => modalType && ([1, 2].includes(addInfo.locationType) ? ![1, 2].includes(d.dictCode) : [1, 2].includes(d.dictCode))}
                rules={[rules.required(t('请选择库位类型'))]}
                renderUnmatched={(r) => r?.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  // 非大货类型库位不允许维护整箱上架为"是"
                  if (value !== 1) {
                    store.changeAddInfoData({
                      boxUp: 0,
                    });
                  }
                  if ([1, 2].includes(value)) {
                    if (addInfo.roadway.length === 1) {
                      store.changeAddInfoData({
                        roadway: `0${addInfo.roadway}`,
                      });
                    }
                  }
                }}
              />
            </Form.Item>
            <Form.Item label={t('库位类别')}>
              <Select
                name="locationCategory"
                data={[{
                  dictCode: 0,
                  dictNameZh: t('请选择'),
                }, ...locationCategoryList]}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                renderUnmatched={(r) => r?.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('巷道')}>
              {
                [1, 2].includes(addInfo.locationType) ? (
                  <Input.Number
                    digits={0}
                    maxLength={2}
                    numType="positive"
                    name="roadway"
                    placeholder={t('必填')}
                    rules={[rules.required(t('请输入巷道')), rules.roadwayValidate()]}
                    onBlur={() => {
                      store.changeAddInfoData({
                        roadway: sliceRoadWayRule(addInfo.roadway),
                      });
                    }}
                  />
                ) : (
                  <Input
                    name="roadway"
                    placeholder={t('必填')}
                    rules={[rules.required(t('请输入巷道'))]}
                  />
                )
              }
            </Form.Item>
            {
              !modalType
                ? (
                  <Form.Item required={addInfo.enabled === 1} label={t('拣货顺序编号')}>
                    <Input
                      name="pickOrder" placeholder={addInfo.enabled === 1 ? t('必填') : ''}
                      disabled={addInfo.enabled === 2}
                      rules={addInfo.enabled === 1 ? [rules.required(t('请输入拣货顺序编号'))] : []}
                    />
                  </Form.Item>
                )
                : (
                  <Form.Item required label={t('拣货顺序编号')}>
                    <Input
                      name="pickOrder" placeholder={t('必填')}
                      rules={[rules.required(t('请输入拣货顺序编号'))]}
                    />
                  </Form.Item>
                )
            }
            <Form.Item label={t('行')}>
              <Input.Number
                name="locationRow" placeholder={t('请输入')} min={0} max={9} digits={0}
                rules={[rules.rowRule()]} style={{ width: 180 }} allowNull
              />
            </Form.Item>
            <Form.Item label={t('列')}>
              <Input.Number
                name="locationColumn" placeholder={t('请输入')} min={0} max={9} digits={0}
                rules={[rules.rowRule()]} style={{ width: 180 }} allowNull
              />
            </Form.Item>
            <Form.Item required label={t('是否整箱上架')}>
              <Select
                name="boxUp"
                data={pauseUpperList}
                disabled={![1, ''].includes(addInfo.locationType)}
                keygen="value"
                format="value"
                placeholder={t('请选择')}
                renderItem="label"
                style={{ width: 180 }}
                renderUnmatched={(r) => r?.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('品项混存上限')}>
              <Input.Number
                name="maxItemNum" placeholder={t('请输入')} min={1} max={99999} digits={0}
                rules={[rules.maxItemNumRule()]} style={{ width: 180 }}
              />
            </Form.Item>
            <Form.Item required label={t('状态')}>
              <Select
                name="enabled"
                data={enabledList}
                keygen="dictCode"
                format="dictCode"
                placeholder={t('请选择')}
                renderItem="dictNameZh"
                style={{ width: 180 }}
                rules={[rules.required(t('请选择状态'))]}
                renderUnmatched={(r) => r?.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(e) => {
                  // 如果状态为注销且为弹窗为新增，则清空拣货顺序编号
                  if (e === 2 && !modalType) {
                    store.changeAddInfoData({
                      pickOrder: '',
                    });
                  }
                }}
              />
            </Form.Item>
            <Form.Item label={t('库位规格')}>
              <Select
                name="extendId"
                data={[{
                  id: 0,
                  name: t('请选择'),
                }, ...enableExtends]}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="name"
                style={{ width: 180 }}
                renderUnmatched={(r) => r?.name || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item label={t('存储属性')}>
              <Select
                name="storeTypes"
                data={storeAttrData}
                keygen="id"
                format="id"
                placeholder={t('请选择')}
                renderItem="name"
                style={{ width: 180 }}
                renderUnmatched={(r) => r?.name || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                multiple
                compressed
                clearable
              />
            </Form.Item>
            <Form.Item label={t('是否暂停上架')}>
              <Select
                name="stopUp"
                data={pauseUpperList}
                keygen="value"
                format="value"
                placeholder={t('请选择')}
                renderItem="label"
                style={{ width: 180 }}
                renderUnmatched={(r) => r?.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('是否同属性混SKC')}>
              <Select
                name="isMultiSkc"
                data={isSkcList}
                keygen="value"
                format="value"
                placeholder={t('请选择')}
                renderItem="label"
                rules={[rules.required(t('是否同属性混SKC'))]}
                style={{ width: 180 }}
                renderUnmatched={(r) => r?.label || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
          </Form>
        </Modal>
        {/* Modal1:库位规格按钮 */}
        <Modal
          maskCloseAble={null}
          title={t('库位规格配置')}
          visible={specConfigModalVisiable}
          width="900px"
          onClose={() => store.changeData({
            specConfigModalVisiable: false,
          })}
          footer={[
            <Button
              key="cancel" onClick={() => store.changeData({
                specConfigModalVisiable: false,
              })}
            >
              {t('取消')}
            </Button>,
            <Button
              key="ok"
              type="primary"
              disabled={!loading}
              onClick={() => store.saveSpecConfig()}
            >
              {t('确定')}
            </Button>,
          ]}
        >
          <Button
            style={{ position: 'relative', top: '-10px' }}
            type="primary"
            onClick={() => store.modifySpecConfigList({
              id: +new Date(),
              isNew: true,
              editting: true,
              name: '',
              maxAvailableRate: '',
              length: '',
              width: '',
              height: '',
              volume: '',
              status: 1,
              operate: t('编辑'),
            })}
          >
            <Icon name="plus" />
            {t('添加')}
          </Button>
          <Table
            keygen={(d) => JSON.stringify(d)}
            bordered
            fixed="both"
            style={{ maxHeight: 300 }}
            columns={specConfigColumns}
            data={specConfigList}
            pagination={false}
          />
        </Modal>

        {/* Modal2:打印按钮 */}
        <Modal
          maskCloseAble={null}
          title={t('请选择纸张规格')}
          visible={togglePaperVisible}
          width={500}
          onClose={() => store.changeData({
            togglePaperVisible: false,
          })}
          footer={[
            <Button
              key="cancel" onClick={() => store.changeData({
                togglePaperVisible: false,
              })}
            >
              {t('取消')}
            </Button>,
            <Button
              key="ok" type="primary" onClick={() => {
                Modal.confirm({
                  title: t('您确定要进行打印?'),
                  text: { ok: t('确认'), cancel: t('取消') },
                  onOk: () => {
                    store.printSaga();
                  },
                });
              }}
            >
              {t('确定')}
            </Button>,
          ]}
        >
          <div className={styles.inner_list} style={{ display: 'flex' }}>
            <span className={styles.labWidth}>
              {t('请选择纸张规格')}
              :
            </span>
            <Select
              keygen="dictCode"
              format="dictCode"
              renderItem="dictNameZh"
              data-bind="paperSize"
              data={[
                {
                  dictCode: 1,
                  dictNameZh: '100*45',
                },
                {
                  dictCode: 2,
                  dictNameZh: '80*50',
                },
                {
                  dictCode: 3,
                  dictNameZh: '80*40',
                },
              ]}
              placeholder={t('请选择')}
            />
          </div>
        </Modal>
        {/* Modal3:打印散货序列号按钮 */}
        <Modal
          maskCloseAble={null}
          title={t('打印散货序列号')}
          visible={sequenceModalVisible}
          width={500}
          onClose={() => store.changeData({
            sequenceModalVisible: false,
          })}
          footer={[
            <Button
              key="cancel" onClick={() => store.changeData({
                sequenceModalVisible: false,
              })}
            >
              {t('取消')}
            </Button>,
            <Button
              disabled={!locationSequenceNum}
              loading={!loading}
              key="ok" type="primary" onClick={() => {
                Modal.confirm({
                  title: t('您确定要进行打印?'),
                  text: { ok: t('确认'), cancel: t('取消') },
                  onOk: () => {
                    if (!isInt(locationSequenceNum)) {
                      Modal.error({ title: t('请输入数字') });
                      return;
                    }
                    store.printSequenceSaga();
                  },
                });
              }}
            >
              {t('确定')}
            </Button>,
          ]}
        >
          <div className={styles.inner_list}>
            <span className={styles.labWidth}>
              {t('打印数量')}
              :
            </span>
            <Input
              type="text"
              style={{ width: 200 }}
              data-bind="locationSequenceNum"
            />
          </div>
          <div style={{ margin: '5px 0 0 15px' }}>
            <span style={{ color: 'red' }}>*</span>
            {t('在库位已有序列号基础上递增打印')}
          </div>
        </Modal>
        {/** Modal: 批量新增 */}
        <BatchModal
          loading={loading}
          type={batchModalType}
          visible={batchModalVisible}
          modalInfo={{
            ...this.props,
          }}
          onClose={() => store.changeData({ batchModalVisible: false })}
          onSubmit={(val) => {
            store.batchOperationGoodsLocation({
              type: batchModalType,
              params: val,
            });
          }}
          onChange={({ type, value }) => {
            switch (type) {
              case 'warehouse':
                store.getSubWarehouse(value);
                store.changeData({
                  areaList: [],
                });
                break;
              case 'subWarehouse':
                store.getArea({ modalType: 1, id: value });
                break;
              default:
                break;
            }
          }}
        />
        {/** Modal: 暂停上架维护 */}
        <PauseListModal
          loading={loading}
          visible={pauseModalVisible}
          modalInfo={{
            ...this.props,
          }}
          onClose={() => store.changeData({ pauseModalVisible: false })}
        />

        {/* Modal：规划品类维护 */}
        <PlanCategoryModal
          loading={loading}
          visible={planCategoryVisible}
          modalInfo={{
            ...this.props,
          }}
          onClose={() => store.changeData({ planCategoryVisible: false })}
        />
      </section>
    );
  }
}

Handle.propTypes = {
  addModal: PropTypes.bool,
  modalType: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()),
  addInfo: PropTypes.shape(),
  enabledList: PropTypes.arrayOf(PropTypes.shape()),
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  exportVisible: PropTypes.bool,
  specConfigModalVisiable: PropTypes.bool,
  specDic: PropTypes.arrayOf(PropTypes.string),
  specConfigList: PropTypes.arrayOf(PropTypes.string),
  togglePaperVisible: PropTypes.bool,
  sequenceModalVisible: PropTypes.bool,
  locationSequenceNum: PropTypes.number,
  locationTypeList: PropTypes.arrayOf(PropTypes.shape()),
  modalSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  modalAreaList: PropTypes.arrayOf(PropTypes.shape()),
  enableExtends: PropTypes.arrayOf(PropTypes.shape()),
  locationCategoryList: PropTypes.arrayOf(PropTypes.shape()),
  storeAttrData: PropTypes.arrayOf(PropTypes.shape()),
  pauseUpperList: PropTypes.arrayOf(PropTypes.shape()),
  isSkcList: PropTypes.arrayOf(PropTypes.shape()),
  importModalVisible: PropTypes.bool,
  batchModalVisible: PropTypes.bool,
  pauseModalVisible: PropTypes.bool,
  batchModalType: PropTypes.string,
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  sum: PropTypes.string,
  planCategoryVisible: PropTypes.bool,
  dataLoading: PropTypes.bool,
  importTypeSelectList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
