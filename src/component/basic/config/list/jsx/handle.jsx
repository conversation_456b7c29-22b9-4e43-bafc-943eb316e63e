import React, { Component } from 'react';
import { Button } from 'shineout';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import store from '../reducers';

class Handle extends Component {
  render() {
    const {
      loading,
    } = this.props;
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              isHandleVisible: true,
              isEdit: false,
            });
          }}
        >
          {t('新增')}
        </Button>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
};
export default Handle;
