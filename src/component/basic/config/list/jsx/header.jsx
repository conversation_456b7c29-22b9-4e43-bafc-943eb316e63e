import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Input, Select } from 'shineout';
import { formatSearchData } from '@public-component/search-queries/utils';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import store, { defaultLimit } from '../reducers';
import styles from '../style.less';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      modelList,
      businessTypeList,
      userTypeList,
      statusList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 80 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => { store.handlePaginationChange({ pageNum: 1 }); }}
          // formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('模块')}
            name="model"
            data={modelList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('业务节点')}
            name="businessType"
            data={businessTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('使用方')}
            name="userType"
            data={userTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Input
            label={t('参数编码')}
            name="configCode"
            maxLength={64}
          />
          <Input
            label={t('参数名称')}
            name="configName"
            maxLength={64}
          />
          <Select
            label={t('状态')}
            name="watchStatusList"
            data={statusList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
          />
          <Input.Number
            min={1}
            allowNull
            hideArrow
            clearable
            digits={0}
            label={t('序号')}
            name="id"
            max={2147483647}
          />
        </SearchAreaContainer>
        {/* 按钮区域 */}
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  modelList: PropTypes.arrayOf(PropTypes.shape()),
  businessTypeList: PropTypes.arrayOf(PropTypes.shape()),
  userTypeList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
