import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Button, Modal, Input, Select, DatePicker, Popover, Textarea,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import Icon from '@shein-components/Icon';
import style from '@src/component/style.less';
import store from '../reducers';
import styles from '../style.less';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordVisible,
      recordId,
      isHandleVisible,
      configCode,
      configName,
      configValue,
      isDetailVisible,
      configValueDetail,
      isEdit,
      model,
      businessType,
      userType,
      modelList,
      businessTypeList,
      userTypeList,
      reserveValue,
      reserveTime,
      watchStatus,
      statusList,
      YesOrNoList,
      isPushWxPermission,
      isPushWx,
    } = this.props;
    const columns = [
      {
        title: t('序号'),
        width: 50,
        render: (record) => (
          <div
            style={{ color: 'blue', cursor: 'pointer' }}
            onClick={() => {
              store.changeData({
                ...record,
                id: record.id,
                configName: record.configName,
                configValue: record.configValue,
                remark: record.remark,
                configCode: record.configCode,
                isEdit: true,
                isHandleVisible: true,
                model: record.model,
                businessType: record.businessType,
                userType: record.userType,
                reserveValue: record.reserveValue,
                reserveTime: record.reserveTime,
                watchStatus: record.watchStatus,
              });
            }}
          >
            {record.id}
          </div>
        ),
      },
      {
        title: t('参数编码'),
        render: 'configCode',
        width: 120,
      },
      {
        title: t('参数名称'),
        render: 'configName',
        width: 120,
      },
      {
        title: t('参数值'),
        width: 80,
        render: (record) => (
          <span
            style={{ cursor: 'pointer' }}
            onClick={() => {
              store.changeData({
                isDetailVisible: true,
                configValueDetail: record.configValue,
              });
            }}
          >
            {`${record.configValue.slice(0, 40)}${record.configValue.length > 40 ? '...' : ''}`}
          </span>
        ),
      },
      {
        title: t('备注'),
        render: 'remark',
        width: 120,
      },
      {
        title: t('模块'),
        render: 'modelName',
        width: 60,
      },
      {
        title: t('业务节点'),
        render: 'businessTypeName',
        width: 80,
      },
      {
        title: t('使用方'),
        render: 'userTypeName',
        width: 80,
      },
      {
        title: t('状态'),
        render: 'watchStatusName',
        width: 80,
      },
      {
        title: t('变更是否预警推送'),
        render: 'isPushWxName',
        width: 120,
      },
      {
        title: t('操作'),
        width: 100,
        fixed: 'right',
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            style={{ fontSize: 14, lineHeight: 1, padding: 0 }}
            onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
          >
            {t('操作日志')}
          </Button>
        ),
      },
    ];

    return (
      <section className={style.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: style.pagination,
              layout: [({ total }) => t('共{}条', String(total)), 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'PARAM_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />

        <Modal
          title={isEdit ? t('修改参数') : t('新增参数')}
          visible={isHandleVisible}
          onOk={() => store.handleConfig()}
          onClose={() => store.closeHandleModal()}
          footer={[
            <Button key="back" onClick={() => store.closeHandleModal()}>{t('取消')}</Button>,
            <Button
              key="submit"
              type="primary"
              disabled={configCode === '' || configName === '' || configValue === '' || !loading || (reserveValue && !reserveTime) || (!reserveValue && reserveTime)}
              onClick={() => store.handleConfig()}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('编码')}
              :
            </span>
            <Input
              type="text"
              // value={configCode}
              disabled={isEdit}
              style={{ width: 270 }}
              maxLength={50}
              placeholder={t('必填')}
              data-bind="configCode"
            />
            <span className={style.redStar}>*</span>
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('名称')}
              :
            </span>
            <Input
              type="text"
              // value={configName}
              style={{ width: 270 }}
              maxLength={100}
              placeholder={t('必填')}
              data-bind="configName"
            />
            <span className={style.redStar}>*</span>
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('值')}
              :
            </span>
            <Textarea
              rows={4}
              style={{ width: 270 }}
              placeholder={t('必填')}
              data-bind="configValue"
            />
            <span className={style.redStar}>*</span>
            {
              ['RESULT_WORK', 'ASSIGN_UPDATE_EXCLUDE_LEAVE_REASON_NAME'].includes(configCode) && (
                <Button
                  size="small"
                  type="primary"
                  onClick={() => store.validConfig()}
                >
                  {t('值校验')}
                  <Popover trigger="hover" position="top">
                    <div style={{ padding: '10px' }}>
                      <div>{t('1.不能有中文逗号“，”')}</div>
                      <div>{t('2.不能有空格“ ”')}</div>
                      <div>{t('3.不能连续出现2个及以上的英文逗号和短下划线')}</div>
                      <div>{t('4.不能有英文字母')}</div>
                      <div>{t('5.不能有阿拉伯数字')}</div>
                      <div>{t('6.不能出现英文逗号“,”和短下划线“_”之外的其他符号')}</div>
                    </div>
                  </Popover>
                  <Icon name="info-circle" style={{ color: 'white', marginLeft: '5px' }} />
                </Button>
              )
            }
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('备注')}
              :
            </span>
            <Input
              type="text"
              // value={remark}
              style={{ width: 270 }}
              placeholder={t('非必填')}
              data-bind="remark"
            />
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('模块')}
              :
            </span>
            <Select
              value={model}
              data={modelList}
              keygen="dictCode"
              format="dictCode"
              width={270}
              placeholder={t('请选择')}
              renderItem="dictNameZh"
              renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
              onChange={(v) => {
                store.changeData({
                  model: v,
                });
              }}
            />
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('业务节点')}
              :
            </span>
            <Select
              value={businessType}
              data={businessTypeList}
              keygen="dictCode"
              format="dictCode"
              width={270}
              placeholder={t('请选择')}
              renderItem="dictNameZh"
              renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
              onChange={(v) => {
                store.changeData({
                  businessType: v,
                });
              }}
            />
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('使用方')}
              :
            </span>
            <Select
              value={userType}
              data={userTypeList}
              keygen="dictCode"
              format="dictCode"
              width={270}
              placeholder={t('请选择')}
              renderItem="dictNameZh"
              renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
              onChange={(v) => {
                store.changeData({
                  userType: v,
                });
              }}
            />
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('预约参数值')}
              :
            </span>
            <Input
              value={reserveValue}
              width={270}
              maxLength={64}
              clearable
              onChange={(v) => {
                store.changeData({
                  reserveValue: v,
                });
              }}
            />
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('预约生效时间')}
              :
            </span>
            <DatePicker
              absolute
              value={reserveTime}
              style={{ width: 270 }}
              type="datetime"
              format="YYYY-MM-dd HH:mm:ss"
              min={Date.now() + 7200000 + 300000}
              placeholder={t('请选择')}
              onChange={(v) => {
                store.changeData({
                  reserveTime: v,
                });
              }}
            />
          </div>
          <div className={style.buttonItem}>
            <span className={style.lab}>
              {t('状态')}
              :
            </span>
            <Select
              value={watchStatus}
              data={statusList}
              keygen="dictCode"
              format="dictCode"
              width={270}
              placeholder={t('请选择')}
              renderItem="dictNameZh"
              renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
              onChange={(v) => {
                store.changeData({
                  watchStatus: v,
                });
              }}
            />
          </div>
          {
            isPushWxPermission && (
              <div className={style.buttonItem}>
                <span className={styles.alertLab}>
                  {t('变更是否预警推送')}
                  :
                </span>
                <Select
                  value={isPushWx}
                  data={YesOrNoList}
                  keygen="dictCode"
                  format="dictCode"
                  width={270}
                  placeholder={t('请选择')}
                  renderItem="dictNameZh"
                  renderUnmatched={(r) => r.dictNameZh
                    || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  clearable
                  onChange={(v) => {
                    store.changeData({
                      isPushWx: v,
                    });
                  }}
                />
              </div>
            )
          }
        </Modal>
        <Modal
          title={t('参数值详情')}
          visible={isDetailVisible}
          onOk={() => {
            store.changeData({
              isDetailVisible: false,
            });
          }}
          onClose={() => store.changeData({
            isDetailVisible: false,
          })}
          footer={null}
        >
          <div style={{ maxHeight: '500px', overflowY: 'scroll' }}>
            {configValueDetail}

          </div>
        </Modal>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  recordId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  isDetailVisible: PropTypes.bool,
  isHandleVisible: PropTypes.bool,
  configCode: PropTypes.string,
  configName: PropTypes.string,
  configValue: PropTypes.string,
  remark: PropTypes.string,
  configValueDetail: PropTypes.string,
  id: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  isEdit: PropTypes.bool,
  model: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  businessType: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  userType: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  modelList: PropTypes.arrayOf(PropTypes.shape()),
  businessTypeList: PropTypes.arrayOf(PropTypes.shape()),
  userTypeList: PropTypes.arrayOf(PropTypes.shape()),
  reserveValue: PropTypes.string,
  reserveTime: PropTypes.string,
  watchStatus: PropTypes.string,
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  YesOrNoList: PropTypes.arrayOf(PropTypes.shape()),
  isPushWxPermission: PropTypes.bool,
  isPushWx: PropTypes.number,
};

export default List;
