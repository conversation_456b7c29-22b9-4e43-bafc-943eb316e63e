import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { t } from '@shein-bbl/react';
import { checkUrlPermissionAPI, configEditApi, configAddApi } from '@src/server/common/common';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import { getListApi, validConfigAPI } from './server';

// 搜索默认值
export const defaultLimit = {
  configCode: '', // 参数编码
  configName: '', // 参数名称
  model: '', // 模块
  businessType: '', // 业务节点
  userType: '', // 使用方
  id: '', // 序号
  watchStatusList: [1], // 状态
};

// 其他默认值
const defaultState = {
  loading: 0, // 0 loading, 1 load success, 2 load fail
  limit: defaultLimit,
  list: [],
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  isHandleVisible: false,
  configCode: '', // 编码
  configName: '', // 名称
  configValue: '', // 值
  remark: '', // 备注
  model: '', // 模块
  businessType: '', // 业务节点
  userType: '', // 使用方
  reserveValue: '',
  reserveTime: '',
  watchStatus: 1, // 状态
  isPushWx: 0, // 默认为否
  isDetailVisible: false,
  id: '',
  isEdit: false,
  modelList: [], // 模块 下拉
  businessTypeList: [], // 业务节点 下拉
  userTypeList: [], // 使用方 下拉
  statusList: [], // 状态 下拉
  YesOrNoList: [],
  isPushWxPermission: false, // 是否有权限配置推送
};

export default {
  state: defaultState,
  // 改变state的值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    yield this.changeLimitData({
      ...defaultLimit,
    });
  },
  /**
   * 初始化数据
   */
  * init() {
    markStatus('loading');
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['PARAMETER_MODULE_TYPE', 'PARAMETER_SERVICE_NODE', 'PARAMETER_USER', 'SYS_CONFIG_WATCH_STATUS', 'SYS_CONFIG_IS_PUSH_WX'] }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        modelList: selectData.info.data.find((item) => item.catCode === 'PARAMETER_MODULE_TYPE').dictListRsps,
        businessTypeList: selectData.info.data.find((item) => item.catCode === 'PARAMETER_SERVICE_NODE').dictListRsps,
        userTypeList: selectData.info.data.find((item) => item.catCode === 'PARAMETER_USER').dictListRsps,
        statusList: selectData.info.data.find((item) => item.catCode === 'SYS_CONFIG_WATCH_STATUS').dictListRsps,
        YesOrNoList: selectData.info.data.find((item) => item.catCode === 'SYS_CONFIG_IS_PUSH_WX').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }
    yield this.search();
    yield this.getPushWxConfig();
  },
  * getPushWxConfig() {
    const { code } = yield checkUrlPermissionAPI({ url: '/wmd/front/config/change_is_push_wx' });
    yield this.changeData({
      isPushWxPermission: code === '0',
    });
  },
  /**
   * 搜索操作
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      id: limit.id ? Number(limit.id) : null,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListApi(param);
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({
        title: msg,
        autoFocusButton: 'ok',
      });
    }
  },
  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(arg = {}) {
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  /**
  * 新增/编辑
  */
  * handleConfig() {
    const {
      isEdit, configCode, configName,
      model, businessType, userType,
      configValue,
      remark,
      id,
      reserveValue,
      reserveTime,
      watchStatus,
      isPushWx,
    } = yield '';
    markStatus('loading');
    const param = {
      configCode,
      configName,
      configValue,
      remark,
      id,
      model,
      businessType,
      userType,
      reserveValue,
      reserveTime,
      watchStatus,
      isPushWx,
    };
    const { code, msg } = isEdit ? yield configEditApi(param) : yield configAddApi(param);
    if (code === '0') {
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
      yield this.closeHandleModal();
    } else {
      Modal.error({ title: msg });
    }
  },
  * validConfig() {
    const {
      configValue,
    } = yield '';
    markStatus('loading');
    const param = {
      configValue,
    };
    const { code, msg, info } = yield validConfigAPI(param);
    if (code === '0') {
      Modal.success({ title: t('校验无误'), content: info });
    } else {
      Modal.error({ title: t('校验有误'), content: msg });
    }
  },
  /**
   * 关闭modal
   */
  * closeHandleModal() {
    yield this.changeData({
      isHandleVisible: false,
      configCode: '',
      configName: '',
      configValue: '',
      remark: '',
      id: '',
      model: '',
      businessType: '',
      userType: '',
      reserveValue: '',
      reserveTime: '',
      watchStatus: 1,
      isPushWx: 0,
    });
  },
};
