import { sendPostRequest } from '@src/server/common/public';

// 查询列表
// eslint-disable-next-line import/prefer-default-export
export const getListApi = (param) => sendPostRequest({
  url: '/config/query_list',
  param,
}, process.env.BASE_URI_WMD);

export const validConfigAPI = (param) => sendPostRequest({
  url: '/config/validConfig',
  param,
}, process.env.BASE_URI_WMD);

// 编辑
// export const configEditApi = (param) => sendPostRequest({
//   url: '/config/modify',
//   param,
// }, process.env.BASE_URI_WMD);

// 新增
// export const configAddApi = (param) => sendPostRequest({
//   url: '/config/add',
//   param,
// }, process.env.BASE_URI_WMD);
