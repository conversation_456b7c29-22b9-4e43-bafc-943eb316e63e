import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Spin } from 'shineout';
import ContainerPage from '@public-component/search-queries/container';
import store from './reducers';
import Header from './jsx/header';
import Handle from './jsx/handle';
import List from './jsx/list';

class Container extends React.Component {
  constructor(props) {
    super(props);
    this.changeSubWarehouseList = this.changeSubWarehouseList.bind(this);
  }

  componentDidMount() {
    // 初始化数据
    store.init();
    window.addEventListener('subWarehouseListUpdate', this.changeSubWarehouseList);
  }

  // eslint-disable-next-line react/sort-comp,class-methods-use-this
  changeSubWarehouseList(e) {
    store.changeData({ warehouseId: e.detail.warehouseId });
    store.changeLimit({ warehouseId: e.detail.warehouseId });
    store.changeWarehouseId({ val: e.detail.warehouseId });
  }

  componentWillUnmount() {
    window.removeEventListener('subWarehouseListUpdate', this.changeSubWarehouseList);
  }

  render() {
    const {
      ready,
    } = this.props;
    if (ready) {
      return (
        <ContainerPage>
          <Header {...this.props} />
          <Handle {...this.props} />
          <List {...this.props} />
        </ContainerPage>
      );
    }
    return (
      <div style={{ textAlign: 'center' }}>
        <Spin size={50} name="default" />
      </div>
    );
  }
}

Container.propTypes = {
  ready: PropTypes.bool.isRequired,
};

const mapStateToProps = (state) => state['basic/link-performance'];
export default connect(mapStateToProps)(Container);
