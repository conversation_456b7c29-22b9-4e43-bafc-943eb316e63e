import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { getSubWarehouseApi } from '@src/server/basic/sub-warehouse';
import { getWarehouseApi, dictSelect } from '@src/server/basic/dictionary';
import { getWarehouseId } from '../../../lib/dealFunc';
import {
  getTableList,
  confirmSaveConfig, // 新增
  confirmSaveEditConfig, // 编辑
  deleteConfig,
} from './server';
import { getSize } from '../../../middlewares/pagesize';

const defaultState = {
  ready: false,
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  editObjVisible: 0, // 新增编辑modal flag
  topSearch: true,
  count: 0,
  selectedRows: [], // 选中行
  tableList: [], // 表格数据
  warehouseList: [], // 仓库
  parkList: [], // 园区
  linkList: [], // 环节
  limit: {
    pageNum: 1, // 默认搜索第一页
    pageSize: getSize(), // 每页显示条数【全局】
    warehouseId: '',
    linkTypeList: [],
    parkTypeList: [],
    subWarehouseIdList: [],

  },
  editObj: {
    // id: '', // 编辑时选中行id 新增为空
    warehouseId: '', // 仓库id不能为空
    parkType: '', // 园区id
    subWarehouseId: '', // 子仓id
    linkType: '', // 环节
    formalOutput: '',
    informalOutput: '',
    keyword: '',
    receivePlanCapacity: '',
    pageSizeOptions: ['10', '20', '50'],
    subWarehouseListModal: [],
    parkListModal: [],
  },
  currentWarehouseList: [], // 权限仓库列表
};

export default {
  state: defaultState,
  $init: (state) => {
    // 为配合做多tab页面切换的功能 保留状态
    assign(state);
  },
  * init() {
    const { currentWarehouseList } = yield 'nav';
    yield this.changeData({
      currentWarehouseList,
    });
    // 根据全局id查找对应子仓和园区
    const warehouseId = getWarehouseId();
    if (warehouseId) {
      yield this.changeLimit({ warehouseId });
      yield this.changeWarehouseId({ val: warehouseId });
    } else {
      Modal.info({
        title: t('请选择仓库'),
      });
    }
    // 获取仓库
    const [warehouseRes, dicObj] = yield Promise.all([
      getWarehouseApi({ enabled: 1 }),
      dictSelect({ catCode: ['LINK_ENUM'] }),
    ]);
    // 仓库
    if (warehouseRes.code === '0') {
      this.state.warehouseList = warehouseRes.info.data || [];
    } else {
      Modal.error({
        title: warehouseRes.msg,
      });
    }
    // 环节
    if (warehouseRes.code === '0') {
      this.state.linkList = dicObj.info.data.find((item) => item.catCode === 'LINK_ENUM').dictListRsps;
    } else {
      Modal.error({
        title: dicObj.msg,
      });
    }

    this.state.ready = true;
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件limit属性值
  changeLimit: (state, data) => {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  // 重置搜索条件
  reset(state, data) {
    // 页数和页码不重置
    assign(state.limit, defaultState.limit, {
      pageNum: data,
      pageSize: getSize(),
    });
  },
  changeSearchParamsData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  changeSearchParamsDataModal(state, data) {
    Object.assign(state, {
      editObj: {
        ...state.editObj,
        ...data,
      },
    });
  },
  // 新增/编辑
  changeConfigObjData(state, data) {
    Object.assign(state, {
      editObj: {
        ...state.editObj,
        ...data,
      },
    });
  },
  // 重置search部分查询条件
  * clearSearchParamsData(param) {
    const { val, property } = param;
    const clearProps = {};
    // eslint-disable-next-line default-case
    switch (property) {
      case 'warehouseId':
        clearProps.parkTypeList = [];
        // eslint-disable-next-line no-fallthrough
      case 'parkTypeList':
        clearProps.subWarehouseIdList = [];
    }
    clearProps[property] = val;
    yield this.changeSearchParamsData(clearProps);
  },
  // 重置modal部分查询条件
  * clearSearchParamsDataModal(param) {
    const { val, property } = param;
    const clearProps = {};
    // eslint-disable-next-line default-case
    switch (property) {
      case 'warehouseId':
        clearProps.parkType = '';
        // eslint-disable-next-line no-fallthrough
      case 'parkType':
        clearProps.subWarehouseId = '';
    }
    clearProps[property] = val;
    yield this.changeSearchParamsDataModal(clearProps);
  },
  // 改变search仓库下拉
  * changeWarehouseId(param) {
    // 查询到园区跟子仓的数据
    markStatus('dataLoading');
    const { code, info, msg } = yield getSubWarehouseApi({ warehouseId: param.val });
    if (code === '0') {
      const parkList = Object.values((info.data || []).reduce((result, curr) => {
        const { parkName, parkType } = curr;
        if (result[parkType]) {
          result[parkType].subWarehouseList.push(curr);
        } else {
          result[parkType] = {
            parkName,
            parkType,
            subWarehouseList: [{ ...curr }],
          };
        }

        return result;
      }, {}));
      yield this.changeData({
        parkList,
        // eslint-disable-next-line max-len
        subWarehouseList: parkList.reduce((result, curr) => ([...result, ...curr.subWarehouseList]), []),
      });
      // 重置条件
      yield this.clearSearchParamsData({ ...param, property: 'warehouseId' });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 改变Modal仓库下拉
  * changeWarehouseIdModal(param) {
    markStatus('dataLoading');
    // 查询到园区跟子仓的数据
    const { code, info, msg } = yield getSubWarehouseApi({ warehouseId: param.val });
    if (code === '0') {
      const parkList = Object.values((info.data || []).reduce((result, curr) => {
        const { parkName, parkType } = curr;
        if (parkType !== 0) { //! 过滤掉parktype为0 显示为空的园区
          if (result[parkType]) {
            result[parkType].subWarehouseList.push(curr);
          } else {
            result[parkType] = {
              parkName,
              parkType,
              subWarehouseList: [{ ...curr }],
            };
          }
        }
        return result;
      }, {}));
      yield this.changeData({
        parkListModal: parkList,
        // eslint-disable-next-line max-len
        subWarehouseListModal: parkList.reduce((result, curr) => ([...result, ...curr.subWarehouseList]), []),
      });
      // 重置条件
      if (this.state.editObjVisible === 1) {
        yield this.clearSearchParamsDataModal({ ...param, property: 'warehouseId' });
      }
    } else {
      Modal.error({ title: msg });
    }
  },
  // 改变search园区
  * changePark(param) {
    markStatus('dataLoading');
    const { val } = param;
    if (val.length !== 0) {
      yield this.changeData({
        // eslint-disable-next-line max-len
        subWarehouseList: this.state.parkList
          .filter((item) => val.includes(item.parkType)) // 查找出含有选中园区的子仓
          .reduce((result, curr) => ([...result, ...curr.subWarehouseList]), []),
      });
    } else {
      yield this.changeData({
        // 全选的话就是所有的子仓都符合条件
        // eslint-disable-next-line max-len
        subWarehouseList: this.state.parkList.reduce((result, curr) => ([...result, ...curr.subWarehouseList]), []),
      });
    }
    yield this.clearSearchParamsData({ ...param, property: 'parkTypeList' });
  },
  // 改变modal园区
  * changeParkModal(param) {
    markStatus('dataLoading');
    const { val } = param;
    if (val.length !== 0) {
      yield this.changeData({
        // eslint-disable-next-line max-len
        subWarehouseListModal: this.state.parkListModal
          .filter((item) => val === item.parkType) // 查找出含有选中园区的子仓
          .reduce((result, curr) => ([...result, ...curr.subWarehouseList]), []),
      });
    } else {
      yield this.changeData({
        // 全选的话就是所有的子仓都符合条件
        // eslint-disable-next-line max-len
        subWarehouseListModal: this.state.parkListModal.reduce((result, curr) => ([...result, ...curr.subWarehouseList]), []),
      });
    }
    if (this.state.editObjVisible === 1) {
      yield this.clearSearchParamsDataModal({ ...param, property: 'parkType' });
    }
  },
  // 改变search子仓
  * changeSubWarehouse(param) {
    const clearProps = {};
    clearProps.subWarehouseIdList = param.val;
    yield this.changeSearchParamsData(clearProps);
  },
  // 改变modal子仓
  * changeSubWarehouseModal(param) {
    if (this.state.editObjVisible === 1) {
      const clearProps = {};
      clearProps.subWarehouseId = param.val;
      yield this.changeSearchParamsDataModal(clearProps);
    }
  },
  // 显示弹窗
  * changeModalData({ editObjVisible, editObj }) {
    yield this.changeData({
      editObjVisible,
      editObj: {
        ...this.state.editObj,
        ...editObj,
      },
    });
  },
  * search() {
    if (!this.state.limit.warehouseId) {
      Modal.info({
        title: t('请选择仓库'),
      });
      return;
    }
    markStatus('dataLoading');
    const { warehouseId } = yield 'nav';
    const res = yield getTableList({ ...this.state.limit, warehouseId });
    if (res.code === '0') {
      yield this.changeData({
        ready: true,
        tableList: res.info.data,
        count: res.info.meta.count,
        selectedRows: [],
      });
    } else {
      Modal.error({ title: res.msg });
      yield this.changeData({ ready: true });
    }
  },
  * confirmSaveConfig() {
    let res;
    if (this.state.editObjVisible === 1) {
      markStatus('dataLoading');
      res = yield confirmSaveConfig(this.state.editObj);
    } else if (this.state.editObjVisible === 2) {
      const {
        formalOutput,
        informalOutput,
        keyword,
        receivePlanCapacity,
      } = this.state.editObj;
      const params = {
        id: this.state.selectedRows[0].id,
        formalOutput,
        informalOutput,
        keyword,
        receivePlanCapacity,
      };
      markStatus('dataLoading');
      res = yield confirmSaveEditConfig(params);
    }
    if (res.code === '0') {
      Modal.success({ title: this.state.editObjVisible === 1 ? t('添加成功') : t('修改成功') });
      yield this.changeData({ editObjVisible: 0 });
      yield this.search();
    } else {
      Modal.error({ title: res.msg });
    }
  },
  * deleteConfig() {
    markStatus('dataLoading');
    const { id } = this.state.selectedRows[0];
    const res = yield deleteConfig({ id });
    if (res.code === '0') {
      yield this.search();
      Modal.success({ title: t('删除成功') });
    } else {
      Modal.error({ title: res.msg });
    }
  },
};
