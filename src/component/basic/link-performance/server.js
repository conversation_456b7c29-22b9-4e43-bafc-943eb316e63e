import { sendPostRequest } from '../../../server/common/public';

// 获取仓库
// export const getWarehouseAPI = (param) => sendPostRequest({
//   url: '/warehouse/get_list',
//   param,
// }, process.env.BASE_URI_WMD);

// 根据仓库id 查询子仓和园区信息(级联)
// export const getSubWarehouseAPI = (param) => sendPostRequest({
//   url: '/sub_warehouse/query_park',
//   param,
// }, process.env.BASE_URI_WMD);

// 根据子仓和库区类型查询库区
// export const getAreaAPI = (param) => sendPostRequest({
//   url: '/area/query_area',
//   param,
// });

// 页面接口

// 查询列表数据
export const getTableList = (param) => sendPostRequest({
  url: '/link_output_config/query',
  param,
}, process.env.BASE_URI_WMD);

// 新增
export const confirmSaveConfig = (param) => sendPostRequest({
  url: '/link_output_config/add',
  param,
}, process.env.BASE_URI_WMD);

// 新增
export const confirmSaveEditConfig = (param) => sendPostRequest({
  url: '/link_output_config/modify',
  param,
}, process.env.BASE_URI_WMD);

// 删除
export const deleteConfig = (param) => sendPostRequest({
  url: '/link_output_config/delete',
  param,
}, process.env.BASE_URI_WMD);
