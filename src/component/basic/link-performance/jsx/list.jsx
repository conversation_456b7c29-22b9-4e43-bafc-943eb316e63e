import React from 'react';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { Table, Pagination } from 'shineout';
import styles from '@src/component/style.less';
import { changeSize, getSize } from '../../../../middlewares/pagesize';
import store from '../reducers';

// 表格行选中
const rowSelectHandle = (rows) => {
  store.changeData({ selectedRows: rows });
};

function List(props) {
  const {
    dispatch,
    dataLoading,
    tableList,
    limit: {
      pageNum,
    },
    pageSizeOptions,
    count,
    selectedRows,
  } = props;

  const handlePaginationChange = (page, size) => {
    store.changeLimit({
      pageNum: page,
      pageSize: size,
    });
    dispatch(changeSize(size));
    store.search({
      data: assign({}, props.limit, {
        pageNum: page,
        pageSize: size,
      }),
    });
  };
  const columns = [
    {
      title: t('仓库'),
      width: 120,
      render: 'warehouseName',
    },
    {
      title: t('园区'),
      width: 160,
      // render: 'parkTypeName',
      render: (row) => {
        if (row.parkType === 0) {
          return '';
        }
        return (
          <span>{row.parkTypeName}</span>
        );
      },
    },
    {
      title: t('子仓'),
      width: 180,
      render: 'subWarehouseName',
    },
    {
      title: t('环节'),
      width: 120,
      render: 'linkTypeName',
    },
    {
      title: t('关键字'),
      width: 120,
      render: 'keyword',
    },
    {
      title: t('正式工效能'),
      width: 140,
      render: 'formalOutput',
    },
    {
      title: t('非正式工效能'),
      width: 140,
      render: 'informalOutput',
    },
    {
      title: t('收货规划量'),
      width: 140,
      render: 'receivePlanCapacity',
    },
    {
      title: t('操作时间'),
      width: 140,
      render: 'lastUpdateTime',
    },
    {
      title: t('操作人'),
      width: 140,
      render: 'createUser',
    },
  ];

  return (
    <section className={styles.tableSection}>
      <Table
        bordered
        fixed="both"
        loading={dataLoading === 0}
        data={tableList}
        columns={columns}
        keygen={(r) => JSON.stringify(r)}
        empty={t('暂无数据')}
        size="small"
        style={{ height: '100%' }}
        width={columns.reduce((pre, current) => pre + current.width, 0)}
        value={selectedRows}
        onRowSelect={rowSelectHandle}
      />
      <div
        style={{
          position: 'relative',
          zIndex: 98,
        }}
      >
        <Pagination
          className={styles.rightDownPagination}
          current={pageNum}
          total={count}
          pageSizeList={pageSizeOptions}
          pageSize={getSize()}
          layout={[({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', 'jumper']}
          text={{ page: `${t('条')} / ${t('页')}` }}
          onChange={handlePaginationChange}
        />
      </div>
    </section>
  );
}

List.propTypes = {
  dispatch: PropTypes.func.isRequired,
  dataLoading: PropTypes.number.isRequired,
  tableList: PropTypes.arrayOf(PropTypes.shape).isRequired,
  pageSizeOptions: PropTypes.arrayOf(PropTypes.string),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  limit: PropTypes.shape().isRequired,
  count: PropTypes.number.isRequired,
};

export default List;
