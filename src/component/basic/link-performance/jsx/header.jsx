import React from 'react';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import {
  Form, Card, Button, Select,
} from 'shineout';
import store from '../reducers';
import styles from '../../../style.less';

const inputStyle = {
  width: '158px',
};

function Header(props) {
  const {
    topSearch,
    dataLoading,
    limit,
    parkList,
    subWarehouseList,
    linkList,
  } = props;

  return (
    <Form
      inline
      onSubmit={() => {
        store.changeLimit({ pageNum: 1 });
        store.search({ param: assign({}, limit, { pageNum: 1 }) });
      }}
    >
      <Card
        collapsible
        collapsed={!topSearch}
        onCollapse={() => {
          store.changeData({ topSearch: !topSearch });
        }}
      >
        <Card.Header style={{ padding: '6px 16px' }}>{t('搜索查询')}</Card.Header>
        <Card.Body style={{ padding: 0 }}>
          <div className={styles.header_wrap_view}>
            <div className={styles.inner_list}>
              <span className={styles.labWidth}>
                {t('园区')}
                :
              </span>
              <Select
                keygen="parkName"
                renderItem="parkName"
                datum={{ format: 'parkType' }}
                value={limit.parkTypeList}
                onChange={(val) => {
                  store.changePark({ val });
                }}
                style={inputStyle}
                data={parkList}
                placeholder={t('全部')}
                multiple
                clearable
                compressed
                onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labWidth}>
                {t('子仓')}
                :
              </span>
              <Select
                keygen="id"
                disabled={limit.parkTypeList.length > 1 || limit.parkTypeList.length === 0}
                renderItem="nameZh"
                datum={{ format: 'id' }}
                value={limit.subWarehouseIdList}
                onChange={(val) => {
                  store.changeSubWarehouse({ val });
                }}
                style={inputStyle}
                data={subWarehouseList}
                placeholder={t('全部')}
                multiple
                clearable
                compressed
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </div>
            <div className={styles.inner_list}>
              <span className={styles.labWidth}>
                {t('环节')}
                :
              </span>
              <Select
                data-bind="limit.linkTypeList"
                className={styles.inputWidth}
                placeholder={t('全部')}
                data={linkList}
                datum={{ format: 'dictCode' }}
                keygen="id"
                renderItem="dictNameZh"
                multiple
                clearable
                compressed
                onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </div>
            <div className={styles.inner_list} style={{ marginLeft: '20px' }}>
              <Button
                loading={dataLoading === 0}
                type="primary"
                htmlType="submit"
                className={styles.searchButton}
              >
                {t('搜索')}
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>
    </Form>
  );
}

Header.propTypes = {
  topSearch: PropTypes.bool.isRequired,
  dataLoading: PropTypes.number.isRequired,
  limit: PropTypes.shape().isRequired,
  parkList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  linkList: PropTypes.arrayOf(PropTypes.shape()),
};

export default Header;
