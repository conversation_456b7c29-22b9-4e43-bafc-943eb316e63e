import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
// import Icon from '@shein-components/Icon';
import {
  Button, Modal, Input, Select, Rule,
} from 'shineout';
import styles from '@src/component/style.less';
import store from '../reducers';
import style from '../style.css';

const rules = Rule();

function Handle(props) {
  const {

    dataLoading,
    editObjVisible,
    editObj,
    parkListModal,
    subWarehouseListModal,
    linkList,
    selectedRows,
    currentWarehouseList,
  } = props;

  const inputStyle = {
    width: '200px',
  };

  return (
    <div className={[styles.handle, 'handleSection'].join(' ')} style={{ marginTop: 0 }}>
      {/* 新增 */}
      <Button
        type="primary"
        size="default"
        onClick={() => {
          store.changeModalData({
            editObjVisible: 1,
            editObj: {
              // id: '', // 库容主键id
              warehouseId: '', // 仓库id不能为空
              parkType: '', // 园区[id]
              subWarehouseId: '', // 子仓[id]
              linkType: '', // 环节
              formalOutput: '',
              informalOutput: '',
              keyword: '',
              receivePlanCapacity: '',
            },
          });
        }}
      >
        {t('新增')}
      </Button>
      {/* 编辑 */}
      <Button
        disabled={selectedRows.length !== 1}
        type="primary"
        size="default"
        onClick={() => {
          store.changeModalData({
            editObjVisible: 2,
            editObj: {
              // id: selectedRows[0].id, // 库容主键id
              warehouseId: selectedRows[0].warehouseId, // 仓库id不能为空
              parkType: selectedRows[0].parkType, // 园区[id]
              subWarehouseId: selectedRows[0].subWarehouseId, // 子仓[id]
              linkType: selectedRows[0].linkType, // 环节
              formalOutput: selectedRows[0].formalOutput,
              informalOutput: selectedRows[0].informalOutput,
              keyword: selectedRows[0].keyword,
              receivePlanCapacity: selectedRows[0].receivePlanCapacity,
            },
          });
          store.changeWarehouseIdModal({ val: selectedRows[0].warehouseId });
        }}
      >
        {t('编辑')}
      </Button>
      {/* 删除 */}
      <Button
        type="primary"
        size="default"
        disabled={selectedRows.length !== 1}
        onClick={() => {
          Modal.confirm({
            title: t('确认删除?'),
            onOk: () => {
              store.deleteConfig();
            },
            text: { ok: t('确定'), cancel: t('取消') },
          });
        }}
      >
        {t('删除')}
      </Button>

      {/* 弹窗 */}
      <Modal
        maskCloseAble={null}
        width={980}
        visible={editObjVisible}
        title={editObjVisible === 1 ? t('新增') : t('编辑')}
        onClose={() => store.changeData({ editObjVisible: 0 })}
        footer={[
          <Button
            onClick={() => { store.changeData({ editObjVisible: 0 }); }}
          >
            {t('取消')}
          </Button>,
          <Button
            loading={!dataLoading}
            type="primary"
            disabled={
                !((editObj.warehouseId
                  && editObj.linkType !== 1
                  && editObj.linkType
                  && editObj.formalOutput
                  && editObj.informalOutput
                  && editObj.keyword
                  && editObj.keyword.length <= 20
                )
                  || (editObj.warehouseId
                    && editObj.linkType
                    && editObj.linkType === 1
                    && editObj.formalOutput
                    && editObj.informalOutput
                    && editObj.keyword
                    && editObj.keyword.length <= 20
                    && editObj.receivePlanCapacity
                  ))
            }
            onClick={() => {
              if (editObjVisible === 1) {
                store.confirmSaveConfig();
              } else {
                Modal.confirm({
                  title: t('提示'),
                  content: <span style={{ color: '#ff0000' }}>{t('请谨慎修改')}</span>,
                  text: { ok: t('确认'), cancel: t('取消') },
                  onOk: () => {
                    store.confirmSaveConfig();
                  },
                  onCancel: () => {
                    store.changeData({ editObjVisible: 0 });
                  },
                });
              }
            }}
          >
            {t('确认')}
          </Button>,
        ]}
      >
        <p className={style.dotted_header}>{t('基础信息')}</p>
        <section>
          <div className={style.block_label}>
            <span className={`${style.add_label} ${style.required}`}>
              {t('仓库')}
              :
            </span>
            <Select
              keygen="id"
              renderItem="nameZh"
              datum={{ format: 'id' }}
              value={editObj.warehouseId}
              disabled={editObjVisible === 2}
              onFilter={(text) => (v) => v.nameZh.toLowerCase().indexOf(text.toLowerCase()) > -1}
              onChange={(val) => {
                store.changeWarehouseIdModal({ val });
              }}
              style={inputStyle}
              data={currentWarehouseList}
              placeholder={t('请选择')}
            />
          </div>
          <div className={style.block_label}>
            <span className={`${style.add_label}`}>
              {t('园区')}
              :
            </span>
            <Select
              keygen="parkType"
              renderItem="parkName"
              datum={{ format: 'parkType' }}
              value={editObj.parkType}
              disabled={editObjVisible === 2}
              onChange={(val) => {
                store.changeParkModal({ val });
              }}
              style={inputStyle}
              data={parkListModal}
              placeholder={t('请选择')}
              onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            />
          </div>
          <div className={style.block_label}>
            <span className={`${style.add_label}`}>
              {t('子仓')}
              :
            </span>
            <Select
              keygen="id"
              renderItem="nameZh"
              datum={{ format: 'id' }}
              value={editObj.subWarehouseId}
              disabled={editObjVisible === 2 || editObj.parkType === ''}
              onChange={(val) => {
                store.changeSubWarehouseModal({ val });
              }}
              style={inputStyle}
              data={subWarehouseListModal}
              placeholder={t('请选择')}
              onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            />
          </div>
          <div className={style.block_label}>
            <span className={`${style.add_label} ${style.required}`}>
              {t('业务环节')}
              :
            </span>
            <Select
              datum={{ format: 'dictCode' }}
              keygen="id"
              renderItem="dictNameZh"
              value={editObj.linkType}
              disabled={editObjVisible === 2}
              onChange={(val) => {
                store.changeConfigObjData({ linkType: val });
                if (val !== 1) {
                  store.changeConfigObjData({ receivePlanCapacity: 0 });
                }
              }}
              style={inputStyle}
              data={linkList}
              placeholder={t('请选择')}
              onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            />
          </div>
        </section>
        <p className={style.dotted_header}>{t('效能配置')}</p>
        <section style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
          <div>
            <div style={{ marginBottom: '15px' }}>
              <span className={`${style.add_label} ${style.required}`}>
                {t('正式工效能')}
                :
              </span>
              <Input.Number
                rules={[rules.required, rules.max(999999), rules.min(1)]}
                max={999999}
                min={1}
                digits={0}
                hideArrow
                coin
                tip="1~999,999"
                popover="top-left"
                data-bind="editObj.formalOutput"
                style={{ width: '80px' }}
              />
              {t('件')}
            </div>
            <div>
              <span className={`${style.add_label} ${style.required}`}>
                {t('非正式工效能')}
                :
              </span>
              <Input.Number
                rules={[rules.required, rules.max(999999), rules.min(1)]}
                max={999999}
                min={1}
                digits={0}
                hideArrow
                coin
                tip="1~999,999"
                popover="top-left"
                data-bind="editObj.informalOutput"
                style={{ width: '80px' }}
              />
              {t('件')}
            </div>
          </div>
          <div>
            <div style={{ marginBottom: '15px' }}>
              <span className={`${style.add_label} ${style.required}`} style={{ width: '135px' }}>
                {t('关键字')}
                :
              </span>
              <Input
                type="text"
                rules={[rules.required, rules.max(20), rules.min(1)]}
                popover="top-left"
                data-bind="editObj.keyword"
                style={{ width: '110px' }}
              />
            </div>
            <div data-if={editObj.linkType === 1}>
              <span className={`${style.add_label} ${style.required}`} style={{ width: '135px' }}>
                {t('收货规划量(单日)')}
                :
              </span>
              <Input.Number
                rules={[rules.required, rules.max(999999999), rules.min(1)]}
                max={999999999}
                min={1}
                digits={0}
                hideArrow
                coin
                tip="1~999,999,999"
                popover="top-left"
                data-bind="editObj.receivePlanCapacity"
                style={{ width: '110px', minWidth: '110px' }}
              />
            </div>
          </div>

        </section>
      </Modal>
    </div>
  );
}

Handle.propTypes = {
  editObjVisible: PropTypes.number,
  dataLoading: PropTypes.number.isRequired,
  editObj: PropTypes.shape().isRequired,
  parkListModal: PropTypes.arrayOf(PropTypes.shape()),
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  linkList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  subWarehouseListModal: PropTypes.arrayOf(PropTypes.shape()),
  currentWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
