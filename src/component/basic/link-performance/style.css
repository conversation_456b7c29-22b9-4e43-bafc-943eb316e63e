.modalCont {
  padding: 10px 0 20px 50px;
}
.modalCont>div {
  margin-bottom: 20px;
}


.input {
  width: 220px;
}
.search_block {
  display: inline-block;
  margin-bottom: 20px;
  padding-left: 20px;
}
.search_btn {
  display: inline-block;
  margin-bottom: 10px;
  padding-left: 20px;
}
.search_label {
  padding-right: 10px;
}
.block_label {
  display: inline-block;
  padding: 0 10px 10px 0;
}
.add_label {
  display: inline-block;
  padding-right: 10px;
  width: 105px;
  text-align: right;
}
.required::before {
  content: '*';
  color: #ff0000;
}
.handle {
  display: inline-block;
  vertical-align: top;
  margin: 0 20px;
}
.mr_b_14 {
  margin-bottom: 14px;
}
.dotted_header {
  border-bottom: #d0d0d0 1px dashed;
  padding-bottom: 5px;
  margin-bottom: 10px;
}
.mix_group {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.mix_group_checkbox {
  margin-left: 10px;
  padding: 4px 6px;
  border: 1px solid rgb(212,210,210)
}