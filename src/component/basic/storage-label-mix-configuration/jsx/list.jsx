import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Table, Button, Tag, Popover,
} from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
// import style from '../style.css';
import store from '../reducers';

/**
 * @description 混装组合的列表展示
 * @param {array} origin [[]], 复杂数组
 * @param {number} maxItem 展示最大项目数
 * @returns {Element}
 */
const detailVoListCabinet = (origin, maxItem) => {
  // 根据 id 构造数据
  // 判断是否展示 Popover 出现... 才展示。
  let showPopover = false;
  const combination = [];

  origin.forEach((item) => {
    const index = Number(item.serialNumber) - 1;
    if (combination[index] === undefined) {
      combination[index] = [];
    }
    combination[index].push(item.storageTagName);
  });
  combination.forEach((item) => {
    if (item.length > maxItem && !showPopover) {
      showPopover = true;
    }
  });
  if (combination.length > 3 && !showPopover) {
    showPopover = true;
  }

  return (
    <div>
      {showPopover
        && (
          <Popover
            type="info"
            position="left"
            style={{ padding: 5 }}
            background="#fff"
            border="#fff"
          >
            {combination.map((items, i) => (
              <div style={{ marginTop: 3 }}>
                <span>{t('第{}组:', i + 1)}</span>
                <span>{items.map((tag) => (<Tag>{tag}</Tag>))}</span>
              </div>
            ))}
          </Popover>
        )}
      {/* 只展示3组 */}
      {combination.slice(0, 3).map((items, i) => (
        <div style={{ marginTop: 3 }}>
          <span>{t('第{}组:', i + 1)}</span>
          <span>
            {items.map((tag, tagIndex) =>
              // eslint-disable-next-line
              (tagIndex < maxItem
                ? (<Tag>{tag}</Tag>)
                : tagIndex === maxItem ? (<span>...</span>) : null))}
          </span>
        </div>
      ))}
      {combination && combination.length > 3 && (
        <div style={{ marginTop: 3 }}>...</div>
      )}
    </div>
  );
};

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      recordId,
      recordVisible,
    } = this.props;

    const columns = [
      {
        title: t('ID'),
        width: 140,
        render: 'configId',
      },
      {
        title: t('仓库'),
        width: 140,
        render: 'warehouseName',
      },
      {
        title: t('园区'),
        width: 140,
        render: 'packTypeName',
      },
      {
        title: t('混装组合'),
        width: 310,
        render: (d) => detailVoListCabinet(d.detailVoList, 3),
      },
      {
        title: t('状态'),
        width: 100,
        render: (d) => {
          if (d.isEnable) {
            return t('启用');
          }
          return t('禁用');
        },
      },
      {
        title: t('更新时间/更新人'),
        width: 240,
        render: (d) => (
          <span>
            {d.lastUpdateTime}
            &nbsp;
            {d.updateUserName}
          </span>
        ),
      },
      {
        title: t('操作'),
        width: 240,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              text
              type="primary"
              className={globalStyles.tableTextButton}
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.configId,
              })}
            >
              {t('操作日志')}
            </Button>
            <Button
              text
              type="primary"
              className={globalStyles.tableTextButton}
              disabled={!loading}
              onClick={() => {
                store.openModal({
                  type: 'edit',
                  data: record,
                });
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              text
              type="danger"
              className={globalStyles.tableTextButton}
              disabled={!loading}
            >
              <Popover.Confirm
                position="top"
                onOk={() => store.deleteId(record.configId)}
                type="warning"
                okType="primary"
              >
                {t('是否确定删除?')}
              </Popover.Confirm>
              {t('删除')}
            </Button>
            <Button
              text
              className={globalStyles.tableTextButton}
              type={record.isEnable ? 'danger' : 'primary'}
              disabled={!loading}
            >
              <Popover.Confirm
                type="warning"
                okType="primary"
                text={{ ok: t('确认'), cancel: t('取消') }}
                onOk={() => {
                  store.enableId({
                    isEnable: record.isEnable ? 0 : 1,
                    id: record.configId,
                  });
                }}
              >
                {record.isEnable ? t('是否确定禁用?') : t('是否确定启用?')}
              </Popover.Confirm>
              {record.isEnable ? t('禁用') : t('启用')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="configId"
            empty={t('暂无数据')}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              className: globalStyles.pagination,
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'STORAGE_TAG_CONFIG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}
List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};
export default List;
