import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import FilterSearchSelect from '@public-component/select/filter-search-select';
import store, { defaultLimit } from '../reducers';
import Modal from './modal';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      parkTypeList,
      statusList,
    } = this.props;

    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Select
            label={t('园区')}
            name="parkTypeList"
            data={parkTypeList}
            keygen="parkType"
            format="parkType"
            placeholder={t('全部')}
            renderItem="parkName"
            onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('状态')}
            name="isEnable"
            data={statusList}
            keygen="value"
            format="value"
            placeholder={t('全部')}
            renderItem="label"
            onFilter={(text) => (d) => d.label.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <FilterSearchSelect
            label={t('更新人')}
            name="updateUserName"
            placeholder={t('请输入')}
            clearable
          />
        </SearchAreaContainer>
        <Modal {...this.props} />
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  parkTypeList: PropTypes.arrayOf(PropTypes.shape),
  statusList: PropTypes.arrayOf(PropTypes.shape),
};

export default Header;
