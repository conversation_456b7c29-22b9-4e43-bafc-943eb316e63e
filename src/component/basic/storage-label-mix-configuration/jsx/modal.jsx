import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Modal, Button, Select, Radio, Checkbox,
} from 'shineout';
import Icon from '@shein-components/Icon';
import style from '../style.less';
import store, { MODAL_VISIBLE_CLOSE, MODAL_VISIBLE_ADD, MODAL_VISIBLE_EDIT } from '../reducers';

class ConfigModal extends React.Component {
  render() {
    const {
      loading,
      editObjVisible,
      editObj,
      statusList,
      warehouseList,
      editObjParkTypeList,
      editStorageTagList,
      canAdd,
      canDelete,
    } = this.props;

    const { serialNumberAndStorageTagVoList } = editObj;

    return (
      <Modal
        maskCloseAble={false}
        visible={editObjVisible !== MODAL_VISIBLE_CLOSE}
        title={editObjVisible === MODAL_VISIBLE_ADD ? t('新增存储标签混装配置') : t('编辑存储标签混装配置')}
        width={680}
        bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
        onClose={() => store.changeData({ editObjVisible: MODAL_VISIBLE_CLOSE })}
        footer={[
          <Button
            onClick={() => {
              store.changeData({ editObjVisible: MODAL_VISIBLE_CLOSE });
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            loading={!loading}
            disabled={
              !(
                editObj.isEnable !== undefined
                && editObj.warehouseId !== undefined
                && editObj.parkType !== ''
                && serialNumberAndStorageTagVoList
                && serialNumberAndStorageTagVoList.every((d) => d.length)
              )
            }
            type="primary"
            onClick={() => {
              Modal.confirm({
                title: t('提示'),
                content: <span style={{ color: '#ff0000' }}>{t('本次修改内容将影响到操作员下一次领取任务的操作,请谨慎修改。')}</span>,
                text: { ok: t('确认'), cancel: t('取消') },
                onOk: () => {
                  store.confirmSaveConfig();
                },
              });
            }}
          >
            {t('保存')}
          </Button>,
        ]}
      >
        <div className={style.modalWrapper}>
          <div className={style.modalWrapperLabel}>
            {t('基础信息')}
          </div>
          <div className={style.flex}>
            <div className={style.inner_list}>
              <span className={style.labWidth}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('状态')}
              </span>
              <Radio.Group
                keygen="id"
                data-bind="editObj.isEnable"
              >
                {(statusList || []).map((d) => (
                  <Radio key={d.value} htmlValue={d.value}>
                    {d.label}
                  </Radio>
                ))}
              </Radio.Group>
            </div>
          </div>
          <div className={style.modalWrapperLabel}>
            {t('类型配置')}
          </div>
          <div className={style.flex}>
            <div className={style.inner_list}>
              <span className={style.labWidth}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('仓库')}
              </span>
              <Select
                data-bind="editObj.warehouseId"
                data={warehouseList}
                disabled={editObjVisible === MODAL_VISIBLE_EDIT}
                keygen="id"
                format="id"
                renderItem="nameZh"
                width={220}
                absolute
                renderUnmatched={(r) => r.nameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                onChange={(value) => {
                  store.changeWarehouse({
                    warehouseId: value,
                    type: 'modal',
                  });
                }}
              />
            </div>
            <div className={style.inner_list}>
              <span className={style.labWidth}>
                <span style={{ color: 'red', marginRight: '5px' }}>*</span>
                {t('园区')}
              </span>
              <Select
                data-bind="editObj.parkType"
                data={editObjParkTypeList}
                disabled={editObjVisible === MODAL_VISIBLE_EDIT}
                keygen="parkType"
                format="parkType"
                renderItem="parkName"
                width={220}
                absolute
                renderUnmatched={(r) => r.parkName || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
                onFilter={(text) => (d) => d.parkName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </div>
          </div>
          <div className={style.modalWrapperLabel}>
            <span style={{ color: 'red', marginRight: '5px' }}>*</span>
            {t('混装组合')}
          </div>
          <section>
            {
              serialNumberAndStorageTagVoList.map((ele, i) => (
                <div className={style.mix_group}>
                  <div style={{ flexShrink: '0' }}>{t('第{}组', i + 1)}</div>
                  <div className={style.mix_group_checkbox}>
                    {editStorageTagList.map((goodStoreTag) => (
                      <Checkbox
                        checked={goodStoreTag.bindGroup !== -1}
                        disabled={
                          !(goodStoreTag.bindGroup === -1 || goodStoreTag.bindGroup === i)
                        }
                        key={goodStoreTag.id}
                        htmlValue={goodStoreTag.id}
                        onChange={(val) => {
                          let selectedList = ele.slice();
                          // 判断是否选中
                          if (val !== undefined) {
                            selectedList.push(goodStoreTag.id);
                          } else {
                            selectedList = selectedList.filter((id) => id !== goodStoreTag.id);
                          }
                          store.chagneEditGoodsStoreTypeList({
                            selectedList,
                            index: i,
                            type: 'change',
                          });
                        }}
                      >
                        <span style={
                          !(goodStoreTag.bindGroup === -1 || goodStoreTag.bindGroup === i)
                            ? { color: 'rgb(173, 177, 188)' } : {}
                        }
                        >
                          {goodStoreTag.name}
                        </span>
                      </Checkbox>
                    ))}
                  </div>
                  <div>
                    <Button
                      type="primary"
                      text
                      loading={!loading}
                      disabled={!canAdd}
                      onClick={() => {
                        store.chagneEditGoodsStoreTypeList({
                          selectedList: [],
                          index: i + 1,
                          type: 'add',
                        });
                      }}
                    >
                      <Icon name="plus-o" />
                    </Button>
                    <Button
                      style={{ marginLeft: 0 }}
                      type="danger"
                      text
                      loading={!loading}
                      disabled={!canDelete}
                      onClick={() => {
                        store.chagneEditGoodsStoreTypeList({
                          selectedList: [],
                          index: i,
                          type: 'delete',
                        });
                      }}
                    >
                      <Icon name="minus-o" />
                    </Button>
                  </div>
                </div>
              ))
            }
          </section>
        </div>
      </Modal>
    );
  }
}

ConfigModal.propTypes = {
  loading: PropTypes.number,
  editObjVisible: PropTypes.number,
  editObj: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  editObjParkTypeList: PropTypes.arrayOf(PropTypes.shape),
  statusList: PropTypes.arrayOf(PropTypes.shape),
  editStorageTagList: PropTypes.arrayOf(PropTypes.shape()),
  canAdd: PropTypes.bool,
  canDelete: PropTypes.bool,
};

export default ConfigModal;
