import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import ContainerPage from '@public-component/search-queries/container';
import store from './reducers';
import Header from './jsx/header';
import List from './jsx/list';

class Container extends Component {
  componentDidMount() {
    const { skuCode, receiptNo } = this.props?.match?.query || {};

    store.init({
      skuCode,
      receiptNo,
    });
  }

  render() {
    return (
      <ContainerPage>
        <Header {...this.props} />
        <List {...this.props} />
      </ContainerPage>
    );
  }
}

Container.propTypes = {
  match: PropTypes.shape(),
};

export default i18n(Container);
