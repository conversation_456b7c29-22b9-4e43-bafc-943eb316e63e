import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
    } = this.props;

    const columns = [
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 100,
      },
      {
        title: t('SKU'),
        render: 'skuCode',
        width: 100,
      },
      {
        title: t('业务单号'),
        render: 'businessCode',
        width: 100,
      },
      {
        title: t('变动类型'),
        render: 'changeTypeName',
        width: 100,
      },
      {
        title: t('来源单号'),
        render: 'originCode',
        width: 100,
      },
      {
        title: t('来源RI发票号'),
        render: 'invoiceNo',
        width: 120,
      },
      {
        title: t('本次变动数量'),
        render: 'changeNum',
        width: 120,
      },
      {
        title: t('货位号'),
        render: 'location',
        width: 100,
      },
      {
        title: t('供应商ID'),
        render: 'supplierId',
        width: 120,
      },
      {
        title: t('变动时间'),
        render: 'lastUpdateTime',
        width: 100,
      },
    ];

    return (
      <section className={[styles.tableSection, styles.listArea].join(' ')}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
};

export default List;
