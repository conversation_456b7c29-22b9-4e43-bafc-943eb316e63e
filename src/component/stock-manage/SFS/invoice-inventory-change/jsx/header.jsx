import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import {
  Input, Select, Rule,
} from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';
import styles from '../style.less';

const rule = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      // 业务单号 来源单号 来源RI发票号 有值时，时间不必填
      if (formData.businessCode || formData.originCode || formData.invoiceNo) {
        callback(true);
        return;
      }
      if (!formData.startTime || !formData.endTime) {
        callback(new Error(t('开始时间或结束时间必选')));
      }
      // 时间范围不能超过1天
      if (moment(formData.endTime)
        .customDiff(moment(formData.startTime), 'days', true) > 1) {
        callback(new Error(t('开始时间和结束时间不能超过{}天', 1)));
      }
      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      changeTypeList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 90 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
        >
          <Input
            label={t('业务单号')}
            name="businessCode"
            placeholder={t('请输入')}
            maxLength={50}
            alwaysVisible
            required
            clearable
          />
          <Input
            label={t('SKU')}
            name="skuCode"
            placeholder={t('请输入')}
            maxLength={50}
            clearable
          />
          <Input
            label={t('来源单号')}
            name="originCode"
            placeholder={t('请输入')}
            maxLength={50}
            alwaysVisible
            required
            clearable
          />
          <Input
            label={t('来源RI发票号')}
            name="invoiceNo"
            placeholder={t('请输入')}
            maxLength={50}
            alwaysVisible
            required
            clearable
          />
          <Select
            label={t('变动类型')}
            name="changeType"
            data={changeTypeList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('请选择')}
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <DateRangePicker
            label={t('变动时间')}
            name={['startTime', 'endTime']}
            required
            alwaysVisible
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[rule.timeRange()]}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  changeTypeList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
