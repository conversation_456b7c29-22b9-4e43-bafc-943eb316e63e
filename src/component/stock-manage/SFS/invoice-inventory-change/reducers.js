import { markStatus } from 'rrc-loader-helper';
import moment from 'moment';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import { paramTrim, clearEmpty } from '@src/lib/deal-func';
import { getListAPI } from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  businessCode: '', // 业务单号
  skuCode: '', // SKU
  originCode: '', // 来源单号
  invoiceNo: '', // 来源RI发票号
  changeType: '', // 变动类型
  startTime: moment().format('YYYY-MM-DD 00:00:00'), // 变动开始时间
  endTime: moment().format('YYYY-MM-DD 23:59:59'), // 变动结束时间
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  changeTypeList: [], // 变动类型 下拉数据
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init({
    skuCode = undefined,
    receiptNo = undefined,
  }) {
    const [selectData] = yield Promise.all([
      dictSelect({ catCode: ['SFS_CHANGE_TYPE'] }),
    ]);

    if (selectData.code === '0') {
      yield this.changeData({
        changeTypeList: selectData.info.data.find((item) => item.catCode === 'SFS_CHANGE_TYPE').dictListRsps || [],
      });
    } else {
      handleListMsg([selectData]);
    }

    if (skuCode) {
      yield this.changeLimitData({
        skuCode,
        originCode: receiptNo,
        startTime: '',
        endTime: '',
      });
      yield this.search();
    }
  },

  /**
   * 搜索
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const { warehouseId } = yield 'nav';
    const param = {
      ...limit,
      warehouseId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(clearEmpty(paramTrim(param), [0, '0', false]));
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
};
