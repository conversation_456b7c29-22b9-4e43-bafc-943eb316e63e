import { sendPostRequest } from '@src/server/common/public';

// 查询
export const getListAPI = (param) => sendPostRequest({
  url: '/out/inventory_check/query_package_goods',
  param,
}, process.env.ORS_FRONT);

// 查询预占优先级
export const queryInventoryAreaAPI = (param) => sendPostRequest({
  url: '/out/inventory_check/query_inventory_area',
  param,
}, process.env.ORS_FRONT);

// 库存分配池子信息查询
export const queryInventoryAllocatePoolAPI = (param) => sendPostRequest({
  url: '/out/inventory_check/query_inventory_allocate_pool',
  param,
}, process.env.ORS_FRONT);

export const queryPackageInfoAPI = (param) => sendPostRequest({
  url: '/out/inventory_check/query_package_info',
  param,
}, process.env.ORS_FRONT);

// 智能波次匹配规则
export const queryAbcWellenRullAPI = (param) => sendPostRequest({
  url: '/out/inventory_check/query_abc_wellen_pulling_rule',
  param,
}, process.env.ORS_FRONT);
