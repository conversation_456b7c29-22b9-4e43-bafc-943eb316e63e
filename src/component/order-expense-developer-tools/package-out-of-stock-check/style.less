/* list.jsx
---------------------------------------------------------------- */
.container {
    background-color: #fff;
    padding: 12px;
}

.itemTitle {
    margin: 15px 0 10px 0;
    font-size: 14px;
    font-weight: bold;
}


.infoWrapper {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;

    td {
        height: 40px;
        min-width: 100px;
        border: 1px solid #dee0e5;
        word-break: break-word;
        word-wrap: break-word;
        padding: 5px;
    }

    .label{
        width: 10%;
    }

    .value {
        width: 15%;
    }
}

.operationWrap {
    display: flex;
    flex-direction: column;
    align-items: center;

    a {
        color: #197afa;
        padding: 5px 8px;
    }
}

.highlight {
    td {
        background-color: yellow;
    }
}