import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Modal } from 'shineout';
import { handleTablePros } from '@src/lib/deal-func';
import { Link } from 'react-router-dom';
import componetStyles from '@src/component/style.less';
import classnames from 'classnames';
import { getWarehouseId } from '@src/lib/dealFunc';
import styles from '../style.less';
import store from '../reducers';
import PackageInfo from './package-info';
import PoolDetailModal from './pool-detail-modal';
import WaveRuleModal from './wave-rule-modal';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      isModalShow,
      preemptionPriorityInfo,
      inventoryAllocatePoolInfo,
      limit: { packageNo },
      modalLoading,
      poolDetailModalVisible,
      waveRuleModalVisible,
      waveRuleList,
    } = this.props;

    const columns = [
      {
        title: t('跨本标识'),
        render: 'areaFlagName',
        width: 100,
      },
      {
        title: t('订单号'),
        render: 'billNo',
        width: 180,
      },
      {
        title: t('order_goods_id'),
        render: 'orderGoodsId',
        width: 180,
      },
      {
        title: t('sku'),
        width: 180,
        render: 'skuCode',
      },
      {
        title: t('skc'),
        width: 140,
        render: 'skcCode',
      },
      {
        title: t('商品尺码'),
        width: 140,
        render: 'goodsAttr',
      },
      {
        title: t('特殊标识'),
        width: 100,
        render: 'specialMarkName',
      },
      {
        title: t('状态'),
        width: 80,
        render: 'statusName',
      },
      {
        title: t('库存ID'),
        width: 180,
        render: 'inventoryId',
      },
      {
        title: t('最后更新时间'),
        width: 180,
        render: 'lastUpdateTime',
      },
      {
        title: t('操作'),
        width: 200,
        render: (r) => (
          <div className={styles.operationWrap}>
            <Link href to={`/in-warehouse/replenishment/list/${getWarehouseId()}/${packageNo}/${r.skuCode}`}>
              {t('查看补货情况')}
            </Link>
            <Button
              type="link"
              onClick={() => {
                store.queryInventoryArea({ skuCode: r.skuCode, skcCode: r.skcCode });
                store.changeData({ isModalShow: true });
              }}
            >
              {t('查看预占优先级')}
            </Button>
            <Link href to={`/stock-manage/inventory-change/${getWarehouseId()}/${r.inventoryId}/${r.skuCode}`}>
              {t('查看库存变动')}
            </Link>
            {r.specialMarkName?.includes(t('定制')) && (
              <Link href to={`/stock-manage/stock-multiple-query/${r.orderGoodsId}`}>
                {t('查看定制商品库存')}
              </Link>
            )}
          </div>
        ),
      },
    ];

    const areaColumns = [{
      title: t('级别'),
      width: 200,
      render: 'priority',
    }, {
      title: t('库存区域'),
      width: 200,
      render: 'areaDimensionName',
    }, {
      title: t('跨本标识'),
      width: 200,
      render: 'areaFlagName',
    }, {
      title: t('库存数量'),
      width: 200,
      render: 'inventoryNum',
    }];

    return (
      <div className={styles.container}>
        <PackageInfo {...this.props} />
        <div className={classnames(styles.itemWrapper)}>
          <div className={styles.itemTitle}>{t('商品信息')}</div>
          <Table
            {...handleTablePros(columns)}
            style={{ maxHeight: 340 }}
            loading={!loading}
            data={list}
            keygen="orderGoodsId"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: componetStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </div>
        <Modal
          title={t('sku预占优先级信息')}
          visible={isModalShow}
          onClose={() => store.changeData({ isModalShow: false, preemptionPriorityInfo: {} })}
        >
          <div style={{ marginBottom: 10 }}>
            <span>{t('总库存数量：')}</span>
            <span>{preemptionPriorityInfo.allNum || 0}</span>
            ，
            <span>{t('拣货区数量：')}</span>
            <span>{preemptionPriorityInfo.pickAreaNum || 0}</span>
            ，
            <span>{t('备货区数量：')}</span>
            <span>{preemptionPriorityInfo.storeAreaNum || 0}</span>
          </div>
          <Table
            style={{ maxHeight: 290 }}
            columns={areaColumns}
            data={preemptionPriorityInfo.areaList || []}
            loading={!modalLoading}
          />
        </Modal>
        <PoolDetailModal
          visible={poolDetailModalVisible}
          list={inventoryAllocatePoolInfo}
          onClose={() => {
            store.changeData({
              poolDetailModalVisible: false,
            });
          }}
          onClickRange={(row) => {
            store.changeData({
              inventoryAllocatePoolInfo: inventoryAllocatePoolInfo.map((v) => ({ ...v, expand: v.id === row.id ? !v.expand : v.expand })),
            });
          }}
        />
        <WaveRuleModal
          visible={waveRuleModalVisible}
          list={waveRuleList}
          onClose={() => {
            store.changeData({
              waveRuleModalVisible: false,
            });
          }}
        />
      </div>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  pageInfo: PropTypes.shape(),
  isModalShow: PropTypes.bool,
  preemptionPriorityInfo: PropTypes.shape(),
  inventoryAllocatePoolInfo: PropTypes.arrayOf(PropTypes.shape()),
  limit: PropTypes.shape(),
  modalLoading: PropTypes.bool,
  poolDetailModalVisible: PropTypes.bool,
  waveRuleModalVisible: PropTypes.bool,
  waveRuleList: PropTypes.arrayOf(PropTypes.shape()),
};

export default List;
