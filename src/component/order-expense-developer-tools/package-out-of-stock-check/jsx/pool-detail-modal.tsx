import React, { useEffect, useCallback } from 'react';
import { Modal, Table, Button } from 'shineout';
import { t } from '@shein-bbl/react';
import componetStyles from '@src/component/style.less';
import { ColumnItem } from 'shineout/lib/Table/Props';
import styles from '../style.less';

interface IScopeItem {
  /** 开始时间 */
  beginTime?:number;
  /** 结束时间 */
  closeTime?:number;
  /** 名称(渠道组/预设波次国家线) */
  scopeName?:string[];
  /** 订单池范围类型 */
  scopeType?:string;
  /** 订单池范围类型名称 */
  scopeTypeName?:string;
  /** 数值(渠道组/预设波次国家线) */
  scopeValue?:string[];
}

type IScope = IScopeItem[];

interface IInfoItem {
  /** 达成件数 */
  allocateComplete?:number;
  /** 是否触发达成件数为已处理量 */
  allocateCompleteType?:boolean;
  /** 分配排序方法 */
  allocateSortMethod?:string;
  /** 分配排序方法 */
  allocateSortMethodName?:string;
  /** 目标件数 */
  allocateTarget?:number;
  /** 单次分配件数上限 */
  allocateUpperOnce?:number;
  /** 特殊规则生效开始时间 */
  enableBeginTime?:string;
  /** 特殊规则生效结束时间 */
  enableCloseTime?:string;
  /** 状态,0:停用,1:启用 */
  enabled?:boolean;
  /** ID */
  id?:number;
  /** 是否匹配 */
  isMatch?:boolean;
  /** 是否触发ORS兜底,0:否,1:是 */
  isOrsUse?:boolean;
  /** 是否触发部分预占,0:否,1:是 */
  isPartOccupy?:boolean;
  /** 是否触发补货查询,0:否,1:是 */
  isReplenish?:boolean;
  /** 最后一次更新时间 */
  lastUpdateTime?:string;
  /** 兜底生效开始时间 */
  orsUseBeginTime?:string;
  /** 兜底生效结束时间 */
  orsUseCloseTime?:string;
  /** 规则编码 */
  ruleCode?:string;
  /** 规则优先级 */
  rulePriority?:string;
  /** 订单池范围 */
  scope?:IScope;
  expand?:boolean;
  scopeName?:string[];
}

interface Props {
  visible?: boolean;
  list?: IInfoItem[];
  onClose: () => void;
  onClickRange: (row: IInfoItem) => void;
}

const rowClassName = (d: IInfoItem) => {
  if (d.isMatch) return styles.highlight;
  return componetStyles.borderInner;
};

function PoolDetailModal(props: Props) {
  const {
    visible, list, onClose, onClickRange,
  } = props;
  const loadData = useCallback(async () => {
  }, []);
  useEffect(() => {
    loadData();
  }, []);
  const columns: ColumnItem<IInfoItem>[] = [{
    title: t('优先级'),
    render: 'rulePriority',
    width: 200,
  }, {
    title: t('编码'),
    render: 'ruleCode',
    width: 200,
  }, {
    title: t('订单池范围'),
    width: 250,
    render: (row: IInfoItem) => row.scope?.map((e, index) => {
      // 范围条件1：xx<CPT<xx
      // 范围条件2：xx<订单流入时间<xx
      // 范围条件3：渠道组
      // 范围条件4：预设波次国家线
      // 范围条件5：其他
      // 范围条件8: 目的地
      let renderItem: React.ReactNode;
      switch (Number(e.scopeType)) {
        case 1:
        case 2:
        case 7:
          renderItem = (
            <span>
              {`${t('范围条件{}: ', index + 1)}${e.beginTime}< ${e.scopeTypeName} < ${e.closeTime}`}
            </span>
          );
          break;
        case 3:
        case 4:
        case 6:
        case 8:
          renderItem = (
            <div>
              <div>
                {t('范围条件{}: ', index + 1)}
                <span style={{ marginRight: 5 }}>
                  {e.scopeTypeName}
                </span>
                {row?.expand ? e.scopeName?.join('、') : e.scopeName?.slice(0, 5).join('、')}
                {!row.expand && e.scopeName && e.scopeName?.length > 5 && (
                <span
                  style={{ marginLeft: 5, cursor: 'pointer' }}
                  onClick={() => {
                    onClickRange(row);
                  }}
                >
                  {t('...')}
                </span>
                )}
              </div>
            </div>
          );
          break;
        case 5:
        case 9:
        case 10:
        case 11:
          renderItem = <div>{`${t('范围条件{}: ', index + 1)}${e.scopeTypeName}`}</div>;
          break;

        default:
          renderItem = null;
          break;
      }
      return (
        <div key={JSON.stringify(index)}>
          {renderItem}
        </div>
      );
    }),
  }];
  return (
    <Modal
      title={t('所在库存分配池子')}
      visible={visible}
      style={{ width: 800, maxHeight: 600, overflowY: 'auto' }}
      onClose={() => {
        if (onClose) {
          onClose();
        }
      }}
      footer={[
        <Button
          type="default"
          onClick={() => {
            if (onClose) {
              onClose();
            }
          }}
        >
          {t('取消')}
        </Button>,
      ]}
    >
      <Table
        bordered
        columns={columns}
        data={list || []}
        keygen="id"
        style={{ maxHeight: 450 }}
        rowClassName={rowClassName}
      />
    </Modal>
  );
}

export default PoolDetailModal;
