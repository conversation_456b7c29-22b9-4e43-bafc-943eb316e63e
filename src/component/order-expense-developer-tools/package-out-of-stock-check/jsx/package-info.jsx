import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Button } from 'shineout';
import styles from '../style.less';
import store from '../reducers';

const getColuText = (val) => {
  if (val === true) {
    return t('是');
  }
  if (val === false) {
    return t('否');
  }
  return '';
};

class PackageInfo extends React.Component {
  render() {
    const {
      packageInfo,
      limit,
    } = this.props;

    const data = [[
      { label: t('订单号'), value: packageInfo.billNo },
      { label: t('下发时间'), value: packageInfo.inflowTime },
      { label: t('付款时间'), value: packageInfo.payTime },
      { label: t('特殊标识'), value: packageInfo.specialMarkName },
    ], [
      { label: t('包裹号'), value: packageInfo.packageNo },
      { label: t('包裹状态'), value: packageInfo.packageStatusName },
      { label: t('计划分配时间 (CAT)'), value: packageInfo.catTime },
      { label: t('渠道组'), value: packageInfo.channelGroupName },
    ], [
      { label: t('商品数量'), value: `${packageInfo.goodsCount || ''} ${packageInfo.goodsCountType ? `（${packageInfo.goodsCountType}）` : ''}` },
      { label: t('超限额等待'), value: packageInfo.excessStatusName },
      { label: t('释放时间'), value: packageInfo.excessReleaseTime },
      { label: t('渠道'), value: packageInfo.channelName },
    ], [
      { label: t('订单波次标记'), value: packageInfo.orderTypeName },
      { label: t('是否缺货'), value: getColuText(packageInfo.isLack) },
      { label: t('合包类型'), value: packageInfo.combinePackageType },
      { label: t('包裹国家线'), value: packageInfo.nationalLineTypeName },
    ], [
      { label: t('是否RBC包裹'), value: getColuText(packageInfo.isRbc) },
      { label: t('跨片区已完全占用'), value: getColuText(packageInfo.isCrossAreaAllOccupy) },
      { label: t('是否RBC异常超时'), value: getColuText(packageInfo.isRbcTimeout) },
      { label: t('预设波次国家线'), value: packageInfo.wellenNationalLineTypeName },
    ], [
      { label: t('智能波次组波失败次数'), value: packageInfo.failCount },
    ]];

    return (
      <div className={styles.itemWrapper}>
        <div className={styles.itemTitle}>
          {t('包裹信息')}
          <Button
            type="primary"
            style={{ marginLeft: 10 }}
            disabled={!limit?.packageNo}
            onClick={() => { store.queryInventoryAllocatePool(); }}
          >
            {t('查看库存分配池子')}
          </Button>
          <Button
            type="primary"
            style={{ marginLeft: 10 }}
            disabled={!limit?.packageNo}
            onClick={() => { store.queryWaveRuleList(); }}
          >
            {t('查看智能波次匹配规则')}
          </Button>
        </div>
        <table className={styles.infoWrapper}>
          <tbody>
            {data.map((row, index) => (
            // eslint-disable-next-line react/no-array-index-key
              <tr key={index}>
                {row.map((col) => (
                  <>
                    <td className={styles.label}>
                      {col.label}
                      :
                    </td>
                    <td className={styles.value}>{col.value}</td>
                  </>
                ))}
              </tr>
            ))}
          </tbody>

        </table>
      </div>
    );
  }
}

PackageInfo.propTypes = {
  packageInfo: PropTypes.shape(),
  limit: PropTypes.shape(),
};

export default PackageInfo;
