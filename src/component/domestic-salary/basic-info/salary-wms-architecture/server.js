import { sendPostRequest } from '@src/server/common/public';

// 搜索
export const getListAPI = (param) => sendPostRequest({
  url: '/salary_wms_architecture/query_list',
  param,
}, process.env.WBMS_FRONT);

// 新增
export const registerAPI = (param) => sendPostRequest({
  url: '/salary_wms_architecture/add',
  param,
}, process.env.WBMS_FRONT);

// 编辑
export const registerEditAPI = (param) => sendPostRequest({
  url: '/salary_wms_architecture/edit',
  param,
}, process.env.WBMS_FRONT);

// 人员架构详情
export const getDetailAPI = (param) => sendPostRequest({
  url: '/salary_wms_architecture/query_detail',
  param,
}, process.env.WBMS_FRONT);

// 获取全部子仓数据
export const queryAllSubWarehouseDataAPI = (param) => sendPostRequest({
  url: '/sub_warehouse/query_list',
  param,
}, process.env.WBMS_FRONT);
