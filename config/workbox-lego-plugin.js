import { definePlugin } from '@shein-lego/apis';
import { InjectManifest } from 'workbox-webpack-plugin';

// docs: https://developer.chrome.com/docs/workbox/reference/workbox-build/#type-WebpackGenerateSWPartial
export default definePlugin(({ register, beforeWebpackCompiled }) => {
  register({ namespace: 'workbox-lego-plugin' });
  const workboxLegoPlugin = new InjectManifest({
    compileSrc: false,
    swSrc: './config/serviceworker.template.js', // 编写好的 sw.js 的位置，相对于根目录
    swDest: 'serviceWorker.js', // 经 webpack 处理后的 sw.js 位置
    maximumFileSizeToCacheInBytes: 209715200, // 适当调整预缓存的单个文件大小上限
  });
  beforeWebpackCompiled((webpackConfig) => {
    webpackConfig.plugins.push(workboxLegoPlugin);
  });
});
