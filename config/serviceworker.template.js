/* eslint-disable no-use-before-define */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-restricted-globals */
// eslint-disable-next-line no-undef
importScripts('https://assets2.dotfashion.cn/unpkg/@shein/workbox@7.1.0/workbox/workbox-sw.js');

// eslint-disable-next-line no-undef
importScripts('https://assets2.dotfashion.cn/unpkg/@wms/wms-public-tools@1.8.17/dist/customStrategies.js');

/** 非实时接口缓存
 * @typedef {Object<string, Object>} fetchCacheConfig,键值为接口缓存返回，sysytem为全局配置，如果需要按页面缓存，可配置为accept-pageurl
 */
const fetchCacheConfig = {
  system: { // 全局配置
    '/was/front/authRule/index/menu': 0,
    '/was/front/data_permission/warehouse/list': 0,
    '/was/front/data_permission/sub_warehouse/list': 0,
    '/wgs/front/menu/carousel/home_page': 1000 * 60 * 60,
    '/wgs/front/menu/system/home_page': 1000 * 60 * 60 * 24 * 3,
    '/wmd/front/warehouse/get_list': 1000 * 60 * 60 * 24 * 5,
    '/wmd/front/sub_warehouse/select': 1000 * 60 * 60 * 24 * 5,
    '/wmd/front/warehouse/get_warehouse_detail': 1000 * 60 * 60 * 24 * 5,
    '/wgs/front/config/getConfig': 0, // 0表示不缓存
    '/wmd/front/sub_warehouse/query_park': 1000 * 60 * 60 * 24 * 1, // 1天
    '/was/front/check_url_permission': {
      duration: 1000 * 60 * 10,
      cacheableResponse: {
        statuses: [200],
      },
    }, // 10分钟
    '/wmd/front/area/get_area': 0,
    '/wmd/front/area/get_area2': 0,
    '/wmd/front/area/get_floor': 1000 * 60 * 60 * 24 * 1, // 1天
    '/wmd/front/goods_store/query': 1000 * 60 * 30, // 30分钟
    '/wmd/front/goods_store_type/get_list': 1000 * 60 * 30, // 30分钟
    '/wmd/front/inf_sub_warehouses/query_region': 1000 * 60 * 60 * 24 * 1, // 1天
    '/wmd/front/area/get_roadway': 1000 * 60 * 60 * 24 * 1, // 1天
    '/wmd/front/sub_warehouse/query_region': 1000 * 60 * 60 * 24 * 1, // 1天
  },
  // accept-pageUrl
  '/wms-standard/work-order-management/work-order-deal/work-order-detail': {
    '/wmd/front/equipment_base/get_list': 0,
    '/wmd/front/equipment_material/get_list': 0,
    '/wmd/wstd/front/warehouse/get_list': 0,
    '/wmd/wstd/front/sub_warehouse/query_park': 0,
    '/wmd/front/sub_warehouse/query_region': 0,
    '/osm/front/staff/get_all_dept': 0,
  },
};

/**
 * 字典配置 & 参数配置 接口缓存
 *  @typedef {Object<string, ConfigDetail>} dictFetchConfig
 *  @typedef {Object} ConfigDetail - 配置详情
 *  @property {string} dbName - indexdb 库名
 *  @property {string} storeName - indexdb 表名
 *  @property {number} durationTime - 缓存时长,超过缓存时长会先返回缓存中的数据，然后闲时请求网络数据，默认为0
 *  @property {function} getCacheKey - 缓存键值，注意：1. param为已经转为下滑线的请求参数 2. 返回值必须为stirng[]
 *  @property {function} putCacheData - 定义如何将接口返回数据info转换为缓存数据，返回值为需要存储的数据，id为唯一标识，content为存储的内容
 *  @property {function} transformCacheData - cache数据转换，定义如何将缓存中的数据转换为接口返回的info, cacheData为{[参数值]对应数据}，cacheKey为getCacheKey返回值
 */
const dictFetchConfig = {
  '/wmd/front/dict/select': { // 字典配置接口请求url
    dbName: 'wmdDictionaryCacheDBSW',
    storeName: 'wmdDictionary',
    durationTime: 0,
    getCacheKey: (param) => param.cat_code,
    putCacheData: (info) => info.data.map((item) => ({
      id: item.cat_code,
      content: item.dict_list_rsps,
    })),
    transformCacheData: (cacheData, cacheKey) => ({
      data: cacheKey.map((item) => ({
        catCode: item,
        dictListRsps: cacheData[item],
      })),
      meta: { count: cacheKey.length, customObj: null },
    }),
  },
  '/wis/front/outside/dict/select': { // 字典配置接口请求url
    dbName: 'qcDictionaryCacheDBSW',
    storeName: 'qcDictionary',
    durationTime: 0,
    getCacheKey: (param) => param.cat_code,
    putCacheData: (info) => info.data.map((item) => ({
      id: item.cat_code,
      content: item.dict_list_rsps,
    })),
    transformCacheData: (cacheData, cacheKey) => ({
      data: cacheKey.map((item) => ({
        catCode: item,
        dictListRsps: cacheData[item],
      })),
      meta: { count: cacheKey.length, customObj: null },
    }),
  },
  '/wmd/front/config/getConfig': { // 参数配置接口请求url
    dbName: 'wmdConfigStoreSW',
    storeName: 'wmdConfigStore',
    durationTime: 1000 * 60 * 10,
    getCacheKey: (param) => [param.param],
    putCacheData: (info) => ([{
      id: info.configCode,
      content: info,
    }]),
    transformCacheData: (cacheData, cacheKey) => {
      const key = cacheKey[0];
      return cacheData[key || ''];
    },
  },
};

const config = {
  system: 'wms',
  version: 'v1',
  preCache: false, // 是否开启预缓存
  strategies: [
    {
      // Routing via a RegExp:
      urlPattern: ({ url }) => /^(https:\/\/assets(2)?\.dotfashion\.cn)((\/unpkg.*\.js)|(.*\.(css|svg|json)))$/.test(url.href)
          || /^https:\/\/assets(2)?\.dotfashion\.cn\/webassets\/wms-front\//.test(url.href),
      handler: 'CacheFirst',
      options: {
        cacheName: 'wms-cache',
        expiration: {
          maxEntries: 1000, // 最多缓存数量
          maxAgeSeconds: 60 * 60 * 24 * 7, // <== 7 days
        },
        cacheableResponse: {
          statuses: [200],
        },
      },
    },
    {
      // 为inbound子应用添加新的缓存策略
      urlPattern: ({ url }) => /^https:\/\/assets(2)?\.dotfashion\.cn\/webassets\/wms-inbound-front\//.test(url.href),
      handler: 'CacheFirst',
      options: {
        cacheName: 'wms-inbound-front-cache',
        expiration: {
          maxEntries: 1000, // 最多缓存数量
          maxAgeSeconds: 60 * 60 * 24 * 7, // <== 7 days
        },
        cacheableResponse: {
          statuses: [200],
        },
      },
    },
    {
      // 为outbound子应用添加新的缓存策略
      urlPattern: /^https:\/\/assets(2)?\.dotfashion\.cn\/webassets\/wms-outbound-front\//,
      handler: 'CacheFirst',
      options: {
        cacheName: 'wms-outbound-front-cache',
        expiration: {
          maxEntries: 1000, // 最多缓存数量
          maxAgeSeconds: 60 * 60 * 24 * 7, // <== 7 days
        },
        cacheableResponse: {
          statuses: [200],
        },
      },
    },
    {
      // 为std子应用添加新的缓存策略
      urlPattern: /^https:\/\/assets(2)?\.dotfashion\.cn\/webassets\/wms-std-front\//,
      handler: 'CacheFirst',
      options: {
        cacheName: 'wms-std-front-cache',
        expiration: {
          maxEntries: 1000, // 最多缓存数量
          maxAgeSeconds: 60 * 60 * 24 * 7, // <== 7 days
        },
        cacheableResponse: {
          statuses: [200],
        },
      },
    },
    {
      // 云配置接口缓存策略
      urlPattern: ({ url }) => /^(https:\/\/cloud-now.sheincorp.cn\/api\/options)/.test(url.href),
      handler: 'StaleWhileRevalidate',
      customStrategies: true,
      options: {
        cacheName: 'wms-cloud-cache',
        expiration: {
          maxEntries: 1000, // 最多缓存数量
          maxAgeSeconds: 60 * 60 * 24 * 7, // <== 7 days
        },
        cacheableResponse: {
          statuses: [200, 204],
        },
      },
    },
    {
      // 静态资源缓存策略
      urlPattern: ({ url }) => /^(https:\/\/assets(2)?\.dotfashion\.cn)(.*\.(css|js|png|svg|icon))$/.test(url.href),
      handler: 'NetworkFirst',
      options: {
        cacheName: 'wms-network-cache',
        expiration: {
          maxEntries: 1000, // 最多缓存数量
          maxAgeSeconds: 60 * 60 * 24 * 30, // <== 30 days
        },
        networkTimeoutSeconds: 5, // 设置networkFirst 的请求超时时间，超过指定秒数后，就直接返回缓存
        cacheableResponse: {
          statuses: [200],
        },
      },
    },
    {
      urlPattern: ({ url, request }) => {
        const pageURL = request?.headers?.get('accept-pageurl') || 'system';
        const pageConfig = fetchCacheConfig[pageURL]?.[url.pathname] !== undefined;
        const systemConfig = fetchCacheConfig.system?.[url.pathname] !== undefined;
        return pageConfig || systemConfig;
      },
      handler: 'CacheWhileValid',
      method: 'POST',
      isCustomStrategies: true,
      options: {
        expirationConfig: fetchCacheConfig,
        cacheableResponse: {
          statuses: [200],
          codes: ['0'],
        },
      },
    },
    {
      urlPattern: ({ url, request }) => {
        const pageURL = request?.headers?.get('accept-pageurl') || 'system';
        const pageConfig = fetchCacheConfig[pageURL]?.[url.pathname] !== undefined;
        const systemConfig = fetchCacheConfig.system?.[url.pathname] !== undefined;
        return pageConfig || systemConfig;
      },
      handler: 'CacheWhileValid',
      method: 'GET',
      isCustomStrategies: true,
      options: {
        expirationConfig: fetchCacheConfig,
        cacheableResponse: {
          statuses: [200],
          codes: ['0'],
        },
      },
    },
    {
      urlPattern: ({ url }) => dictFetchConfig[url.pathname] !== undefined,
      handler: 'CacheWhileAllValid',
      method: 'POST',
      isCustomStrategies: true,
      options: {
        expirationConfig: dictFetchConfig,
        cacheableResponse: {
          statuses: [200],
          codes: ['0'],
        },
      },
    },
  ],
  detectUnregiter: false, // 黑五期间暂停轮询
  detectUnregiterIntervalTick: 60 * 1000, // 若detectUnregiter为true，则每分钟进行一次检查sw是否禁止。固定间隔为60秒
  networkHostBackup: [
    {
      originHost: '',
      targetHost: '',
      timeout: 300000,
      abnormalLogCenter: null,
    },
  ],
  debug: false,
};

if (config.debug) {
  // eslint-disable-next-line no-undef
  workbox.setConfig({ debug: true });
}

if (config.system) {
  // eslint-disable-next-line no-undef
  workbox.core.setCacheNameDetails({ prefix: config.system });
}

// sw 安装的回调，只有安装以及更新的时候才会执行，同时会跳过waiting状态，直接进入activate；
self.addEventListener('install', () => {
  self.skipWaiting();
});

// 清除缓存：不能直接用 ExpirationPlugin 的删除 API，否则删除的是新一轮的图片资源（此时还没有），而旧的不会被涉及
self.addEventListener('activate', (event) => {
  // 获取全部缓存空间
  const cacheNameList = config.strategies.reduce((prev, cur) => {
    if (!prev.includes(cur.options.cacheName) && !!cur.options.cacheName) {
      prev.push(cur.options.cacheName);
    }
    return prev;
  }, []);
  const promiseDeleteCaches = Promise.all(
    cacheNameList.map((cacheName) => caches
      .open(cacheName)
      .then((cache) => {
        cache.keys().then((cacheNames) => {
          cacheNames.forEach((item) => cache.delete(item));
        });
      })),
  );
  event.waitUntil(promiseDeleteCaches.then(() => {
    self.clients.claim();
  }));
});

// 检测是否强制卸载，则强制卸载此版本sw
if (config.detectUnregiter) { // 这个行为是在sw内部发起，所以是卸载线上sw的保底，也是最后兜底的手段
  const detectUnregiterInterval = setInterval(() => {
    detectForceUninstall(detectUnregiterInterval, config);
  }, config.detectUnregiterIntervalTick);
}

if (config.preCache) {
  // eslint-disable-next-line no-restricted-globals
  const precacheList = self.__WB_MANIFEST || []; // webpack编译后的文件列表
  // eslint-disable-next-line no-undef
  workbox.precaching.precacheAndRoute(precacheList);
  // eslint-disable-next-line no-undef
  workbox.precaching.cleanupOutdatedCaches(); // 清除已过期预缓存
}

if (config.strategies && config.strategies.length > 0) {
  config.strategies.forEach((item) => {
    const {
      urlPattern, options = {}, method, isCustomStrategies,
    } = item;
    const {
      cacheName, expiration, cacheableResponse, ...rest
    } = options;

    if (isCustomStrategies) {
      // eslint-disable-next-line no-undef
      workbox.routing.registerRoute(
        urlPattern,
        // eslint-disable-next-line no-undef
        new wmsPublicTools.customStrategies[item.handler]({
          cacheableResponse,
          ...rest,
        }),
        method,
      );
    } else {
      const plugins = [
        // eslint-disable-next-line no-undef
        cacheableResponse && new workbox.cacheableResponse.CacheableResponsePlugin(cacheableResponse), // 请求成功才缓存
        // eslint-disable-next-line no-undef
        expiration && new workbox.expiration.ExpirationPlugin(expiration), // 缓存条数、时间限制
        {
          fetchDidSucceed: async ({
            request, response,
          }) => {
            // 当 no-cors 模式失败时，尝试使用 cors 模式
            if (request.mode === 'no-cors' && (!response || !response.url || !response.ok)) {
              console.warn('no-cors fetch failed, retrying with cors mode:', request.url);
              return fetch(request.url, { mode: 'cors' });
            }
            return response;
          },
        },
      ].filter(Boolean);
      // 缓存优先/网络优先
      // eslint-disable-next-line no-undef
      workbox.routing.registerRoute(
        urlPattern,
        // eslint-disable-next-line no-undef
        new workbox.strategies[item.handler]({
          cacheName,
          plugins,
          ...rest,
        }),
      );
    }
  });
}

function detectForceUninstall(quitInterval) {
  // if (!version || !system) return;
  fetch(`/serviceWorker.js?t=${+new Date()}`, { method: 'HEAD' })
    .then((resp) => {
      if (resp.status === 404) { // 文件不存在，则卸载sw代理
        if (quitInterval) clearInterval(quitInterval); // 这里如果不clear，虽然sw已经卸载且不running，但是请求还会一直发送
        // https://love2dev.com/blog/how-to-uninstall-a-service-worker/
        self.registration
          .unregister()
          .then((isOk) => {
            if (isOk) {
              console.info('service worker is forbidden, uninstall success!');
              return self.clients.matchAll();
            }
            console.warn(
              'service worker unregister return false, can not found any register, skip uninstall!',
            );
            return null;
          })
          .then((clients) => {
            // eslint-disable-next-line no-useless-return
            if (!clients) return;
            // 在sw卸载后，刷新用户的每个页面，停止sw。 否则每个页面tab中，仍然会有sw脚本running
            clients.forEach((client) => {
              if ('navigate' in client && client.url) {
                console.log('client', client.url);
                // 刷新可能导致数据操作中断，谨慎处理
                // client.navigate(client.url);
              }
            });
          });
      }
    })
    .catch((error) => {
      console.error('detectForceUninstall error', error);
    });
}
