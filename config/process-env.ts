import * as process from 'process';

const BUILD_ENV = process.env.BUILD_ENV || process.env.DEV_ENV;
const isDev = process.argv.slice(-1)[0] === 'dev';

const local = {
  CLOUD_APPKEY: JSON.stringify('e8f062a43cbbd30f0a9a42dd'),
  wmsenv: JSON.stringify('local'),
  IS_DEV: JSON.stringify(isDev), // 用来兼容开发环境不配置external也能成功应用主题
};

const common = {
  QC_URI: JSON.stringify(''),
  LOCALE: JSON.stringify('zh-CN'),
  USE_REACT_CONTEXT: JSON.stringify(true),
  NPID: JSON.stringify('3'),
  APP_VERSION: JSON.stringify(process.env.VERSION || ''),
  PDF_URL: JSON.stringify('https://pdf.dotfashion.cn'),
  ALPHA_URL: JSON.stringify('wmshd.dotfashion.cn'),
  PROD_HOST: JSON.stringify('wms.dotfashion.cn'),
  // ALPHA_WMS_ACCEPT_AUTH_UID: JSON.stringify('wms_alpha_accept_auth'),
  // ALPHA_WMS_ACCEPT_AUTH_UID_KEY: JSON.stringify('wms_alpha_accept_auth_key'),
  CLOUD_KEY: JSON.stringify('pdaGuideline'),
  IM_GATEWAY_ADDR: JSON.stringify('https://im-gateway-b-test.sheincorp.cn'), // cloud-message云消息平台
  WMS_INTERNAL: JSON.stringify('/wms/internal/front'),
  WOS_URI: JSON.stringify('/wos/front'),
  WSOS_URI: JSON.stringify('/wsos/front'),
  WWS_URI: JSON.stringify('/wws/front'),
  WPOC_URI: JSON.stringify('/wpoc/front'),
  WAS_URI: JSON.stringify('/was/front'),
  INTERNAL_URI: JSON.stringify('/wms/internal/front'),
  BASE_URI: JSON.stringify('/wms/front'),
  BASE_URI_WMD: JSON.stringify('/wmd/front'),
  WTS_URI: JSON.stringify('/wts/front'),
  WSS_FRONT_CONFIG: JSON.stringify('wss/front/config'),
  WSS_FRONT: JSON.stringify('/wss/front'),
  WMS_FDG_FRONT: JSON.stringify('/wms/fdg/front'),
  WCS_FRONT: JSON.stringify('/wms_wcs/front'),
  WGS_FRONT: JSON.stringify('/wgs/front'),
  WBMS_FRONT: JSON.stringify('/wbms/front'),
  WGS_FRONT_BRIDGE: JSON.stringify('/wgs/front/bridge'),
  QMS: JSON.stringify('/qms'),
  QMS_FRONT: JSON.stringify('/qms/front'),
  WFRS_FRONT: JSON.stringify('/wfrs/front'),
  WOKB: JSON.stringify('/wokb'),
  WOKB_FRONT: JSON.stringify('/wokb/front'),
  WDS_FRONT: JSON.stringify('/wds/front'),
  BUILD_ENV: JSON.stringify(BUILD_ENV),
  SYSTEM: JSON.stringify('/system'),
  WATT_FRONT: JSON.stringify('/watt/front'),
  WST_FRONT: JSON.stringify('/wst/front'),
  WTS_FRONT: JSON.stringify('/wts/front'),
  ORS_FRONT: JSON.stringify('/ors/front'),
  OSM_FRONT: JSON.stringify('/osm/front'),
  WKB_FRONT: JSON.stringify('/wkb/front'),
  WOS_FRONT_FAST_HAND: JSON.stringify('/wos/front/fast_hand'),
  WOM_FRONT: JSON.stringify('/wom/front'),
  WOM_PRINT: JSON.stringify('/wom/front/print'),
  OSM_FRONT_ANDON: JSON.stringify('/osm/front/andon'),
  WSS_FRONT_INNER: JSON.stringify('/wss/front/inner'),
  WAS_FRONT_LOGIN: JSON.stringify('/was/front/login'),
  WTS_FRONT_BASIC: JSON.stringify('/wts/front/basic'),
  WIS: JSON.stringify('/wis'),
  WIS_FRONT: JSON.stringify('/wis/front'),
  WDG_FRONT: JSON.stringify('/wdg/front'),
  WAS_FRONT: JSON.stringify('/was/front'),
  WAS_FRONT_PDA: JSON.stringify('/was/front/pda'),
  WMD_STD_FRONT: JSON.stringify('/wmd/wstd/front'),
};

const dev = {
  wmsenv: JSON.stringify('dev'),
  LOGIN_ADDR: JSON.stringify('http://ulp-dev01.sheincorp.cn'),
  CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message-dev01.sheincorp.cn'),
  CLOUD_KEY: JSON.stringify('pdaGuidelineTest'),
  PDF_SERVER_URL: JSON.stringify('https://pdf-dev01.dotfashion.cn'), // PDF地址
  PDF_SERVER_DEGRADE_URL: JSON.stringify('https://pdf-dev01.dotfashion.cn'), // PDF降级服务地址
  WMS_TEST_SERVER_URL: JSON.stringify('https://wms-test01.dotfashion.cn'), // WMS测试环境地址
  IM_GATEWAY_ADDR: JSON.stringify('https://im-gateway-b-dev.sheincorp.cn'), // cloud-message云消息平台
};

const test = {
  wmsenv: JSON.stringify('test'),
  LOGIN_ADDR: JSON.stringify('http://ulp-test01.sheincorp.cn'),
  CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message-test01.sheincorp.cn'),
  CLOUD_KEY: JSON.stringify('pdaGuidelineTest'),
  AUTH_ENV: JSON.stringify('https://ulp-test01.sheincorp.cn'),
  CLOUD_APPKEY: JSON.stringify('e8f062a43cbbd30f0a9a42dd'),
  PDF_SERVER_URL: JSON.stringify('https://pdf-test01.dotfashion.cn'), // PDF地址
  PDF_SERVER_DEGRADE_URL: JSON.stringify('https://pdf-test01.dotfashion.cn'), // PDF降级服务地址
  WMS_TEST_SERVER_URL: JSON.stringify('wms-test01.dotfashion.cn'), // WMS测试环境地址
  IM_GATEWAY_ADDR: JSON.stringify('https://im-gateway-b-test.sheincorp.cn'), // cloud-message云消息平台
};

const sit = {
  wmsenv: JSON.stringify('sit'),
  LOGIN_ADDR: JSON.stringify('http://ulp-sit01.sheincorp.cn'),
  CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message-sit01.sheincorp.cn'),
  CLOUD_KEY: JSON.stringify('pdaGuidelineTest'),
  CLOUD_APPKEY: JSON.stringify('e8f062a43cbbd30f0a9a42dd'),
  PDF_SERVER_URL: JSON.stringify('https://pdf-sit01.dotfashion.cn'), // PDF地址
  PDF_SERVER_DEGRADE_URL: JSON.stringify('https://pdf-sit01.dotfashion.cn'), // PDF降级服务地址
  IM_GATEWAY_ADDR: JSON.stringify('https://im-gateway-b-sit.sheincorp.cn'), // cloud-message云消息平台
}

const pre = {
  wmsenv: JSON.stringify('alpha'),
  LOGIN_ADDR: JSON.stringify('https://ulp.sheincorp.cn'),
  AUTH_ENV: JSON.stringify('https://ulp.sheincorp.cn'),
  CLOUD_APPKEY: JSON.stringify('491e190771170666e64e5ba5'),
  CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message.sheincorp.cn'),
  PDF_SERVER_URL: JSON.stringify('https://pdf-service-ha.dotfashion.cn'), // PDF wms专用高可用 ha服务
  PDF_SERVER_DEGRADE_URL: JSON.stringify('https://pdf.dotfashion.cn'), // PDF降级服务地址
  IM_GATEWAY_ADDR: JSON.stringify('https://im-gateway-b.sheincorp.cn'), // cloud-message云消息平台
};

const pro = {
  wmsenv: JSON.stringify('production'),
  LOGIN_ADDR: JSON.stringify('https://ulp.sheincorp.cn'),
  AUTH_ENV: JSON.stringify('https://ulp.sheincorp.cn'),
  CLOUD_APPKEY: JSON.stringify('491e190771170666e64e5ba5'),
  CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message.sheincorp.cn'),
  PDF_SERVER_URL: JSON.stringify('https://pdf-service-ha.dotfashion.cn'), // PDF wms专用高可用 ha服务
  PDF_SERVER_DEGRADE_URL: JSON.stringify('https://pdf.dotfashion.cn'), // PDF降级服务地址
  IM_GATEWAY_ADDR: JSON.stringify('https://im-gateway-b.sheincorp.cn'), // cloud-message云消息平台
};

const envMap = {
  dev,
  test,
  sit,
  pre,
  pro,
};

const finalEnv = {
  ...common,
  ...(envMap[BUILD_ENV] || {}),
  ...(isDev ? local : {}),
};

const transformEnv = () => Object.keys(finalEnv).reduce((result, key) => {
  result[`process.env.${key}`] = finalEnv[key];
  return result;
}, {});

export default transformEnv();
