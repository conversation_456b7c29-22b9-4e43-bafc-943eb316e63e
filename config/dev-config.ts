/* eslint-disable @typescript-eslint/no-unused-vars */
import easyMock from '../esay-mock';

export default {
  alitaEntries: {
    outbound: 'http://localhost:8700/outbound.html',
    inbound: 'http://localhost:8770/inbound.html',
    'inbound-standard': 'http://localhost:8771/inbound-standard.html',
    'wms-standard': 'http://localhost:8870/wms-standard.html',
  },
  devServer: {
    host: 'localhost',
    port: 8000, // 自定义的本地端口
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: {
      '/': {
        target: 'http://wgw-cneast-test-test.test.paas-test.sheincorp.cn', // 测试环境
        secure: false,
        changeOrigin: true,
        // ...easyMock, /** 不需要本地mock时, 注释掉此行，重启服务即可 */
      },
    },
  },
};
